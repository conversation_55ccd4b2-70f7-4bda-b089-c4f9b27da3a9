#pragma once

#include <string>

namespace CryptoAudit {

// 审计事件类型枚举
enum class AuditEventType {
    // 密钥相关事件
    KEY_CREATED,
    KEY_ACCESSED,
    KEY_ROTATED,
    KEY_EXPIRED,
    KEY_REVOKED,
    
    // 策略相关事件
    POLICY_APPLIED,
    POLICY_VIOLATED,
    
    // 文档相关事件
    DOCUMENT_ENCRYPTED,
    DOCUMENT_DECRYPTED,
    
    // 外设相关事件
    PERIPHERAL_ACCESS,
    
    // 认证相关事件
    AUTH_SUCCESS,
    AUTH_FAILURE,
    
    // 系统事件
    SYSTEM_ERROR
};

// 审计事件结构
struct AuditEvent {
    AuditEventType type;
    std::string userId;
    std::string resourceId;     // 可能是文件ID、密钥ID等
    std::string resourceType;   // 如 "document", "key", "policy"
    std::string timestamp;      // ISO 8601 格式
    std::string details;        // 额外信息JSON
    std::string result;         // "success", "failure", "denied"等
};

} // namespace CryptoAudit 