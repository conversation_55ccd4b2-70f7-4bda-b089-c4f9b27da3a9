import Foundation
import SystemExtensions
import OSLog
import ServiceManagement

/**
 * 系统集成管理器
 * 负责系统扩展注册、权限管理和应用安装
 */
class SystemIntegration: NSObject, ObservableObject {
    
    // MARK: - Properties
    
    private let logger = Logger(subsystem: "com.cryptosystem.macos", category: "SystemIntegration")
    private let extensionBundleIdentifier = "com.cryptosystem.macos.file-provider"
    
    @Published var extensionStatus: ExtensionStatus = .unknown
    @Published var permissionStatus: PermissionStatus = .unknown
    @Published var installationProgress: InstallationProgress = .idle
    
    // MARK: - Enums
    
    enum ExtensionStatus {
        case unknown
        case notInstalled
        case installing
        case installed
        case enabled
        case error(String)
    }
    
    enum PermissionStatus {
        case unknown
        case denied
        case authorized
        case requesting
    }
    
    enum InstallationProgress {
        case idle
        case requestingPermissions
        case installingExtension
        case configuringServices
        case completed
        case failed(String)
    }
    
    // MARK: - Public Methods
    
    /**
     * 检查系统状态
     */
    func checkSystemStatus() {
        logger.info("检查系统状态")
        
        checkExtensionStatus()
        checkPermissionStatus()
    }
    
    /**
     * 完整系统安装
     */
    func performFullInstallation() async {
        logger.info("开始完整系统安装")
        
        await MainActor.run {
            installationProgress = .requestingPermissions
        }
        
        do {
            // 1. 请求权限
            try await requestPermissions()
            
            await MainActor.run {
                installationProgress = .installingExtension
            }
            
            // 2. 安装系统扩展
            try await installSystemExtension()
            
            await MainActor.run {
                installationProgress = .configuringServices
            }
            
            // 3. 配置服务
            try await configureServices()
            
            await MainActor.run {
                installationProgress = .completed
                extensionStatus = .enabled
                permissionStatus = .authorized
            }
            
            logger.info("系统安装完成")
            
        } catch {
            logger.error("系统安装失败: \(error.localizedDescription)")
            
            await MainActor.run {
                installationProgress = .failed(error.localizedDescription)
                extensionStatus = .error(error.localizedDescription)
            }
        }
    }
    
    /**
     * 卸载系统组件
     */
    func uninstallSystemComponents() async {
        logger.info("卸载系统组件")
        
        do {
            // 停止服务
            try await stopServices()
            
            // 卸载系统扩展
            try await uninstallSystemExtension()
            
            // 清理配置
            cleanupConfiguration()
            
            await MainActor.run {
                extensionStatus = .notInstalled
                installationProgress = .idle
            }
            
            logger.info("系统组件卸载完成")
            
        } catch {
            logger.error("卸载失败: \(error.localizedDescription)")
        }
    }
    
    // MARK: - Private Methods
    
    private func checkExtensionStatus() {
        let request = OSSystemExtensionRequest.propertiesRequest(
            forExtensionWithIdentifier: extensionBundleIdentifier,
            queue: .main
        ) { [weak self] properties, error in
            DispatchQueue.main.async {
                if let error = error {
                    self?.logger.error("检查扩展状态失败: \(error.localizedDescription)")
                    self?.extensionStatus = .error(error.localizedDescription)
                    return
                }
                
                if let properties = properties {
                    switch properties.state {
                    case .enabled:
                        self?.extensionStatus = .enabled
                    case .enabledNeedsReboot:
                        self?.extensionStatus = .installed
                    default:
                        self?.extensionStatus = .notInstalled
                    }
                } else {
                    self?.extensionStatus = .notInstalled
                }
            }
        }
        
        OSSystemExtensionManager.shared.submitRequest(request)
    }
    
    private func checkPermissionStatus() {
        // 检查Full Disk Access权限
        let testPath = "/System/Library/CoreServices"
        let fileManager = FileManager.default
        
        if fileManager.isReadableFile(atPath: testPath) {
            permissionStatus = .authorized
        } else {
            permissionStatus = .denied
        }
    }
    
    private func requestPermissions() async throws {
        logger.info("请求权限")
        
        await MainActor.run {
            permissionStatus = .requesting
        }
        
        // 检查并请求Full Disk Access
        if !hasFullDiskAccess() {
            try await requestFullDiskAccess()
        }
        
        await MainActor.run {
            permissionStatus = .authorized
        }
    }
    
    private func hasFullDiskAccess() -> Bool {
        let testPath = "/Library/Application Support"
        return FileManager.default.isReadableFile(atPath: testPath)
    }
    
    private func requestFullDiskAccess() async throws {
        // 打开系统偏好设置的隐私面板
        let url = URL(string: "x-apple.systempreferences:com.apple.preference.security?Privacy_AllFiles")!
        
        if NSWorkspace.shared.open(url) {
            logger.info("已打开系统偏好设置，请授权Full Disk Access")
            
            // 等待用户授权
            while !hasFullDiskAccess() {
                try await Task.sleep(nanoseconds: 1_000_000_000) // 1秒
            }
        } else {
            throw SystemIntegrationError.cannotOpenSystemPreferences
        }
    }
    
    private func installSystemExtension() async throws {
        logger.info("安装系统扩展")
        
        await MainActor.run {
            extensionStatus = .installing
        }
        
        return try await withCheckedThrowingContinuation { continuation in
            let request = OSSystemExtensionRequest.activationRequest(
                forExtensionWithIdentifier: extensionBundleIdentifier,
                queue: .main
            )
            
            request.delegate = SystemExtensionDelegate(continuation: continuation)
            OSSystemExtensionManager.shared.submitRequest(request)
        }
    }
    
    private func uninstallSystemExtension() async throws {
        logger.info("卸载系统扩展")
        
        return try await withCheckedThrowingContinuation { continuation in
            let request = OSSystemExtensionRequest.deactivationRequest(
                forExtensionWithIdentifier: extensionBundleIdentifier,
                queue: .main
            )
            
            request.delegate = SystemExtensionDelegate(continuation: continuation)
            OSSystemExtensionManager.shared.submitRequest(request)
        }
    }
    
    private func configureServices() async throws {
        logger.info("配置服务")
        
        // 创建配置目录
        let configDir = "/etc/cryptosystem"
        try FileManager.default.createDirectory(atPath: configDir, withIntermediateDirectories: true)
        
        // 创建日志目录
        let logDir = "/var/log/cryptosystem"
        try FileManager.default.createDirectory(atPath: logDir, withIntermediateDirectories: true)
        
        // 创建数据目录
        let dataDir = "/var/lib/cryptosystem"
        try FileManager.default.createDirectory(atPath: dataDir, withIntermediateDirectories: true)
        
        // 写入默认配置
        try createDefaultConfiguration()
        
        logger.info("服务配置完成")
    }
    
    private func createDefaultConfiguration() throws {
        let config = """
        {
            "server": {
                "base_url": "https://api.cryptosystem.com",
                "api_key": "",
                "timeout": 30,
                "retry_count": 3
            },
            "crypto": {
                "algorithm": "SM4-GCM",
                "key_size": 256,
                "enable_integrity_check": true
            },
            "monitoring": {
                "paths": [
                    "/Users/<USER>/Documents",
                    "/Users/<USER>/Desktop",
                    "/Users/<USER>/Downloads"
                ],
                "file_patterns": [
                    "*.doc", "*.docx", "*.pdf", "*.txt",
                    "*.xls", "*.xlsx", "*.ppt", "*.pptx"
                ],
                "exclude_patterns": [
                    "*.tmp", "*.log", "*.cache"
                ]
            },
            "audit": {
                "enabled": true,
                "batch_size": 50,
                "upload_interval": 900
            }
        }
        """
        
        let configPath = "/etc/cryptosystem/config.json"
        try config.write(toFile: configPath, atomically: true, encoding: .utf8)
    }
    
    private func stopServices() async throws {
        logger.info("停止服务")
        
        // 这里可以添加停止特定服务的逻辑
        try await Task.sleep(nanoseconds: 1_000_000_000) // 模拟停止时间
    }
    
    private func cleanupConfiguration() {
        logger.info("清理配置")
        
        let paths = [
            "/etc/cryptosystem",
            "/var/log/cryptosystem",
            "/var/lib/cryptosystem"
        ]
        
        for path in paths {
            do {
                try FileManager.default.removeItem(atPath: path)
            } catch {
                logger.warning("清理路径失败 \(path): \(error.localizedDescription)")
            }
        }
    }
}

// MARK: - System Extension Delegate

private class SystemExtensionDelegate: NSObject, OSSystemExtensionRequestDelegate {
    
    private let continuation: CheckedContinuation<Void, Error>
    private let logger = Logger(subsystem: "com.cryptosystem.macos", category: "SystemExtensionDelegate")
    
    init(continuation: CheckedContinuation<Void, Error>) {
        self.continuation = continuation
        super.init()
    }
    
    func request(_ request: OSSystemExtensionRequest, didFinishWithResult result: OSSystemExtensionRequest.Result) {
        logger.info("系统扩展请求完成: \(result.rawValue)")
        continuation.resume()
    }
    
    func request(_ request: OSSystemExtensionRequest, didFailWithError error: Error) {
        logger.error("系统扩展请求失败: \(error.localizedDescription)")
        continuation.resume(throwing: error)
    }
    
    func requestNeedsUserApproval(_ request: OSSystemExtensionRequest) {
        logger.info("系统扩展需要用户批准")
        // 继续等待用户批准
    }
    
    func request(_ request: OSSystemExtensionRequest, actionForReplacingExtension existing: OSSystemExtensionProperties, withExtension ext: OSSystemExtensionProperties) -> OSSystemExtensionRequest.ReplacementAction {
        logger.info("替换现有系统扩展")
        return .replace
    }
}

// MARK: - Errors

enum SystemIntegrationError: LocalizedError {
    case cannotOpenSystemPreferences
    case permissionDenied
    case extensionInstallationFailed
    case configurationError
    
    var errorDescription: String? {
        switch self {
        case .cannotOpenSystemPreferences:
            return "无法打开系统偏好设置"
        case .permissionDenied:
            return "权限被拒绝"
        case .extensionInstallationFailed:
            return "系统扩展安装失败"
        case .configurationError:
            return "配置错误"
        }
    }
} 