﻿#include <iostream>
#include <string>
#include <memory>
#include <thread>
#include <chrono>
#include <windows.h>
#include <ShlObj.h>
#include <fstream>

#include "FSDriver.h"
#include "CoreEngine.h"
#include "WatermarkManager.h"
#include "DeviceControlManager.h"
#include "../keymanager/KeyManager.h"
#include "../keymanager/PolicyManager.h"

// 简化日志函数，避免宏定义的问题
inline void logInfo(const std::string& msg) { std::cout << "[INFO] " << msg << std::endl; }
inline void logError(const std::string& msg) { std::cerr << "[ERROR] " << msg << std::endl; }
inline void logSuccess(const std::string& msg) { std::cout << "[SUCCESS] " << msg << std::endl; }

// 测试目录选择对话框
std::wstring selectMonitorDirectory() {
    // 默认监控目录
    std::wstring defaultDir = L"C:\\MonitorTest";
    
    // 检查默认目录是否存在，不存在则尝试创建
    if (CreateDirectoryW(defaultDir.c_str(), NULL) || GetLastError() == ERROR_ALREADY_EXISTS) {
        logInfo("将使用默认监控目录: " + std::string(defaultDir.begin(), defaultDir.end()));
        logInfo("(你可以在此目录中创建/修改文件来测试加密功能)");
        return defaultDir;
    }
    
    // 如果创建失败，则使用用户文档目录
    WCHAR documentsPath[MAX_PATH];
    HRESULT result = SHGetFolderPathW(NULL, CSIDL_PERSONAL, NULL, SHGFP_TYPE_CURRENT, documentsPath);
    
    if (SUCCEEDED(result)) {
        logInfo("将使用文档目录: " + std::string(documentsPath, documentsPath + wcslen(documentsPath)));
        return std::wstring(documentsPath);
    } else {
        // 最后尝试使用临时目录
        logError("无法获取用户文档目录，将使用临时目录");
        WCHAR tempPath[MAX_PATH];
        GetTempPathW(MAX_PATH, tempPath);
        return std::wstring(tempPath);
    }
}

// 测试文件处理函数
void testFileProcessing(const std::shared_ptr<CoreEngine>& engine, const std::wstring& testDir) {
    // 测试文件路径
    std::wstring testFilePath = testDir + L"\\test_doc.txt";
    
    // 创建测试文件
    std::ofstream testFile(testFilePath.c_str());
    if (testFile.is_open()) {
        testFile << "这是一个测试文档，用于验证加密功能。" << std::endl;
        testFile << "This is a test document for encryption verification." << std::endl;
        testFile.close();
        logSuccess("创建了测试文件: " + std::string(testFilePath.begin(), testFilePath.end()));
        
        // 手动处理文件
        logInfo("尝试手动处理文件...");
        if (engine->processFile(testFilePath, true)) {
            logSuccess("文件处理成功");
        } else {
            logError("文件处理失败");
        }
    } else {
        logError("无法创建测试文件");
    }
}

// 显示设备列表
void displayDevices() {
    auto& deviceManager = DeviceControlManager::getInstance();
    auto devices = deviceManager.getConnectedDevices();
    
    logInfo("----- 已连接设备列表 -----");
    if (devices.empty()) {
        logInfo("无已连接设备");
    } else {
        for (const auto& device : devices) {
            std::string type;
            switch (device.type) {
                case DeviceType::USB_STORAGE: type = "USB存储"; break;
                case DeviceType::CDROM: type = "光驱"; break;
                case DeviceType::FLOPPY: type = "软驱"; break;
                default: type = "其他"; break;
            }
            
            std::string access;
            switch (device.currentAccess) {
                case DeviceAccess::ALLOW: access = "完全允许"; break;
                case DeviceAccess::READ_ONLY: access = "只读"; break;
                case DeviceAccess::DENY: access = "禁止"; break;
                case DeviceAccess::FORCE_ENCRYPT: access = "强制加密"; break;
                default: access = "未知"; break;
            }
            
            std::wstring wideDesc = device.description;
            std::string desc(wideDesc.begin(), wideDesc.end());
            
            logInfo("设备ID: " + std::string(device.deviceId.begin(), device.deviceId.end()).substr(0, 30) + "...");
            logInfo("描述: " + desc);
            logInfo("类型: " + type + " | 访问权限: " + access);
            logInfo("连接时间: " + std::string(device.lastConnectTime.begin(), device.lastConnectTime.end()));
            logInfo("------------------------");
        }
    }
}

// 初始化水印
bool initializeWatermark() {
    auto& watermarkManager = WatermarkManager::getInstance();
    if (!watermarkManager.initialize()) {
        logError("初始化水印管理器失败");
        return false;
    }
    
    // 配置水印
    WatermarkConfig config;
    config.contentTemplate = L"机密文件 - {username} - {ip} - {datetime}";
    config.opacity = 0.2f; // 20%透明度
    config.fontSize = 18.0f;
    config.rotation = 25.0f;
    config.color = {0.8f, 0.0f, 0.0f, 1.0f}; // 红色
    config.refreshInterval = 60000; // 60秒刷新一次
    
    watermarkManager.setConfig(config);
    return true;
}

// 初始化设备管控
bool initializeDeviceControl(HWND hwnd) {
    auto& deviceManager = DeviceControlManager::getInstance();
    if (!deviceManager.initialize()) {
        logError("初始化设备管控失败");
        return false;
    }
    
    // 启动设备监控
    if (!deviceManager.startMonitoring(hwnd)) {
        logError("启动设备监控失败");
        return false;
    }
    
    // 设置默认策略
    deviceManager.setDefaultPolicy(DeviceType::USB_STORAGE, DeviceAccess::READ_ONLY);
    deviceManager.setDefaultPolicy(DeviceType::CDROM, DeviceAccess::ALLOW);
    deviceManager.setDefaultPolicy(DeviceType::FLOPPY, DeviceAccess::DENY);
    
    return true;
}

// 显示帮助菜单
void showHelpMenu() {
    logInfo("----- 命令菜单 -----");
    logInfo("t - 创建并处理测试文件");
    logInfo("s - 显示系统状态");
    logInfo("p - 暂停/恢复监控");
    logInfo("w - 开启/关闭屏幕水印");
    logInfo("d - 显示已连接设备");
    logInfo("u - 设置USB设备权限 (0-禁止/1-只读/2-允许)");
    logInfo("q - 退出程序");
    logInfo("h - 显示此帮助菜单");
    logInfo("-------------------");
}

// 消息处理函数（用于处理设备通知消息）
LRESULT CALLBACK WindowProc(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam) {
    // 处理设备变更消息
    if (msg == WM_DEVICECHANGE) {
        auto& deviceManager = DeviceControlManager::getInstance();
        if (deviceManager.handleDeviceMessage(hwnd, msg, wParam, lParam)) {
            return TRUE;
        }
    }
    
    return DefWindowProc(hwnd, msg, wParam, lParam);
}

// 创建隐藏窗口用于接收设备变更消息
HWND createMessageWindow() {
    // 注册窗口类
    WNDCLASSEX wcex = {};
    wcex.cbSize = sizeof(WNDCLASSEX);
    wcex.lpfnWndProc = WindowProc;
    wcex.hInstance = GetModuleHandle(NULL);
    wcex.lpszClassName = L"CryptoSystemMsgWindow";
    
    RegisterClassEx(&wcex);
    
    // 创建隐藏窗口
    HWND hwnd = CreateWindow(
        L"CryptoSystemMsgWindow",
        L"CryptoSystem Message Handler",
        0, // 不可见窗口
        0, 0, 0, 0, // 位置和大小无关紧要
        HWND_MESSAGE, // 消息窗口
        NULL,
        GetModuleHandle(NULL),
        NULL
    );
    
    return hwnd;
}

int main(int argc, char* argv[]) {
    logInfo("Windows客户端启动中...");
    
    // 创建隐藏窗口用于接收设备通知
    HWND msgWindow = createMessageWindow();
    if (!msgWindow) {
        logError("创建消息窗口失败");
        return 1;
    }
    
    // 选择监控目录
    std::wstring targetDirectory = selectMonitorDirectory();
    
    try {
        // 初始化组件
        logInfo("初始化核心组件...");
        
        // 初始化CoreEngine
        auto engine = std::make_shared<CoreEngine>();
        if (!engine->initialize()) {
            logError("初始化核心引擎失败！");
            return 1;
        }
        
        // 初始化文件系统驱动
        logInfo("启动文件系统驱动...");
        FSDriver driver(targetDirectory, engine);
        if (!driver.startMonitoring()) {
            logError("启动文件系统驱动失败！");
            return 1;
        }
        
        // 初始化水印管理器
        logInfo("初始化屏幕水印组件...");
        if (!initializeWatermark()) {
            logError("初始化水印管理器失败！");
            // 继续执行，水印功能不是核心功能
        }
        
        // 初始化设备管控
        logInfo("初始化设备管控组件...");
        if (!initializeDeviceControl(msgWindow)) {
            logError("初始化设备管控失败！");
            // 继续执行，设备管控不是核心功能
        }
        
        std::cout << "[SUCCESS] 监控目录: " << std::string(targetDirectory.begin(), targetDirectory.end()) << std::endl;
        logInfo("系统已成功启动！");
        
        // 显示帮助菜单
        showHelpMenu();
        
        // 创建用于标记暂停状态的变量
        bool isPaused = false;
        
        // 用于屏幕水印状态
        bool watermarkEnabled = false;
        
        // 主循环
        bool running = true;
        while (running) {
            std::cout << "\n>: ";
            char command;
            std::cin >> command;
            
            switch(command) {
                case 't': // 测试文件处理
                    testFileProcessing(engine, targetDirectory);
                    break;
                    
                case 's': // 显示状态
                    {
                        EngineState state = engine->getState();
                        std::string stateStr;
                        switch (state) {
                            case EngineState::INIT: stateStr = "初始化中"; break;
                            case EngineState::RUNNING: stateStr = "正常运行"; break;
                            case EngineState::PAUSED: stateStr = "已暂停"; break;
                            case EngineState::ERROR: stateStr = "错误状态"; break;
                            default: stateStr = "未知"; break;
                        }
                        logInfo("系统状态: " + stateStr);
                        logInfo("屏幕水印: " + std::string(watermarkEnabled ? "已启用" : "已禁用"));
                        logInfo("设备管控: 已启用");
                    }
                    break;
                    
                case 'p': // 暂停/恢复
                    isPaused = !isPaused;
                    engine->setPaused(isPaused);
                    if (isPaused) {
                        logInfo("系统监控已暂停");
                    } else {
                        logInfo("系统监控已恢复");
                    }
                    break;
                    
                case 'w': // 水印开关
                    {
                        auto& watermarkManager = WatermarkManager::getInstance();
                        if (watermarkEnabled) {
                            watermarkManager.disable();
                            watermarkEnabled = false;
                            logInfo("屏幕水印已禁用");
                        } else {
                            if (watermarkManager.enable()) {
                                watermarkEnabled = true;
                                logInfo("屏幕水印已启用");
                            } else {
                                logError("启用屏幕水印失败");
                            }
                        }
                    }
                    break;
                    
                case 'd': // 显示设备列表
                    displayDevices();
                    break;
                    
                case 'u': // 设置USB权限
                    {
                        int accessLevel;
                        std::cout << "输入USB设备权限等级 (0-禁止/1-只读/2-允许): ";
                        std::cin >> accessLevel;
                        
                        DeviceAccess access;
                        switch (accessLevel) {
                            case 0: access = DeviceAccess::DENY; break;
                            case 1: access = DeviceAccess::READ_ONLY; break;
                            case 2: access = DeviceAccess::ALLOW; break;
                            default: 
                                logError("无效的权限等级");
                                continue;
                        }
                        
                        auto& deviceManager = DeviceControlManager::getInstance();
                        deviceManager.setDefaultPolicy(DeviceType::USB_STORAGE, access);
                        
                        std::string accessStr;
                        switch (access) {
                            case DeviceAccess::DENY: accessStr = "禁止"; break;
                            case DeviceAccess::READ_ONLY: accessStr = "只读"; break;
                            case DeviceAccess::ALLOW: accessStr = "允许"; break;
                            default: accessStr = "未知"; break;
                        }
                        
                        logInfo("已将USB设备默认权限设置为: " + accessStr);
                    }
                    break;
                    
                case 'h': // 帮助
                    showHelpMenu();
                    break;
                    
                case 'q': // 退出
                    running = false;
                    logInfo("准备退出程序...");
                    break;
                    
                default:
                    logError("未知命令，输入'h'查看帮助");
                    break;
            }
        }
        
        // 关闭水印
        logInfo("关闭屏幕水印...");
        WatermarkManager::getInstance().shutdown();
        
        // 关闭设备管控
        logInfo("关闭设备管控...");
        DeviceControlManager::getInstance().shutdown();
        
        logInfo("停止文件系统驱动...");
        driver.stopMonitoring();
        
        logInfo("关闭核心引擎...");
        engine->shutdown();
        
    } catch (const std::exception& e) {
        logError("发生异常: " + std::string(e.what()));
        return 1;
    } catch (...) {
        logError("发生未知异常");
        return 1;
    }
    
    // 销毁消息窗口
    if (msgWindow) {
        DestroyWindow(msgWindow);
    }
    
    logSuccess("Windows客户端已安全关闭");
    return 0;
} 