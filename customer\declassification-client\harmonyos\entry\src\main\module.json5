{"module": {"name": "entry", "type": "entry", "description": "$string:module_desc", "mainElement": "EntryAbility", "deviceTypes": ["phone", "tablet", "2in1", "car", "wearable", "tv"], "deliveryWithInstall": true, "installationFree": false, "pages": "$profile:main_pages", "abilities": [{"name": "EntryAbility", "srcEntry": "./ets/entryability/EntryAbility.ets", "description": "$string:EntryAbility_desc", "icon": "$media:layered_image", "label": "$string:EntryAbility_label", "startWindowIcon": "$media:startIcon", "startWindowBackground": "$color:start_window_background", "exported": true, "skills": [{"entities": ["entity.system.home"], "actions": ["ohos.want.action.home"]}]}], "extensionAbilities": [{"name": "EntryBackupAbility", "srcEntry": "./ets/entrybackupability/EntryBackupAbility.ets", "type": "backup", "exported": false, "metadata": [{"name": "ohos.extension.backup", "resource": "$profile:backup_config"}]}], "requestPermissions": [{"name": "ohos.permission.INTERNET", "reason": "$string:permission_internet_reason", "usedScene": {"abilities": ["EntryAbility"], "when": "inuse"}}, {"name": "ohos.permission.WRITE_USER_STORAGE", "reason": "$string:permission_storage_reason", "usedScene": {"abilities": ["EntryAbility"], "when": "inuse"}}, {"name": "ohos.permission.READ_USER_STORAGE", "reason": "$string:permission_storage_reason", "usedScene": {"abilities": ["EntryAbility"], "when": "inuse"}}, {"name": "ohos.permission.FILE_ACCESS_MANAGER", "reason": "$string:permission_file_reason", "usedScene": {"abilities": ["EntryAbility"], "when": "inuse"}}]}}