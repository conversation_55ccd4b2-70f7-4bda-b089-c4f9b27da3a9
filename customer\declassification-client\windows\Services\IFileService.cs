using CryptoSystem.DeclassificationClient.Models;

namespace CryptoSystem.DeclassificationClient.Services
{
    /// <summary>
    /// 文件服务接口
    /// </summary>
    public interface IFileService
    {
        /// <summary>
        /// 分析文件信息
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件信息</returns>
        Task<DeclassificationFile?> AnalyzeFileAsync(string filePath);

        /// <summary>
        /// 批量分析文件
        /// </summary>
        /// <param name="filePaths">文件路径列表</param>
        /// <param name="progress">进度回调</param>
        /// <returns>文件信息列表</returns>
        Task<List<DeclassificationFile>> AnalyzeFilesAsync(List<string> filePaths, IProgress<int>? progress = null);

        /// <summary>
        /// 计算文件MD5哈希
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>MD5哈希值</returns>
        Task<string> CalculateFileMD5Async(string filePath);

        /// <summary>
        /// 检测文件类型
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件类型</returns>
        FileType DetectFileType(string filePath);

        /// <summary>
        /// 检查文件是否加密
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否加密</returns>
        Task<bool> IsFileEncryptedAsync(string filePath);

        /// <summary>
        /// 获取文件安全等级
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>安全等级</returns>
        Task<SecurityLevel> GetFileSecurityLevelAsync(string filePath);

        /// <summary>
        /// 复制文件
        /// </summary>
        /// <param name="sourcePath">源文件路径</param>
        /// <param name="targetPath">目标文件路径</param>
        /// <param name="overwrite">是否覆盖</param>
        /// <returns>是否成功</returns>
        Task<bool> CopyFileAsync(string sourcePath, string targetPath, bool overwrite = false);

        /// <summary>
        /// 移动文件
        /// </summary>
        /// <param name="sourcePath">源文件路径</param>
        /// <param name="targetPath">目标文件路径</param>
        /// <param name="overwrite">是否覆盖</param>
        /// <returns>是否成功</returns>
        Task<bool> MoveFileAsync(string sourcePath, string targetPath, bool overwrite = false);

        /// <summary>
        /// 删除文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否成功</returns>
        Task<bool> DeleteFileAsync(string filePath);

        /// <summary>
        /// 创建目录
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        /// <returns>是否成功</returns>
        Task<bool> CreateDirectoryAsync(string directoryPath);

        /// <summary>
        /// 获取临时文件路径
        /// </summary>
        /// <param name="extension">文件扩展名</param>
        /// <returns>临时文件路径</returns>
        string GetTempFilePath(string extension = ".tmp");

        /// <summary>
        /// 获取临时目录路径
        /// </summary>
        /// <returns>临时目录路径</returns>
        string GetTempDirectoryPath();

        /// <summary>
        /// 清理临时文件
        /// </summary>
        /// <param name="olderThanHours">清理多少小时前的文件</param>
        /// <returns>清理的文件数量</returns>
        Task<int> CleanupTempFilesAsync(int olderThanHours = 24);

        /// <summary>
        /// 验证文件完整性
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="expectedMD5">期望的MD5值</param>
        /// <returns>是否完整</returns>
        Task<bool> ValidateFileIntegrityAsync(string filePath, string expectedMD5);

        /// <summary>
        /// 获取文件大小
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件大小（字节）</returns>
        long GetFileSize(string filePath);

        /// <summary>
        /// 格式化文件大小
        /// </summary>
        /// <param name="bytes">字节数</param>
        /// <returns>格式化的大小字符串</returns>
        string FormatFileSize(long bytes);

        /// <summary>
        /// 检查文件是否存在
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否存在</returns>
        bool FileExists(string filePath);

        /// <summary>
        /// 检查目录是否存在
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        /// <returns>是否存在</returns>
        bool DirectoryExists(string directoryPath);

        /// <summary>
        /// 获取文件扩展名
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件扩展名</returns>
        string GetFileExtension(string filePath);

        /// <summary>
        /// 获取不带扩展名的文件名
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>不带扩展名的文件名</returns>
        string GetFileNameWithoutExtension(string filePath);

        /// <summary>
        /// 获取文件名
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件名</returns>
        string GetFileName(string filePath);

        /// <summary>
        /// 获取目录路径
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>目录路径</returns>
        string GetDirectoryPath(string filePath);
    }
} 