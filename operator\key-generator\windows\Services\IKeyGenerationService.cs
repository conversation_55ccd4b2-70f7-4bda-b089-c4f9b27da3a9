using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using KeyGenerator.Models;

namespace KeyGenerator.Services
{
    /// <summary>
    /// 密钥生成服务接口（精简版 - 仅支持需求明确的功能）
    /// </summary>
    public interface IKeyGenerationService
    {
        /// <summary>
        /// 生成主密钥（AES-256）
        /// </summary>
        /// <param name="request">密钥生成请求</param>
        /// <returns>生成的密钥信息</returns>
        Task<KeyGenerationResult> GenerateMasterKeyAsync(KeyGenerationRequest request);

        /// <summary>
        /// 获取支持的算法列表
        /// </summary>
        /// <returns>支持的算法列表</returns>
        List<CryptoAlgorithm> GetSupportedAlgorithms();
    }

    /// <summary>
    /// 密钥生成请求
    /// </summary>
    public class KeyGenerationRequest
    {
        /// <summary>
        /// 密钥类型
        /// </summary>
        public KeyType KeyType { get; set; }

        /// <summary>
        /// 密钥名称
        /// </summary>
        public required string KeyName { get; set; }

        /// <summary>
        /// 密钥描述
        /// </summary>
        public required string KeyDescription { get; set; }

        /// <summary>
        /// 加密算法
        /// </summary>
        public CryptoAlgorithm Algorithm { get; set; }

        /// <summary>
        /// 密钥长度（位）
        /// </summary>
        public int KeyLength { get; set; }

        /// <summary>
        /// 客户单位ID
        /// </summary>
        public required string ClientId { get; set; }

        /// <summary>
        /// 客户单位名称
        /// </summary>
        public required string ClientName { get; set; }

        /// <summary>
        /// 生效时间
        /// </summary>
        public DateTime EffectiveDate { get; set; }

        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTime ExpirationDate { get; set; }

        /// <summary>
        /// 使用策略
        /// </summary>
        public required string UsagePolicy { get; set; }

        /// <summary>
        /// 启用备份
        /// </summary>
        public bool EnableBackup { get; set; }

        /// <summary>
        /// 备注信息
        /// </summary>
        public required string Description { get; set; }

        /// <summary>
        /// 是否启用国密算法
        /// </summary>
        public bool UseNationalCrypto { get; set; }
    }

    /// <summary>
    /// 密钥生成结果
    /// </summary>
    public class KeyGenerationResult
    {
        /// <summary>
        /// 密钥ID
        /// </summary>
        public required string KeyId { get; set; }

        /// <summary>
        /// 密钥数据（加密存储）
        /// </summary>
        public required byte[] KeyData { get; set; }

        /// <summary>
        /// 密钥哈希值（用于验证）
        /// </summary>
        public required string KeyHash { get; set; }

        /// <summary>
        /// 生成时间
        /// </summary>
        public DateTime GenerationTime { get; set; }

        /// <summary>
        /// 密钥状态
        /// </summary>
        public KeyStatus Status { get; set; }

        /// <summary>
        /// 错误信息（如果生成失败）
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 生成是否成功
        /// </summary>
        public bool IsSuccess => string.IsNullOrEmpty(ErrorMessage);
    }


} 