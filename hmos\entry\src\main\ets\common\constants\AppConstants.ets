/**
 * 应用常量定义
 */

/**
 * 应用信息常量
 */
export class AppConstants {
  /** 应用名称 */
  static readonly APP_NAME = '企业密钥生成器';
  
  /** 应用版本 */
  static readonly APP_VERSION = '1.0.0';
  
  /** 日志标签 */
  static readonly LOG_TAG = 'KeyGenerator';
  
  /** 日志域 */
  static readonly LOG_DOMAIN = 0x0000;
}

/**
 * 数据库常量
 */
export class DatabaseConstants {
  /** 数据库名称 */
  static readonly DB_NAME = 'key_generator.db';
  
  /** 数据库版本 */
  static readonly DB_VERSION = 1;
  
  /** 表名 */
  static readonly TABLE_KEYS = 'keys';
  static readonly TABLE_CLIENTS = 'clients';
  static readonly TABLE_TASKS = 'distribution_tasks';
  static readonly TABLE_LOGS = 'audit_logs';
  static readonly TABLE_SETTINGS = 'system_settings';
}

/**
 * UI常量
 */
export class UIConstants {
  /** 颜色 */
  static readonly PRIMARY_COLOR = '#0066cc';
  static readonly SUCCESS_COLOR = '#00c851';
  static readonly WARNING_COLOR = '#ff8800';
  static readonly ERROR_COLOR = '#ff4444';
  static readonly CRITICAL_COLOR = '#cc0000';
  
  /** 字体大小 */
  static readonly FONT_SIZE_SMALL = 12;
  static readonly FONT_SIZE_NORMAL = 14;
  static readonly FONT_SIZE_MEDIUM = 16;
  static readonly FONT_SIZE_LARGE = 18;
  static readonly FONT_SIZE_TITLE = 24;
  
  /** 间距 */
  static readonly PADDING_SMALL = 8;
  static readonly PADDING_NORMAL = 16;
  static readonly PADDING_LARGE = 24;
  
  /** 圆角 */
  static readonly BORDER_RADIUS_SMALL = 4;
  static readonly BORDER_RADIUS_NORMAL = 8;
  static readonly BORDER_RADIUS_LARGE = 12;
}

/**
 * 密钥常量
 */
export class KeyConstants {
  /** 默认密钥长度 */
  static readonly DEFAULT_KEY_LENGTH = 256;
  
  /** 支持的密钥长度 */
  static readonly SUPPORTED_KEY_LENGTHS = [128, 192, 256];
  
  /** 默认有效期（天） */
  static readonly DEFAULT_VALIDITY_DAYS = 365;
  
  /** 最大密钥名称长度 */
  static readonly MAX_KEY_NAME_LENGTH = 100;
  
  /** 最大备注长度 */
  static readonly MAX_REMARKS_LENGTH = 500;
}

/**
 * 网络常量
 */
export class NetworkConstants {
  /** 默认超时时间（毫秒） */
  static readonly DEFAULT_TIMEOUT = 30000;
  
  /** 重试次数 */
  static readonly MAX_RETRY_COUNT = 3;
  
  /** 默认端口 */
  static readonly DEFAULT_PORT = 8443;
}

/**
 * 存储常量
 */
export class StorageConstants {
  /** 偏好设置键名 */
  static readonly PREF_USER_SETTINGS = 'user_settings';
  static readonly PREF_SYSTEM_CONFIG = 'system_config';
  static readonly PREF_LAST_LOGIN = 'last_login';
  
  /** 缓存键名 */
  static readonly CACHE_KEY_LIST = 'key_list_cache';
  static readonly CACHE_CLIENT_LIST = 'client_list_cache';
  static readonly CACHE_TASK_LIST = 'task_list_cache';
}

/**
 * 错误码常量
 */
export class ErrorConstants {
  /** 通用错误 */
  static readonly ERROR_UNKNOWN = 'UNKNOWN_ERROR';
  static readonly ERROR_NETWORK = 'NETWORK_ERROR';
  static readonly ERROR_TIMEOUT = 'TIMEOUT_ERROR';
  
  /** 数据库错误 */
  static readonly ERROR_DB_CONNECTION = 'DB_CONNECTION_ERROR';
  static readonly ERROR_DB_QUERY = 'DB_QUERY_ERROR';
  
  /** 密钥相关错误 */
  static readonly ERROR_KEY_GENERATION = 'KEY_GENERATION_ERROR';
  static readonly ERROR_KEY_NOT_FOUND = 'KEY_NOT_FOUND';
  static readonly ERROR_KEY_EXPIRED = 'KEY_EXPIRED';
  
  /** 权限错误 */
  static readonly ERROR_PERMISSION_DENIED = 'PERMISSION_DENIED';
  static readonly ERROR_UNAUTHORIZED = 'UNAUTHORIZED';
}

/**
 * 事件常量
 */
export class EventConstants {
  /** 密钥事件 */
  static readonly EVENT_KEY_GENERATED = 'key_generated';
  static readonly EVENT_KEY_UPDATED = 'key_updated';
  static readonly EVENT_KEY_DELETED = 'key_deleted';
  
  /** 客户事件 */
  static readonly EVENT_CLIENT_ADDED = 'client_added';
  static readonly EVENT_CLIENT_UPDATED = 'client_updated';
  static readonly EVENT_CLIENT_DELETED = 'client_deleted';
  
  /** 分发事件 */
  static readonly EVENT_TASK_CREATED = 'task_created';
  static readonly EVENT_TASK_EXECUTED = 'task_executed';
  static readonly EVENT_TASK_COMPLETED = 'task_completed';
  
  /** 系统事件 */
  static readonly EVENT_SETTINGS_CHANGED = 'settings_changed';
  static readonly EVENT_USER_LOGIN = 'user_login';
  static readonly EVENT_USER_LOGOUT = 'user_logout';
}
