using System;
using System.Threading.Tasks;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using KeyGenerator.Models;
using KeyGenerator.Services;
using Microsoft.Extensions.Logging;

namespace KeyGenerator.ViewModels;

/// <summary>
/// 密钥生成页面ViewModel (简化版)
/// </summary>
public partial class KeyGenerationViewModel : ViewModelBase
{
    private readonly IKeyGenerationService _keyGenerationService;
    private readonly ILogger<KeyGenerationViewModel> _logger;

    [ObservableProperty]
    [NotifyCanExecuteChangedFor(nameof(GenerateKeyCommand))]
    private string _clientName = "";

    [ObservableProperty]
    private string _clientCode = "";

    [ObservableProperty]
    [NotifyCanExecuteChangedFor(nameof(GenerateKeyCommand))]
    private string _keyName = "";

    [ObservableProperty]
    [NotifyCanExecuteChangedFor(nameof(GenerateKeyCommand))]
    private DateTime _effectiveDate = DateTime.Today;

    [ObservableProperty]
    [NotifyCanExecuteChangedFor(nameof(GenerateKeyCommand))]
    private DateTime _expirationDate = DateTime.Today.AddYears(1);

    [ObservableProperty]
    private string _remarks = "";

    [ObservableProperty]
    private bool _isGenerating;

    [ObservableProperty]
    private string _generationStatusText = "请填写密钥信息以生成密钥。";

    public KeyGenerationViewModel(
        IKeyGenerationService keyGenerationService,
        ILogger<KeyGenerationViewModel> logger)
    {
        _keyGenerationService = keyGenerationService;
        _logger = logger;
    }

    [RelayCommand(CanExecute = nameof(CanGenerateKey))]
    private async Task GenerateKeyAsync()
    {
        IsGenerating = true;
        GenerationStatusText = "正在生成密钥，请稍候...";
        
        try
        {
            var request = new KeyGenerationRequest
            {
                ClientId = ClientCode,
                ClientName = ClientName,
                KeyName = KeyName,
                KeyDescription = Remarks,
                Algorithm = CryptoAlgorithm.AES256, // 固定使用AES256
                KeyLength = 256, // 固定256位
                EffectiveDate = EffectiveDate,
                ExpirationDate = ExpirationDate,
                UsagePolicy = "",
                EnableBackup = true,
                Description = Remarks
            };

            var result = await _keyGenerationService.GenerateMasterKeyAsync(request);
            GenerationStatusText = result.IsSuccess 
                ? $"成功生成密钥！ID: {result.KeyId}" 
                : $"生成失败: {result.ErrorMessage}";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成密钥时发生意外错误。");
            GenerationStatusText = "发生严重错误，请查看日志。";
        }
        finally
        {
            IsGenerating = false;
        }
    }

    [RelayCommand]
    private void ResetForm()
    {
        ClientName = "";
        ClientCode = "";
        KeyName = "";
        EffectiveDate = DateTime.Today;
        ExpirationDate = DateTime.Today.AddYears(1);
        Remarks = "";
        GenerationStatusText = "表单已重置。";
    }



    private bool CanGenerateKey()
    {
        return !string.IsNullOrWhiteSpace(ClientName) && 
               !string.IsNullOrWhiteSpace(KeyName) && 
               EffectiveDate < ExpirationDate &&
               !IsGenerating;
    }
} 