import Foundation
import SwiftUI
import Combine

/// 配置管理器
/// 负责管理应用程序的所有配置设置
@MainActor
class ConfigurationManager: ObservableObject {
    
    // MARK: - Singleton
    static let shared = ConfigurationManager()
    
    // MARK: - Published Properties
    @Published var configuration = AppConfiguration.default
    @Published var isLoading = false
    
    // MARK: - Private Properties
    private let configFile: URL
    private let userDefaults = UserDefaults.standard
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Configuration Keys
    private struct Keys {
        static let configuration = "app_configuration"
        static let lastUpdateCheck = "last_update_check"
        static let firstLaunch = "first_launch"
    }
    
    // MARK: - Initialization
    private init() {
        // 设置配置文件路径
        let appSupport = FileManager.default.urls(for: .applicationSupportDirectory, in: .userDomainMask).first!
        let configDirectory = appSupport.appendingPathComponent("DeclassificationClient")
        
        // 创建配置目录
        try? FileManager.default.createDirectory(at: configDirectory, withIntermediateDirectories: true)
        
        configFile = configDirectory.appendingPathComponent("configuration.json")
        
        setupConfigurationObserver()
    }
    
    // MARK: - Public Methods
    
    /// 加载配置
    func loadConfiguration() async {
        isLoading = true
        defer { isLoading = false }
        
        // 首先尝试从文件加载
        if let fileConfig = loadConfigurationFromFile() {
            configuration = fileConfig
        } else {
            // 如果文件不存在，尝试从UserDefaults加载
            if let defaultsConfig = loadConfigurationFromUserDefaults() {
                configuration = defaultsConfig
            } else {
                // 如果都没有，使用默认配置
                configuration = AppConfiguration.default
                await saveConfiguration()
            }
        }
        
        // 检查是否为首次启动
        if !userDefaults.bool(forKey: Keys.firstLaunch) {
            await handleFirstLaunch()
        }
    }
    
    /// 保存配置
    func saveConfiguration() async {
        do {
            // 保存到文件
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            encoder.outputFormatting = .prettyPrinted
            
            let data = try encoder.encode(configuration)
            try data.write(to: configFile)
            
            // 同时保存到UserDefaults作为备份
            userDefaults.set(data, forKey: Keys.configuration)
            
            await AuditLogger.shared.logEvent(
                type: .systemEvent,
                message: "保存应用配置",
                details: ["configFile": configFile.path]
            )
            
        } catch {
            await AuditLogger.shared.logEvent(
                type: .errorEvent,
                message: "保存配置失败",
                details: ["error": error.localizedDescription]
            )
        }
    }
    
    /// 重置配置为默认值
    func resetToDefault() async {
        configuration = AppConfiguration.default
        await saveConfiguration()
        
        await AuditLogger.shared.logEvent(
            type: .systemEvent,
            message: "重置配置为默认值"
        )
    }
    
    /// 导入配置
    func importConfiguration(from url: URL) async throws {
        let data = try Data(contentsOf: url)
        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = .iso8601
        
        let importedConfig = try decoder.decode(AppConfiguration.self, from: data)
        
        // 验证配置
        guard validateConfiguration(importedConfig) else {
            throw ConfigurationError.invalidConfiguration("导入的配置文件无效")
        }
        
        configuration = importedConfig
        await saveConfiguration()
        
        await AuditLogger.shared.logEvent(
            type: .systemEvent,
            message: "导入配置文件",
            details: ["importPath": url.path]
        )
    }
    
    /// 导出配置
    func exportConfiguration(to url: URL) async throws {
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        encoder.outputFormatting = .prettyPrinted
        
        let data = try encoder.encode(configuration)
        try data.write(to: url)
        
        await AuditLogger.shared.logEvent(
            type: .systemEvent,
            message: "导出配置文件",
            details: ["exportPath": url.path]
        )
    }
    
    /// 更新服务器配置
    func updateServerConfiguration(_ serverConfig: ServerConfiguration) async {
        configuration.server = serverConfig
        await saveConfiguration()
        
        await AuditLogger.shared.logEvent(
            type: .systemEvent,
            message: "更新服务器配置",
            details: [
                "host": serverConfig.host,
                "port": String(serverConfig.port)
            ]
        )
    }
    
    /// 更新安全配置
    func updateSecurityConfiguration(_ securityConfig: SecurityConfiguration) async {
        configuration.security = securityConfig
        await saveConfiguration()
        
        await AuditLogger.shared.logEvent(
            type: .securityEvent,
            message: "更新安全配置",
            details: [
                "encryptionEnabled": String(securityConfig.encryptionEnabled),
                "auditEnabled": String(securityConfig.auditEnabled)
            ]
        )
    }
    
    /// 更新用户界面配置
    func updateUIConfiguration(_ uiConfig: UIConfiguration) async {
        configuration.ui = uiConfig
        await saveConfiguration()
        
        await AuditLogger.shared.logEvent(
            type: .userAction,
            message: "更新界面配置",
            details: [
                "theme": uiConfig.theme.rawValue,
                "language": uiConfig.language.rawValue
            ]
        )
    }
    
    /// 检查配置更新
    func checkForConfigurationUpdates() async {
        let lastCheck = userDefaults.double(forKey: Keys.lastUpdateCheck)
        let lastCheckDate = Date(timeIntervalSince1970: lastCheck)
        
        // 如果距离上次检查超过24小时，则执行检查
        if Date().timeIntervalSince(lastCheckDate) > 86400 {
            // 这里可以实现从服务器获取配置更新的逻辑
            userDefaults.set(Date().timeIntervalSince1970, forKey: Keys.lastUpdateCheck)
            
            await AuditLogger.shared.logEvent(
                type: .systemEvent,
                message: "检查配置更新"
            )
        }
    }
    
    // MARK: - Private Methods
    
    private func loadConfigurationFromFile() -> AppConfiguration? {
        guard FileManager.default.fileExists(atPath: configFile.path) else { return nil }
        
        do {
            let data = try Data(contentsOf: configFile)
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601
            
            return try decoder.decode(AppConfiguration.self, from: data)
        } catch {
            print("从文件加载配置失败: \(error)")
            return nil
        }
    }
    
    private func loadConfigurationFromUserDefaults() -> AppConfiguration? {
        guard let data = userDefaults.data(forKey: Keys.configuration) else { return nil }
        
        do {
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601
            
            return try decoder.decode(AppConfiguration.self, from: data)
        } catch {
            print("从UserDefaults加载配置失败: \(error)")
            return nil
        }
    }
    
    private func validateConfiguration(_ config: AppConfiguration) -> Bool {
        // 验证服务器配置
        guard !config.server.host.isEmpty,
              config.server.port > 0 && config.server.port <= 65535 else {
            return false
        }
        
        // 验证其他必要字段
        guard !config.app.version.isEmpty else {
            return false
        }
        
        return true
    }
    
    private func setupConfigurationObserver() {
        // 监听配置变化并自动保存
        $configuration
            .dropFirst() // 忽略初始值
            .debounce(for: .seconds(1), scheduler: RunLoop.main)
            .sink { [weak self] _ in
                Task {
                    await self?.saveConfiguration()
                }
            }
            .store(in: &cancellables)
    }
    
    private func handleFirstLaunch() async {
        userDefaults.set(true, forKey: Keys.firstLaunch)
        
        // 设置默认配置
        configuration = AppConfiguration.default
        await saveConfiguration()
        
        await AuditLogger.shared.logEvent(
            type: .systemEvent,
            message: "应用首次启动",
            details: ["version": configuration.app.version]
        )
    }
}

// MARK: - Configuration Models

struct AppConfiguration: Codable {
    var app: AppInfo
    var server: ServerConfiguration
    var security: SecurityConfiguration
    var ui: UIConfiguration
    var processing: ProcessingConfiguration
    var audit: AuditConfiguration
    
    static let `default` = AppConfiguration(
        app: AppInfo(
            version: "1.0.0",
            buildNumber: "1001",
            lastUpdated: Date()
        ),
        server: ServerConfiguration(
            host: "localhost",
            port: 8443,
            useSSL: true,
            timeout: 30,
            retryCount: 3
        ),
        security: SecurityConfiguration(
            encryptionEnabled: true,
            auditEnabled: true,
            passwordPolicy: PasswordPolicy.strict,
            sessionTimeout: 3600,
            maxLoginAttempts: 3
        ),
        ui: UIConfiguration(
            theme: .system,
            language: .systemDefault,
            fontSize: .medium,
            showNotifications: true,
            autoRefresh: true
        ),
        processing: ProcessingConfiguration(
            maxConcurrentTasks: 3,
            maxFileSize: 100 * 1024 * 1024, // 100MB
            allowedFileTypes: [
                "pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx",
                "txt", "rtf", "jpg", "png", "gif", "zip"
            ],
            tempDirectory: NSTemporaryDirectory()
        ),
        audit: AuditConfiguration(
            logLevel: .info,
            maxLogAge: 90,
            exportFormat: .json,
            autoCleanup: true
        )
    )
}

struct AppInfo: Codable {
    let version: String
    let buildNumber: String
    let lastUpdated: Date
}

struct ServerConfiguration: Codable {
    var host: String
    var port: Int
    var useSSL: Bool
    var timeout: TimeInterval
    var retryCount: Int
    var username: String?
    var apiKey: String?
}

struct SecurityConfiguration: Codable {
    var encryptionEnabled: Bool
    var auditEnabled: Bool
    var passwordPolicy: PasswordPolicy
    var sessionTimeout: TimeInterval
    var maxLoginAttempts: Int
    var requireApproval: Bool = false
    var watermarkEnabled: Bool = true
}

struct UIConfiguration: Codable {
    var theme: AppTheme
    var language: AppLanguage
    var fontSize: FontSize
    var showNotifications: Bool
    var autoRefresh: Bool
    var refreshInterval: TimeInterval = 30
}

struct ProcessingConfiguration: Codable {
    var maxConcurrentTasks: Int
    var maxFileSize: Int64
    var allowedFileTypes: [String]
    var tempDirectory: String
    var compressionEnabled: Bool = true
}

struct AuditConfiguration: Codable {
    var logLevel: LogLevel
    var maxLogAge: Int
    var exportFormat: ExportFormat
    var autoCleanup: Bool
    var includeSystemEvents: Bool = true
}

// MARK: - Enums

enum AppTheme: String, Codable, CaseIterable {
    case light = "light"
    case dark = "dark"
    case system = "system"
    
    var displayName: String {
        switch self {
        case .light: return "浅色"
        case .dark: return "深色"
        case .system: return "跟随系统"
        }
    }
}

enum AppLanguage: String, Codable, CaseIterable {
    case systemDefault = "system"
    case chinese = "zh-Hans"
    case english = "en"
    
    var displayName: String {
        switch self {
        case .systemDefault: return "跟随系统"
        case .chinese: return "简体中文"
        case .english: return "English"
        }
    }
}

enum FontSize: String, Codable, CaseIterable {
    case small = "small"
    case medium = "medium"
    case large = "large"
    
    var displayName: String {
        switch self {
        case .small: return "小"
        case .medium: return "中"
        case .large: return "大"
        }
    }
    
    var pointSize: CGFloat {
        switch self {
        case .small: return 12
        case .medium: return 14
        case .large: return 16
        }
    }
}

enum PasswordPolicy: String, Codable, CaseIterable {
    case basic = "basic"
    case strict = "strict"
    case enterprise = "enterprise"
    
    var displayName: String {
        switch self {
        case .basic: return "基础"
        case .strict: return "严格"
        case .enterprise: return "企业级"
        }
    }
}

enum LogLevel: String, Codable, CaseIterable {
    case debug = "debug"
    case info = "info"
    case warning = "warning"
    case error = "error"
    
    var displayName: String {
        switch self {
        case .debug: return "调试"
        case .info: return "信息"
        case .warning: return "警告"
        case .error: return "错误"
        }
    }
}

enum ExportFormat: String, Codable, CaseIterable {
    case json = "json"
    case csv = "csv"
    case xml = "xml"
    
    var displayName: String {
        switch self {
        case .json: return "JSON"
        case .csv: return "CSV"
        case .xml: return "XML"
        }
    }
    
    var fileExtension: String {
        return rawValue
    }
}

// MARK: - Error Types

enum ConfigurationError: LocalizedError {
    case fileNotFound(String)
    case invalidConfiguration(String)
    case saveError(String)
    case loadError(String)
    
    var errorDescription: String? {
        switch self {
        case .fileNotFound(let path):
            return "配置文件未找到: \(path)"
        case .invalidConfiguration(let message):
            return "无效的配置: \(message)"
        case .saveError(let message):
            return "保存配置失败: \(message)"
        case .loadError(let message):
            return "加载配置失败: \(message)"
        }
    }
} 