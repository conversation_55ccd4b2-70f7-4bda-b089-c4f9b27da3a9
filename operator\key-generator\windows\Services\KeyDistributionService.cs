#pragma warning disable CS8632 // 只能在 "#nullable" 注释上下文内的代码中使用可为 null 的引用类型的注释

using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.IO;
using System.Net.Http;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;

namespace KeyGenerator.Services
{
    /// <summary>
    /// 密钥分发状态
    /// </summary>
    public enum KeyDistributionStatus
    {
        Pending,      // 待分发
        InProgress,   // 分发中
        Completed,    // 已完成
        Failed,       // 失败
        Cancelled,    // 已取消
        Retrying      // 重试中
    }

    /// <summary>
    /// 密钥层级类型
    /// </summary>
    public enum KeyHierarchyLevel
    {
        Master,       // 主密钥(MK)
        Organization, // 组织密钥(OK)
        User,         // 用户密钥(UK)
        Device,       // 设备密钥(DK)
        File          // 文件密钥(FK)
    }

    /// <summary>
    /// 密钥分发任务
    /// </summary>
    public class KeyDistributionTask
    {
        public string TaskId { get; set; } = Guid.NewGuid().ToString();
        public string TaskName { get; set; } = "";
        public KeyDistributionStatus Status { get; set; } = KeyDistributionStatus.Pending;
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? StartedAt { get; set; }
        public DateTime? CompletedAt { get; set; }
        public string CreatedBy { get; set; } = "";
        public string Description { get; set; } = "";
        public int Progress { get; set; } = 0;
        public string ErrorMessage { get; set; } = "";
        public int RetryCount { get; set; } = 0;
        public int MaxRetries { get; set; } = 3;
        
        // 分发目标
        public List<string> TargetClients { get; set; } = new();
        public List<string> TargetUsers { get; set; } = new();
        public List<string> TargetDevices { get; set; } = new();
        
        // 密钥信息
        public List<KeyDistributionItem> Keys { get; set; } = new();
        
        // 分发结果
        public List<KeyDistributionResult> Results { get; set; } = new();
    }

    /// <summary>
    /// 密钥分发项
    /// </summary>
    public class KeyDistributionItem
    {
        public string KeyId { get; set; } = "";
        public string KeyName { get; set; } = "";
        public KeyHierarchyLevel Level { get; set; }
        public string Algorithm { get; set; } = "SM4";
        public DateTime ExpiresAt { get; set; }
        public string ParentKeyId { get; set; } = "";
        public Dictionary<string, object> Metadata { get; set; } = new();
        public byte[] EncryptedKeyData { get; set; } = Array.Empty<byte>();
    }

    /// <summary>
    /// 密钥分发结果
    /// </summary>
    public class KeyDistributionResult
    {
        public string TargetId { get; set; } = "";
        public string TargetType { get; set; } = ""; // Client, User, Device
        public string KeyId { get; set; } = "";
        public bool Success { get; set; }
        public string ErrorMessage { get; set; } = "";
        public DateTime AttemptedAt { get; set; } = DateTime.Now;
        public int RetryCount { get; set; } = 0;
        public string ResponseData { get; set; } = "";
    }

    /// <summary>
    /// 密钥分发统计
    /// </summary>
    public class KeyDistributionStatistics
    {
        public int TotalTasks { get; set; }
        public int PendingTasks { get; set; }
        public int InProgressTasks { get; set; }
        public int CompletedTasks { get; set; }
        public int FailedTasks { get; set; }
        public int TotalKeysDistributed { get; set; }
        public int SuccessfulDistributions { get; set; }
        public int FailedDistributions { get; set; }
        public double SuccessRate { get; set; }
        public DateTime LastDistribution { get; set; }
    }

    /// <summary>
    /// 密钥分发配置
    /// </summary>
    public class KeyDistributionConfig
    {
        public int MaxConcurrentTasks { get; set; } = 5;
        public int MaxRetries { get; set; } = 3;
        public int RetryDelayMs { get; set; } = 5000;
        public int TaskTimeoutMs { get; set; } = 30000;
        public int BatchSize { get; set; } = 10;
        public bool EnableEncryption { get; set; } = true;
        public string EncryptionAlgorithm { get; set; } = "SM4";
        public bool EnableAuditLog { get; set; } = true;
        public string AuditLogLevel { get; set; } = "INFO";
    }

    /// <summary>
    /// 密钥分发服务接口
    /// </summary>
    public interface IKeyDistributionService
    {
        Task<string> CreateDistributionTaskAsync(KeyDistributionTask task);
        Task<bool> StartDistributionAsync(string taskId);
        Task<bool> PauseDistributionAsync(string taskId);
        Task<bool> CancelDistributionAsync(string taskId);
        Task<bool> RetryDistributionAsync(string taskId);
        Task<KeyDistributionTask?> GetDistributionTaskAsync(string taskId);
        Task<List<KeyDistributionTask>> GetDistributionTasksAsync(int page = 1, int pageSize = 20);
        Task<KeyDistributionStatistics> GetDistributionStatisticsAsync();
        Task<bool> DeleteDistributionTaskAsync(string taskId);
        Task<List<KeyDistributionResult>> GetDistributionResultsAsync(string taskId);
    }

    /// <summary>
    /// 密钥分发服务实现
    /// </summary>
    public class KeyDistributionService : IKeyDistributionService
    {
        private readonly ILogger<KeyDistributionService> _logger;
        private readonly IConfiguration _configuration;
        private readonly HttpClient _httpClient;
        private readonly KeyDistributionConfig _config;
        private readonly Dictionary<string, KeyDistributionTask> _tasks;
        private readonly Dictionary<string, CancellationTokenSource> _cancellationTokens;
        private readonly SemaphoreSlim _semaphore;

        public KeyDistributionService(
            ILogger<KeyDistributionService> logger,
            IConfiguration configuration,
            HttpClient httpClient)
        {
            _logger = logger;
            _configuration = configuration;
            _httpClient = httpClient;
            _config = LoadConfiguration();
            _tasks = new Dictionary<string, KeyDistributionTask>();
            _cancellationTokens = new Dictionary<string, CancellationTokenSource>();
            _semaphore = new SemaphoreSlim(_config.MaxConcurrentTasks, _config.MaxConcurrentTasks);
            
            // 初始化示例数据
            InitializeSampleData();
        }

        private KeyDistributionConfig LoadConfiguration()
        {
            var config = new KeyDistributionConfig();
            _configuration.GetSection("KeyDistribution").Bind(config);
            return config;
        }

        private void InitializeSampleData()
        {
            // 创建示例分发任务
            var sampleTask1 = new KeyDistributionTask
            {
                TaskId = "DIST-001",
                TaskName = "主密钥分发任务",
                Status = KeyDistributionStatus.Completed,
                CreatedAt = DateTime.Now.AddDays(-3),
                StartedAt = DateTime.Now.AddDays(-3).AddMinutes(5),
                CompletedAt = DateTime.Now.AddDays(-2).AddMinutes(15),
                CreatedBy = "admin",
                Description = "向所有客户端分发企业主密钥",
                Progress = 100,
                TargetClients = new() { "CLIENT-001", "CLIENT-002", "CLIENT-003" },
                Keys = new()
                {
                    new KeyDistributionItem
                    {
                        KeyId = "MK-001",
                        KeyName = "企业主密钥",
                        Level = KeyHierarchyLevel.Master,
                        Algorithm = "SM4",
                        ExpiresAt = DateTime.Now.AddYears(1),
                        EncryptedKeyData = GenerateRandomBytes(32)
                    }
                }
            };

            var sampleTask2 = new KeyDistributionTask
            {
                TaskId = "DIST-002",
                TaskName = "用户密钥批量分发",
                Status = KeyDistributionStatus.InProgress,
                CreatedAt = DateTime.Now.AddHours(-3),
                StartedAt = DateTime.Now.AddHours(-2),
                CreatedBy = "admin",
                Description = "向新用户分发个人密钥",
                Progress = 65,
                TargetUsers = new() { "USER-001", "USER-002", "USER-003", "USER-004" },
                Keys = new()
                {
                    new KeyDistributionItem
                    {
                        KeyId = "UK-001",
                        KeyName = "用户密钥组",
                        Level = KeyHierarchyLevel.User,
                        Algorithm = "SM4",
                        ExpiresAt = DateTime.Now.AddMonths(6),
                        ParentKeyId = "MK-001",
                        EncryptedKeyData = GenerateRandomBytes(32)
                    }
                }
            };

            var sampleTask3 = new KeyDistributionTask
            {
                TaskId = "DIST-003",
                TaskName = "设备密钥紧急分发",
                Status = KeyDistributionStatus.Failed,
                CreatedAt = DateTime.Now.AddHours(-1),
                StartedAt = DateTime.Now.AddMinutes(-45),
                CreatedBy = "admin",
                Description = "紧急向故障设备分发新密钥",
                Progress = 25,
                ErrorMessage = "网络连接超时",
                RetryCount = 2,
                TargetDevices = new() { "DEV-001", "DEV-002" },
                Keys = new()
                {
                    new KeyDistributionItem
                    {
                        KeyId = "DK-001",
                        KeyName = "设备密钥",
                        Level = KeyHierarchyLevel.Device,
                        Algorithm = "SM4",
                        ExpiresAt = DateTime.Now.AddMonths(3),
                        ParentKeyId = "UK-001",
                        EncryptedKeyData = GenerateRandomBytes(32)
                    }
                }
            };

            _tasks[sampleTask1.TaskId] = sampleTask1;
            _tasks[sampleTask2.TaskId] = sampleTask2;
            _tasks[sampleTask3.TaskId] = sampleTask3;
        }

        public async Task<string> CreateDistributionTaskAsync(KeyDistributionTask task)
        {
            try
            {
                task.TaskId = Guid.NewGuid().ToString();
                task.CreatedAt = DateTime.Now;
                task.Status = KeyDistributionStatus.Pending;
                
                _tasks[task.TaskId] = task;
                
                _logger.LogInformation("创建密钥分发任务: {TaskId} - {TaskName}", task.TaskId, task.TaskName);
                
                // 记录审计日志
                await LogAuditEventAsync("CREATE_DISTRIBUTION_TASK", task.TaskId, task.CreatedBy);
                
                return task.TaskId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建密钥分发任务失败: {Message}", ex.Message);
                throw;
            }
        }

        public async Task<bool> StartDistributionAsync(string taskId)
        {
            try
            {
                if (!_tasks.TryGetValue(taskId, out var task))
                {
                    _logger.LogWarning("找不到分发任务: {TaskId}", taskId);
                    return false;
                }

                if (task.Status != KeyDistributionStatus.Pending)
                {
                    _logger.LogWarning("任务状态不允许启动: {TaskId} - {Status}", taskId, task.Status);
                    return false;
                }

                task.Status = KeyDistributionStatus.InProgress;
                task.StartedAt = DateTime.Now;
                task.Progress = 0;

                // 创建取消令牌
                var cts = new CancellationTokenSource();
                _cancellationTokens[taskId] = cts;

                // 异步执行分发任务
                _ = Task.Run(async () => await ExecuteDistributionAsync(task, cts.Token));

                _logger.LogInformation("启动密钥分发任务: {TaskId}", taskId);
                
                // 记录审计日志
                await LogAuditEventAsync("START_DISTRIBUTION", taskId, "system");
                
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "启动密钥分发任务失败: {TaskId} - {Message}", taskId, ex.Message);
                return false;
            }
        }

        public async Task<bool> PauseDistributionAsync(string taskId)
        {
            try
            {
                if (!_tasks.TryGetValue(taskId, out var task))
                {
                    return false;
                }

                if (task.Status != KeyDistributionStatus.InProgress)
                {
                    return false;
                }

                if (_cancellationTokens.TryGetValue(taskId, out var cts))
                {
                    cts.Cancel();
                    _cancellationTokens.Remove(taskId);
                }

                task.Status = KeyDistributionStatus.Pending;
                
                _logger.LogInformation("暂停密钥分发任务: {TaskId}", taskId);
                
                // 记录审计日志
                await LogAuditEventAsync("PAUSE_DISTRIBUTION", taskId, "system");
                
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "暂停密钥分发任务失败: {TaskId} - {Message}", taskId, ex.Message);
                return false;
            }
        }

        public async Task<bool> CancelDistributionAsync(string taskId)
        {
            try
            {
                if (!_tasks.TryGetValue(taskId, out var task))
                {
                    return false;
                }

                if (_cancellationTokens.TryGetValue(taskId, out var cts))
                {
                    cts.Cancel();
                    _cancellationTokens.Remove(taskId);
                }

                task.Status = KeyDistributionStatus.Cancelled;
                
                _logger.LogInformation("取消密钥分发任务: {TaskId}", taskId);
                
                // 记录审计日志
                await LogAuditEventAsync("CANCEL_DISTRIBUTION", taskId, "system");
                
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取消密钥分发任务失败: {TaskId} - {Message}", taskId, ex.Message);
                return false;
            }
        }

        public async Task<bool> RetryDistributionAsync(string taskId)
        {
            try
            {
                if (!_tasks.TryGetValue(taskId, out var task))
                {
                    return false;
                }

                if (task.Status != KeyDistributionStatus.Failed)
                {
                    return false;
                }

                if (task.RetryCount >= task.MaxRetries)
                {
                    _logger.LogWarning("任务已达到最大重试次数: {TaskId}", taskId);
                    return false;
                }

                task.Status = KeyDistributionStatus.Retrying;
                task.RetryCount++;
                task.ErrorMessage = "";
                
                // 重新启动分发
                return await StartDistributionAsync(taskId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重试密钥分发任务失败: {TaskId} - {Message}", taskId, ex.Message);
                return false;
            }
        }

        public async Task<KeyDistributionTask?> GetDistributionTaskAsync(string taskId)
        {
            await Task.CompletedTask;
            return _tasks.TryGetValue(taskId, out var task) ? task : null;
        }

        public async Task<List<KeyDistributionTask>> GetDistributionTasksAsync(int page = 1, int pageSize = 20)
        {
            await Task.CompletedTask;
            return _tasks.Values
                .OrderByDescending(t => t.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToList();
        }

        public async Task<KeyDistributionStatistics> GetDistributionStatisticsAsync()
        {
            await Task.CompletedTask;
            
            var tasks = _tasks.Values.ToList();
            var totalKeys = tasks.SelectMany(t => t.Keys).Count();
            var successfulKeys = tasks.Where(t => t.Status == KeyDistributionStatus.Completed)
                .SelectMany(t => t.Keys).Count();
            
            return new KeyDistributionStatistics
            {
                TotalTasks = tasks.Count,
                PendingTasks = tasks.Count(t => t.Status == KeyDistributionStatus.Pending),
                InProgressTasks = tasks.Count(t => t.Status == KeyDistributionStatus.InProgress),
                CompletedTasks = tasks.Count(t => t.Status == KeyDistributionStatus.Completed),
                FailedTasks = tasks.Count(t => t.Status == KeyDistributionStatus.Failed),
                TotalKeysDistributed = totalKeys,
                SuccessfulDistributions = successfulKeys,
                FailedDistributions = totalKeys - successfulKeys,
                SuccessRate = totalKeys > 0 ? (double)successfulKeys / totalKeys * 100 : 0,
                LastDistribution = tasks.Where(t => t.CompletedAt.HasValue)
                    .OrderByDescending(t => t.CompletedAt)
                    .FirstOrDefault()?.CompletedAt ?? DateTime.MinValue
            };
        }

        public async Task<bool> DeleteDistributionTaskAsync(string taskId)
        {
            try
            {
                if (!_tasks.ContainsKey(taskId))
                {
                    return false;
                }

                // 如果任务正在运行，先取消
                if (_cancellationTokens.TryGetValue(taskId, out var cts))
                {
                    cts.Cancel();
                    _cancellationTokens.Remove(taskId);
                }

                _tasks.Remove(taskId);
                
                _logger.LogInformation("删除密钥分发任务: {TaskId}", taskId);
                
                // 记录审计日志
                await LogAuditEventAsync("DELETE_DISTRIBUTION_TASK", taskId, "system");
                
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除密钥分发任务失败: {TaskId} - {Message}", taskId, ex.Message);
                return false;
            }
        }

        public async Task<List<KeyDistributionResult>> GetDistributionResultsAsync(string taskId)
        {
            await Task.CompletedTask;
            
            if (_tasks.TryGetValue(taskId, out var task))
            {
                return task.Results;
            }
            
            return new List<KeyDistributionResult>();
        }

        private async Task ExecuteDistributionAsync(KeyDistributionTask task, CancellationToken cancellationToken)
        {
            try
            {
                await _semaphore.WaitAsync(cancellationToken);
                
                try
                {
                    _logger.LogInformation("开始执行密钥分发任务: {TaskId}", task.TaskId);
                    
                    var totalTargets = task.TargetClients.Count + task.TargetUsers.Count + task.TargetDevices.Count;
                    var completedTargets = 0;
                    
                    // 分发到客户端
                    foreach (var clientId in task.TargetClients)
                    {
                        if (cancellationToken.IsCancellationRequested)
                            break;
                            
                        await DistributeToTargetAsync(task, clientId, "Client", cancellationToken);
                        completedTargets++;
                        task.Progress = (int)((double)completedTargets / totalTargets * 100);
                    }
                    
                    // 分发到用户
                    foreach (var userId in task.TargetUsers)
                    {
                        if (cancellationToken.IsCancellationRequested)
                            break;
                            
                        await DistributeToTargetAsync(task, userId, "User", cancellationToken);
                        completedTargets++;
                        task.Progress = (int)((double)completedTargets / totalTargets * 100);
                    }
                    
                    // 分发到设备
                    foreach (var deviceId in task.TargetDevices)
                    {
                        if (cancellationToken.IsCancellationRequested)
                            break;
                            
                        await DistributeToTargetAsync(task, deviceId, "Device", cancellationToken);
                        completedTargets++;
                        task.Progress = (int)((double)completedTargets / totalTargets * 100);
                    }
                    
                    if (cancellationToken.IsCancellationRequested)
                    {
                        task.Status = KeyDistributionStatus.Cancelled;
                    }
                    else
                    {
                        task.Status = KeyDistributionStatus.Completed;
                        task.CompletedAt = DateTime.Now;
                        task.Progress = 100;
                    }
                    
                    _logger.LogInformation("密钥分发任务完成: {TaskId}", task.TaskId);
                }
                finally
                {
                    _semaphore.Release();
                    _cancellationTokens.Remove(task.TaskId);
                }
            }
            catch (Exception ex)
            {
                task.Status = KeyDistributionStatus.Failed;
                task.ErrorMessage = ex.Message;
                _logger.LogError(ex, "密钥分发任务执行失败: {TaskId} - {Message}", task.TaskId, ex.Message);
            }
        }

        private async Task DistributeToTargetAsync(KeyDistributionTask task, string targetId, string targetType, CancellationToken cancellationToken)
        {
            try
            {
                // 模拟分发过程
                await Task.Delay(Random.Shared.Next(1000, 3000), cancellationToken);
                
                // 模拟成功/失败
                var success = Random.Shared.NextDouble() > 0.1; // 90%成功率
                
                var result = new KeyDistributionResult
                {
                    TargetId = targetId,
                    TargetType = targetType,
                    KeyId = task.Keys.FirstOrDefault()?.KeyId ?? "",
                    Success = success,
                    ErrorMessage = success ? "" : "网络连接失败",
                    AttemptedAt = DateTime.Now,
                    ResponseData = success ? "OK" : "ERROR"
                };
                
                task.Results.Add(result);
                
                _logger.LogInformation("密钥分发到 {TargetType} {TargetId}: {Result}", targetType, targetId, success ? "成功" : "失败");
            }
            catch (Exception ex)
            {
                var result = new KeyDistributionResult
                {
                    TargetId = targetId,
                    TargetType = targetType,
                    KeyId = task.Keys.FirstOrDefault()?.KeyId ?? "",
                    Success = false,
                    ErrorMessage = ex.Message,
                    AttemptedAt = DateTime.Now
                };
                
                task.Results.Add(result);
                
                _logger.LogError(ex, "密钥分发到 {TargetType} {TargetId} 失败: {Message}", targetType, targetId, ex.Message);
            }
        }

        private async Task LogAuditEventAsync(string eventType, string resourceId, string userId)
        {
            try
            {
                // 记录审计日志的实现
                _logger.LogInformation("审计日志: {EventType} - {ResourceId} - {UserId}", eventType, resourceId, userId);
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "记录审计日志失败: {Message}", ex.Message);
            }
        }

        private static byte[] GenerateRandomBytes(int length)
        {
            var bytes = new byte[length];
            using var rng = RandomNumberGenerator.Create();
            rng.GetBytes(bytes);
            return bytes;
        }
    }
} 