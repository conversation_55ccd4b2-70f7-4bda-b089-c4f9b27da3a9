#include "ui/widgets/file_list_widget.h"

#include <QTableWidget>
#include <QHeaderView>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QMenu>
#include <QLocale>
#include <QFileDialog>
#include <QFileInfo>

namespace DeclassificationClient::UI {

FileListWidget::FileListWidget(QWidget* parent) : QWidget(parent) {
    setupUI();
    setupConnections();
}

FileListWidget::~FileListWidget() = default;

void FileListWidget::updateFiles(const std::vector<Models::File>& files) {
    fileTableWidget_->setRowCount(0);
    fileTableWidget_->setSortingEnabled(false);

    for (const auto& file : files) {
        int row = fileTableWidget_->rowCount();
        fileTableWidget_->insertRow(row);

        // 0: File Name
        auto* nameItem = new QTableWidgetItem(QString::fromStdString(file.fileName));
        fileTableWidget_->setItem(row, 0, nameItem);

        // 1: Size
        QLocale locale;
        auto* sizeItem = new QTableWidgetItem(locale.formattedDataSize(file.fileSize));
        sizeItem->setTextAlignment(Qt::AlignRight | Qt::AlignVCenter);
        fileTableWidget_->setItem(row, 1, sizeItem);

        // 2: Status
        QString statusText;
        QIcon statusIcon;
        switch (file.status) {
            case Models::FileStatus::Encrypted:
                statusText = tr("Encrypted");
                statusIcon = QIcon::fromTheme("dialog-password");
                break;
            case Models::FileStatus::Decrypted:
                statusText = tr("Decrypted");
                statusIcon = QIcon::fromTheme("emblem-ok");
                break;
            case Models::FileStatus::Failed:
                statusText = tr("Failed");
                statusIcon = QIcon::fromTheme("emblem-important");
                break;
        }
        auto* statusItem = new QTableWidgetItem(statusIcon, statusText);
        fileTableWidget_->setItem(row, 2, statusItem);

        // 3: Path
        auto* pathItem = new QTableWidgetItem(QString::fromStdString(file.filePath));
        fileTableWidget_->setItem(row, 3, pathItem);
    }
    
    fileTableWidget_->setSortingEnabled(true);
}

int FileListWidget::getRowCount() const {
    return fileTableWidget_->rowCount();
}

void FileListWidget::updateFileStatus(int row, const QString& status, const QIcon& icon) {
    if (row < 0 || row >= fileTableWidget_->rowCount()) {
        return;
    }

    auto* statusItem = fileTableWidget_->item(row, 2);
    if (statusItem) {
        statusItem->setText(status);
        statusItem->setIcon(icon);
    } else {
        // This case should ideally not happen if the table is populated correctly
        statusItem = new QTableWidgetItem(icon, status);
        fileTableWidget_->setItem(row, 2, statusItem);
    }
}

void FileListWidget::addFile() {
    QStringList filePaths = QFileDialog::getOpenFileNames(
        this,
        tr("Select Files to Add"),
        QDir::homePath(),
        tr("All Files (*.*)")
    );

    if (filePaths.isEmpty()) {
        return;
    }

    fileTableWidget_->setSortingEnabled(false);
    for (const auto& path : filePaths) {
        QFileInfo fileInfo(path);
        int row = fileTableWidget_->rowCount();
        fileTableWidget_->insertRow(row);

        // 0: File Name
        auto* nameItem = new QTableWidgetItem(fileInfo.fileName());
        fileTableWidget_->setItem(row, 0, nameItem);

        // 1: Size
        QLocale locale;
        auto* sizeItem = new QTableWidgetItem(locale.formattedDataSize(fileInfo.size()));
        sizeItem->setTextAlignment(Qt::AlignRight | Qt::AlignVCenter);
        fileTableWidget_->setItem(row, 1, sizeItem);

        // 2: Status
        // For now, new files are just added with a placeholder status.
        auto* statusItem = new QTableWidgetItem(QIcon::fromTheme("dialog-question"), tr("Pending"));
        fileTableWidget_->setItem(row, 2, statusItem);

        // 3: Path
        auto* pathItem = new QTableWidgetItem(fileInfo.absoluteFilePath());
        fileTableWidget_->setItem(row, 3, pathItem);

        emit logMessage(tr("Added file: %1").arg(fileInfo.fileName()));
    }
    fileTableWidget_->setSortingEnabled(true);
}

void FileListWidget::removeSelectedFile() {
    auto selectedItems = fileTableWidget_->selectedItems();
    if (selectedItems.isEmpty()) {
        return;
    }

    QList<int> rowsToRemove;
    for (const auto& item : selectedItems) {
        if (!rowsToRemove.contains(item->row())) {
            rowsToRemove.append(item->row());
        }
    }
    
    // Sort rows in descending order to avoid messing up indices while removing
    std::sort(rowsToRemove.rbegin(), rowsToRemove.rend());

    for (int row : rowsToRemove) {
        QString fileName = fileTableWidget_->item(row, 0)->text();
        fileTableWidget_->removeRow(row);
        emit logMessage(tr("Removed file: %1").arg(fileName));
    }
}

void FileListWidget::showContextMenu(const QPoint& point) {
    if (fileTableWidget_->itemAt(point)) { // Only show if an item is clicked
        contextMenu_->exec(fileTableWidget_->mapToGlobal(point));
    }
}

void FileListWidget::setupUI() {
    auto* mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(0, 0, 0, 0);

    // Table Widget
    fileTableWidget_ = new QTableWidget(this);
    fileTableWidget_->setColumnCount(4);
    fileTableWidget_->setHorizontalHeaderLabels({tr("File Name"), tr("Size"), tr("Status"), tr("Path")});
    fileTableWidget_->horizontalHeader()->setSectionResizeMode(0, QHeaderView::Stretch);
    fileTableWidget_->horizontalHeader()->setSectionResizeMode(3, QHeaderView::Stretch);
    fileTableWidget_->setSelectionBehavior(QAbstractItemView::SelectRows);
    fileTableWidget_->setEditTriggers(QAbstractItemView::NoEditTriggers);
    
    mainLayout->addWidget(fileTableWidget_);

    // Button Layout
    auto* buttonLayout = new QHBoxLayout();
    addFileButton_ = new QPushButton(tr("Add File..."), this);
    removeFileButton_ = new QPushButton(tr("Remove Selected"), this);
    
    buttonLayout->addStretch();
    buttonLayout->addWidget(addFileButton_);
    buttonLayout->addWidget(removeFileButton_);
    
    mainLayout->addLayout(buttonLayout);
    setLayout(mainLayout);

    // Context Menu
    fileTableWidget_->setContextMenuPolicy(Qt::CustomContextMenu);
    contextMenu_ = new QMenu(this);
    contextMenu_->addAction(tr("Remove Selected File"), this, &FileListWidget::removeSelectedFile);
}

void FileListWidget::setupConnections() {
    connect(addFileButton_, &QPushButton::clicked, this, &FileListWidget::addFile);
    connect(removeFileButton_, &QPushButton::clicked, this, &FileListWidget::removeSelectedFile);
    connect(fileTableWidget_, &QTableWidget::customContextMenuRequested, this, &FileListWidget::showContextMenu);
}

} // namespace DeclassificationClient::UI 