using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CryptoSystem.SystemManager.Models
{
    /// <summary>
    /// 策略类型枚举
    /// </summary>
    public enum PolicyType
    {
        [Description("文件加密策略")]
        FileEncryption = 1,
        
        [Description("访问控制策略")]
        AccessControl = 2,
        
        [Description("设备控制策略")]
        DeviceControl = 3,
        
        [Description("网络控制策略")]
        NetworkControl = 4,
        
        [Description("水印策略")]
        Watermark = 5,
        
        [Description("审计策略")]
        Audit = 6,
        
        [Description("密钥管理策略")]
        KeyManagement = 7
    }

    /// <summary>
    /// 策略状态枚举
    /// </summary>
    public enum PolicyStatus
    {
        [Description("草稿")]
        Draft = 1,
        
        [Description("已启用")]
        Enabled = 2,
        
        [Description("已禁用")]
        Disabled = 3,
        
        [Description("已删除")]
        Deleted = 4
    }

    /// <summary>
    /// 策略优先级枚举
    /// </summary>
    public enum PolicyPriority
    {
        [Description("低")]
        Low = 1,
        
        [Description("中")]
        Medium = 2,
        
        [Description("高")]
        High = 3,
        
        [Description("最高")]
        Critical = 4
    }

    /// <summary>
    /// 应用模式枚举
    /// </summary>
    public enum ApplyMode
    {
        [Description("立即应用")]
        Immediate = 1,
        
        [Description("延迟应用")]
        Delayed = 2,
        
        [Description("计划应用")]
        Scheduled = 3
    }

    /// <summary>
    /// 策略实体
    /// </summary>
    [Table("sys_policies")]
    public class Policy
    {
        /// <summary>
        /// 策略ID
        /// </summary>
        [Key]
        [Column("policy_id")]
        public string PolicyId { get; set; } = string.Empty;

        /// <summary>
        /// 策略名称
        /// </summary>
        [Required]
        [MaxLength(200)]
        [Column("policy_name")]
        public string PolicyName { get; set; } = string.Empty;

        /// <summary>
        /// 策略描述
        /// </summary>
        [MaxLength(1000)]
        [Column("description")]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 策略类型
        /// </summary>
        [Column("policy_type")]
        public PolicyType PolicyType { get; set; } = PolicyType.FileEncryption;

        /// <summary>
        /// 策略状态
        /// </summary>
        [Column("status")]
        public PolicyStatus Status { get; set; } = PolicyStatus.Draft;

        /// <summary>
        /// 策略优先级
        /// </summary>
        [Column("priority")]
        public PolicyPriority Priority { get; set; } = PolicyPriority.Medium;

        /// <summary>
        /// 应用模式
        /// </summary>
        [Column("apply_mode")]
        public ApplyMode ApplyMode { get; set; } = ApplyMode.Immediate;

        /// <summary>
        /// 策略版本
        /// </summary>
        [MaxLength(50)]
        [Column("version")]
        public string Version { get; set; } = "1.0";

        /// <summary>
        /// 策略内容（JSON格式）
        /// </summary>
        [Column("policy_content")]
        public string PolicyContent { get; set; } = "{}";

        /// <summary>
        /// 生效时间
        /// </summary>
        [Column("effective_time")]
        public DateTime? EffectiveTime { get; set; }

        /// <summary>
        /// 失效时间
        /// </summary>
        [Column("expiry_time")]
        public DateTime? ExpiryTime { get; set; }

        /// <summary>
        /// 是否启用继承
        /// </summary>
        [Column("enable_inheritance")]
        public bool EnableInheritance { get; set; } = true;

        /// <summary>
        /// 父策略ID
        /// </summary>
        [Column("parent_policy_id")]
        public string? ParentPolicyId { get; set; }

        /// <summary>
        /// 是否为默认策略
        /// </summary>
        [Column("is_default")]
        public bool IsDefault { get; set; } = false;

        /// <summary>
        /// 适用范围（JSON格式）
        /// </summary>
        [Column("apply_scope")]
        public string ApplyScope { get; set; } = "{}";

        /// <summary>
        /// 排除范围（JSON格式）
        /// </summary>
        [Column("exclude_scope")]
        public string ExcludeScope { get; set; } = "{}";

        /// <summary>
        /// 计划应用时间
        /// </summary>
        [Column("scheduled_time")]
        public DateTime? ScheduledTime { get; set; }

        /// <summary>
        /// 最后应用时间
        /// </summary>
        [Column("last_applied_time")]
        public DateTime? LastAppliedTime { get; set; }

        /// <summary>
        /// 应用成功设备数
        /// </summary>
        [Column("applied_device_count")]
        public int AppliedDeviceCount { get; set; } = 0;

        /// <summary>
        /// 应用失败设备数
        /// </summary>
        [Column("failed_device_count")]
        public int FailedDeviceCount { get; set; } = 0;

        /// <summary>
        /// 创建时间
        /// </summary>
        [Column("created_time")]
        public DateTime CreatedTime { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 创建者
        /// </summary>
        [MaxLength(100)]
        [Column("created_by")]
        public string CreatedBy { get; set; } = string.Empty;

        /// <summary>
        /// 最后修改时间
        /// </summary>
        [Column("last_modified_time")]
        public DateTime LastModifiedTime { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 最后修改者
        /// </summary>
        [MaxLength(100)]
        [Column("last_modified_by")]
        public string LastModifiedBy { get; set; } = string.Empty;

        /// <summary>
        /// 备注信息
        /// </summary>
        [MaxLength(1000)]
        [Column("remarks")]
        public string Remarks { get; set; } = string.Empty;

        // 导航属性
        public virtual Policy? ParentPolicy { get; set; }
        public virtual ICollection<Policy> ChildPolicies { get; set; } = new List<Policy>();
        public virtual ICollection<UserPolicy> UserPolicies { get; set; } = new List<UserPolicy>();
        public virtual ICollection<DevicePolicy> DevicePolicies { get; set; } = new List<DevicePolicy>();
        public virtual ICollection<PolicyDeployment> Deployments { get; set; } = new List<PolicyDeployment>();
    }

    /// <summary>
    /// 用户策略关联实体
    /// </summary>
    [Table("sys_user_policies")]
    public class UserPolicy
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Key]
        [Column("user_id")]
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// 策略ID
        /// </summary>
        [Key]
        [Column("policy_id")]
        public string PolicyId { get; set; } = string.Empty;

        /// <summary>
        /// 分配时间
        /// </summary>
        [Column("assigned_time")]
        public DateTime AssignedTime { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 分配者
        /// </summary>
        [MaxLength(100)]
        [Column("assigned_by")]
        public string AssignedBy { get; set; } = string.Empty;

        /// <summary>
        /// 是否启用
        /// </summary>
        [Column("is_enabled")]
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 生效时间
        /// </summary>
        [Column("effective_time")]
        public DateTime? EffectiveTime { get; set; }

        /// <summary>
        /// 失效时间
        /// </summary>
        [Column("expiry_time")]
        public DateTime? ExpiryTime { get; set; }

        // 导航属性
        public virtual User User { get; set; } = null!;
        public virtual Policy Policy { get; set; } = null!;
    }

    /// <summary>
    /// 设备策略关联实体
    /// </summary>
    [Table("sys_device_policies")]
    public class DevicePolicy
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        [Key]
        [Column("device_id")]
        public string DeviceId { get; set; } = string.Empty;

        /// <summary>
        /// 策略ID
        /// </summary>
        [Key]
        [Column("policy_id")]
        public string PolicyId { get; set; } = string.Empty;

        /// <summary>
        /// 部署时间
        /// </summary>
        [Column("deployed_time")]
        public DateTime DeployedTime { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 部署者
        /// </summary>
        [MaxLength(100)]
        [Column("deployed_by")]
        public string DeployedBy { get; set; } = string.Empty;

        /// <summary>
        /// 部署状态
        /// </summary>
        [Column("deploy_status")]
        public DeployStatus DeployStatus { get; set; } = DeployStatus.Pending;

        /// <summary>
        /// 应用状态
        /// </summary>
        [Column("apply_status")]
        public ApplyStatus ApplyStatus { get; set; } = ApplyStatus.NotApplied;

        /// <summary>
        /// 最后检查时间
        /// </summary>
        [Column("last_checked_time")]
        public DateTime? LastCheckedTime { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        [MaxLength(1000)]
        [Column("error_message")]
        public string ErrorMessage { get; set; } = string.Empty;

        // 导航属性
        public virtual Device Device { get; set; } = null!;
        public virtual Policy Policy { get; set; } = null!;
    }

    /// <summary>
    /// 部署状态枚举
    /// </summary>
    public enum DeployStatus
    {
        [Description("待部署")]
        Pending = 1,
        
        [Description("部署中")]
        Deploying = 2,
        
        [Description("部署成功")]
        Success = 3,
        
        [Description("部署失败")]
        Failed = 4,
        
        [Description("已撤销")]
        Revoked = 5
    }

    /// <summary>
    /// 应用状态枚举
    /// </summary>
    public enum ApplyStatus
    {
        [Description("未应用")]
        NotApplied = 1,
        
        [Description("应用中")]
        Applying = 2,
        
        [Description("已应用")]
        Applied = 3,
        
        [Description("应用失败")]
        Failed = 4,
        
        [Description("需要更新")]
        NeedUpdate = 5
    }

    /// <summary>
    /// 策略部署记录
    /// </summary>
    [Table("sys_policy_deployments")]
    public class PolicyDeployment
    {
        /// <summary>
        /// 部署ID
        /// </summary>
        [Key]
        [Column("deployment_id")]
        public string DeploymentId { get; set; } = string.Empty;

        /// <summary>
        /// 策略ID
        /// </summary>
        [Column("policy_id")]
        public string PolicyId { get; set; } = string.Empty;

        /// <summary>
        /// 部署名称
        /// </summary>
        [MaxLength(200)]
        [Column("deployment_name")]
        public string DeploymentName { get; set; } = string.Empty;

        /// <summary>
        /// 目标范围（JSON格式）
        /// </summary>
        [Column("target_scope")]
        public string TargetScope { get; set; } = "{}";

        /// <summary>
        /// 部署状态
        /// </summary>
        [Column("status")]
        public DeployStatus Status { get; set; } = DeployStatus.Pending;

        /// <summary>
        /// 开始时间
        /// </summary>
        [Column("start_time")]
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 完成时间
        /// </summary>
        [Column("complete_time")]
        public DateTime? CompleteTime { get; set; }

        /// <summary>
        /// 目标设备数
        /// </summary>
        [Column("target_device_count")]
        public int TargetDeviceCount { get; set; } = 0;

        /// <summary>
        /// 成功设备数
        /// </summary>
        [Column("success_device_count")]
        public int SuccessDeviceCount { get; set; } = 0;

        /// <summary>
        /// 失败设备数
        /// </summary>
        [Column("failed_device_count")]
        public int FailedDeviceCount { get; set; } = 0;

        /// <summary>
        /// 部署者
        /// </summary>
        [MaxLength(100)]
        [Column("deployed_by")]
        public string DeployedBy { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        [Column("created_time")]
        public DateTime CreatedTime { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 错误信息
        /// </summary>
        [MaxLength(2000)]
        [Column("error_message")]
        public string ErrorMessage { get; set; } = string.Empty;

        // 导航属性
        public virtual Policy Policy { get; set; } = null!;
    }

    /// <summary>
    /// 策略模板实体
    /// </summary>
    [Table("sys_policy_templates")]
    public class PolicyTemplate
    {
        /// <summary>
        /// 模板ID
        /// </summary>
        [Key]
        [Column("template_id")]
        public string TemplateId { get; set; } = string.Empty;

        /// <summary>
        /// 模板名称
        /// </summary>
        [Required]
        [MaxLength(200)]
        [Column("template_name")]
        public string TemplateName { get; set; } = string.Empty;

        /// <summary>
        /// 模板描述
        /// </summary>
        [MaxLength(1000)]
        [Column("description")]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 策略类型
        /// </summary>
        [Column("policy_type")]
        public PolicyType PolicyType { get; set; } = PolicyType.FileEncryption;

        /// <summary>
        /// 模板内容（JSON格式）
        /// </summary>
        [Column("template_content")]
        public string TemplateContent { get; set; } = "{}";

        /// <summary>
        /// 是否为系统模板
        /// </summary>
        [Column("is_system")]
        public bool IsSystem { get; set; } = false;

        /// <summary>
        /// 是否启用
        /// </summary>
        [Column("is_enabled")]
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 排序号
        /// </summary>
        [Column("sort_order")]
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 创建时间
        /// </summary>
        [Column("created_time")]
        public DateTime CreatedTime { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 创建者
        /// </summary>
        [MaxLength(100)]
        [Column("created_by")]
        public string CreatedBy { get; set; } = string.Empty;
    }

    /// <summary>
    /// 策略统计信息
    /// </summary>
    public class PolicyStatistics
    {
        /// <summary>
        /// 总策略数
        /// </summary>
        public int TotalPolicies { get; set; }

        /// <summary>
        /// 启用的策略数
        /// </summary>
        public int EnabledPolicies { get; set; }

        /// <summary>
        /// 禁用的策略数
        /// </summary>
        public int DisabledPolicies { get; set; }

        /// <summary>
        /// 草稿状态的策略数
        /// </summary>
        public int DraftPolicies { get; set; }

        /// <summary>
        /// 按类型分组的策略数
        /// </summary>
        public Dictionary<PolicyType, int> PoliciesByType { get; set; } = new();

        /// <summary>
        /// 按优先级分组的策略数
        /// </summary>
        public Dictionary<PolicyPriority, int> PoliciesByPriority { get; set; } = new();

        /// <summary>
        /// 按状态分组的策略数
        /// </summary>
        public Dictionary<PolicyStatus, int> PoliciesByStatus { get; set; } = new();
    }
} 