# 运营商组件 (Operator Components)

![组件状态](https://img.shields.io/badge/状态-部分完成-yellow)
![平台支持](https://img.shields.io/badge/平台-Windows%20%7C%20HarmonyOS%20%7C%20Linux-blue)
![数据库](https://img.shields.io/badge/数据库-PostgreSQL%20%7C%20MySQL-orange)

## 📋 概述

运营商组件是由开发商/运营商部署和管理的核心基础设施，为客户单位提供密钥管理服务和数据存储服务。这些组件运行在高安全等级的环境中，确保整个加密系统的安全性和可靠性。

### 🎯 核心职责
- **密钥生命周期管理**: 为客户单位生成、管理和维护主密钥
- **数据中心服务**: 提供高可用、高性能的数据存储和身份验证服务
- **运营监控**: 实时监控系统状态和服务质量
- **安全保障**: 确保密钥和数据的最高级别安全防护

## 🏗️ 组件架构

### 组件构成及状态

| 组件名称 | 功能职责 | Windows | Linux | HarmonyOS | 完成度 | 状态 |
|---------|---------|---------|-------|-----------|--------|------|
| **密钥生成器** | 密钥管理服务 | ✅ 90% | ❌ | 🟡 45% | 70% | 🟡 部分完成 |
| **数据库服务器** | 数据存储服务 | ✅ 85% | ✅ 85% | ❌ | 85% | 🟡 部分完成 |

### 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    运营商基础设施                              │
├─────────────────────────────────────────────────────────────┤
│  🔑 密钥生成器 (Key Generator)                               │
│  ├── 客户密钥管理                                            │
│  ├── 密钥生命周期控制                                         │
│  └── 运营监控界面                                            │
├─────────────────────────────────────────────────────────────┤
│  🗄️ 数据库服务器 (Database Server)                          │
│  ├── 用户/设备数据存储                                       │
│  ├── 策略配置管理                                            │
│  ├── 审计日志存储                                            │
│  └── 身份验证服务                                            │
└─────────────────────────────────────────────────────────────┘
                              ↓
                    ═══════════════════════
                       安全API接口
                    ═══════════════════════
                              ↓
┌─────────────────────────────────────────────────────────────┐
│                    客户端基础设施                              │
│  🎛️ 系统管理器 + 🛡️ 客户端代理 + 📤 脱密客户端            │
└─────────────────────────────────────────────────────────────┘
```

## 🔑 密钥生成器 (Key Generator)

### 功能特性
- **🔐 密钥管理**: 生成、修改、删除客户单位主密钥
- **👥 客户管理**: 维护客户基本信息和密钥关联关系
- **📊 状态监控**: 实时监控数据库连接状态和系统运行状态
- **🔒 安全存储**: 安全存储密钥信息，确保密钥安全

### 平台支持
- **Windows版本**: C# + WPF，核心功能基本完成
- **HarmonyOS版本**: ArkTS + ArkUI，架构设计完成，核心功能开发中

### 核心功能
1. **密钥生成**: 为新客户生成AES-256主密钥
2. **密钥修改**: 支持密钥续费延期和信息更新
3. **密钥删除**: 安全删除停用客户的密钥
4. **监控告警**: 数据库连接状态实时监控

### 技术规格
- **加密算法**: AES-256 (固定标准)
- **密钥长度**: 256位强随机密钥
- **存储安全**: 硬件安全模块(HSM)支持
- **通信协议**: TLS 1.3双向认证

## 🗄️ 数据库服务器 (Database Server)

### 功能特性
- **📊 数据存储**: 用户、设备、策略、审计日志的安全存储
- **🔐 身份验证**: 多种认证方式和权限验证服务
- **🔄 数据同步**: 分布式环境下的数据同步和一致性保证
- **💾 备份恢复**: 完整的数据备份和恢复机制

### 支持的数据库系统
- **PostgreSQL** (推荐): 高性能、高可靠性
- **MySQL**: 广泛兼容性支持
- **达梦数据库**: 国产化数据库支持
- **人大金仓**: 国产化数据库支持

### 部署模式
1. **集中部署** (默认): 运营商高安全环境部署
2. **本地部署** (可选): 客户单位内部部署
3. **云端部署**: 公有云VPC内部署
4. **混合部署**: 核心数据本地，备份数据云端

### 数据架构
```sql
-- 核心数据表结构
├── 🏢 组织管理
│   ├── organizations (组织信息)
│   ├── departments (部门结构)
│   └── users (用户账户)
├── 🔑 密钥管理
│   ├── master_keys (主密钥)
│   ├── derived_keys (派生密钥)
│   └── key_history (密钥历史)
├── 🛡️ 策略配置
│   ├── encryption_policies (加密策略)
│   ├── access_policies (访问策略)
│   └── approval_workflows (审批流程)
└── 📊 审计日志
    ├── operation_logs (操作日志)
    ├── security_events (安全事件)
    └── system_logs (系统日志)
```

## 🚀 快速开始

### 环境要求

#### 开发环境
- **Windows**: Visual Studio 2019+, .NET 6/8
- **HarmonyOS**: DevEco Studio 4.0+
- **数据库**: PostgreSQL 12+, MySQL 8.0+

#### 运行环境
- **操作系统**: Windows Server 2019+, CentOS 7+, Ubuntu 18.04+
- **内存**: 最低8GB RAM，推荐16GB+
- **存储**: 最低100GB可用空间，推荐SSD存储
- **网络**: 稳定的互联网连接，支持TLS 1.3

### 部署步骤

#### 1. 数据库服务器部署
```bash
# PostgreSQL部署
cd operator/database-server
chmod +x deploy.sh
./deploy.sh

# 配置数据库
psql -U postgres -f schema/postgres/01_init.sql
psql -U postgres -f schema/postgres/02_init_data.sql
```

#### 2. 密钥生成器部署

**Windows版本:**
```bash
cd operator/key-generator/windows
dotnet build KeyGenerator.csproj --configuration Release
dotnet publish --runtime win-x64 --self-contained
```

**HarmonyOS版本:**
```bash
cd operator/key-generator/harmonyos
hvigor assembleHap
```

### 配置说明

#### 数据库配置
```bash
# 编辑配置文件
cp operator/database-server/env.template .env
nano .env

# 配置参数
DB_HOST=localhost
DB_PORT=5432
DB_NAME=cryptosystem
DB_USER=crypto_admin
DB_PASSWORD=secure_password
```

#### 密钥生成器配置
```json
{
  "DatabaseConnection": {
    "ConnectionString": "Host=localhost;Database=cryptosystem;Username=crypto_admin;Password=secure_password"
  },
  "Security": {
    "EnableHSM": true,
    "KeyLength": 256,
    "Algorithm": "AES-256"
  },
  "Monitoring": {
    "CheckInterval": 60,
    "AlertThreshold": 5
  }
}
```

## 🔒 安全配置

### 网络安全
- **TLS配置**: 强制使用TLS 1.3加密通信
- **证书管理**: 定期更新SSL/TLS证书
- **防火墙**: 配置严格的网络访问控制
- **VPN访问**: 建议通过VPN访问管理界面

### 数据安全
- **加密存储**: 敏感数据采用AES-256加密存储
- **访问控制**: 基于角色的细粒度权限控制
- **审计日志**: 所有操作的完整审计记录
- **备份加密**: 备份数据采用独立密钥加密

### 运营安全
- **多因素认证**: 管理员访问强制MFA
- **操作审计**: 详细的操作日志和告警机制
- **定期检查**: 自动化安全检查和漏洞扫描
- **应急响应**: 完整的安全事件响应流程

## 📊 监控和维护

### 系统监控
- **服务状态**: 实时监控各服务运行状态
- **性能指标**: CPU、内存、磁盘、网络使用情况
- **数据库监控**: 连接数、查询性能、存储空间
- **安全监控**: 异常访问、安全事件告警

### 维护任务
- **日常维护**: 日志清理、性能优化、状态检查
- **定期维护**: 系统更新、安全补丁、备份验证
- **应急维护**: 故障恢复、安全事件处理

### 告警机制
```yaml
# 告警配置示例
alerts:
  database_connection:
    threshold: 5_minutes
    action: email + sms
  
  disk_space:
    threshold: 85%
    action: email
  
  security_event:
    threshold: immediate
    action: email + sms + phone
```

## 📚 相关文档

### 组件文档
- 🔑 [密钥生成器详细文档](key-generator/README.md)
- 🗄️ [数据库服务器配置指南](database-server/README.md)

### 技术文档
- 📖 [API接口文档](../docs/API_DOCUMENTATION.md)
- 🔒 [安全配置指南](../docs/SECURITY_GUIDE.md)
- 🚀 [部署最佳实践](../docs/DEPLOYMENT_GUIDE.md)

### 运营文档
- 📋 [运营手册](../docs/OPERATIONS_MANUAL.md)
- 🚨 [应急响应指南](../docs/INCIDENT_RESPONSE.md)
- 📊 [监控配置指南](../docs/MONITORING_GUIDE.md)

## 🆘 技术支持

### 联系方式
- 📧 **技术支持**: <EMAIL>
- 📞 **紧急热线**: +86-400-XXX-XXXX
- 💬 **在线支持**: [技术支持门户]

### 支持时间
- **工作时间**: 周一至周五 9:00-18:00
- **紧急支持**: 7×24小时 (安全事件)
- **响应时间**: 
  - 一般问题: 4小时内响应
  - 紧急问题: 1小时内响应
  - 安全事件: 30分钟内响应

---

**Copyright © 2024 Document Encryption System. All rights reserved.** 