using Microsoft.EntityFrameworkCore;
using CryptoSystem.SystemManager.Models;

namespace CryptoSystem.SystemManager.Data
{
    public class SystemManagerDbContext : DbContext
    {
        public SystemManagerDbContext(DbContextOptions<SystemManagerDbContext> options) : base(options)
        {
        }

        // 数据库表
        public DbSet<User> Users { get; set; }
        public DbSet<Device> Devices { get; set; }
        public DbSet<Policy> Policies { get; set; }
        public DbSet<Department> Departments { get; set; }
        public DbSet<UserDevice> UserDevices { get; set; }
        public DbSet<UserPolicy> UserPolicies { get; set; }
        public DbSet<DevicePolicy> DevicePolicies { get; set; }
        public DbSet<PolicyDeployment> PolicyDeployments { get; set; }
        public DbSet<PolicyTemplate> PolicyTemplates { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // 配置复合主键
            modelBuilder.Entity<UserDevice>()
                .HasKey(ud => new { ud.UserId, ud.DeviceId });

            modelBuilder.Entity<UserPolicy>()
                .HasKey(up => new { up.UserId, up.PolicyId });

            modelBuilder.Entity<DevicePolicy>()
                .HasKey(dp => new { dp.DeviceId, dp.PolicyId });

            // 配置关系
            ConfigureUserRelationships(modelBuilder);
            ConfigureDeviceRelationships(modelBuilder);
            ConfigurePolicyRelationships(modelBuilder);
            ConfigureDepartmentRelationships(modelBuilder);
        }

        private void ConfigureUserRelationships(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<User>()
                .HasOne(u => u.Department)
                .WithMany(d => d.Users)
                .HasForeignKey(u => u.DepartmentId)
                .OnDelete(DeleteBehavior.SetNull);
        }

        private void ConfigureDeviceRelationships(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Device>()
                .HasOne(d => d.AuthorizedUser)
                .WithMany()
                .HasForeignKey(d => d.AuthorizedUserId)
                .OnDelete(DeleteBehavior.SetNull);
        }

        private void ConfigurePolicyRelationships(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Policy>()
                .HasOne(p => p.ParentPolicy)
                .WithMany(p => p.ChildPolicies)
                .HasForeignKey(p => p.ParentPolicyId)
                .OnDelete(DeleteBehavior.Restrict);
        }

        private void ConfigureDepartmentRelationships(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Department>()
                .HasOne(d => d.ParentDepartment)
                .WithMany(d => d.ChildDepartments)
                .HasForeignKey(d => d.ParentDepartmentId)
                .OnDelete(DeleteBehavior.Restrict);
        }
    }
} 