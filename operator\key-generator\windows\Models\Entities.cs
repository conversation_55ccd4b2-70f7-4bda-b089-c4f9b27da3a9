using System;
using System.ComponentModel.DataAnnotations;

namespace KeyGenerator.Models
{
    /// <summary>
    /// 主密钥实体
    /// </summary>
    public class MasterKeyEntity
    {
        /// <summary>
        /// 密钥ID
        /// </summary>
        public string KeyId { get; set; } = string.Empty;

        /// <summary>
        /// 密钥名称
        /// </summary>
        public string KeyName { get; set; } = string.Empty;

        /// <summary>
        /// 客户ID
        /// </summary>
        public string ClientId { get; set; } = string.Empty;

        /// <summary>
        /// 客户名称
        /// </summary>
        public string ClientName { get; set; } = string.Empty;

        /// <summary>
        /// 算法ID
        /// </summary>
        public int AlgorithmId { get; set; }

        /// <summary>
        /// 密钥长度
        /// </summary>
        public int KeyLength { get; set; }

        /// <summary>
        /// 加密后的密钥数据
        /// </summary>
        public byte[] KeyDataEncrypted { get; set; } = [];

        /// <summary>
        /// 密钥哈希值
        /// </summary>
        public string KeyHash { get; set; } = string.Empty;

        /// <summary>
        /// 密钥版本
        /// </summary>
        public int KeyVersion { get; set; } = 1;

        /// <summary>
        /// 生成方法
        /// </summary>
        public string GenerationMethod { get; set; } = string.Empty;

        /// <summary>
        /// 生效日期
        /// </summary>
        public DateTime EffectiveDate { get; set; }

        /// <summary>
        /// 过期日期
        /// </summary>
        public DateTime? ExpirationDate { get; set; }

        /// <summary>
        /// 密钥状态
        /// </summary>
        public KeyStatus KeyStatus { get; set; }

        /// <summary>
        /// 使用策略
        /// </summary>
        public required string UsagePolicy { get; set; }

        /// <summary>
        /// 最大使用次数
        /// </summary>
        public long? MaxUsageCount { get; set; }

        /// <summary>
        /// 当前使用次数
        /// </summary>
        public long CurrentUsageCount { get; set; } = 0;

        /// <summary>
        /// 备份状态
        /// </summary>
        public string? BackupStatus { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 创建者
        /// </summary>
        public string? CreatedBy { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdatedTime { get; set; }

        /// <summary>
        /// 更新者
        /// </summary>
        public string? UpdatedBy { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remarks { get; set; }

        /// <summary>
        /// 是否选中（用于UI选择）
        /// </summary>
        public bool IsSelected { get; set; } = false;
    }

    /// <summary>
    /// 客户实体
    /// </summary>
    public class ClientEntity
    {
        /// <summary>
        /// 客户ID
        /// </summary>
        public string ClientId { get; set; } = string.Empty;

        /// <summary>
        /// 客户名称
        /// </summary>
        public string ClientName { get; set; } = string.Empty;

        /// <summary>
        /// 客户代码
        /// </summary>
        public string ClientCode { get; set; } = string.Empty;

        /// <summary>
        /// 联系人
        /// </summary>
        public string? ContactPerson { get; set; }

        /// <summary>
        /// 联系电话
        /// </summary>
        public string? ContactPhone { get; set; }

        /// <summary>
        /// 联系邮箱
        /// </summary>
        public string? ContactEmail { get; set; }

        /// <summary>
        /// 地址
        /// </summary>
        public string? Address { get; set; }

        /// <summary>
        /// 行业
        /// </summary>
        public string? Industry { get; set; }

        /// <summary>
        /// 客户级别
        /// </summary>
        public string? ClientLevel { get; set; }

        /// <summary>
        /// 许可证类型
        /// </summary>
        public string? LicenseType { get; set; }

        /// <summary>
        /// 最大用户数
        /// </summary>
        public int? MaxUsers { get; set; }

        /// <summary>
        /// 最大设备数
        /// </summary>
        public int? MaxDevices { get; set; }

        /// <summary>
        /// 合同开始日期
        /// </summary>
        public DateTime? ContractStartDate { get; set; }

        /// <summary>
        /// 合同结束日期
        /// </summary>
        public DateTime? ContractEndDate { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 状态
        /// </summary>
        public string? Status { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 创建者
        /// </summary>
        public string? CreatedBy { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdatedTime { get; set; }

        /// <summary>
        /// 更新者
        /// </summary>
        public string? UpdatedBy { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remarks { get; set; }
    }

    /// <summary>
    /// 审计日志实体
    /// </summary>
    public class AuditLogEntity
    {
        /// <summary>
        /// 日志ID
        /// </summary>
        public string LogId { get; set; } = string.Empty;

        /// <summary>
        /// 日志类型
        /// </summary>
        public string LogType { get; set; } = string.Empty;

        /// <summary>
        /// 事件分类
        /// </summary>
        public string EventCategory { get; set; } = string.Empty;

        /// <summary>
        /// 事件动作
        /// </summary>
        public string EventAction { get; set; } = string.Empty;

        /// <summary>
        /// 事件结果
        /// </summary>
        public string EventResult { get; set; } = string.Empty;

        /// <summary>
        /// 客户ID
        /// </summary>
        public string? ClientId { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public string? UserId { get; set; }

        /// <summary>
        /// 操作员ID
        /// </summary>
        public string? OperatorId { get; set; }

        /// <summary>
        /// 操作员姓名
        /// </summary>
        public string? OperatorName { get; set; }

        /// <summary>
        /// 操作员IP
        /// </summary>
        public string? OperatorIp { get; set; }

        /// <summary>
        /// 目标资源类型
        /// </summary>
        public string? TargetResourceType { get; set; }

        /// <summary>
        /// 目标资源ID
        /// </summary>
        public string? TargetResourceId { get; set; }

        /// <summary>
        /// 目标资源名称
        /// </summary>
        public string? TargetResourceName { get; set; }

        /// <summary>
        /// 事件描述
        /// </summary>
        public string? EventDescription { get; set; }

        /// <summary>
        /// 请求参数
        /// </summary>
        public string? RequestParameters { get; set; }

        /// <summary>
        /// 响应数据
        /// </summary>
        public string? ResponseData { get; set; }

        /// <summary>
        /// 错误代码
        /// </summary>
        public string? ErrorCode { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 会话ID
        /// </summary>
        public string? SessionId { get; set; }

        /// <summary>
        /// 跟踪ID
        /// </summary>
        public string? TraceId { get; set; }

        /// <summary>
        /// 事件时间
        /// </summary>
        public DateTime EventTime { get; set; }

        /// <summary>
        /// 服务器名称
        /// </summary>
        public string? ServerName { get; set; }

        /// <summary>
        /// 应用程序名称
        /// </summary>
        public string? ApplicationName { get; set; }

        /// <summary>
        /// 应用程序版本
        /// </summary>
        public string? ApplicationVersion { get; set; }

        /// <summary>
        /// 严重程度级别
        /// </summary>
        public string? SeverityLevel { get; set; }

        /// <summary>
        /// 保留天数
        /// </summary>
        public int? RetentionDays { get; set; }
    }
} 