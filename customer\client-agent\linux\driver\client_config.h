/*
 * client_config.h
 *
 * Cryptosystem Linux 客户端配置头文件
 * 管理客户端配置和设备注册
 */

#ifndef CLIENT_CONFIG_H
#define CLIENT_CONFIG_H

#include <stdint.h>

// 配置状态
typedef enum {
    CONFIG_STATUS_OK = 0,           // 成功
    CONFIG_STATUS_ERROR = -1,       // 一般错误
    CONFIG_STATUS_NOT_FOUND = -2,   // 配置不存在
    CONFIG_STATUS_INVALID = -3,     // 配置无效
    CONFIG_STATUS_IO_ERROR = -4     // IO错误
} config_status;

// 设备注册状态
typedef enum {
    DEVICE_STATUS_UNREGISTERED = 0, // 未注册
    DEVICE_STATUS_REGISTERED = 1,   // 已注册
    DEVICE_STATUS_PENDING = 2,      // 待批准
    DEVICE_STATUS_DISABLED = 3      // 已禁用
} device_status;

/**
 * 初始化客户端配置
 * 
 * @param config_dir 配置目录路径
 * 
 * @return 成功返回CONFIG_STATUS_OK，失败返回错误状态码
 */
config_status config_init(const char *config_dir);

/**
 * 清理客户端配置
 */
void config_cleanup(void);

/**
 * 检查设备注册状态
 * 
 * @return 设备注册状态
 */
device_status config_check_device_status(void);

/**
 * 注册设备
 * 
 * @param server_url 服务器URL
 * @param org_id     组织ID
 * @param user_email 用户邮箱
 * @param device_name 设备名称
 * 
 * @return 成功返回CONFIG_STATUS_OK，失败返回错误状态码
 */
config_status config_register_device(const char *server_url, const char *org_id,
                                   const char *user_email, const char *device_name);

/**
 * 获取设备ID
 * 
 * @param buffer 输出缓冲区
 * @param size   缓冲区大小
 * 
 * @return 成功返回CONFIG_STATUS_OK，失败返回错误状态码
 */
config_status config_get_device_id(char *buffer, size_t size);

/**
 * 获取设备密钥
 * 
 * @param buffer 输出缓冲区
 * @param size   缓冲区大小
 * 
 * @return 成功返回CONFIG_STATUS_OK，失败返回错误状态码
 */
config_status config_get_device_key(char *buffer, size_t size);

/**
 * 获取服务器URL
 * 
 * @param buffer 输出缓冲区
 * @param size   缓冲区大小
 * 
 * @return 成功返回CONFIG_STATUS_OK，失败返回错误状态码
 */
config_status config_get_server_url(char *buffer, size_t size);

/**
 * 设置启用加密
 * 
 * @param enabled 是否启用
 * 
 * @return 成功返回CONFIG_STATUS_OK，失败返回错误状态码
 */
config_status config_set_encryption_enabled(int enabled);

/**
 * 获取是否启用加密
 * 
 * @return 启用返回1，禁用返回0，错误返回-1
 */
int config_get_encryption_enabled(void);

/**
 * 设置加密路径
 * 
 * @param path 要加密的路径
 * 
 * @return 成功返回CONFIG_STATUS_OK，失败返回错误状态码
 */
config_status config_add_encryption_path(const char *path);

/**
 * 移除加密路径
 * 
 * @param path 要移除的加密路径
 * 
 * @return 成功返回CONFIG_STATUS_OK，失败返回错误状态码
 */
config_status config_remove_encryption_path(const char *path);

/**
 * 获取加密路径列表
 * 
 * @param paths    路径数组
 * @param max_paths 数组最大容量
 * @param count    输出实际路径数量
 * 
 * @return 成功返回CONFIG_STATUS_OK，失败返回错误状态码
 */
config_status config_get_encryption_paths(char **paths, size_t max_paths, size_t *count);

/**
 * 保存配置到文件
 * 
 * @return 成功返回CONFIG_STATUS_OK，失败返回错误状态码
 */
config_status config_save(void);

#endif /* CLIENT_CONFIG_H */ 