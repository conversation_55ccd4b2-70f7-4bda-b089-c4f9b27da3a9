namespace CryptoSystem.DeclassificationClient.Services
{
    /// <summary>
    /// 用户角色
    /// </summary>
    public enum UserRole
    {
        /// <summary>
        /// 普通用户
        /// </summary>
        User = 0,

        /// <summary>
        /// 管理员
        /// </summary>
        Administrator = 1,

        /// <summary>
        /// 审计员
        /// </summary>
        Auditor = 2,

        /// <summary>
        /// 安全管理员
        /// </summary>
        SecurityAdmin = 3
    }

    /// <summary>
    /// 权限类型
    /// </summary>
    public enum Permission
    {
        /// <summary>
        /// 创建脱密任务
        /// </summary>
        CreateTask = 0,

        /// <summary>
        /// 查看脱密任务
        /// </summary>
        ViewTask = 1,

        /// <summary>
        /// 编辑脱密任务
        /// </summary>
        EditTask = 2,

        /// <summary>
        /// 删除脱密任务
        /// </summary>
        DeleteTask = 3,

        /// <summary>
        /// 处理脱密任务
        /// </summary>
        ProcessTask = 4,

        /// <summary>
        /// 生成安全包
        /// </summary>
        GeneratePackage = 5,

        /// <summary>
        /// 下载安全包
        /// </summary>
        DownloadPackage = 6,

        /// <summary>
        /// 查看审计日志
        /// </summary>
        ViewAuditLogs = 7,

        /// <summary>
        /// 导出审计日志
        /// </summary>
        ExportAuditLogs = 8,

        /// <summary>
        /// 管理系统配置
        /// </summary>
        ManageConfiguration = 9,

        /// <summary>
        /// 管理用户
        /// </summary>
        ManageUsers = 10
    }

    /// <summary>
    /// 用户信息
    /// </summary>
    public class UserInfo
    {
        public string UserId { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Department { get; set; } = string.Empty;
        public UserRole Role { get; set; }
        public List<Permission> Permissions { get; set; } = new List<Permission>();
        public DateTime LastLoginTime { get; set; }
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// 安全服务接口
    /// </summary>
    public interface ISecurityService
    {
        /// <summary>
        /// 用户登录
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="password">密码</param>
        /// <returns>登录是否成功</returns>
        Task<bool> LoginAsync(string username, string password);

        /// <summary>
        /// 用户登出
        /// </summary>
        /// <returns>登出是否成功</returns>
        Task<bool> LogoutAsync();

        /// <summary>
        /// 获取当前用户信息
        /// </summary>
        /// <returns>用户信息</returns>
        UserInfo? GetCurrentUser();

        /// <summary>
        /// 检查用户是否已登录
        /// </summary>
        /// <returns>是否已登录</returns>
        bool IsLoggedIn();

        /// <summary>
        /// 检查用户权限
        /// </summary>
        /// <param name="permission">权限类型</param>
        /// <returns>是否有权限</returns>
        bool HasPermission(Permission permission);

        /// <summary>
        /// 检查用户角色
        /// </summary>
        /// <param name="role">角色类型</param>
        /// <returns>是否具有该角色</returns>
        bool HasRole(UserRole role);

        /// <summary>
        /// 获取用户权限列表
        /// </summary>
        /// <returns>权限列表</returns>
        List<Permission> GetUserPermissions();

        /// <summary>
        /// 验证密码强度
        /// </summary>
        /// <param name="password">密码</param>
        /// <returns>密码强度评分（0-100）</returns>
        int ValidatePasswordStrength(string password);

        /// <summary>
        /// 更改密码
        /// </summary>
        /// <param name="currentPassword">当前密码</param>
        /// <param name="newPassword">新密码</param>
        /// <returns>是否更改成功</returns>
        Task<bool> ChangePasswordAsync(string currentPassword, string newPassword);

        /// <summary>
        /// 生成访问令牌
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="expiryHours">过期小时数</param>
        /// <returns>访问令牌</returns>
        string GenerateAccessToken(string userId, int expiryHours = 8);

        /// <summary>
        /// 验证访问令牌
        /// </summary>
        /// <param name="token">访问令牌</param>
        /// <returns>用户ID（如果令牌有效）</returns>
        string? ValidateAccessToken(string token);

        /// <summary>
        /// 刷新访问令牌
        /// </summary>
        /// <param name="token">当前令牌</param>
        /// <returns>新令牌</returns>
        string? RefreshAccessToken(string token);

        /// <summary>
        /// 获取会话信息
        /// </summary>
        /// <returns>会话信息</returns>
        Dictionary<string, object> GetSessionInfo();

        /// <summary>
        /// 检查账户锁定状态
        /// </summary>
        /// <param name="username">用户名</param>
        /// <returns>是否被锁定</returns>
        Task<bool> IsAccountLockedAsync(string username);

        /// <summary>
        /// 锁定账户
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="lockDurationMinutes">锁定时长（分钟）</param>
        /// <param name="reason">锁定原因</param>
        /// <returns>是否锁定成功</returns>
        Task<bool> LockAccountAsync(string username, int lockDurationMinutes = 30, string reason = "");

        /// <summary>
        /// 解锁账户
        /// </summary>
        /// <param name="username">用户名</param>
        /// <returns>是否解锁成功</returns>
        Task<bool> UnlockAccountAsync(string username);

        /// <summary>
        /// 记录登录失败
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="clientIP">客户端IP</param>
        /// <param name="reason">失败原因</param>
        /// <returns>失败次数</returns>
        Task<int> RecordLoginFailureAsync(string username, string clientIP = "", string reason = "");

        /// <summary>
        /// 清除登录失败记录
        /// </summary>
        /// <param name="username">用户名</param>
        /// <returns>是否清除成功</returns>
        Task<bool> ClearLoginFailuresAsync(string username);

        /// <summary>
        /// 获取安全配置
        /// </summary>
        /// <returns>安全配置</returns>
        Dictionary<string, object> GetSecurityConfiguration();

        /// <summary>
        /// 更新安全配置
        /// </summary>
        /// <param name="configuration">配置项</param>
        /// <returns>是否更新成功</returns>
        Task<bool> UpdateSecurityConfigurationAsync(Dictionary<string, object> configuration);

        /// <summary>
        /// 生成二维码认证密钥
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>认证密钥</returns>
        string GenerateTwoFactorSecret(string userId);

        /// <summary>
        /// 验证二维码认证码
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="code">认证码</param>
        /// <returns>是否验证成功</returns>
        bool VerifyTwoFactorCode(string userId, string code);

        /// <summary>
        /// 启用二维码认证
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="secret">认证密钥</param>
        /// <param name="verificationCode">验证码</param>
        /// <returns>是否启用成功</returns>
        Task<bool> EnableTwoFactorAsync(string userId, string secret, string verificationCode);

        /// <summary>
        /// 禁用二维码认证
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>是否禁用成功</returns>
        Task<bool> DisableTwoFactorAsync(string userId);

        /// <summary>
        /// 检查是否启用二维码认证
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>是否启用</returns>
        Task<bool> IsTwoFactorEnabledAsync(string userId);
    }
} 