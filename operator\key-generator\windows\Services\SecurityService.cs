using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using KeyGenerator.Models;

namespace KeyGenerator.Services
{
    /// <summary>
    /// 安全服务接口
    /// </summary>
    public interface ISecurityService
    {
        Task<byte[]> EncryptKeyDataAsync(byte[] keyData);
        Task<byte[]> DecryptKeyDataAsync(byte[] encryptedData);
        Task<byte[]> EncryptWithPasswordAsync(byte[] data, string password);
        Task<byte[]> DecryptWithPasswordAsync(byte[] encryptedData, string password);
        string GenerateSecurePassword(int length = 16);
        bool ValidatePasswordStrength(string password);
        Task<string> ComputeHashAsync(string input);
        Task<bool> VerifyHashAsync(string input, string hash);
    }

    /// <summary>
    /// 安全服务实现
    /// </summary>
    public class SecurityService : ISecurityService
    {
        private readonly ILogger<SecurityService> _logger;
        private readonly IConfiguration _configuration;
        private readonly byte[] _keyEncryptionKey;

        public SecurityService(ILogger<SecurityService> logger, IConfiguration configuration)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            
            // 初始化密钥加密密钥
            var kekConfig = _configuration["CryptoSettings:KeyEncryptionKey"];
            if (string.IsNullOrEmpty(kekConfig) || kekConfig == "YOUR_KEY_ENCRYPTION_KEY_HERE")
            {
                _logger.LogWarning("使用默认密钥加密密钥，生产环境中请配置专用密钥");
                _keyEncryptionKey = Encoding.UTF8.GetBytes("DefaultKEK2025!@#$%^&*()123456");
            }
            else
            {
                try {
                    _keyEncryptionKey = Convert.FromBase64String(kekConfig);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "无法解析配置的密钥加密密钥，使用默认密钥");
                    _keyEncryptionKey = Encoding.UTF8.GetBytes("DefaultKEK2025!@#$%^&*()123456");
                }
            }
        }

        /// <summary>
        /// 加密密钥数据
        /// </summary>
        public async Task<byte[]> EncryptKeyDataAsync(byte[] keyData)
        {
            try
            {
                await Task.Delay(1); // 模拟异步操作

                using var aes = Aes.Create();
                aes.Key = _keyEncryptionKey;
                aes.GenerateIV();

                using var encryptor = aes.CreateEncryptor();
                var encryptedData = encryptor.TransformFinalBlock(keyData, 0, keyData.Length);
                
                // 组合IV和加密数据
                var result = new byte[aes.IV.Length + encryptedData.Length];
                Buffer.BlockCopy(aes.IV, 0, result, 0, aes.IV.Length);
                Buffer.BlockCopy(encryptedData, 0, result, aes.IV.Length, encryptedData.Length);
                
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加密密钥数据失败");
                throw;
            }
        }

        /// <summary>
        /// 解密密钥数据
        /// </summary>
        public async Task<byte[]> DecryptKeyDataAsync(byte[] encryptedData)
        {
            try
            {
                await Task.Delay(1); // 模拟异步操作

                using var aes = Aes.Create();
                aes.Key = _keyEncryptionKey;
                
                // 提取IV
                var iv = new byte[aes.BlockSize / 8];
                Buffer.BlockCopy(encryptedData, 0, iv, 0, iv.Length);
                aes.IV = iv;

                // 提取加密数据
                var cipherData = new byte[encryptedData.Length - iv.Length];
                Buffer.BlockCopy(encryptedData, iv.Length, cipherData, 0, cipherData.Length);

                using var decryptor = aes.CreateDecryptor();
                return decryptor.TransformFinalBlock(cipherData, 0, cipherData.Length);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "解密密钥数据失败");
                throw;
            }
        }

        /// <summary>
        /// 使用密码加密数据
        /// </summary>
        public async Task<byte[]> EncryptWithPasswordAsync(byte[] data, string password)
        {
            try
            {
                await Task.Delay(1); // 模拟异步操作

                // 生成盐值
                var salt = new byte[16];
                using var rng = RandomNumberGenerator.Create();
                rng.GetBytes(salt);

                // 使用PBKDF2生成密钥
                using var pbkdf2 = new Rfc2898DeriveBytes(password, salt, 10000, HashAlgorithmName.SHA256);
                var key = pbkdf2.GetBytes(32); // 256位密钥

                using var aes = Aes.Create();
                aes.Key = key;
                aes.GenerateIV();

                using var encryptor = aes.CreateEncryptor();
                var encryptedData = encryptor.TransformFinalBlock(data, 0, data.Length);
                
                // 组合盐值、IV和加密数据
                var result = new byte[salt.Length + aes.IV.Length + encryptedData.Length];
                Buffer.BlockCopy(salt, 0, result, 0, salt.Length);
                Buffer.BlockCopy(aes.IV, 0, result, salt.Length, aes.IV.Length);
                Buffer.BlockCopy(encryptedData, 0, result, salt.Length + aes.IV.Length, encryptedData.Length);
                
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "使用密码加密数据失败");
                throw;
            }
        }

        /// <summary>
        /// 使用密码解密数据
        /// </summary>
        public async Task<byte[]> DecryptWithPasswordAsync(byte[] encryptedData, string password)
        {
            try
            {
                await Task.Delay(1); // 模拟异步操作

                // 提取盐值
                var salt = new byte[16];
                Buffer.BlockCopy(encryptedData, 0, salt, 0, salt.Length);

                // 使用PBKDF2生成密钥
                using var pbkdf2 = new Rfc2898DeriveBytes(password, salt, 10000, HashAlgorithmName.SHA256);
                var key = pbkdf2.GetBytes(32); // 256位密钥

                using var aes = Aes.Create();
                aes.Key = key;
                
                // 提取IV
                var iv = new byte[aes.BlockSize / 8];
                Buffer.BlockCopy(encryptedData, salt.Length, iv, 0, iv.Length);
                aes.IV = iv;

                // 提取加密数据
                var cipherData = new byte[encryptedData.Length - salt.Length - iv.Length];
                Buffer.BlockCopy(encryptedData, salt.Length + iv.Length, cipherData, 0, cipherData.Length);

                using var decryptor = aes.CreateDecryptor();
                return decryptor.TransformFinalBlock(cipherData, 0, cipherData.Length);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "使用密码解密数据失败");
                throw;
            }
        }

        /// <summary>
        /// 生成安全密码
        /// </summary>
        public string GenerateSecurePassword(int length = 16)
        {
            const string chars = "ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789!@#$%^&*";
            var password = new StringBuilder();
            
            using var rng = RandomNumberGenerator.Create();
            var randomBytes = new byte[length];
            rng.GetBytes(randomBytes);
            
            for (int i = 0; i < length; i++)
            {
                password.Append(chars[randomBytes[i] % chars.Length]);
            }
            
            return password.ToString();
        }

        /// <summary>
        /// 验证密码强度
        /// </summary>
        public bool ValidatePasswordStrength(string password)
        {
            if (string.IsNullOrEmpty(password))
                return false;

            var minLength = _configuration.GetValue<int>("SecuritySettings:PasswordMinLength", 8);
            var requireComplexity = _configuration.GetValue<bool>("SecuritySettings:EnablePasswordComplexity", true);

            if (password.Length < minLength)
                return false;

            if (!requireComplexity)
                return true;

            // 检查复杂性要求
            bool hasUpper = false, hasLower = false, hasDigit = false, hasSpecial = false;
            
            foreach (char c in password)
            {
                if (char.IsUpper(c)) hasUpper = true;
                else if (char.IsLower(c)) hasLower = true;
                else if (char.IsDigit(c)) hasDigit = true;
                else if (!char.IsLetterOrDigit(c)) hasSpecial = true;
            }

            return hasUpper && hasLower && hasDigit && hasSpecial;
        }

        /// <summary>
        /// 计算哈希值
        /// </summary>
        public async Task<string> ComputeHashAsync(string input)
        {
            await Task.Delay(1); // 模拟异步操作

            var inputBytes = Encoding.UTF8.GetBytes(input);
            var hashBytes = SHA256.HashData(inputBytes);
            return Convert.ToBase64String(hashBytes);
        }

        /// <summary>
        /// 验证哈希值
        /// </summary>
        public async Task<bool> VerifyHashAsync(string input, string hash)
        {
            var computedHash = await ComputeHashAsync(input);
            return computedHash == hash;
        }
    }

    /// <summary>
    /// 密钥管理服务接口
    /// </summary>
    public interface IKeyManagementService
    {
        Task<List<KeyInfo>> GetKeyListAsync(string? clientId = null);
        Task<KeyInfo> GetKeyByIdAsync(string keyId);
        Task<bool> DeleteKeyAsync(string keyId);
        Task<bool> UpdateKeyStatusAsync(string keyId, KeyStatus status);
        Task<bool> RevokeKeyAsync(string keyId, string reason);
    }

    /// <summary>
    /// 密钥管理服务实现
    /// </summary>
    public class KeyManagementService(ILogger<KeyManagementService> logger, IConfiguration configuration) : IKeyManagementService
    {
        private readonly ILogger<KeyManagementService> _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        private readonly IConfiguration _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));

        public async Task<List<KeyInfo>> GetKeyListAsync(string? clientId = null)
        {
            // TODO: 从数据库查询密钥列表
            await Task.Delay(10); // 模拟数据库查询

            // 返回模拟数据
            return [
                                new()
                {
                    KeyId = "MK_CLIENT_001_20250119_1234",
                    KeyName = "演示客户主密钥",
                    ClientId = "CLIENT_001",
                    ClientName = "演示客户",
                    Algorithm = CryptoAlgorithm.AES256,
                    KeyLength = 256,
                    Status = KeyStatus.Active,
                    CreatedTime = DateTime.Now.AddDays(-30),
                    ExpirationDate = DateTime.Now.AddYears(1),
                    CreatedBy = "演示系统",
                    LastModifiedBy = "演示系统",
                    Description = "演示客户的主密钥",
                    KeyHash = "demo_main_key_hash",
                    EffectiveDate = DateTime.Now.AddDays(-30),
                    LastModified = DateTime.Now
                }
            ];
        }

        public async Task<KeyInfo> GetKeyByIdAsync(string keyId)
        {
            // TODO: 从数据库查询特定密钥
            await Task.Delay(10);
            
            return new()
            {
                KeyId = keyId,
                ClientId = "DEMO_CLIENT_ID",
                ClientName = "演示客户",
                KeyName = "示例密钥",
                Algorithm = CryptoAlgorithm.AES256,
                KeyLength = 256,
                Status = KeyStatus.Active,
                CreatedTime = DateTime.Now,
                CreatedBy = "演示用户",
                LastModifiedBy = "演示用户",
                Description = "演示用的示例密钥",
                KeyHash = "demo_key_hash",
                EffectiveDate = DateTime.Now,
                ExpirationDate = DateTime.Now.AddYears(1),
                LastModified = DateTime.Now
            };
        }

        public async Task<bool> DeleteKeyAsync(string keyId)
        {
            // TODO: 删除数据库中的密钥
            await Task.Delay(10);
            _logger.LogInformation("密钥已删除：{KeyId}", keyId);
            return true;
        }

        public async Task<bool> UpdateKeyStatusAsync(string keyId, KeyStatus status)
        {
            // TODO: 更新数据库中的密钥状态
            await Task.Delay(10);
            _logger.LogInformation("密钥状态已更新：{KeyId} -> {Status}", keyId, status);
            return true;
        }

        public async Task<bool> RevokeKeyAsync(string keyId, string reason)
        {
            // TODO: 吊销数据库中的密钥
            await Task.Delay(10);
            _logger.LogInformation("密钥已吊销：{KeyId}，原因：{Reason}", keyId, reason);
            return true;
        }
    }

    /// <summary>
    /// 配置服务接口
    /// </summary>
    public interface IConfigurationService
    {
        Task<T?> GetConfigValueAsync<T>(string key, T? defaultValue = default);
        Task<bool> SetConfigValueAsync<T>(string key, T value);
        Task<Dictionary<string, object>> GetAllConfigsAsync();
        Task<bool> ReloadConfigAsync();
    }

    /// <summary>
    /// 配置服务实现
    /// </summary>
    public class ConfigurationService(ILogger<ConfigurationService> logger, IConfiguration configuration) : IConfigurationService
    {
        private readonly ILogger<ConfigurationService> _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        private readonly IConfiguration _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));

        public async Task<T?> GetConfigValueAsync<T>(string key, T? defaultValue = default)
        {
            await Task.Delay(1);
            var configValue = _configuration.GetValue<T?>(key);
            return configValue ?? defaultValue;
        }

        public async Task<bool> SetConfigValueAsync<T>(string key, T value)
        {
            // TODO: 实现配置值的持久化保存
            await Task.Delay(1);
            _logger.LogInformation("配置已更新：{Key} = {Value}", key, value);
            return true;
        }

        public async Task<Dictionary<string, object>> GetAllConfigsAsync()
        {
            await Task.Delay(1);
            
            var configs = new Dictionary<string, object>();
            
            // 获取主要配置项
            foreach (var section in _configuration.GetChildren())
            {
                var sectionKey = section.Key ?? string.Empty;
                if (!string.IsNullOrEmpty(section.Value))
                {
                    configs[sectionKey] = section.Value;
                }
                else
                {
                    configs[sectionKey] = section.GetChildren()
                        .Where(x => !string.IsNullOrEmpty(x.Key))
                        .ToDictionary(x => x.Key!, x => (object)(x.Value ?? string.Empty));
                }
            }
            
            return configs;
        }

        public async Task<bool> ReloadConfigAsync()
        {
            await Task.Delay(1);
            // TODO: 重新加载配置文件
            _logger.LogInformation("配置已重新加载");
            return true;
        }
    }
} 