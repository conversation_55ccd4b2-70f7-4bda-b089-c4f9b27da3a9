using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using KeyGenerator.Models;
using KeyGenerator.Services;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows;
using System.Linq;

namespace KeyGenerator.ViewModels
{
    /// <summary>
    /// 密钥分发视图模型
    /// </summary>
    public partial class KeyDistributionViewModel : ViewModelBase
    {
        private readonly ILogger<KeyDistributionViewModel> _logger;
        private readonly IKeyDistributionService _distributionService;
        private string _masterKeyId = string.Empty;

        [ObservableProperty]
        private MasterKeyEntity? _masterKey;

        [ObservableProperty]
        private ObservableCollection<KeyDistributionTask> _distributionTasks = new();

        [ObservableProperty]
        private KeyDistributionTask? _selectedTask;

        [ObservableProperty]
        private bool _isLoading = true;

        [ObservableProperty]
        private string _statusText = "正在加载分发任务...";

        public KeyDistributionViewModel(
            ILogger<KeyDistributionViewModel> logger,
            IKeyDistributionService distributionService)
        {
            _logger = logger;
            _distributionService = distributionService;
        }

        public async Task LoadTasksAsync(string keyId)
        {
            _masterKeyId = keyId;
            IsLoading = true;
            StatusText = $"正在加载密钥 {keyId} 的分发任务...";
            _logger.LogInformation("开始加载密钥 {KeyId} 的分发任务", keyId);

            try
            {
                // In a real app, you would fetch the key details and its distribution tasks.
                // Here we will just use the sample data from the service.
                var tasks = await _distributionService.GetDistributionTasksAsync(1, 100);
                
                // Filter tasks that are related to our key (mock logic)
                var relatedTasks = tasks.Where(t => t.Keys.Any(k => k.ParentKeyId == _masterKeyId || k.KeyId == _masterKeyId)).ToList();

                Application.Current.Dispatcher.Invoke(() =>
                {
                    DistributionTasks.Clear();
                    foreach (var task in relatedTasks)
                    {
                        DistributionTasks.Add(task);
                    }
                });
                
                StatusText = $"找到 {DistributionTasks.Count} 个相关的分发任务。";
            }
            catch (Exception ex)
            {
                StatusText = "加载分发任务失败。";
                _logger.LogError(ex, "加载密钥 {KeyId} 的分发任务时发生错误", keyId);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async Task CreateNewTaskAsync()
        {
            StatusText = "正在创建新的分发任务...";
            // This would open a dialog to configure and create a new KeyDistributionTask.
            // For now, we just log it.
            _logger.LogInformation("请求为密钥 {KeyId} 创建新的分发任务", _masterKeyId);

            var newTask = new KeyDistributionTask
            {
                TaskName = $"分发任务 for {_masterKeyId}",
                CreatedBy = "operator",
                Description = $"手动创建的任务 at {DateTime.Now}",
                TargetClients = new() { "CLIENT-NEW-001" },
                Keys = new() { new KeyDistributionItem { KeyId = _masterKeyId, ParentKeyId = _masterKeyId, Level = KeyHierarchyLevel.Master } }
            };

            var taskId = await _distributionService.CreateDistributionTaskAsync(newTask);
            await LoadTasksAsync(_masterKeyId); // Refresh the list
            StatusText = $"已创建新任务: {taskId}";
        }

        [RelayCommand(CanExecute = nameof(CanStartTask))]
        private async Task StartTaskAsync(KeyDistributionTask? task)
        {
            if (task == null) return;
            await _distributionService.StartDistributionAsync(task.TaskId);
            await LoadTasksAsync(_masterKeyId);
        }

        private bool CanStartTask(KeyDistributionTask? task)
        {
            return task != null && task.Status is KeyDistributionStatus.Pending or KeyDistributionStatus.Failed;
        }
    }
} 