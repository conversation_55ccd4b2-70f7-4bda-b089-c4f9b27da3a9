import Foundation
import CryptoKit
import CommonCrypto

/// 加密工具类
/// 提供加密、解密、数字签名、密钥生成等核心加密功能
class CryptoUtils {
    
    // MARK: - Constants
    static let keyLength = 32 // 256位密钥
    static let nonceLength = 12 // 96位nonce
    static let tagLength = 16 // 128位认证标签
    
    // MARK: - Key Generation
    
    /// 生成随机密钥
    static func generateKey(length: Int = keyLength) -> Data {
        var keyData = Data(count: length)
        let result = keyData.withUnsafeMutableBytes {
            SecRandomCopyBytes(kSecRandomDefault, length, $0.baseAddress!)
        }
        
        assert(result == errSecSuccess, "密钥生成失败")
        return keyData
    }
    
    /// 生成随机nonce
    static func generateNonce(length: Int = nonceLength) -> Data {
        var nonceData = Data(count: length)
        let result = nonceData.withUnsafeMutableBytes {
            SecRandomCopyBytes(kSecRandomDefault, length, $0.baseAddress!)
        }
        
        assert(result == errSecSuccess, "Nonce生成失败")
        return nonceData
    }
    
    /// 生成RSA密钥对
    static func generateKeyPair() throws -> KeyPair {
        let attributes: [String: Any] = [
            kSecAttrKeyType as String: kSecAttrKeyTypeRSA,
            kSecAttrKeySizeInBits as String: 2048,
            kSecPrivateKeyAttrs as String: [
                kSecAttrIsPermanent as String: false,
                kSecAttrApplicationTag as String: "com.cryptosystem.declassification.keypair".data(using: .utf8)!
            ]
        ]
        
        var error: Unmanaged<CFError>?
        guard let privateKey = SecKeyCreateRandomKey(attributes as CFDictionary, &error) else {
            if let error = error {
                throw CryptoError.keyGenerationFailed(error.takeRetainedValue().localizedDescription)
            }
            throw CryptoError.keyGenerationFailed("未知错误")
        }
        
        guard let publicKey = SecKeyCopyPublicKey(privateKey) else {
            throw CryptoError.keyGenerationFailed("无法提取公钥")
        }
        
        // 导出密钥数据
        let privateKeyData = try exportKey(privateKey)
        let publicKeyData = try exportKey(publicKey)
        
        return KeyPair(privateKey: privateKeyData, publicKey: publicKeyData)
    }
    
    /// 导出密钥数据
    static func exportKey(_ key: SecKey) throws -> Data {
        var error: Unmanaged<CFError>?
        guard let keyData = SecKeyCopyExternalRepresentation(key, &error) else {
            if let error = error {
                throw CryptoError.keyExportFailed(error.takeRetainedValue().localizedDescription)
            }
            throw CryptoError.keyExportFailed("未知错误")
        }
        
        return keyData as Data
    }
    
    // MARK: - AES-GCM Encryption/Decryption
    
    /// AES-GCM加密
    static func encrypt(data: Data, key: Data, nonce: Data) throws -> Data {
        guard key.count == keyLength else {
            throw CryptoError.invalidKeyLength("密钥长度必须为\(keyLength)字节")
        }
        
        guard nonce.count == nonceLength else {
            throw CryptoError.invalidNonceLength("Nonce长度必须为\(nonceLength)字节")
        }
        
        do {
            let symmetricKey = SymmetricKey(data: key)
            let gcmNonce = try AES.GCM.Nonce(data: nonce)
            let sealedBox = try AES.GCM.seal(data, using: symmetricKey, nonce: gcmNonce)
            
            // 组合密文和认证标签
            var encryptedData = Data()
            encryptedData.append(sealedBox.ciphertext)
            encryptedData.append(sealedBox.tag)
            
            return encryptedData
        } catch {
            throw CryptoError.encryptionFailed(error.localizedDescription)
        }
    }
    
    /// AES-GCM解密
    static func decrypt(data: Data, key: Data, nonce: Data) throws -> Data {
        guard key.count == keyLength else {
            throw CryptoError.invalidKeyLength("密钥长度必须为\(keyLength)字节")
        }
        
        guard nonce.count == nonceLength else {
            throw CryptoError.invalidNonceLength("Nonce长度必须为\(nonceLength)字节")
        }
        
        guard data.count > tagLength else {
            throw CryptoError.decryptionFailed("数据长度不足")
        }
        
        do {
            // 分离密文和认证标签
            let ciphertext = data.prefix(data.count - tagLength)
            let tag = data.suffix(tagLength)
            
            let symmetricKey = SymmetricKey(data: key)
            let gcmNonce = try AES.GCM.Nonce(data: nonce)
            let sealedBox = try AES.GCM.SealedBox(nonce: gcmNonce, ciphertext: ciphertext, tag: tag)
            
            return try AES.GCM.open(sealedBox, using: symmetricKey)
        } catch {
            throw CryptoError.decryptionFailed(error.localizedDescription)
        }
    }
    
    // MARK: - Digital Signature
    
    /// 使用私钥对数据进行签名
    static func sign(data: Data, privateKey: Data) throws -> Data {
        let key = try importPrivateKey(privateKey)
        
        var error: Unmanaged<CFError>?
        guard let signature = SecKeyCreateSignature(
            key,
            .rsaSignatureMessagePKCS1v15SHA256,
            data as CFData,
            &error
        ) else {
            if let error = error {
                throw CryptoError.signatureFailed(error.takeRetainedValue().localizedDescription)
            }
            throw CryptoError.signatureFailed("签名失败")
        }
        
        return signature as Data
    }
    
    /// 使用公钥验证签名
    static func verify(signature: Data, data: Data, publicKey: Data) throws -> Bool {
        let key = try importPublicKey(publicKey)
        
        var error: Unmanaged<CFError>?
        let isValid = SecKeyVerifySignature(
            key,
            .rsaSignatureMessagePKCS1v15SHA256,
            data as CFData,
            signature as CFData,
            &error
        )
        
        if let error = error {
            throw CryptoError.verificationFailed(error.takeRetainedValue().localizedDescription)
        }
        
        return isValid
    }
    
    /// 导入私钥
    static func importPrivateKey(_ keyData: Data) throws -> SecKey {
        let attributes: [String: Any] = [
            kSecAttrKeyType as String: kSecAttrKeyTypeRSA,
            kSecAttrKeyClass as String: kSecAttrKeyClassPrivate,
            kSecAttrKeySizeInBits as String: 2048
        ]
        
        var error: Unmanaged<CFError>?
        guard let key = SecKeyCreateWithData(keyData as CFData, attributes as CFDictionary, &error) else {
            if let error = error {
                throw CryptoError.keyImportFailed(error.takeRetainedValue().localizedDescription)
            }
            throw CryptoError.keyImportFailed("私钥导入失败")
        }
        
        return key
    }
    
    /// 导入公钥
    static func importPublicKey(_ keyData: Data) throws -> SecKey {
        let attributes: [String: Any] = [
            kSecAttrKeyType as String: kSecAttrKeyTypeRSA,
            kSecAttrKeyClass as String: kSecAttrKeyClassPublic,
            kSecAttrKeySizeInBits as String: 2048
        ]
        
        var error: Unmanaged<CFError>?
        guard let key = SecKeyCreateWithData(keyData as CFData, attributes as CFDictionary, &error) else {
            if let error = error {
                throw CryptoError.keyImportFailed(error.takeRetainedValue().localizedDescription)
            }
            throw CryptoError.keyImportFailed("公钥导入失败")
        }
        
        return key
    }
    
    // MARK: - Hash Functions
    
    /// SHA-256哈希
    static func sha256(data: Data) -> Data {
        return Data(SHA256.hash(data: data))
    }
    
    /// SHA-512哈希
    static func sha512(data: Data) -> Data {
        return Data(SHA512.hash(data: data))
    }
    
    /// HMAC-SHA256
    static func hmacSHA256(data: Data, key: Data) -> Data {
        let symmetricKey = SymmetricKey(data: key)
        let authenticationCode = HMAC<SHA256>.authenticationCode(for: data, using: symmetricKey)
        return Data(authenticationCode)
    }
    
    // MARK: - Key Derivation
    
    /// PBKDF2密钥派生
    static func deriveKey(from password: String, salt: Data, iterations: Int = 100000, keyLength: Int = keyLength) throws -> Data {
        guard let passwordData = password.data(using: .utf8) else {
            throw CryptoError.passwordEncodingFailed("密码编码失败")
        }
        
        var derivedKey = Data(count: keyLength)
        let result = derivedKey.withUnsafeMutableBytes { derivedKeyBytes in
            salt.withUnsafeBytes { saltBytes in
                passwordData.withUnsafeBytes { passwordBytes in
                    CCKeyDerivationPBKDF(
                        CCPBKDFAlgorithm(kCCPBKDF2),
                        passwordBytes.baseAddress?.assumingMemoryBound(to: Int8.self),
                        passwordData.count,
                        saltBytes.baseAddress?.assumingMemoryBound(to: UInt8.self),
                        salt.count,
                        CCPseudoRandomAlgorithm(kCCPRFHmacAlgSHA256),
                        UInt32(iterations),
                        derivedKeyBytes.baseAddress?.assumingMemoryBound(to: UInt8.self),
                        keyLength
                    )
                }
            }
        }
        
        guard result == kCCSuccess else {
            throw CryptoError.keyDerivationFailed("密钥派生失败")
        }
        
        return derivedKey
    }
    
    /// HKDF密钥派生
    static func hkdfDeriveKey(from inputKey: Data, salt: Data, info: Data, keyLength: Int = keyLength) -> Data {
        let inputSymmetricKey = SymmetricKey(data: inputKey)
        let derivedKey = HKDF<SHA256>.deriveKey(
            inputKeyMaterial: inputSymmetricKey,
            salt: salt,
            info: info,
            outputByteCount: keyLength
        )
        
        return derivedKey.withUnsafeBytes { Data($0) }
    }
    
    // MARK: - Utility Functions
    
    /// 安全比较两个数据
    static func constantTimeEquals(_ a: Data, _ b: Data) -> Bool {
        guard a.count == b.count else { return false }
        
        var result: UInt8 = 0
        for i in 0..<a.count {
            result |= a[i] ^ b[i]
        }
        
        return result == 0
    }
    
    /// 生成随机盐
    static func generateSalt(length: Int = 16) -> Data {
        return generateKey(length: length)
    }
    
    /// 计算文件校验和
    static func calculateFileChecksum(at url: URL) throws -> String {
        let data = try Data(contentsOf: url)
        let hash = sha256(data: data)
        return hash.map { String(format: "%02hhx", $0) }.joined()
    }
    
    /// 验证文件完整性
    static func verifyFileIntegrity(at url: URL, expectedChecksum: String) throws -> Bool {
        let actualChecksum = try calculateFileChecksum(at: url)
        return constantTimeEquals(
            actualChecksum.data(using: .utf8) ?? Data(),
            expectedChecksum.data(using: .utf8) ?? Data()
        )
    }
}

// MARK: - Supporting Types

struct KeyPair {
    let privateKey: Data
    let publicKey: Data
}

// MARK: - Error Types

enum CryptoError: LocalizedError {
    case invalidKeyLength(String)
    case invalidNonceLength(String)
    case encryptionFailed(String)
    case decryptionFailed(String)
    case keyGenerationFailed(String)
    case keyExportFailed(String)
    case keyImportFailed(String)
    case signatureFailed(String)
    case verificationFailed(String)
    case passwordEncodingFailed(String)
    case keyDerivationFailed(String)
    case checksumVerificationFailed(String)
    
    var errorDescription: String? {
        switch self {
        case .invalidKeyLength(let message):
            return "无效的密钥长度: \(message)"
        case .invalidNonceLength(let message):
            return "无效的Nonce长度: \(message)"
        case .encryptionFailed(let message):
            return "加密失败: \(message)"
        case .decryptionFailed(let message):
            return "解密失败: \(message)"
        case .keyGenerationFailed(let message):
            return "密钥生成失败: \(message)"
        case .keyExportFailed(let message):
            return "密钥导出失败: \(message)"
        case .keyImportFailed(let message):
            return "密钥导入失败: \(message)"
        case .signatureFailed(let message):
            return "签名失败: \(message)"
        case .verificationFailed(let message):
            return "验证失败: \(message)"
        case .passwordEncodingFailed(let message):
            return "密码编码失败: \(message)"
        case .keyDerivationFailed(let message):
            return "密钥派生失败: \(message)"
        case .checksumVerificationFailed(let message):
            return "校验和验证失败: \(message)"
        }
    }
} 