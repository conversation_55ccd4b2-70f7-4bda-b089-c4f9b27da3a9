/*
 * perf_buffer_pool.c
 * 
 * Windows MiniFilter驱动缓冲区池管理实现
 * 解决频繁内存分配的性能瓶颈
 */

#include <fltKernel.h>
#include <ntstrsafe.h>
#include "perf_optimization.h"

// 外部全局变量
extern PERF_MANAGER g_PerfManager;

// 缓冲区池大小映射表
static const ULONG g_BufferSizes[BufferSizeMax] = {
    PERF_BUFFER_POOL_SIZE_4K,
    PERF_BUFFER_POOL_SIZE_16K,
    PERF_BUFFER_POOL_SIZE_64K,
    PERF_BUFFER_POOL_SIZE_256K
};

static const ULONG g_BufferCounts[BufferSizeMax] = {
    PERF_BUFFER_POOL_COUNT_4K,
    PERF_BUFFER_POOL_COUNT_16K,
    PERF_BUFFER_POOL_COUNT_64K,
    PERF_BUFFER_POOL_COUNT_256K
};

// 前向声明
static NTSTATUS
PerfInitializeBufferPool(
    _Inout_ PPERF_BUFFER_POOL Pool,
    _In_ ULONG BufferSize,
    _In_ ULONG InitialCount
    );

static VOID
PerfCleanupBufferPool(
    _Inout_ PPERF_BUFFER_POOL Pool
    );

static PPERF_BUFFER_ENTRY
PerfAllocateBufferEntry(
    _In_ PPERF_BUFFER_POOL Pool
    );

static VOID
PerfFreeBufferEntry(
    _In_ PPERF_BUFFER_POOL Pool,
    _In_ PPERF_BUFFER_ENTRY Entry
    );

//
// 初始化所有缓冲区池
//
NTSTATUS
PerfInitializeAllBufferPools(
    VOID
    )
{
    NTSTATUS status = STATUS_SUCCESS;
    ULONG i;

    PERF_DEBUG_PRINT("Initializing buffer pools...");

    for (i = 0; i < BufferSizeMax; i++) {
        status = PerfInitializeBufferPool(
            &g_PerfManager.BufferPools[i],
            g_BufferSizes[i],
            g_BufferCounts[i]
        );

        if (!NT_SUCCESS(status)) {
            PERF_DEBUG_PRINT("Failed to initialize buffer pool %lu, status: 0x%08x", i, status);
            
            // 清理已初始化的池
            while (i > 0) {
                i--;
                PerfCleanupBufferPool(&g_PerfManager.BufferPools[i]);
            }
            break;
        }

        PERF_DEBUG_PRINT("Buffer pool %lu initialized: size=%lu, count=%lu", 
                        i, g_BufferSizes[i], g_BufferCounts[i]);
    }

    return status;
}

//
// 清理所有缓冲区池
//
VOID
PerfCleanupAllBufferPools(
    VOID
    )
{
    ULONG i;

    PERF_DEBUG_PRINT("Cleaning up buffer pools...");

    for (i = 0; i < BufferSizeMax; i++) {
        PerfCleanupBufferPool(&g_PerfManager.BufferPools[i]);
    }
}

//
// 分配缓冲区
//
NTSTATUS
PerfAllocateBuffer(
    _In_ ULONG Size,
    _Out_ PVOID *Buffer,
    _Out_ PERF_BUFFER_SIZE_TYPE *BufferType
    )
{
    NTSTATUS status = STATUS_SUCCESS;
    PERF_BUFFER_SIZE_TYPE type;
    PPERF_BUFFER_POOL pool;
    PPERF_BUFFER_ENTRY entry;
    KIRQL oldIrql;

    if (Buffer == NULL || BufferType == NULL) {
        return STATUS_INVALID_PARAMETER;
    }

    *Buffer = NULL;
    *BufferType = BufferSizeMax;

    // 确定缓冲区类型
    type = PerfGetBufferSizeType(Size);
    if (type >= BufferSizeMax) {
        // 请求的大小超过了最大缓冲区大小，直接分配
        PERF_DEBUG_PRINT("Buffer size %lu exceeds maximum pool size, direct allocation", Size);
        
        *Buffer = ExAllocatePoolWithTag(NonPagedPool, Size, 'pfbE');
        if (*Buffer == NULL) {
            status = STATUS_INSUFFICIENT_RESOURCES;
        }
        return status;
    }

    pool = &g_PerfManager.BufferPools[type];
    *BufferType = type;

    // 获取自旋锁
    KeAcquireSpinLock(&pool->SpinLock, &oldIrql);

    __try {
        // 更新统计
        pool->AllocRequests++;

        // 尝试从空闲列表获取
        if (!IsListEmpty(&pool->FreeList)) {
            PLIST_ENTRY listEntry = RemoveHeadList(&pool->FreeList);
            entry = CONTAINING_RECORD(listEntry, PERF_BUFFER_ENTRY, ListEntry);
            
            // 移动到使用列表
            InsertTailList(&pool->UsedList, &entry->ListEntry);
            entry->InUse = TRUE;
            KeQuerySystemTime(&entry->LastUsed);
            
            pool->FreeCount--;
            pool->UsedCount++;
            pool->AllocHits++;
            
            *Buffer = entry->Buffer;
            
            PERF_DEBUG_PRINT("Buffer allocated from pool %lu: %p", type, *Buffer);
        } else {
            // 缓冲区池为空，直接分配
            pool->AllocMisses++;
            
            *Buffer = ExAllocatePoolWithTag(NonPagedPool, g_BufferSizes[type], 'pfbE');
            if (*Buffer == NULL) {
                status = STATUS_INSUFFICIENT_RESOURCES;
                __leave;
            }
            
            PERF_DEBUG_PRINT("Buffer pool %lu empty, direct allocation: %p", type, *Buffer);
        }
    }
    __finally {
        KeReleaseSpinLock(&pool->SpinLock, oldIrql);
    }

    return status;
}

//
// 释放缓冲区
//
VOID
PerfFreeBuffer(
    _In_ PVOID Buffer,
    _In_ PERF_BUFFER_SIZE_TYPE BufferType
    )
{
    PPERF_BUFFER_POOL pool;
    PPERF_BUFFER_ENTRY entry;
    PLIST_ENTRY listEntry;
    KIRQL oldIrql;
    BOOLEAN found = FALSE;

    if (Buffer == NULL) {
        return;
    }

    // 如果超出池范围，直接释放
    if (BufferType >= BufferSizeMax) {
        PERF_DEBUG_PRINT("Freeing buffer outside pool range: %p", Buffer);
        ExFreePool(Buffer);
        return;
    }

    pool = &g_PerfManager.BufferPools[BufferType];

    // 获取自旋锁
    KeAcquireSpinLock(&pool->SpinLock, &oldIrql);

    __try {
        pool->FreeRequests++;

        // 在使用列表中查找该缓冲区
        for (listEntry = pool->UsedList.Flink; 
             listEntry != &pool->UsedList; 
             listEntry = listEntry->Flink) {
            
            entry = CONTAINING_RECORD(listEntry, PERF_BUFFER_ENTRY, ListEntry);
            if (entry->Buffer == Buffer) {
                // 找到匹配的条目
                RemoveEntryList(&entry->ListEntry);
                
                // 检查池是否已满
                if (pool->FreeCount < g_BufferCounts[BufferType]) {
                    // 移动到空闲列表
                    InsertTailList(&pool->FreeList, &entry->ListEntry);
                    entry->InUse = FALSE;
                    KeQuerySystemTime(&entry->LastUsed);
                    
                    pool->FreeCount++;
                    pool->UsedCount--;
                    
                    PERF_DEBUG_PRINT("Buffer returned to pool %lu: %p", BufferType, Buffer);
                } else {
                    // 池已满，直接释放
                    ExFreePool(entry->Buffer);
                    ExFreePool(entry);
                    pool->TotalCount--;
                    pool->UsedCount--;
                    
                    PERF_DEBUG_PRINT("Buffer pool %lu full, direct free: %p", BufferType, Buffer);
                }
                
                found = TRUE;
                break;
            }
        }
        
        if (!found) {
            // 没有在池中找到，可能是直接分配的缓冲区
            PERF_DEBUG_PRINT("Buffer not found in pool %lu, direct free: %p", BufferType, Buffer);
            ExFreePool(Buffer);
        }
    }
    __finally {
        KeReleaseSpinLock(&pool->SpinLock, oldIrql);
    }
}

//
// 获取缓冲区大小类型
//
PERF_BUFFER_SIZE_TYPE
PerfGetBufferSizeType(
    _In_ ULONG Size
    )
{
    if (Size <= PERF_BUFFER_POOL_SIZE_4K) {
        return BufferSize4K;
    } else if (Size <= PERF_BUFFER_POOL_SIZE_16K) {
        return BufferSize16K;
    } else if (Size <= PERF_BUFFER_POOL_SIZE_64K) {
        return BufferSize64K;
    } else if (Size <= PERF_BUFFER_POOL_SIZE_256K) {
        return BufferSize256K;
    }
    
    return BufferSizeMax; // 超出范围
}

//
// 获取缓冲区池统计信息
//
VOID
PerfGetBufferPoolStats(
    _In_ PERF_BUFFER_SIZE_TYPE BufferType,
    _Out_ PULONG TotalCount,
    _Out_ PULONG FreeCount,
    _Out_ PULONG UsedCount,
    _Out_ PULONG AllocHits,
    _Out_ PULONG AllocMisses
    )
{
    PPERF_BUFFER_POOL pool;
    KIRQL oldIrql;

    if (BufferType >= BufferSizeMax) {
        return;
    }

    pool = &g_PerfManager.BufferPools[BufferType];

    KeAcquireSpinLock(&pool->SpinLock, &oldIrql);
    
    if (TotalCount) *TotalCount = pool->TotalCount;
    if (FreeCount) *FreeCount = pool->FreeCount;
    if (UsedCount) *UsedCount = pool->UsedCount;
    if (AllocHits) *AllocHits = pool->AllocHits;
    if (AllocMisses) *AllocMisses = pool->AllocMisses;
    
    KeReleaseSpinLock(&pool->SpinLock, oldIrql);
}

//
// 初始化单个缓冲区池
//
static NTSTATUS
PerfInitializeBufferPool(
    _Inout_ PPERF_BUFFER_POOL Pool,
    _In_ ULONG BufferSize,
    _In_ ULONG InitialCount
    )
{
    NTSTATUS status = STATUS_SUCCESS;
    PPERF_BUFFER_ENTRY entry;
    ULONG i;

    // 初始化池结构
    InitializeListHead(&Pool->FreeList);
    InitializeListHead(&Pool->UsedList);
    KeInitializeSpinLock(&Pool->SpinLock);
    
    Pool->Size = BufferSize;
    Pool->TotalCount = 0;
    Pool->FreeCount = 0;
    Pool->UsedCount = 0;
    Pool->AllocRequests = 0;
    Pool->AllocHits = 0;
    Pool->AllocMisses = 0;
    Pool->FreeRequests = 0;

    // 预分配缓冲区
    for (i = 0; i < InitialCount; i++) {
        entry = PerfAllocateBufferEntry(Pool);
        if (entry == NULL) {
            status = STATUS_INSUFFICIENT_RESOURCES;
            break;
        }
        
        InsertTailList(&Pool->FreeList, &entry->ListEntry);
        Pool->TotalCount++;
        Pool->FreeCount++;
    }

    return status;
}

//
// 清理单个缓冲区池
//
static VOID
PerfCleanupBufferPool(
    _Inout_ PPERF_BUFFER_POOL Pool
    )
{
    PLIST_ENTRY listEntry;
    PPERF_BUFFER_ENTRY entry;
    KIRQL oldIrql;

    KeAcquireSpinLock(&Pool->SpinLock, &oldIrql);

    // 清理空闲列表
    while (!IsListEmpty(&Pool->FreeList)) {
        listEntry = RemoveHeadList(&Pool->FreeList);
        entry = CONTAINING_RECORD(listEntry, PERF_BUFFER_ENTRY, ListEntry);
        
        if (entry->Buffer != NULL) {
            ExFreePool(entry->Buffer);
        }
        ExFreePool(entry);
    }

    // 清理使用列表
    while (!IsListEmpty(&Pool->UsedList)) {
        listEntry = RemoveHeadList(&Pool->UsedList);
        entry = CONTAINING_RECORD(listEntry, PERF_BUFFER_ENTRY, ListEntry);
        
        if (entry->Buffer != NULL) {
            ExFreePool(entry->Buffer);
        }
        ExFreePool(entry);
    }

    Pool->TotalCount = 0;
    Pool->FreeCount = 0;
    Pool->UsedCount = 0;

    KeReleaseSpinLock(&Pool->SpinLock, oldIrql);

    PERF_DEBUG_PRINT("Buffer pool cleaned up: size=%lu", Pool->Size);
}

//
// 分配缓冲区条目
//
static PPERF_BUFFER_ENTRY
PerfAllocateBufferEntry(
    _In_ PPERF_BUFFER_POOL Pool
    )
{
    PPERF_BUFFER_ENTRY entry;

    entry = (PPERF_BUFFER_ENTRY)ExAllocatePoolWithTag(
        NonPagedPool,
        sizeof(PERF_BUFFER_ENTRY),
        'epbE'
    );

    if (entry == NULL) {
        return NULL;
    }

    entry->Buffer = ExAllocatePoolWithTag(
        NonPagedPool,
        Pool->Size,
        'fpbE'
    );

    if (entry->Buffer == NULL) {
        ExFreePool(entry);
        return NULL;
    }

    entry->Size = Pool->Size;
    entry->InUse = FALSE;
    KeQuerySystemTime(&entry->LastUsed);

    return entry;
}

//
// 释放缓冲区条目
//
static VOID
PerfFreeBufferEntry(
    _In_ PPERF_BUFFER_POOL Pool,
    _In_ PPERF_BUFFER_ENTRY Entry
    )
{
    UNREFERENCED_PARAMETER(Pool);

    if (Entry != NULL) {
        if (Entry->Buffer != NULL) {
            ExFreePool(Entry->Buffer);
        }
        ExFreePool(Entry);
    }
} 