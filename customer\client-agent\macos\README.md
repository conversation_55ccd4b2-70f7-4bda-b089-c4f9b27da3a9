# macOS 客户端代理

## 概述

macOS 客户端代理是文档加密系统在 macOS 平台的实现，基于 System Extensions 技术提供透明的文件加解密功能。

## 技术架构

### 核心组件

1. **CryptoManager** (`driver/crypto/CryptoManager.swift`)
   - 企业级加密管理器（28KB）
   - 支持 AES-GCM 和 SM4-GCM 双算法
   - Keychain 密钥管理和缓存
   - 完整的文件头创建和验证

2. **FileProviderExtension** (`driver/file_filter/FileProviderExtension.swift`)
   - 文件提供者扩展（13KB）
   - 透明文件加解密
   - 策略驱动的加密判断
   - 完整的错误处理和日志记录

3. **PolicyManager** (`driver/PolicyManager.swift`)
   - 策略管理器（8KB）
   - 支持文件扩展名和路径模式匹配
   - 优先级排序策略处理
   - 持久化存储和通知机制

4. **KeySyncService** (`driver/KeySyncService.swift`) 🆕
   - 密钥同步服务（15KB）
   - 与密钥服务器安全通信
   - 本地密钥缓存管理
   - 自动重试和错误恢复

5. **FileMonitorEngine** (`driver/FileMonitorEngine.swift`) 🆕
   - 文件监控引擎（12KB）
   - 实时文件系统监控
   - 策略触发和执行
   - 系统睡眠/唤醒处理

### 项目结构

```
macos/
├── CryptoSystemMac.xcodeproj/      # Xcode项目配置
├── driver/                         # 核心驱动组件
│   ├── crypto/
│   │   └── CryptoManager.swift     # 加密管理器
│   ├── file_filter/
│   │   └── FileProviderExtension.swift  # 文件提供者扩展
│   ├── PolicyManager.swift         # 策略管理器
│   ├── KeySyncService.swift        # 密钥同步服务 🆕
│   └── FileMonitorEngine.swift     # 文件监控引擎 🆕
├── build.sh                        # 构建脚本 🆕
└── README.md                       # 本文档
```

## 功能特性

### ✅ 已实现功能

- **透明加解密**: 用户无感知的文件加解密操作
- **双算法支持**: AES-GCM（国际标准）+ SM4-GCM（国密标准）
- **策略管理**: 基于文件路径和扩展名的灵活策略配置
- **密钥管理**: 安全的 Keychain 密钥存储和缓存
- **文件监控**: 实时文件系统事件监控和处理
- **密钥同步**: 与服务器的安全密钥同步机制
- **错误处理**: 完整的错误处理和恢复机制
- **日志记录**: 详细的操作日志和调试信息

### 🚀 核心优势

- **企业级安全**: 符合企业安全标准的加密实现
- **高性能**: 优化的加密算法和缓存机制
- **可扩展**: 模块化设计，易于扩展新功能
- **可靠性**: 完整的错误处理和恢复机制
- **兼容性**: 支持 macOS 10.15+ 系统

## 系统要求

- **操作系统**: macOS 10.15 (Catalina) 或更高版本
- **开发工具**: Xcode 12.0 或更高版本
- **Swift版本**: Swift 5.3 或更高版本
- **权限要求**: 
  - System Extension 权限
  - Full Disk Access 权限
  - Keychain Access 权限

## 构建指南

### 使用构建脚本（推荐）

项目提供了完整的构建脚本 `build.sh`，支持多种构建选项：

```bash
# 基本构建（Debug模式）
./build.sh

# Release构建
./build.sh -c Release

# 完整构建流程（构建、归档、导出）
./build.sh -c Release -a -e

# 运行测试
./build.sh -t

# 代码检查
./build.sh -l

# 清理构建目录
./build.sh --clean

# 查看帮助
./build.sh --help
```

### 手动构建

1. **打开项目**
   ```bash
   open CryptoSystemMac.xcodeproj
   ```

2. **配置签名**
   - 在 Xcode 中选择项目
   - 配置 Team 和 Bundle Identifier
   - 确保代码签名设置正确

3. **构建项目**
   - 选择目标设备或模拟器
   - 按 Cmd+B 进行构建

### 依赖管理

项目使用以下系统框架：
- `Foundation`: 基础功能
- `FileProvider`: 文件提供者扩展
- `Security`: 安全和加密功能
- `CryptoKit`: 现代加密API
- `Network`: 网络通信
- `Combine`: 响应式编程
- `CoreServices`: 文件系统事件

## 部署指南

### 开发环境部署

1. **启用开发者模式**
   ```bash
   sudo spctl developer-mode enable-terminal
   ```

2. **安装系统扩展**
   - 构建项目
   - 在系统偏好设置中允许系统扩展
   - 授予必要的权限

3. **配置权限**
   - Full Disk Access
   - System Extension
   - Keychain Access

### 生产环境部署

1. **代码签名**
   - 使用有效的开发者证书
   - 配置 Provisioning Profile
   - 启用 Hardened Runtime

2. **公证（Notarization）**
   ```bash
   # 导出应用
   ./build.sh -c Release -a -e
   
   # 提交公证
   xcrun altool --notarize-app --primary-bundle-id "com.yourcompany.cryptosystem" \
                --username "your-apple-id" --password "@keychain:AC_PASSWORD" \
                --file "export/CryptoSystemMac.app"
   ```

3. **分发**
   - 通过企业分发渠道
   - 或提交到 Mac App Store

## 配置说明

### 策略配置

策略管理器支持以下配置选项：

```swift
// 文件扩展名策略
policyManager.addPolicy(
    name: "Office文档",
    patterns: ["*.doc", "*.docx", "*.xls", "*.xlsx", "*.ppt", "*.pptx"],
    action: .encrypt,
    priority: 1
)

// 路径模式策略
policyManager.addPolicy(
    name: "机密目录",
    patterns: ["/Users/<USER>/Documents/机密/*"],
    action: .encrypt,
    priority: 2
)
```

### 密钥同步配置

```swift
// 初始化密钥同步服务
let keySyncService = KeySyncService(
    serverHost: "your-key-server.com",
    serverPort: 8443,
    syncInterval: 300  // 5分钟同步一次
)

// 启动定期同步
keySyncService.startPeriodicSync()
```

### 文件监控配置

```swift
// 初始化文件监控引擎
let fileMonitor = FileMonitorEngine(
    policyManager: policyManager,
    cryptoManager: cryptoManager
)

// 开始监控指定路径
try fileMonitor.startMonitoring(paths: [
    "/Users/<USER>/Documents",
    "/Users/<USER>/Desktop"
])
```

## 调试指南

### 日志查看

```bash
# 查看系统日志
log show --predicate 'subsystem == "com.yourcompany.cryptosystem"' --last 1h

# 查看控制台日志
log stream --predicate 'subsystem == "com.yourcompany.cryptosystem"'
```

### 常见问题

1. **权限问题**
   - 确保已授予 Full Disk Access 权限
   - 检查系统扩展是否已启用

2. **性能问题**
   - 检查文件监控范围是否过大
   - 优化策略匹配规则

3. **网络问题**
   - 检查密钥服务器连接
   - 验证网络防火墙设置

## 开发状态

### 当前进度：85% 完成 🚀 基本完成

#### ✅ 已完成组件
- [x] CryptoManager - 加密管理器 (100%)
- [x] FileProviderExtension - 文件提供者扩展 (100%)
- [x] PolicyManager - 策略管理器 (100%)
- [x] KeySyncService - 密钥同步服务 (100%)
- [x] FileMonitorEngine - 文件监控引擎 (100%)
- [x] Xcode项目配置 (100%)
- [x] 构建脚本 (100%)
- [x] 性能优化 (100%)
- [x] 错误恢复机制完善 (100%)

#### 🔄 进行中
- [ ] 单元测试覆盖 (70%)
- [ ] 系统扩展安装器 (60%)

#### 📋 待完成
- [ ] 用户界面（可选）
- [ ] 高级配置工具
- [ ] 性能监控面板

## 贡献指南

1. **代码规范**
   - 遵循 Swift 代码规范
   - 使用 SwiftLint 进行代码检查
   - 添加适当的注释和文档

2. **测试要求**
   - 为新功能添加单元测试
   - 确保测试覆盖率 > 80%
   - 运行集成测试

3. **提交规范**
   - 使用有意义的提交信息
   - 遵循 Git 流程规范
   - 进行代码审查

## 许可证

本项目采用企业内部许可证，仅供授权用户使用。

## 联系方式

- **技术支持**: <EMAIL>
- **问题反馈**: <EMAIL>
- **文档更新**: <EMAIL>

---

*最后更新: 2025-01-20*
*版本: 1.4.0* 