#!/bin/bash

# =====================================================
# 企业文档加密系统 - PostgreSQL数据库部署脚本
# 版本: 1.4.0
# 作者: 数据库管理员
# 说明: 专注于PostgreSQL数据库的部署和管理
# 更新: 2025-01-20 - 专注PostgreSQL，增强功能
# =====================================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ENV_FILE="${SCRIPT_DIR}/.env"
ENV_TEMPLATE="${SCRIPT_DIR}/env.template"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${PURPLE}[DEBUG]${NC} $1"
}

# 进度条函数
show_progress() {
    local duration=$1
    local step=$2
    local total=$3
    
    echo -ne "${CYAN}[${step}/${total}]${NC} "
    for ((i=0; i<duration; i++)); do
        echo -ne "▓"
        sleep 0.1
    done
    echo " 完成"
}

# 检查PostgreSQL是否已安装
check_postgres_installed() {
    if command -v psql &> /dev/null && command -v pg_ctl &> /dev/null; then
        local version=$(psql --version 2>/dev/null | head -n1 | awk '{print $3}')
        log_success "PostgreSQL已安装 (版本: $version)"
        return 0
    else
        log_error "PostgreSQL未安装，请先安装PostgreSQL"
        echo
        echo "📋 安装指令："
        echo "  Ubuntu/Debian: sudo apt-get install postgresql postgresql-contrib"
        echo "  CentOS/RHEL:   sudo yum install postgresql-server postgresql-contrib"
        echo "  macOS:         brew install postgresql"
        echo "  Windows:       从官网下载安装包"
        echo "                 https://www.postgresql.org/download/windows/"
        echo
        return 1
    fi
}

# 检查和创建环境变量文件
setup_env() {
    if [ ! -f "$ENV_FILE" ]; then
        log_warning "环境变量文件不存在，从模板创建..."
        cp "$ENV_TEMPLATE" "$ENV_FILE"
        log_warning "⚠️  请编辑 .env 文件并设置合适的密码！"
        echo
        echo "  建议执行: nano ${ENV_FILE}"
        echo "  或者执行: vim ${ENV_FILE}"
        echo
        read -p "按回车键继续（确保已配置.env文件）..."
    fi
    
    # 加载环境变量
    source "$ENV_FILE"
    log_success "环境变量加载完成"
}

# 创建必要的目录
create_directories() {
    log_info "创建数据目录..."
    
    # 创建目录
    mkdir -p "${SCRIPT_DIR}/logs"
    mkdir -p "${SCRIPT_DIR}/backups"
    mkdir -p "${SCRIPT_DIR}/temp"
    
    # 设置权限
    chmod 755 "${SCRIPT_DIR}/logs"
    chmod 755 "${SCRIPT_DIR}/backups"
    chmod 755 "${SCRIPT_DIR}/temp"
    
    log_success "数据目录创建完成"
}

# 检查PostgreSQL服务状态
check_postgres_service() {
    if systemctl is-active --quiet postgresql 2>/dev/null; then
        return 0
    elif service postgresql status &> /dev/null; then
        return 0
    else
        return 1
    fi
}

# 启动PostgreSQL服务
start_postgres_service() {
    log_info "启动PostgreSQL服务..."
    
    if systemctl start postgresql 2>/dev/null; then
        log_success "PostgreSQL服务启动成功"
    elif service postgresql start 2>/dev/null; then
        log_success "PostgreSQL服务启动成功"
    else
        log_error "无法启动PostgreSQL服务，请检查配置"
        return 1
    fi
    
    # 等待服务完全启动
    log_info "等待服务完全启动..."
    sleep 5
    
    if check_postgres_service; then
        log_success "PostgreSQL服务运行正常"
    else
        log_error "PostgreSQL服务启动异常"
        return 1
    fi
}

# 创建数据库和用户
setup_database() {
    log_info "🔧 设置PostgreSQL数据库和用户..."
    
    # 检查数据库是否存在
    if sudo -u postgres psql -lqt | cut -d \| -f 1 | grep -qw "${DB_NAME}"; then
        log_info "📦 数据库 ${DB_NAME} 已存在"
    else
        log_info "📦 创建数据库 ${DB_NAME}..."
        sudo -u postgres createdb "${DB_NAME}"
        log_success "数据库创建成功"
    fi
    
    # 检查用户是否存在
    if sudo -u postgres psql -tAc "SELECT 1 FROM pg_roles WHERE rolname='${DB_USER}'" | grep -q 1; then
        log_info "👤 用户 ${DB_USER} 已存在"
        # 确保用户有正确的权限
        sudo -u postgres psql -c "ALTER USER ${DB_USER} WITH PASSWORD '${DB_PASSWORD}';"
        sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE ${DB_NAME} TO ${DB_USER};"
        log_success "用户权限更新完成"
    else
        log_info "👤 创建用户 ${DB_USER}..."
        sudo -u postgres psql -c "CREATE USER ${DB_USER} WITH PASSWORD '${DB_PASSWORD}';"
        sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE ${DB_NAME} TO ${DB_USER};"
        log_success "用户创建成功"
    fi
    
    # 设置用户为数据库的所有者
    sudo -u postgres psql -c "ALTER DATABASE ${DB_NAME} OWNER TO ${DB_USER};"
    
    # 确保public schema的权限
    PGPASSWORD="${DB_PASSWORD}" psql -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" -c "GRANT ALL ON SCHEMA public TO ${DB_USER};" 2>/dev/null || true
    
    log_success "数据库和用户设置完成"
}

# 初始化数据库结构
init_database_schema() {
    log_info "📊 初始化PostgreSQL数据库结构..."
    
    # 执行初始化脚本
    if [ -f "${SCRIPT_DIR}/schema/postgres/01_init.sql" ]; then
        log_info "🚀 执行数据库初始化脚本..."
        if PGPASSWORD="${DB_PASSWORD}" psql -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" -f "${SCRIPT_DIR}/schema/postgres/01_init.sql" > /dev/null 2>&1; then
            log_success "数据库结构初始化完成"
        else
            log_error "数据库结构初始化失败"
            return 1
        fi
    else
        log_error "初始化脚本不存在: ${SCRIPT_DIR}/schema/postgres/01_init.sql"
        return 1
    fi
    
    # 执行数据初始化脚本
    if [ -f "${SCRIPT_DIR}/schema/postgres/02_init_data.sql" ]; then
        log_info "📋 执行数据初始化脚本..."
        if PGPASSWORD="${DB_PASSWORD}" psql -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" -f "${SCRIPT_DIR}/schema/postgres/02_init_data.sql" > /dev/null 2>&1; then
            log_success "初始数据导入完成"
        else
            log_warning "数据初始化脚本执行有警告，请检查"
        fi
    else
        log_warning "数据初始化脚本不存在: ${SCRIPT_DIR}/schema/postgres/02_init_data.sql"
    fi
}

# 测试数据库连接
test_connection() {
    log_info "🔍 测试PostgreSQL数据库连接..."
    
    if PGPASSWORD="${DB_PASSWORD}" psql -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" -c "SELECT version();" > /dev/null 2>&1; then
        log_success "数据库连接测试成功"
        
        # 显示版本信息
        local version=$(PGPASSWORD="${DB_PASSWORD}" psql -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" -tAc "SELECT version();" 2>/dev/null)
        log_info "💡 PostgreSQL版本: $(echo $version | cut -d' ' -f1-3)"
        
        return 0
    else
        log_error "数据库连接测试失败"
        log_error "请检查数据库配置和网络连接"
        return 1
    fi
}

# 查看服务状态
status_services() {
    log_info "📊 PostgreSQL数据库服务状态检查:"
    echo
    
    if check_postgres_service; then
        echo "✅ PostgreSQL服务: 运行中"
        
        # 显示连接信息
        echo "📋 连接信息:"
        echo "   🌐 主机: ${DB_HOST}"
        echo "   🔌 端口: ${DB_PORT}"
        echo "   🗄️  数据库: ${DB_NAME}"
        echo "   👤 用户: ${DB_USER}"
        
        # 显示数据库大小
        if test_connection; then
            local db_size=$(PGPASSWORD="${DB_PASSWORD}" psql -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" -tAc "SELECT pg_size_pretty(pg_database_size('${DB_NAME}'));" 2>/dev/null)
            echo "   📦 数据库大小: ${db_size}"
            
            # 显示表统计
            local table_count=$(PGPASSWORD="${DB_PASSWORD}" psql -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" -tAc "SELECT count(*) FROM information_schema.tables WHERE table_schema='public';" 2>/dev/null)
            echo "   📊 表数量: ${table_count}"
            
            # 显示连接数
            local connections=$(PGPASSWORD="${DB_PASSWORD}" psql -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" -tAc "SELECT count(*) FROM pg_stat_activity;" 2>/dev/null)
            echo "   🔗 当前连接数: ${connections}"
        fi
    else
        echo "❌ PostgreSQL服务: 未运行"
    fi
    echo
}

# 备份数据库
backup_database() {
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_file="${SCRIPT_DIR}/backups/postgres_backup_${timestamp}.sql"
    
    log_info "💾 备份PostgreSQL数据库..."
    
    # 确认操作
    echo "📋 备份信息:"
    echo "   数据库: ${DB_NAME}"
    echo "   文件: $(basename ${backup_file})"
    echo "   时间: $(date)"
    echo
    
    read -p "确认开始备份? (y/N): " confirm
    if [[ ! $confirm =~ ^[Yy]$ ]]; then
        log_info "取消备份操作"
        return 0
    fi
    
    if PGPASSWORD="${DB_PASSWORD}" pg_dump -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" --verbose --format=plain --no-owner --no-privileges > "${backup_file}" 2>/dev/null; then
        log_success "数据库备份完成: ${backup_file}"
        
        # 压缩备份文件
        if gzip "${backup_file}"; then
            log_info "📦 备份文件已压缩: ${backup_file}.gz"
            backup_file="${backup_file}.gz"
        fi
        
        # 显示备份文件大小
        local backup_size=$(du -h "${backup_file}" | cut -f1)
        log_info "📏 备份文件大小: ${backup_size}"
        
        # 验证备份完整性
        if [ -s "${backup_file}" ]; then
            log_success "✅ 备份文件完整性验证通过"
        else
            log_error "❌ 备份文件为空或损坏"
            return 1
        fi
    else
        log_error "数据库备份失败"
        return 1
    fi
}

# 恢复数据库
restore_database() {
    local backup_file=$1
    
    if [ ! -f "$backup_file" ]; then
        log_error "备份文件不存在: $backup_file"
        return 1
    fi
    
    log_info "📥 恢复PostgreSQL数据库..."
    log_warning "⚠️  此操作将覆盖现有数据，请确认！"
    
    echo "📋 恢复信息:"
    echo "   备份文件: $backup_file"
    echo "   文件大小: $(du -h "$backup_file" | cut -f1)"
    echo "   目标数据库: ${DB_NAME}"
    echo
    
    read -p "确认恢复数据库? (y/N): " confirm
    if [[ ! $confirm =~ ^[Yy]$ ]]; then
        log_info "取消恢复操作"
        return 0
    fi
    
    # 如果是压缩文件，先解压
    if [[ "$backup_file" == *.gz ]]; then
        log_info "📦 解压备份文件..."
        local temp_file="${backup_file%.gz}"
        gunzip -c "$backup_file" > "$temp_file"
        backup_file="$temp_file"
    fi
    
    if PGPASSWORD="${DB_PASSWORD}" psql -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" < "$backup_file" > /dev/null 2>&1; then
        log_success "数据库恢复完成"
        
        # 清理临时文件
        if [[ "$backup_file" == *temp* ]]; then
            rm -f "$backup_file"
        fi
    else
        log_error "数据库恢复失败"
        return 1
    fi
}

# 清理旧备份
cleanup_backups() {
    local retention_days=${1:-30}
    log_info "🧹 清理${retention_days}天前的备份文件..."
    
    if [ ! -d "${SCRIPT_DIR}/backups" ]; then
        log_warning "备份目录不存在"
        return 0
    fi
    
    local before_count=$(find "${SCRIPT_DIR}/backups" -name "postgres_backup_*.sql*" -type f | wc -l)
    local cleaned_count=$(find "${SCRIPT_DIR}/backups" -name "postgres_backup_*.sql*" -type f -mtime +$retention_days -delete -print | wc -l)
    local after_count=$(find "${SCRIPT_DIR}/backups" -name "postgres_backup_*.sql*" -type f | wc -l)
    
    log_success "清理完成："
    echo "   📊 清理前: ${before_count} 个备份文件"
    echo "   🗑️  已清理: ${cleaned_count} 个备份文件"  
    echo "   📊 清理后: ${after_count} 个备份文件"
}

# 显示数据库统计信息
show_stats() {
    log_info "📊 PostgreSQL数据库统计信息:"
    echo
    
    if ! test_connection; then
        log_error "无法连接到数据库"
        return 1
    fi
    
    # 数据库基本信息
    echo "📋 数据库概览:"
    PGPASSWORD="${DB_PASSWORD}" psql -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" -c "
    SELECT 
        '数据库大小' as 指标,
        pg_size_pretty(pg_database_size('${DB_NAME}')) as 值
    UNION ALL
    SELECT 
        '活跃连接数' as 指标,
        count(*)::text as 值
    FROM pg_stat_activity 
    WHERE state = 'active'
    UNION ALL
    SELECT 
        '总连接数' as 指标,
        count(*)::text as 值
    FROM pg_stat_activity
    UNION ALL
    SELECT 
        '缓存命中率' as 指标,
        round(sum(blks_hit)*100.0/sum(blks_hit+blks_read), 2)::text || '%' as 值
    FROM pg_stat_database
    WHERE datname = '${DB_NAME}';
    "
    
    echo
    echo "📊 表统计 (TOP 10):"
    PGPASSWORD="${DB_PASSWORD}" psql -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" -c "
    SELECT 
        schemaname as 模式,
        tablename as 表名,
        n_tup_ins as 插入数,
        n_tup_upd as 更新数,
        n_tup_del as 删除数,
        n_live_tup as 活跃行数
    FROM pg_stat_user_tables 
    ORDER BY n_tup_ins DESC
    LIMIT 10;
    "
    
    echo
    echo "💾 存储统计 (TOP 10):"
    PGPASSWORD="${DB_PASSWORD}" psql -h "${DB_HOST}" -p "${DB_PORT}" -U "${DB_USER}" -d "${DB_NAME}" -c "
    SELECT 
        schemaname as 模式,
        tablename as 表名,
        pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as 总大小,
        pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) as 表大小,
        pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename) - pg_relation_size(schemaname||'.'||tablename)) as 索引大小
    FROM pg_tables 
    WHERE schemaname = 'public'
    ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
    LIMIT 10;
    "
}

# 显示帮助
show_help() {
    echo "企业文档加密系统 - PostgreSQL数据库管理脚本"
    echo
    echo "用法: $0 [命令] [参数]"
    echo
    echo "核心命令:"
    echo "  init                          初始化数据库环境"
    echo "  start                         启动数据库服务"
    echo "  stop                          停止数据库服务"
    echo "  restart                       重启数据库服务"
    echo "  status                        查看服务状态"
    echo "  test                          测试数据库连接"
    echo
    echo "数据管理:"
    echo "  backup                        备份数据库"
    echo "  restore <file>                恢复数据库"
    echo "  cleanup [days]                清理旧备份（默认30天）"
    echo "  stats                         显示数据库统计信息"
    echo
    echo "系统命令:"
    echo "  help                          显示帮助信息"
    echo
    echo "示例:"
    echo "  $0 init                       # 初始化数据库环境"
    echo "  $0 start                      # 启动数据库服务"
    echo "  $0 backup                     # 备份数据库"
    echo "  $0 restore backup.sql.gz     # 恢复数据库"
    echo "  $0 stats                      # 显示统计信息"
    echo "  $0 cleanup 7                  # 清理7天前的备份"
    echo
    echo "🔧 专注支持: PostgreSQL 数据库系统"
}

# 初始化环境
init_environment() {
    log_info "🚀 初始化PostgreSQL数据库环境..."
    echo
    
    # 检查PostgreSQL安装
    if ! check_postgres_installed; then
        exit 1
    fi
    show_progress 10 1 6
    
    # 设置环境变量
    setup_env
    show_progress 5 2 6
    
    # 创建目录
    create_directories
    show_progress 5 3 6
    
    # 启动PostgreSQL服务
    if ! check_postgres_service; then
        start_postgres_service
    fi
    show_progress 10 4 6
    
    # 设置数据库
    setup_database
    show_progress 10 5 6
    
    # 初始化数据库结构
    init_database_schema
    show_progress 15 6 6
    
    # 测试连接
    test_connection
    
    echo
    log_success "🎉 PostgreSQL数据库环境初始化完成！"
    echo
    echo "📋 下一步操作:"
    echo "   1. 运行 $0 status 查看服务状态"
    echo "   2. 运行 $0 stats 查看数据库统计"
    echo "   3. 运行 $0 backup 创建首次备份"
    echo
}

# 主逻辑
main() {
    cd "$SCRIPT_DIR"
    
    case "${1:-help}" in
        init)
            init_environment
            ;;
        start)
            setup_env
            if ! check_postgres_service; then
                start_postgres_service
            else
                log_info "PostgreSQL服务已在运行"
            fi
            ;;
        stop)
            log_info "⏹️  停止PostgreSQL服务..."
            if systemctl stop postgresql 2>/dev/null || service postgresql stop 2>/dev/null; then
                log_success "PostgreSQL服务已停止"
            else
                log_error "无法停止PostgreSQL服务"
                exit 1
            fi
            ;;
        restart)
            setup_env
            log_info "🔄 重启PostgreSQL服务..."
            if systemctl restart postgresql 2>/dev/null || service postgresql restart 2>/dev/null; then
                log_success "PostgreSQL服务已重启"
                sleep 3
                test_connection
            else
                log_error "无法重启PostgreSQL服务"
                exit 1
            fi
            ;;
        status)
            setup_env
            status_services
            ;;
        backup)
            setup_env
            backup_database
            ;;
        restore)
            if [ -z "$2" ]; then
                log_error "请指定备份文件"
                echo "用法: $0 restore <backup_file>"
                exit 1
            fi
            setup_env
            restore_database "$2"
            ;;
        cleanup)
            cleanup_backups "$2"
            ;;
        test)
            setup_env
            test_connection
            ;;
        stats)
            setup_env
            show_stats
            ;;
        help)
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            echo
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@" 