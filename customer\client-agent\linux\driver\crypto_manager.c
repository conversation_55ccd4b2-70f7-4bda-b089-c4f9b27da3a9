/*
 * crypto_manager.c
 *
 * Cryptosystem Linux 加密模块实现
 * 采用OpenSSL实现加密功能
 */

#include "crypto_manager.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <openssl/evp.h>
#include <openssl/rand.h>
#include <openssl/err.h>
#include <openssl/aes.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <unistd.h>
#include <libsecret/secret.h>
#include <glib.h>

// 外部C API头文件
#include "../common/src/api/crypto_utils_c_api.h"

// 加密密钥存储路径（用户会话存储）
// #define KEY_STORE_PATH "/tmp/.cryptosystem_keys"

// 简单密钥存储结构
// typedef struct {
//     uint32_t algorithm;
//     uint32_t version;
//     uint32_t key_length;
//     uint8_t key_data[];
// } key_entry_t;

// 静态函数声明
// static int save_keys_to_file(void);
// static int load_keys_from_file(void);
// static key_entry_t* find_key_entry(crypto_algorithm algorithm, uint32_t key_version);

// 内部密钥存储
// static uint8_t *keys_storage = NULL;
// static size_t keys_storage_size = 0;
// static size_t keys_storage_used = 0;

// 密钥存储必要变量
static int is_initialized = 0;
static GMainLoop *loop = NULL; // 用于异步操作 (如果需要)

// libsecret Schema 定义
static const SecretSchema CRYPTOSYSTEM_KEY_SCHEMA = {
    "org.cryptosystem.Key", SECRET_SCHEMA_NONE,
    {
        { "algorithm", SECRET_SCHEMA_ATTRIBUTE_INTEGER },
        { "version", SECRET_SCHEMA_ATTRIBUTE_INTEGER },
        { "key_type", SECRET_SCHEMA_ATTRIBUTE_STRING }, // e.g., "user", "document"
        { "created_at", SECRET_SCHEMA_ATTRIBUTE_STRING },
        // 可以添加更多属性
        { NULL, 0 },
    }
};

// 初始化加密模块
int crypto_init(void) {
    if (is_initialized) {
        return CRYPTO_SUCCESS; // 已经初始化过
    }

    // 初始化OpenSSL
    OpenSSL_add_all_algorithms();
    ERR_load_crypto_strings();
    
    // // 初始分配密钥存储空间 - 不再需要
    // keys_storage_size = 4096; // 初始4KB
    // keys_storage = (uint8_t *)malloc(keys_storage_size);
    
    // if (!keys_storage) {
    //     return CRYPTO_ERROR_MEMORY;
    // }
    
    // keys_storage_used = 0;
    
    // // 尝试加载已有密钥 - 不再需要
    // load_keys_from_file();
    
    // 初始化 GLib (如果尚未初始化)
    // 注意：如果应用是 GTK 应用，通常已经初始化
    // if (!g_thread_get_initialized()) {
    //     g_thread_init(NULL);
    // }
    // loop = g_main_loop_new (NULL, FALSE);
    
    is_initialized = 1;
    printf("[CryptoManager] Initialized.\n"); // 替换为日志
    return CRYPTO_SUCCESS;
}

// 清理加密模块
void crypto_cleanup(void) {
    if (!is_initialized) {
        return;
    }

    // // 保存密钥到文件 - 不再需要
    // save_keys_to_file();
    
    // // 释放密钥存储 - 不再需要
    // if (keys_storage) {
    //     memset(keys_storage, 0, keys_storage_size); // 安全擦除
    //     free(keys_storage);
    //     keys_storage = NULL;
    // }
    
    // keys_storage_size = 0;
    // keys_storage_used = 0;
    
    // 清理OpenSSL
    EVP_cleanup();
    ERR_free_strings();
    
    // if (loop) {
    //     g_main_loop_unref(loop);
    //     loop = NULL;
    // }
    
    is_initialized = 0;
    printf("[CryptoManager] Cleaned up.\n"); // 替换为日志
}

// 加密数据
int crypto_encrypt(
    crypto_algorithm algorithm,
    crypto_mode mode,
    uint32_t key_version,
    const uint8_t *iv,
    const uint8_t *plaintext,
    size_t plaintext_len,
    uint8_t *ciphertext,
    size_t *ciphertext_len
) {
    if (!is_initialized) {
        return CRYPTO_ERROR_INVALID;
    }
    
    if (!iv || !plaintext || !ciphertext || !ciphertext_len || plaintext_len == 0) {
        return CRYPTO_ERROR_INVALID;
    }
    
    // 获取密钥
    uint8_t key[CRYPTO_KEY_SIZE_AES_256];
    size_t key_length = sizeof(key);
    
    int ret = crypto_get_key(algorithm, key_version, key, &key_length);
    if (ret != CRYPTO_SUCCESS) {
        return ret;
    }
    
    // 选择适当的加密算法与模式
    const EVP_CIPHER *cipher = NULL;
    if (algorithm == ALGORITHM_AES) {
        if (key_length == CRYPTO_KEY_SIZE_AES_128) {
            if (mode == MODE_CBC) {
                cipher = EVP_aes_128_cbc();
            } else if (mode == MODE_GCM) {
                cipher = EVP_aes_128_gcm();
            } else if (mode == MODE_CTR) {
                cipher = EVP_aes_128_ctr();
            }
        } else if (key_length == CRYPTO_KEY_SIZE_AES_256) {
            if (mode == MODE_CBC) {
                cipher = EVP_aes_256_cbc();
            } else if (mode == MODE_GCM) {
                cipher = EVP_aes_256_gcm();
            } else if (mode == MODE_CTR) {
                cipher = EVP_aes_256_ctr();
            }
        }
    } else if (algorithm == ALGORITHM_SM4 && key_length == CRYPTO_KEY_SIZE_SM4) {
        // SM4目前只支持CBC模式 (原有注释)
        if (mode == MODE_CBC) {
            cipher = EVP_sm4_cbc();
        } else if (mode == MODE_CTR) {
            cipher = EVP_sm4_ctr();
        } else if (mode == MODE_GCM) {
            // 使用外部 C API 进行 SM4-GCM 加密
            // 在此模式下，内部处理IV，忽略传入的IV参数
            uint8_t sm4_iv[12]; // GCM 推荐 12 字节 IV
            if (crypto_generate_random(sm4_iv, sizeof(sm4_iv)) != CRYPTO_SUCCESS) {
                 OPENSSL_cleanse(key, key_length);
                 return CRYPTO_ERROR_CRYPTO; // Failed to generate IV
            }
            
            // 准备输出缓冲区大小检查
            size_t required_output_size = sizeof(sm4_iv) + plaintext_len + CRYPTO_TAG_SIZE; // IV + Plaintext + Tag
            if (*ciphertext_len < required_output_size) {
                *ciphertext_len = required_output_size; // 返回所需大小
                OPENSSL_cleanse(key, key_length);
                return CRYPTO_ERROR_INVALID; // Buffer too small
            }
            
            size_t actual_output_len = *ciphertext_len; // 将容量传递给C API
            int c_api_ret = sm4_gcm_encrypt_c_api(
                plaintext,
                plaintext_len,
                key,            // 密钥
                key_length,     // 必须是16
                sm4_iv,         // 使用新生成的12字节IV
                sizeof(sm4_iv),
                ciphertext,     // 输出缓冲区
                &actual_output_len // 输入容量，输出实际长度
            );
            
            OPENSSL_cleanse(key, key_length); // 清理密钥
            
            if (c_api_ret == C_API_SUCCESS) {
                *ciphertext_len = actual_output_len; // 更新实际输出长度
                return CRYPTO_SUCCESS;
            } else if (c_api_ret == C_API_ERROR_BUFFER_TOO_SMALL) {
                 *ciphertext_len = actual_output_len; // C API返回了所需的实际大小
                 return CRYPTO_ERROR_INVALID; // Buffer too small
            } else {
                // 其他 C API 错误映射到 CRYPTO_ERROR_CRYPTO
                return CRYPTO_ERROR_CRYPTO; 
            }
            // 注意：SM4-GCM分支直接返回，不执行下面的EVP流程
        }
    }
    
    if (!cipher) {
        // 清理密钥内存 (即使 cipher 为 NULL)
        OPENSSL_cleanse(key, key_length);
        return CRYPTO_ERROR_ALGORITHM;
    }
    
    // 加密
    EVP_CIPHER_CTX *ctx = EVP_CIPHER_CTX_new();
    if (!ctx) {
        // 清理密钥内存
        OPENSSL_cleanse(key, key_length);
        return CRYPTO_ERROR_MEMORY;
    }
    
    int success = 1;
    int len = 0;
    int ciphertext_total_len = 0;
    size_t required_size = plaintext_len + EVP_CIPHER_block_size(cipher);
    
    // 检查缓冲区大小
    if (*ciphertext_len < required_size) {
        *ciphertext_len = required_size;
        EVP_CIPHER_CTX_free(ctx);
        return CRYPTO_ERROR_INVALID;
    }
    
    // 初始化加密上下文
    success &= EVP_EncryptInit_ex(ctx, cipher, NULL, key, iv);
    
    // 如果是GCM模式，需要设置IV长度
    if (mode == MODE_GCM) {
        success &= EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_GCM_SET_IVLEN, CRYPTO_IV_SIZE, NULL);
    }
    
    if (!success) {
        EVP_CIPHER_CTX_free(ctx);
        return CRYPTO_ERROR_CRYPTO;
    }
    
    // 添加数据校验值(只对AES-GCM使用)
    if (mode == MODE_GCM) {
        uint32_t checksum = crypto_compute_checksum(plaintext, plaintext_len);
        uint8_t checksum_bytes[CRYPTO_CHECKSUM_SIZE];
        
        // 转换为网络字节序
        checksum_bytes[0] = (checksum >> 24) & 0xFF;
        checksum_bytes[1] = (checksum >> 16) & 0xFF;
        checksum_bytes[2] = (checksum >> 8) & 0xFF;
        checksum_bytes[3] = checksum & 0xFF;
        
        success &= EVP_EncryptUpdate(ctx, NULL, &len, checksum_bytes, CRYPTO_CHECKSUM_SIZE);
    }
    
    // 加密数据
    success &= EVP_EncryptUpdate(ctx, ciphertext, &len, plaintext, plaintext_len);
    ciphertext_total_len += len;
    
    // 处理最后的数据块
    success &= EVP_EncryptFinal_ex(ctx, ciphertext + ciphertext_total_len, &len);
    ciphertext_total_len += len;
    
    // 如果是GCM模式，获取认证标签
    if (mode == MODE_GCM) {
        success &= EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_GCM_GET_TAG, CRYPTO_TAG_SIZE, 
                                      ciphertext + ciphertext_total_len);
        ciphertext_total_len += CRYPTO_TAG_SIZE;
    }
    
    EVP_CIPHER_CTX_free(ctx);
    
    // 清理密钥内存
    OPENSSL_cleanse(key, key_length);
    
    if (!success) {
        return CRYPTO_ERROR_CRYPTO;
    }
    
    *ciphertext_len = ciphertext_total_len;
    return CRYPTO_SUCCESS;
}

// 解密数据
int crypto_decrypt(
    crypto_algorithm algorithm,
    crypto_mode mode,
    uint32_t key_version,
    const uint8_t *iv, // 注意：对于SM4-GCM模式，此参数将被忽略
    const uint8_t *ciphertext,
    size_t ciphertext_len,
    uint8_t *plaintext,
    size_t *plaintext_len
) {
    if (!is_initialized) {
        return CRYPTO_ERROR_INVALID;
    }
    
    // 对于GCM模式，输入的ciphertext应包含 IV + Ciphertext + Tag
    // 对于其他模式，输入的ciphertext是纯密文，IV是单独传入的
    if (!ciphertext || !plaintext || !plaintext_len) {
        return CRYPTO_ERROR_INVALID;
    }
    if (mode != MODE_GCM && !iv) { // 非GCM模式必须提供IV
         return CRYPTO_ERROR_INVALID;
    }
    
    // 获取密钥
    uint8_t key[CRYPTO_KEY_SIZE_AES_256]; // 假设最大密钥长度
    size_t key_length = sizeof(key);
    int ret = crypto_get_key(algorithm, key_version, key, &key_length);
    if (ret != CRYPTO_SUCCESS) {
        return ret;
    }
    
    // 检查密钥长度是否与算法匹配
    size_t expected_key_len = 0;
    if (algorithm == ALGORITHM_AES) expected_key_len = CRYPTO_KEY_SIZE_AES_256; // 假设优先使用256
    else if (algorithm == ALGORITHM_SM4) expected_key_len = CRYPTO_KEY_SIZE_SM4;
    
    // 实际获取到的key_length可能小于缓冲区大小，但需要与算法要求匹配
    // crypto_get_key 应该返回实际长度在 key_length 中
    if (expected_key_len == 0 || key_length != expected_key_len) { 
        OPENSSL_cleanse(key, key_length); 
        return CRYPTO_ERROR_KEYVER; // Key size mismatch for algorithm
    }

    // --- SM4-GCM 特殊处理 (使用C API) ---
    if (algorithm == ALGORITHM_SM4 && mode == MODE_GCM) {
        // 检查输入数据长度 (至少 IV(12) + Tag(16))
        size_t min_input_len = 12 + CRYPTO_TAG_SIZE;
        if (ciphertext_len < min_input_len) {
            OPENSSL_cleanse(key, key_length);
            return CRYPTO_ERROR_INVALID; // Input too short
        }
        
        // 准备输出缓冲区大小检查
        size_t max_plaintext_len = ciphertext_len - 12 - CRYPTO_TAG_SIZE;
        if (*plaintext_len < max_plaintext_len) {
             *plaintext_len = max_plaintext_len; // 返回可能需要的最大大小
             OPENSSL_cleanse(key, key_length);
             return CRYPTO_ERROR_INVALID; // Buffer too small
        }
        
        size_t actual_plaintext_len = *plaintext_len; // 将容量传递给 C API
        int c_api_ret = sm4_gcm_decrypt_c_api(
            ciphertext,         // 输入: IV + Ciphertext + Tag
            ciphertext_len,
            key,
            key_length,
            plaintext,          // 输出缓冲区
            &actual_plaintext_len // 输入容量，输出实际长度
        );
        
        OPENSSL_cleanse(key, key_length); // 清理密钥
        
        if (c_api_ret == C_API_SUCCESS) {
            *plaintext_len = actual_plaintext_len; // 更新实际输出长度
            return CRYPTO_SUCCESS;
        } else if (c_api_ret == C_API_ERROR_BUFFER_TOO_SMALL) {
            *plaintext_len = actual_plaintext_len; // C API 返回了所需的实际大小
            return CRYPTO_ERROR_INVALID; // Buffer too small
        } else if (c_api_ret == C_API_ERROR_AUTH_FAILED) {
            return CRYPTO_ERROR_AUTH; // 映射到认证失败错误码
        } else {
            // 其他 C API 错误映射到 CRYPTO_ERROR_CRYPTO
            return CRYPTO_ERROR_CRYPTO;
        }
        // SM4-GCM分支直接返回，不执行下面的EVP流程
    }

    // --- 原有的 EVP 解密流程 (适用于 CBC, CTR, AES-GCM) ---
    const EVP_CIPHER *cipher = NULL;
    if (algorithm == ALGORITHM_AES) {
        if (key_length == CRYPTO_KEY_SIZE_AES_128) {
            if (mode == MODE_CBC) cipher = EVP_aes_128_cbc();
            else if (mode == MODE_GCM) cipher = EVP_aes_128_gcm(); 
            else if (mode == MODE_CTR) cipher = EVP_aes_128_ctr();
        } else if (key_length == CRYPTO_KEY_SIZE_AES_256) {
            if (mode == MODE_CBC) cipher = EVP_aes_256_cbc();
            else if (mode == MODE_GCM) cipher = EVP_aes_256_gcm();
            else if (mode == MODE_CTR) cipher = EVP_aes_256_ctr();
        } 
    } else if (algorithm == ALGORITHM_SM4) { // 只剩 CBC / CTR
        if (mode == MODE_CBC) cipher = EVP_sm4_cbc();
        else if (mode == MODE_CTR) cipher = EVP_sm4_ctr();
    }
    
    if (!cipher) {
        OPENSSL_cleanse(key, key_length);
        return CRYPTO_ERROR_ALGORITHM;
    }
    
    EVP_CIPHER_CTX *ctx = EVP_CIPHER_CTX_new();
    if (!ctx) {
        OPENSSL_cleanse(key, key_length);
        return CRYPTO_ERROR_MEMORY;
    }
    
    int success = 1;
    int len = 0;
    int plaintext_total_len = 0;
    size_t required_size = ciphertext_len; // 解密时明文通常 <= 密文
    
    // 检查缓冲区大小 (对于非GCM，明文长度<=密文长度)
    if (*plaintext_len < required_size && mode != MODE_GCM) { 
         // GCM模式的ciphertext包含IV和Tag，不能直接比较
         // GCM模式已在前面处理
        *plaintext_len = required_size;
        EVP_CIPHER_CTX_free(ctx);
        OPENSSL_cleanse(key, key_length);
        return CRYPTO_ERROR_INVALID;
    }
    
    // 初始化解密上下文
    success &= EVP_DecryptInit_ex(ctx, cipher, NULL, key, iv); // 使用传入的IV (仅非GCM)
    
    // 如果是 AES-GCM 模式，设置 IV 长度
    if (algorithm == ALGORITHM_AES && mode == MODE_GCM) {
        success &= EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_GCM_SET_IVLEN, CRYPTO_IV_SIZE, NULL);
    }
    
    if (!success) {
        EVP_CIPHER_CTX_free(ctx);
        OPENSSL_cleanse(key, key_length);
        return CRYPTO_ERROR_CRYPTO;
    }
    
    uint8_t checksum_bytes[CRYPTO_CHECKSUM_SIZE];
    int aad_len = 0; // 存储AAD长度，以便后续计算密文主体长度
    
    // 如果是 AES-GCM 模式，处理 AAD (校验和)
    if (algorithm == ALGORITHM_AES && mode == MODE_GCM) {
        // 这里假设校验和在外部处理或此时不需要验证AAD? 
        // 原有加密逻辑是添加了checksum作为AAD，解密时理论上也需要提供。
        // 如果解密时不提供AAD，则认证会失败。
        // 暂时假设解密时不处理AAD，依赖Tag验证。
        // 或者，如果校验和存在于密文前部，需要先提取？
        // *** 需要澄清解密时AAD的处理方式 ***
        // 假设目前不处理AAD，仅依赖TAG
    }
    
    // 计算实际需要解密的密文主体长度 (总长度 - Tag长度 - AAD长度)
    size_t actual_ciphertext_len = ciphertext_len;
    uint8_t tag[CRYPTO_TAG_SIZE];
    
    if (algorithm == ALGORITHM_AES && mode == MODE_GCM) {
        if (actual_ciphertext_len < CRYPTO_TAG_SIZE + aad_len) { 
            EVP_CIPHER_CTX_free(ctx); OPENSSL_cleanse(key, key_length); return CRYPTO_ERROR_INVALID; 
        }
        actual_ciphertext_len -= CRYPTO_TAG_SIZE; // 减去Tag长度
        // 提取Tag
        memcpy(tag, ciphertext + aad_len + actual_ciphertext_len, CRYPTO_TAG_SIZE); 
    }
    
    // 解密数据 (仅解密密文主体部分)
    success &= EVP_DecryptUpdate(ctx, plaintext, &len, ciphertext + aad_len, actual_ciphertext_len);
    plaintext_total_len += len;
    
    // 如果是 AES-GCM 模式，设置期望的认证标签
    if (algorithm == ALGORITHM_AES && mode == MODE_GCM) {
        success &= EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_GCM_SET_TAG, CRYPTO_TAG_SIZE, tag);
    }
    
    // 处理最后的数据块 (对于GCM，会验证Tag)
    success &= EVP_DecryptFinal_ex(ctx, plaintext + plaintext_total_len, &len);
    
    if (!success) { // 如果这里失败，对于GCM通常意味着认证失败
        EVP_CIPHER_CTX_free(ctx);
        OPENSSL_cleanse(key, key_length);
        if (algorithm == ALGORITHM_AES && mode == MODE_GCM) {
             return CRYPTO_ERROR_AUTH; // 认证失败
        }
        return CRYPTO_ERROR_CRYPTO; // 其他解密错误
    }
    plaintext_total_len += len;
    
    // 如果是AES-GCM, 还需要比较之前提取/计算的checksum？ (如果需要)
    // uint32_t computed_checksum = crypto_compute_checksum(plaintext, plaintext_total_len);
    // if (computed_checksum != original_checksum) { ... } 
    
    EVP_CIPHER_CTX_free(ctx);
    OPENSSL_cleanse(key, key_length);
    
    *plaintext_len = plaintext_total_len;
    return CRYPTO_SUCCESS;
}

// 计算数据校验和
uint32_t crypto_compute_checksum(const uint8_t *data, size_t data_len) {
    if (!data || data_len == 0) {
        return 0;
    }
    
    // 使用CRC32算法计算校验和
    // 这里使用简化的实现，实际应用中可以使用更优化的CRC32实现
    uint32_t crc = 0xFFFFFFFF;
    
    for (size_t i = 0; i < data_len; i++) {
        uint8_t byte = data[i];
        crc ^= byte;
        
        for (int j = 0; j < 8; j++) {
            if (crc & 1) {
                crc = (crc >> 1) ^ 0xEDB88320; // CRC32多项式
            } else {
                crc >>= 1;
            }
        }
    }
    
    return ~crc; // 最终取反
}

// 生成随机数据
int crypto_generate_random(uint8_t *buffer, size_t length) {
    if (!is_initialized) {
        return CRYPTO_ERROR_INVALID;
    }
    
    if (!buffer || length == 0) {
        return CRYPTO_ERROR_INVALID;
    }
    
    // 使用OpenSSL的随机数生成器
    if (RAND_bytes(buffer, length) != 1) {
        // RAND_bytes失败，尝试使用系统随机设备作为备选
        int fd = open("/dev/urandom", O_RDONLY);
        if (fd < 0) {
            return CRYPTO_ERROR_CRYPTO;
        }
        
        size_t bytes_read = 0;
        while (bytes_read < length) {
            ssize_t result = read(fd, buffer + bytes_read, length - bytes_read);
            if (result <= 0) {
                close(fd);
                return CRYPTO_ERROR_CRYPTO;
            }
            bytes_read += result;
        }
        
        close(fd);
    }
    
    return CRYPTO_SUCCESS;
}

// 获取密钥 (从 libsecret 获取)
int crypto_get_key(
    crypto_algorithm algorithm,
    uint32_t key_version,
    uint8_t *key_buffer,
    size_t *key_length
) {
    if (!is_initialized) {
        return CRYPTO_ERROR_INVALID;
    }
    if (!key_buffer || !key_length || *key_length == 0) {
        return CRYPTO_ERROR_INVALID;
    }

    GError *error = NULL;
    gchar *password = NULL;
    char version_str[16];
    char algorithm_str[16];

    snprintf(version_str, sizeof(version_str), "%u", key_version);
    snprintf(algorithm_str, sizeof(algorithm_str), "%u", algorithm);

    // 同步查找密码
    password = secret_password_lookup_sync(
        &CRYPTOSYSTEM_KEY_SCHEMA, // 使用我们定义的 Schema
        NULL, // 默认 keyring
        &error,
        "algorithm", algorithm_str,
        "version", version_str,
        NULL // 结束属性列表
    );

    if (error) {
        fprintf(stderr, "[CryptoManager] Failed to lookup key: %s\n", error->message);
        g_error_free(error);
        return CRYPTO_ERROR_KEYVER; // 或其他错误码
    }

    if (!password) {
        // 密钥未找到
        return CRYPTO_ERROR_KEYVER;
    }

    // 成功找到密码 (密钥)
    size_t secret_len = strlen(password); // 注意：libsecret 返回的是 C 字符串
                                          // 如果存储的是二进制，需要用其他方式获取长度
                                          // 假设我们存储的是 hex 编码或 base64?
                                          // 或者 store/lookup 时使用 blob 类型?
                                          // --- 假设存储的是原始二进制 --- 
                                          // 需要使用 secret_item_get_secret() 获取 SecretValue
                                          // 然后 secret_value_get() 获取 GBytes
                                          // g_bytes_get_data() 和 g_bytes_get_size()
    
    // ---- 临时简化：假设密码是密钥本身，且长度已知或可通过 strlen 获取 ----
    // ---- 实际应用中需要处理二进制存储和长度问题！ ----
    if (*key_length < secret_len) {
        *key_length = secret_len; // 报告需要的长度
        secret_password_free(password); // 清理密码内存
        return CRYPTO_ERROR_INVALID; // 缓冲区太小
    }
    
    // 复制密钥到缓冲区
    memcpy(key_buffer, password, secret_len);
    *key_length = secret_len;
    
    // 安全擦除从 libsecret 获取的密码内存
    // 注意：secret_password_free 会调用 g_free，但可能不会安全擦除
    // 需要在 free 之前擦除，但这不标准。最好的方式是复制后立即擦除 password 指向的内容
    // 或者使用 OPENSSL_cleanse
    OPENSSL_cleanse(password, secret_len);
    secret_password_free(password);

    printf("[CryptoManager] Key fetched (alg=%u, ver=%u).\n", algorithm, key_version);
    return CRYPTO_SUCCESS;
}

// 设置/存储密钥 (到 libsecret)
int crypto_set_key(
    crypto_algorithm algorithm,
    uint32_t key_version,
    const uint8_t *key_buffer,
    size_t key_length
) {
    if (!is_initialized) {
        return CRYPTO_ERROR_INVALID;
    }
    if (!key_buffer || key_length == 0) {
        return CRYPTO_ERROR_INVALID;
    }

    GError *error = NULL;
    char version_str[16];
    char algorithm_str[16];
    // 获取当前时间字符串 (可选)
    // time_t now = time(NULL);
    // struct tm *tstruct = localtime(&now);
    // char time_str[80];
    // strftime(time_str, sizeof(time_str), "%Y-%m-%dT%H:%M:%SZ", tstruct);

    snprintf(version_str, sizeof(version_str), "%u", key_version);
    snprintf(algorithm_str, sizeof(algorithm_str), "%u", algorithm);

    // 创建标签和描述
    char label[100];
    snprintf(label, sizeof(label), "Cryptosystem Key (Alg: %u, Ver: %u)", algorithm, key_version);

    // 存储密码 (密钥)
    // 注意：这里将二进制密钥直接作为 C 字符串存储，可能会因 NULL 字符中断
    // 正确的方式是使用 secret_password_store_sync 存储 GBytes 或 Base64/Hex 编码的字符串
    // ---- 临时简化：假设密钥数据不含 NULL 字符 ----
    // ---- 实际应用中必须处理二进制存储！ ----
    gboolean stored = secret_password_store_sync(
        &CRYPTOSYSTEM_KEY_SCHEMA, // Schema
        SECRET_COLLECTION_DEFAULT, // 存储到默认 keyring
        label, // 标签
        (const gchar*)key_buffer, // 密码 (密钥数据)
        NULL, // 取消对象
        &error,
        // 属性列表
        "algorithm", algorithm_str,
        "version", version_str,
        "key_type", "generic", // 可以根据上下文设置 "user" 或 "document"
        // "created_at", time_str,
        NULL // 结束
    );

    if (error) {
        fprintf(stderr, "[CryptoManager] Failed to store key: %s\n", error->message);
        g_error_free(error);
        return CRYPTO_ERROR_CRYPTO;
    }

    if (!stored) {
        fprintf(stderr, "[CryptoManager] Failed to store key (returned FALSE).\n");
        return CRYPTO_ERROR_CRYPTO;
    }

    printf("[CryptoManager] Key stored (alg=%u, ver=%u).\n", algorithm, key_version);
    return CRYPTO_SUCCESS;
}

// 移除 find_key_entry
// static key_entry_t* find_key_entry(...) { ... }

// 移除 save_keys_to_file 和 load_keys_from_file
// static int save_keys_to_file(void) { ... }
// static int load_keys_from_file(void) { ... } 