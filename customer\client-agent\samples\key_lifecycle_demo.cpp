#include <iostream>
#include <chrono>
#include <thread>
#include <iomanip>
#include "../common/include/api/api_client.h"

using namespace crypto;
using namespace crypto::api;

/**
 * 格式化时间为可读字符串
 */
std::string FormatTime(const std::chrono::system_clock::time_point& time) {
    auto timeT = std::chrono::system_clock::to_time_t(time);
    std::tm tm = *std::localtime(&timeT);
    std::stringstream ss;
    ss << std::put_time(&tm, "%Y-%m-%d %H:%M:%S");
    return ss.str();
}

/**
 * 演示密钥生命周期管理功能
 */
int main(int argc, char* argv[]) {
    try {
        std::cout << "密钥生命周期管理演示程序" << std::endl;
        std::cout << "-----------------------------------------" << std::endl;
        
        // 配置API客户端
        ApiClientConfig config;
        config.serviceUrl = "http://***********:8081"; // 根据实际情况修改
        config.apiKey = "demo-api-key"; // 根据实际情况修改
        config.timeoutMs = 10000;
        config.useTls = false; // 本地测试时可能为false
        
        // 创建API客户端
        ApiClient apiClient(config);
        
        // 获取密钥服务和生命周期管理客户端
        auto& keyService = apiClient.GetKeyServiceClient();
        auto& lifecycleClient = apiClient.GetKeyLifecycleClient();
        
        // 1. 创建一个文件密钥用于演示
        std::cout << "创建测试文件密钥..." << std::endl;
        auto fileKey = keyService.CreateFileKey(
            "user-device-key-id", // 根据实际情况修改
            "demo-file-id",
            "/path/to/demo/file.txt",
            "AES-256-GCM"
        );
        std::cout << "文件密钥创建成功，ID: " << fileKey->GetKeyId() << std::endl;
        
        // 2. 设置密钥过期时间（设为30天后过期）
        auto expiryTime = std::chrono::system_clock::now() + std::chrono::hours(24 * 30);
        std::cout << "设置密钥过期时间: " << FormatTime(expiryTime) << std::endl;
        
        bool success = lifecycleClient.SetKeyExpiryDate(fileKey->GetKeyId(), expiryTime);
        if (success) {
            std::cout << "密钥过期时间设置成功" << std::endl;
        } else {
            std::cout << "密钥过期时间设置失败" << std::endl;
        }
        
        // 3. 轮换密钥
        std::cout << "\n演示密钥轮换..." << std::endl;
        
        KeyRotationRequest rotateRequest;
        rotateRequest.keyId = fileKey->GetKeyId();
        rotateRequest.reEncryptChildren = true;
        rotateRequest.description = "演示密钥轮换";
        
        KeyRotationResult rotateResult = lifecycleClient.RotateKey(rotateRequest);
        
        if (rotateResult.completed) {
            std::cout << "密钥轮换成功!" << std::endl;
            std::cout << "旧密钥ID: " << rotateResult.oldKeyId << std::endl;
            std::cout << "新密钥ID: " << rotateResult.newKeyId << std::endl;
            std::cout << "密钥类型: " << rotateResult.keyType << std::endl;
            std::cout << "重新加密的子密钥: " << rotateResult.childKeysReEncrypted << std::endl;
            
            // 4. 获取密钥轮换历史
            std::cout << "\n获取密钥轮换历史..." << std::endl;
            std::vector<key::Key> history = lifecycleClient.GetKeyRotationHistory(rotateResult.oldKeyId);
            
            std::cout << "找到 " << history.size() << " 个相关密钥:" << std::endl;
            for (const auto& key : history) {
                std::cout << "  密钥ID: " << key.keyId_ << std::endl;
                std::cout << "  类型: " << key.keyType_ << std::endl;
                std::cout << "  状态: " << static_cast<int>(key.status_) << std::endl;
                std::cout << "  创建时间: " << key.creationTimestamp_ << std::endl;
                std::cout << "  -----------------" << std::endl;
            }
        } else {
            std::cout << "密钥轮换失败" << std::endl;
        }
        
        // 5. 手动触发过期密钥处理
        std::cout << "\n手动触发过期密钥处理..." << std::endl;
        int expiredCount = lifecycleClient.ProcessExpiredKeys();
        std::cout << "处理了 " << expiredCount << " 个过期密钥" << std::endl;
        
        std::cout << "\n演示完成!" << std::endl;
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "错误: " << e.what() << std::endl;
        return 1;
    }
} 