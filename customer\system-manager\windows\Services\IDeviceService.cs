using CryptoSystem.SystemManager.Models;

namespace CryptoSystem.SystemManager.Services
{
    public interface IDeviceService
    {
        Task<IEnumerable<Device>> GetAllDevicesAsync(bool includeOffline = true);
        Task<Device?> GetDeviceByIdAsync(string deviceId);
        Task<(IEnumerable<Device> Devices, int TotalCount)> GetDevicesPagedAsync(
            int pageIndex, int pageSize, string? searchKeyword = null,
            DeviceType? deviceType = null, DeviceStatus? status = null);
            
        Task<(bool Success, string Message)> RegisterDeviceAsync(Device device, string registeredBy);
        Task<(bool Success, string Message)> UpdateDeviceAsync(Device device, string modifiedBy);
        Task<(bool Success, string Message)> DeleteDeviceAsync(string deviceId, string deletedBy);
        Task<(bool Success, string Message)> SetDeviceStatusAsync(string deviceId, DeviceStatus status, string operatedBy);
        
        Task<DeviceStatistics> GetDeviceStatisticsAsync();
        Task<IEnumerable<Device>> GetOnlineDevicesAsync();
        Task<IEnumerable<Device>> GetOfflineDevicesAsync(int hoursThreshold = 24);
        
        Task<bool> SendCommandToDeviceAsync(string deviceId, string command, object? parameters = null);
        Task<(bool Success, string Message)> LockDeviceAsync(string deviceId, string reason, string operatedBy);
        Task<(bool Success, string Message)> UnlockDeviceAsync(string deviceId, string operatedBy);
    }
} 