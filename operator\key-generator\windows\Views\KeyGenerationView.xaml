<UserControl x:Class="KeyGenerator.Views.KeyGenerationView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" mc:Ignorable="d" d:DesignHeight="500" d:DesignWidth="800">

    <UserControl.Resources>
        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
            <Setter Property="Margin" Value="0,20,0,15"/>
        </Style>

        <Style x:Key="FormLabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="{StaticResource SecondaryTextBrush}"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
        </Style>

        <Style x:Key="FormControlStyle" TargetType="Control">
            <Setter Property="Margin" Value="0,5,0,15"/>
            <Setter Property="Height" Value="35"/>
            <Setter Property="FontSize" Value="14"/>
        </Style>

        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="25,10"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="MinWidth" Value="120"/>
            <Setter Property="Height" Value="40"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" CornerRadius="5" Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="{StaticResource PrimaryHoverBrush}"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="{StaticResource PrimaryPressedBrush}"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Background" Value="{StaticResource ButtonDisabledBrush}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SecondaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
            <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="Padding" Value="25,10"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="MinWidth" Value="120"/>
            <Setter Property="Height" Value="40"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="5" Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="{StaticResource PrimaryLightBrush}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid Margin="40">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 页面标题 -->
        <TextBlock Grid.Row="0" Text="生成密钥" FontSize="28" FontWeight="Bold" Foreground="{StaticResource HeaderTextBrush}" Margin="0,0,0,30"/>

        <!-- 表单内容 -->
        <StackPanel Grid.Row="1" MaxWidth="500" HorizontalAlignment="Center">

            <!-- 客户信息 -->
            <TextBlock Text="客户信息" Style="{StaticResource SectionHeaderStyle}"/>

            <TextBlock Text="客户名称 *" Style="{StaticResource FormLabelStyle}"/>
            <TextBox x:Name="ClientNameTextBox" Style="{StaticResource FormControlStyle}" Text="{Binding ClientName, UpdateSourceTrigger=PropertyChanged}" ToolTip="输入客户公司或组织的完整名称"/>

            <TextBlock Text="客户代码" Style="{StaticResource FormLabelStyle}"/>
            <TextBox x:Name="ClientCodeTextBox" Style="{StaticResource FormControlStyle}" Text="{Binding ClientCode, UpdateSourceTrigger=PropertyChanged}" ToolTip="可选：输入客户的唯一标识代码"/>

            <!-- 密钥配置 -->
            <TextBlock Text="密钥配置" Style="{StaticResource SectionHeaderStyle}"/>

            <TextBlock Text="密钥名称 *" Style="{StaticResource FormLabelStyle}"/>
            <TextBox x:Name="KeyNameTextBox" Style="{StaticResource FormControlStyle}" Text="{Binding KeyName, UpdateSourceTrigger=PropertyChanged}" ToolTip="为密钥指定一个便于识别的名称"/>

            <TextBlock Text="有效期 *" Style="{StaticResource FormLabelStyle}"/>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="20"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <DatePicker Grid.Column="0" x:Name="EffectiveDatePicker" Style="{StaticResource FormControlStyle}" SelectedDate="{Binding EffectiveDate}" ToolTip="密钥生效日期"/>

                <DatePicker Grid.Column="2" x:Name="ExpirationDatePicker" Style="{StaticResource FormControlStyle}" SelectedDate="{Binding ExpirationDate}" ToolTip="密钥过期日期"/>
            </Grid>

            <TextBlock Text="备注" Style="{StaticResource FormLabelStyle}"/>
            <TextBox x:Name="RemarksTextBox" Height="80" TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto" Text="{Binding Remarks, UpdateSourceTrigger=PropertyChanged}" ToolTip="可选：添加备注信息"/>

        </StackPanel>

        <!-- 操作按钮 -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,30,0,0">

            <Button Content="重置" Style="{StaticResource SecondaryButtonStyle}" Command="{Binding ResetFormCommand}" ToolTip="清空表单内容" Margin="0,0,20,0"/>

            <Button Content="生成密钥" Style="{StaticResource PrimaryButtonStyle}" Command="{Binding GenerateKeyCommand}" IsEnabled="{Binding CanGenerateKey}" ToolTip="生成新的密钥"/>
        </StackPanel>

        <!-- 生成状态指示器 -->
        <StackPanel Grid.Row="1" VerticalAlignment="Bottom" HorizontalAlignment="Center" Margin="0,20,0,0" Visibility="{Binding IsGenerating, Converter={StaticResource BooleanToVisibilityConverter}}">
            <ProgressBar Width="300" Height="8" IsIndeterminate="True" Margin="0,0,0,10"/>
            <TextBlock Text="{Binding GenerationStatusText}" FontSize="14" Foreground="{StaticResource SecondaryTextBrush}" HorizontalAlignment="Center"/>
        </StackPanel>
    </Grid>
</UserControl> 