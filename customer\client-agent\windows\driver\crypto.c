/*
 * crypto.c
 * 
 * 加密模块实现文件
 * 实现文件加密解密的核心功能
 */

#include <fltKernel.h>
#include <ntstrsafe.h>
#include "crypto.h"
#include "utils.h"
#include <bcrypt.h>
#include "../../../common/src/api/crypto_utils_c_api.h"

// 全局加密上下文存储
typedef struct _ENC_KEY_ENTRY {
    ULONG KeyVersion;
    UCHAR Key[64];
    ULONG KeyLength;
    LIST_ENTRY ListEntry;
} ENC_KEY_ENTRY, *PENC_KEY_ENTRY;

// 全局数据
static BOOLEAN g_CryptoInitialized = FALSE;
static LIST_ENTRY g_KeyListHead;
static ERESOURCE g_KeyResource;
static ULONG g_KeyCount = 0;

// 签名常量
static const UCHAR ENC_SIGNATURE[4] = {'E', 'N', 'C', 'F'};

// 前向声明
static NTSTATUS CryptoSM4Encrypt(
    _In_ PENC_CRYPTO_CONTEXT Context,
    _In_ PVOID PlainText,
    _In_ ULONG PlainTextLength,
    _Out_ PVOID CipherText,
    _Inout_ PULONG CipherTextLength
);

static NTSTATUS CryptoSM4Decrypt(
    _In_ PENC_CRYPTO_CONTEXT Context,
    _In_ PVOID CipherText,
    _In_ ULONG CipherTextLength,
    _Out_ PVOID PlainText,
    _Inout_ PULONG PlainTextLength
);

static NTSTATUS CryptoAESEncrypt(
    _In_ PENC_CRYPTO_CONTEXT Context,
    _In_ PVOID PlainText,
    _In_ ULONG PlainTextLength,
    _Out_ PVOID CipherText,
    _Inout_ PULONG CipherTextLength
);

static NTSTATUS CryptoAESDecrypt(
    _In_ PENC_CRYPTO_CONTEXT Context,
    _In_ PVOID CipherText,
    _In_ ULONG CipherTextLength,
    _Out_ PVOID PlainText,
    _Inout_ PULONG PlainTextLength
);

//
// 加密模块初始化
//
NTSTATUS
CryptoInitialize(
    VOID
    )
{
    if (g_CryptoInitialized) {
        return STATUS_SUCCESS;
    }

    InitializeListHead(&g_KeyListHead);
    ExInitializeResourceLite(&g_KeyResource);
    g_KeyCount = 0;
    g_CryptoInitialized = TRUE;

    return STATUS_SUCCESS;
}

//
// 加密模块清理
//
VOID
CryptoCleanup(
    VOID
    )
{
    PLIST_ENTRY entry;
    PENC_KEY_ENTRY keyEntry;

    if (!g_CryptoInitialized) {
        return;
    }

    // 获取独占锁
    ExAcquireResourceExclusiveLite(&g_KeyResource, TRUE);

    // 清理所有密钥
    while (!IsListEmpty(&g_KeyListHead)) {
        entry = RemoveHeadList(&g_KeyListHead);
        keyEntry = CONTAINING_RECORD(entry, ENC_KEY_ENTRY, ListEntry);
        
        // 清零密钥内存并释放
        RtlSecureZeroMemory(keyEntry->Key, keyEntry->KeyLength);
        ExFreePool(keyEntry);
    }

    g_KeyCount = 0;
    ExReleaseResourceLite(&g_KeyResource);
    ExDeleteResourceLite(&g_KeyResource);
    g_CryptoInitialized = FALSE;
}

//
// 加载密钥
//
NTSTATUS
CryptoLoadKey(
    _In_ ULONG KeyVersion,
    _In_ PUCHAR KeyData,
    _In_ ULONG KeyLength
    )
{
    NTSTATUS status = STATUS_SUCCESS;
    PENC_KEY_ENTRY keyEntry = NULL;
    PLIST_ENTRY entry = NULL;
    PENC_KEY_ENTRY currentEntry = NULL;
    BOOLEAN keyExists = FALSE;

    if (!g_CryptoInitialized) {
        return STATUS_DEVICE_NOT_READY;
    }

    if (KeyData == NULL || KeyLength > 64) {
        return STATUS_INVALID_PARAMETER;
    }

    // 获取独占锁
    ExAcquireResourceExclusiveLite(&g_KeyResource, TRUE);

    // 检查密钥是否已存在
    for (entry = g_KeyListHead.Flink; entry != &g_KeyListHead; entry = entry->Flink) {
        currentEntry = CONTAINING_RECORD(entry, ENC_KEY_ENTRY, ListEntry);
        if (currentEntry->KeyVersion == KeyVersion) {
            keyExists = TRUE;
            break;
        }
    }

    if (keyExists) {
        // 更新现有密钥
        RtlSecureZeroMemory(currentEntry->Key, currentEntry->KeyLength);
        RtlCopyMemory(currentEntry->Key, KeyData, KeyLength);
        currentEntry->KeyLength = KeyLength;
    } else {
        // 创建新密钥条目
        keyEntry = (PENC_KEY_ENTRY)ExAllocatePoolWithTag(NonPagedPool, 
                                                      sizeof(ENC_KEY_ENTRY), 
                                                      'yekE');
        if (keyEntry == NULL) {
            status = STATUS_INSUFFICIENT_RESOURCES;
            goto Exit;
        }

        keyEntry->KeyVersion = KeyVersion;
        keyEntry->KeyLength = KeyLength;
        RtlCopyMemory(keyEntry->Key, KeyData, KeyLength);
        
        // 添加到链表
        InsertTailList(&g_KeyListHead, &keyEntry->ListEntry);
        g_KeyCount++;
    }

Exit:
    ExReleaseResourceLite(&g_KeyResource);
    return status;
}

//
// 移除密钥
//
NTSTATUS
CryptoRemoveKey(
    _In_ ULONG KeyVersion
    )
{
    NTSTATUS status = STATUS_NOT_FOUND;
    PLIST_ENTRY entry = NULL;
    PENC_KEY_ENTRY keyEntry = NULL;

    if (!g_CryptoInitialized) {
        return STATUS_DEVICE_NOT_READY;
    }

    // 获取独占锁
    ExAcquireResourceExclusiveLite(&g_KeyResource, TRUE);

    // 查找并移除指定密钥
    for (entry = g_KeyListHead.Flink; entry != &g_KeyListHead; entry = entry->Flink) {
        keyEntry = CONTAINING_RECORD(entry, ENC_KEY_ENTRY, ListEntry);
        if (keyEntry->KeyVersion == KeyVersion) {
            RemoveEntryList(&keyEntry->ListEntry);
            
            // 安全清零密钥数据
            RtlSecureZeroMemory(keyEntry->Key, keyEntry->KeyLength);
            ExFreePool(keyEntry);
            
            g_KeyCount--;
            status = STATUS_SUCCESS;
            break;
        }
    }

    ExReleaseResourceLite(&g_KeyResource);
    return status;
}

//
// 查找密钥
//
static NTSTATUS
CryptoFindKey(
    _In_ ULONG KeyVersion,
    _Out_ PUCHAR Key,
    _Inout_ PULONG KeyLength
    )
{
    NTSTATUS status = STATUS_NOT_FOUND;
    PLIST_ENTRY entry = NULL;
    PENC_KEY_ENTRY keyEntry = NULL;

    if (!g_CryptoInitialized) {
        return STATUS_DEVICE_NOT_READY;
    }

    // 获取共享锁
    ExAcquireResourceSharedLite(&g_KeyResource, TRUE);

    // 查找指定版本的密钥
    for (entry = g_KeyListHead.Flink; entry != &g_KeyListHead; entry = entry->Flink) {
        keyEntry = CONTAINING_RECORD(entry, ENC_KEY_ENTRY, ListEntry);
        if (keyEntry->KeyVersion == KeyVersion) {
            if (*KeyLength < keyEntry->KeyLength) {
                status = STATUS_BUFFER_TOO_SMALL;
                *KeyLength = keyEntry->KeyLength;
            } else {
                RtlCopyMemory(Key, keyEntry->Key, keyEntry->KeyLength);
                *KeyLength = keyEntry->KeyLength;
                status = STATUS_SUCCESS;
            }
            break;
        }
    }

    ExReleaseResourceLite(&g_KeyResource);
    return status;
}

//
// 创建加密上下文
//
NTSTATUS
CryptoCreateContext(
    _In_ ENC_ALGORITHM_TYPE Algorithm,
    _In_ ENC_MODE_TYPE Mode,
    _In_ ULONG KeyVersion,
    _Out_ PENC_CRYPTO_CONTEXT *CryptoContext
    )
{
    NTSTATUS status;
    PENC_CRYPTO_CONTEXT context = NULL;
    ULONG keyLength = 64;

    if (!g_CryptoInitialized) {
        return STATUS_DEVICE_NOT_READY;
    }

    if (Algorithm >= AlgorithmMaximum || Mode >= ModeMaximum || CryptoContext == NULL) {
        return STATUS_INVALID_PARAMETER;
    }

    // 分配上下文
    context = (PENC_CRYPTO_CONTEXT)ExAllocatePoolWithTag(NonPagedPool, 
                                                       sizeof(ENC_CRYPTO_CONTEXT), 
                                                       'tpeE');
    if (context == NULL) {
        return STATUS_INSUFFICIENT_RESOURCES;
    }

    RtlZeroMemory(context, sizeof(ENC_CRYPTO_CONTEXT));
    context->Algorithm = Algorithm;
    context->Mode = Mode;
    context->KeyVersion = KeyVersion;
    context->KeyLength = keyLength;

    // 获取密钥
    status = CryptoFindKey(KeyVersion, context->Key, &context->KeyLength);
    if (!NT_SUCCESS(status)) {
        ExFreePool(context);
        return status;
    }

    // 初始化特定算法的上下文
    // 这里可以根据不同的算法实现对应的上下文初始化
    // 例如，如果是SM4或AES，可能需要生成轮密钥等

    *CryptoContext = context;
    return STATUS_SUCCESS;
}

//
// 销毁加密上下文
//
VOID
CryptoDestroyContext(
    _In_ PENC_CRYPTO_CONTEXT CryptoContext
    )
{
    if (CryptoContext == NULL) {
        return;
    }

    // 清理算法特定上下文
    if (CryptoContext->AlgorithmContext != NULL) {
        RtlSecureZeroMemory(CryptoContext->AlgorithmContext, 
                            sizeof(CryptoContext->AlgorithmContext));
        ExFreePool(CryptoContext->AlgorithmContext);
    }

    // 安全清零密钥数据
    RtlSecureZeroMemory(CryptoContext, sizeof(ENC_CRYPTO_CONTEXT));
    ExFreePool(CryptoContext);
}

//
// 加密数据
//
NTSTATUS
CryptoEncrypt(
    _In_ PENC_CRYPTO_CONTEXT CryptoContext,
    _In_ PVOID PlainText,
    _In_ ULONG PlainTextLength,
    _Out_ PVOID CipherText,
    _Inout_ PULONG CipherTextLength
    )
{
    if (!g_CryptoInitialized) {
        return STATUS_DEVICE_NOT_READY;
    }

    if (CryptoContext == NULL || PlainText == NULL || 
        CipherText == NULL || CipherTextLength == NULL) {
        return STATUS_INVALID_PARAMETER;
    }

    // 根据算法类型调用相应的加密函数
    switch (CryptoContext->Algorithm) {
    case AlgorithmSM4:
        return CryptoSM4Encrypt(CryptoContext, PlainText, PlainTextLength, 
                                CipherText, CipherTextLength);
    case AlgorithmAES256:
    case AlgorithmAES512:
        return CryptoAESEncrypt(CryptoContext, PlainText, PlainTextLength, 
                               CipherText, CipherTextLength);
    default:
        return STATUS_NOT_SUPPORTED;
    }
}

//
// 解密数据
//
NTSTATUS
CryptoDecrypt(
    _In_ PENC_CRYPTO_CONTEXT CryptoContext,
    _In_ PVOID CipherText,
    _In_ ULONG CipherTextLength,
    _Out_ PVOID PlainText,
    _Inout_ PULONG PlainTextLength
    )
{
    if (!g_CryptoInitialized) {
        return STATUS_DEVICE_NOT_READY;
    }

    if (CryptoContext == NULL || CipherText == NULL || 
        PlainText == NULL || PlainTextLength == NULL) {
        return STATUS_INVALID_PARAMETER;
    }

    // 根据算法类型调用相应的解密函数
    switch (CryptoContext->Algorithm) {
    case AlgorithmSM4:
        return CryptoSM4Decrypt(CryptoContext, CipherText, CipherTextLength, 
                                PlainText, PlainTextLength);
    case AlgorithmAES256:
    case AlgorithmAES512:
        return CryptoAESDecrypt(CryptoContext, CipherText, CipherTextLength, 
                               PlainText, PlainTextLength);
    default:
        return STATUS_NOT_SUPPORTED;
    }
}

//
// 创建文件头
//
NTSTATUS
CryptoCreateFileHeader(
    _In_ PENC_CRYPTO_CONTEXT CryptoContext,
    _In_ ULONGLONG OriginalFileSize,
    _In_opt_ PVOID Metadata,
    _In_opt_ ULONG MetadataLength,
    _Out_ PENC_FILE_HEADER FileHeader,
    _Inout_ PULONG FileHeaderSize
    )
{
    ULONG requiredSize;
    
    if (!g_CryptoInitialized) {
        return STATUS_DEVICE_NOT_READY;
    }

    if (CryptoContext == NULL || FileHeader == NULL || FileHeaderSize == NULL) {
        return STATUS_INVALID_PARAMETER;
    }

    // 计算所需的空间
    requiredSize = sizeof(ENC_FILE_HEADER);
    if (Metadata != NULL && MetadataLength > 0) {
        requiredSize += MetadataLength;
    }

    if (*FileHeaderSize < requiredSize) {
        *FileHeaderSize = requiredSize;
        return STATUS_BUFFER_TOO_SMALL;
    }

    RtlZeroMemory(FileHeader, requiredSize);

    // 填充文件头
    RtlCopyMemory(FileHeader->Signature, ENC_SIGNATURE, 4);
    FileHeader->Version = 1;
    FileHeader->Algorithm = (UCHAR)CryptoContext->Algorithm;
    FileHeader->Mode = (UCHAR)CryptoContext->Mode;
    FileHeader->KeyVersion = CryptoContext->KeyVersion;
    
    // 生成随机IV
    LARGE_INTEGER tickCount;
    KeQueryTickCount(&tickCount);
    RtlCopyMemory(FileHeader->IV, &tickCount, sizeof(tickCount));
    
    // 这里应该使用加密安全的随机数生成器生成IV
    // 为了简化演示，我们使用系统时间，实际应用中应使用专门的随机数生成函数
    
    FileHeader->OriginalFileSize = OriginalFileSize;
    
    // 处理元数据
    if (Metadata != NULL && MetadataLength > 0) {
        FileHeader->MetadataLength = MetadataLength;
        RtlCopyMemory((PUCHAR)FileHeader + sizeof(ENC_FILE_HEADER), 
                      Metadata, MetadataLength);
    } else {
        FileHeader->MetadataLength = 0;
    }

    // 计算校验和
    CryptoComputeChecksum(FileHeader, sizeof(ENC_FILE_HEADER) - sizeof(ULONG), 
                          &FileHeader->Checksum);

    *FileHeaderSize = requiredSize;
    return STATUS_SUCCESS;
}

//
// 验证文件头
//
NTSTATUS
CryptoVerifyFileHeader(
    _In_ PENC_FILE_HEADER FileHeader,
    _In_ ULONG FileHeaderSize,
    _Out_opt_ PENC_CRYPTO_CONTEXT *CryptoContext
    )
{
    ULONG calculatedChecksum;
    ULONG storedChecksum;
    
    if (!g_CryptoInitialized) {
        return STATUS_DEVICE_NOT_READY;
    }

    if (FileHeader == NULL) {
        return STATUS_INVALID_PARAMETER;
    }

    if (FileHeaderSize < sizeof(ENC_FILE_HEADER)) {
        return STATUS_BUFFER_TOO_SMALL;
    }

    // 验证签名
    if (RtlCompareMemory(FileHeader->Signature, ENC_SIGNATURE, 4) != 4) {
        return STATUS_NOT_A_REPARSE_POINT;  // 不是加密文件
    }

    // 验证版本
    if (FileHeader->Version != 1) {
        return STATUS_REVISION_MISMATCH;
    }

    // 检查算法和模式
    if (FileHeader->Algorithm >= AlgorithmMaximum || 
        FileHeader->Mode >= ModeMaximum) {
        return STATUS_INVALID_PARAMETER;
    }

    // 验证校验和
    storedChecksum = FileHeader->Checksum;
    FileHeader->Checksum = 0;  // 计算校验和时需要清零校验和字段
    
    CryptoComputeChecksum(FileHeader, sizeof(ENC_FILE_HEADER) - sizeof(ULONG), 
                          &calculatedChecksum);
    
    FileHeader->Checksum = storedChecksum;  // 恢复校验和
    
    if (calculatedChecksum != storedChecksum) {
        return STATUS_CRC_ERROR;
    }

    // 创建加密上下文
    if (CryptoContext != NULL) {
        NTSTATUS status;
        status = CryptoCreateContext((ENC_ALGORITHM_TYPE)FileHeader->Algorithm,
                                     (ENC_MODE_TYPE)FileHeader->Mode,
                                     FileHeader->KeyVersion,
                                     CryptoContext);
        
        if (!NT_SUCCESS(status)) {
            return status;
        }

        // 设置IV
        RtlCopyMemory((*CryptoContext)->IV, FileHeader->IV, 16);
    }

    return STATUS_SUCCESS;
}

//
// 计算校验和 - 使用标准CRC32算法
//
NTSTATUS
CryptoComputeChecksum(
    _In_ PVOID Data,
    _In_ ULONG DataLength,
    _Out_ PULONG Checksum
    )
{
    if (Data == NULL || Checksum == NULL) {
        return STATUS_INVALID_PARAMETER;
    }

    // 预计算的CRC32表
    static const ULONG CRC32_TABLE[256] = {
        0x00000000, 0x77073096, 0xEE0E612C, 0x990951BA, 0x076DC419, 0x706AF48F, 0xE963A535, 0x9E6495A3,
        0x0EDB8832, 0x79DCB8A4, 0xE0D5E91E, 0x97D2D988, 0x09B64C2B, 0x7EB17CBD, 0xE7B82D07, 0x90BF1D91,
        0x1DB71064, 0x6AB020F2, 0xF3B97148, 0x84BE41DE, 0x1ADAD47D, 0x6DDDE4EB, 0xF4D4B551, 0x83D385C7,
        0x136C9856, 0x646BA8C0, 0xFD62F97A, 0x8A65C9EC, 0x14015C4F, 0x63066CD9, 0xFA0F3D63, 0x8D080DF5,
        0x3B6E20C8, 0x4C69105E, 0xD56041E4, 0xA2677172, 0x3C03E4D1, 0x4B04D447, 0xD20D85FD, 0xA50AB56B,
        0x35B5A8FA, 0x42B2986C, 0xDBBBC9D6, 0xACBCF940, 0x32D86CE3, 0x45DF5C75, 0xDCD60DCF, 0xABD13D59,
        0x26D930AC, 0x51DE003A, 0xC8D75180, 0xBFD06116, 0x21B4F4B5, 0x56B3C423, 0xCFBA9599, 0xB8BDA50F,
        0x2802B89E, 0x5F058808, 0xC60CD9B2, 0xB10BE924, 0x2F6F7C87, 0x58684C11, 0xC1611DAB, 0xB6662D3D,
        0x76DC4190, 0x01DB7106, 0x98D220BC, 0xEFD5102A, 0x71B18589, 0x06B6B51F, 0x9FBFE4A5, 0xE8B8D433,
        0x7807C9A2, 0x0F00F934, 0x9609A88E, 0xE10E9818, 0x7F6A0DBB, 0x086D3D2D, 0x91646C97, 0xE6635C01,
        0x6B6B51F4, 0x1C6C6162, 0x856530D8, 0xF262004E, 0x6C0695ED, 0x1B01A57B, 0x8208F4C1, 0xF50FC457,
        0x65B0D9C6, 0x12B7E950, 0x8BBEB8EA, 0xFCB9887C, 0x62DD1DDF, 0x15DA2D49, 0x8CD37CF3, 0xFBD44C65,
        0x4DB26158, 0x3AB551CE, 0xA3BC0074, 0xD4BB30E2, 0x4ADFA541, 0x3DD895D7, 0xA4D1C46D, 0xD3D6F4FB,
        0x4369E96A, 0x346ED9FC, 0xAD678846, 0xDA60B8D0, 0x44042D73, 0x33031DE5, 0xAA0A4C5F, 0xDD0D7CC9,
        0x5005713C, 0x270241AA, 0xBE0B1010, 0xC90C2086, 0x5768B525, 0x206F85B3, 0xB966D409, 0xCE61E49F,
        0x5EDEF90E, 0x29D9C998, 0xB0D09822, 0xC7D7A8B4, 0x59B33D17, 0x2EB40D81, 0xB7BD5C3B, 0xC0BA6CAD,
        0xEDB88320, 0x9ABFB3B6, 0x03B6E20C, 0x74B1D29A, 0xEAD54739, 0x9DD277AF, 0x04DB2615, 0x73DC1683,
        0xE3630B12, 0x94643B84, 0x0D6D6A3E, 0x7A6A5AA8, 0xE40ECF0B, 0x9309FF9D, 0x0A00AE27, 0x7D079EB1,
        0xF00F9344, 0x8708A3D2, 0x1E01F268, 0x6906C2FE, 0xF762575D, 0x806567CB, 0x196C3671, 0x6E6B06E7,
        0xFED41B76, 0x89D32BE0, 0x10DA7A5A, 0x67DD4ACC, 0xF9B9DF6F, 0x8EBEEFF9, 0x17B7BE43, 0x60B08ED5,
        0xD6D6A3E8, 0xA1D1937E, 0x38D8C2C4, 0x4FDFF252, 0xD1BB67F1, 0xA6BC5767, 0x3FB506DD, 0x48B2364B,
        0xD80D2BDA, 0xAF0A1B4C, 0x36034AF6, 0x41047A60, 0xDF60EFC3, 0xA867DF55, 0x316E8EEF, 0x4669BE79,
        0xCB61B38C, 0xBC66831A, 0x256FD2A0, 0x5268E236, 0xCC0C7795, 0xBB0B4703, 0x220216B9, 0x5505262F,
        0xC5BA3BBE, 0xB2BD0B28, 0x2BB45A92, 0x5CB36A04, 0xC2D7FFA7, 0xB5D0CF31, 0x2CD99E8B, 0x5BDEAE1D,
        0x9B64C2B0, 0xEC63F226, 0x756AA39C, 0x026D930A, 0x9C0906A9, 0xEB0E363F, 0x72076785, 0x05005713,
        0x95BF4A82, 0xE2B87A14, 0x7BB12BAE, 0x0CB61B38, 0x92D28E9B, 0xE5D5BE0D, 0x7CDCEFB7, 0x0BDBDF21,
        0x86D3D2D4, 0xF1D4E242, 0x68DDB3F8, 0x1FDA836E, 0x81BE16CD, 0xF6B9265B, 0x6FB077E1, 0x18B74777,
        0x88085AE6, 0xFF0F6A70, 0x66063BCA, 0x11010B5C, 0x8F659EFF, 0xF862AE69, 0x616BFFD3, 0x166CCF45,
        0xA00AE278, 0xD70DD2EE, 0x4E048354, 0x3903B3C2, 0xA7672661, 0xD06016F7, 0x4969474D, 0x3E6E77DB,
        0xAED16A4A, 0xD9D65ADC, 0x40DF0B66, 0x37D83BF0, 0xA9BCAE53, 0xDEBB9EC5, 0x47B2CF7F, 0x30B5FFE9,
        0xBDBDF21C, 0xCABAC28A, 0x53B39330, 0x24B4A3A6, 0xBAD03605, 0xCDD70693, 0x54DE5729, 0x23D967BF,
        0xB3667A2E, 0xC4614AB8, 0x5D681B02, 0x2A6F2B94, 0xB40BBE37, 0xC30C8EA1, 0x5A05DF1B, 0x2D02EF8D
    };

    // CRC32 计算
    ULONG crc = 0xFFFFFFFF;
    PUCHAR byteData = (PUCHAR)Data;
    
    for (ULONG i = 0; i < DataLength; i++) {
        crc = (crc >> 8) ^ CRC32_TABLE[(crc & 0xFF) ^ byteData[i]];
    }
    
    *Checksum = crc ^ 0xFFFFFFFF;
    return STATUS_SUCCESS;
}

// 实现SM4算法加密
// 使用Windows CNG提供的标准加密实现 -- 将替换为调用 common crypto API
static NTSTATUS 
CryptoSM4Encrypt(
    _In_ PENC_CRYPTO_CONTEXT Context,
    _In_ PVOID PlainText,
    _In_ ULONG PlainTextLength,
    _Out_ PVOID CipherText,
    _Inout_ PULONG CipherTextLength
    )
{
    NTSTATUS status = STATUS_SUCCESS;
    const ULONG GCM_IV_LENGTH_SM4 = 12; // SM4-GCM IV length is 12 bytes
    const ULONG GCM_TAG_LENGTH_SM4 = 16; // SM4-GCM Auth Tag length is 16 bytes
    PUCHAR pbOutputBuffer = (PUCHAR)CipherText;
    ULONG cbOutputBufferLen = *CipherTextLength;
    size_t required_output_len_by_spec = 0;
    int c_api_ret = 0;

    // 1. 检查Context和关键参数的有效性
    if (Context == NULL || PlainText == NULL || CipherText == NULL || CipherTextLength == NULL) {
        return STATUS_INVALID_PARAMETER_MIX;
    }
    if (Context->Key == NULL || Context->KeyLength != 16) { // SM4 key must be 16 bytes
        return STATUS_INVALID_PARAMETER; // Or a more specific key error
    }
    // IV的来源和唯一性至关重要。这里假设Context->IV是有效的并且长度正确。
    // 驱动程序中应确保IV的正确管理。
    if (Context->IV == NULL) { // IV 必须提供，并且是12字节
        // TODO: 驱动层面应确保IV的生成和管理，例如使用RtlGenRandom
        // For now, assume Context->IV should be valid and unique per encryption.
        return STATUS_INVALID_PARAMETER; 
    }

    // 2. 检查输出缓冲区大小是否足够 (IV + PlainText + Tag)
    // C API (sm4_gcm_encrypt_c_api) 的输出是 IV + Ciphertext + Tag
    required_output_len_by_spec = GCM_IV_LENGTH_SM4 + PlainTextLength + GCM_TAG_LENGTH_SM4;
    if (cbOutputBufferLen < required_output_len_by_spec) {
        *CipherTextLength = (ULONG)required_output_len_by_spec;
        return STATUS_BUFFER_TOO_SMALL;
    }

    // 3. 调用 client/common 的 SM4 GCM 加密函数
    // sm4_gcm_encrypt_c_api 的参数:
    // const uint8_t* plaintext, size_t plaintext_len,
    // const uint8_t* key, size_t key_len,
    // const uint8_t* iv, size_t iv_len,
    // uint8_t* out_combined_data,  (Output: IV + Ciphertext + Tag)
    // size_t* p_out_combined_data_len (In: capacity, Out: actual size written)

    size_t c_api_actual_output_len = cbOutputBufferLen; // Pass the full capacity

    c_api_ret = sm4_gcm_encrypt_c_api(
        (const uint8_t*)PlainText,
        (size_t)PlainTextLength,
        (const uint8_t*)Context->Key,
        (size_t)Context->KeyLength,      // Should be 16
        (const uint8_t*)Context->IV,     // Use IV from context (must be 12 bytes and unique)
        (size_t)GCM_IV_LENGTH_SM4,       // IV length is 12
        (uint8_t*)pbOutputBuffer,        // Output buffer for IV+Ciphertext+Tag
        &c_api_actual_output_len         // Actual length written by C API
    );

    // 4. 处理 C API 返回结果
    if (c_api_ret == C_API_SUCCESS) { // 0 for success
        // 验证 C API 写入的实际长度是否与预期一致
        if (c_api_actual_output_len == required_output_len_by_spec) {
            *CipherTextLength = (ULONG)c_api_actual_output_len;
            status = STATUS_SUCCESS;
        } else if (PlainTextLength == 0 && c_api_actual_output_len == (GCM_IV_LENGTH_SM4 + GCM_TAG_LENGTH_SM4)) {
            // 特殊情况：明文长度为0时，输出只有 IV 和 Tag
            *CipherTextLength = (ULONG)c_api_actual_output_len;
            status = STATUS_SUCCESS;
        } else {
            // C API 返回成功，但写入长度不符合预期规范，这可能指示内部逻辑问题
            // 或者C API对输出长度的计算方式与驱动的预期不一致
            KdPrint(("CryptoSM4Encrypt: C_API_SUCCESS but len mismatch. Expected %zu, Got %zu\n", 
                     required_output_len_by_spec, c_api_actual_output_len));
            status = STATUS_CRYPTO_SYSTEM_INTERNAL_ERROR; // Placeholder for a more specific internal error
        }
    } else if (c_api_ret == C_API_ERROR_BUFFER_TOO_SMALL) { // 5
        // C API 指示缓冲区太小，并已在 c_api_actual_output_len 中返回所需大小
        *CipherTextLength = (ULONG)c_api_actual_output_len;
        status = STATUS_BUFFER_TOO_SMALL;
    } else if (c_api_ret == C_API_ERROR_INVALID_PARAM) { // 1
        status = STATUS_INVALID_PARAMETER;
    } else if (c_api_ret == C_API_ERROR_ENCRYPTION_FAILED) { // 3
        status = STATUS_ENCRYPTION_FAILED; // Windows CNG 有这个错误码
    } else {
        KdPrint(("CryptoSM4Encrypt: sm4_gcm_encrypt_c_api failed with code %d\n", c_api_ret));
        status = STATUS_UNSUCCESSFUL; // General failure
    }

    return status;
}

// 实现SM4算法解密
// 使用Windows CNG提供的标准加密实现 -- 将替换为调用 common crypto API
static NTSTATUS 
CryptoSM4Decrypt(
    _In_ PENC_CRYPTO_CONTEXT Context,
    _In_ PVOID CipherText, // Input: IV + Ciphertext + Tag
    _In_ ULONG CipherTextLength,
    _Out_ PVOID PlainText,
    _Inout_ PULONG PlainTextLength
    )
{
    NTSTATUS status = STATUS_SUCCESS;
    const ULONG GCM_IV_LENGTH_SM4 = 12;
    const ULONG GCM_TAG_LENGTH_SM4 = 16;
    ULONG min_input_len = GCM_IV_LENGTH_SM4 + GCM_TAG_LENGTH_SM4; // 密文至少包含IV和Tag
    int c_api_ret = 0;
    size_t expected_plaintext_len_by_spec = 0;

    // 1. 检查Context和关键参数的有效性
    if (Context == NULL || CipherText == NULL || PlainText == NULL || PlainTextLength == NULL) {
        return STATUS_INVALID_PARAMETER_MIX;
    }
    if (Context->Key == NULL || Context->KeyLength != 16) { // SM4 key must be 16 bytes
        return STATUS_INVALID_PARAMETER;
    }
    if (CipherTextLength < min_input_len) {
        return STATUS_INVALID_BUFFER_SIZE; // 或 STATUS_DATA_ERROR
    }

    // 2. 计算预期的明文长度并检查输出缓冲区
    // CipherTextLength (IV + Ciphertext_proper + Tag) - IV - Tag = Plaintext
    if (CipherTextLength >= min_input_len) { // 避免负数或下溢
        expected_plaintext_len_by_spec = CipherTextLength - GCM_IV_LENGTH_SM4 - GCM_TAG_LENGTH_SM4;
    } else {
        // 这种情况理论上已被上面的 CipherTextLength < min_input_len 捕获
        return STATUS_INVALID_BUFFER_SIZE; 
    }

    if (*PlainTextLength < expected_plaintext_len_by_spec) {
        *PlainTextLength = (ULONG)expected_plaintext_len_by_spec;
        return STATUS_BUFFER_TOO_SMALL;
    }

    // 3. 调用 client/common 的 SM4 GCM 解密函数
    // sm4_gcm_decrypt_c_api 参数:
    // const uint8_t* combined_data, (Input: IV + Ciphertext + Tag)
    // size_t combined_data_len,
    // const uint8_t* key, size_t key_len,
    // uint8_t* out_plaintext,
    // size_t* p_out_plaintext_len (In: capacity, Out: actual size written)

    size_t c_api_actual_plaintext_len = *PlainTextLength; // Pass the full capacity of PlainText buffer

    c_api_ret = sm4_gcm_decrypt_c_api(
        (const uint8_t*)CipherText,
        (size_t)CipherTextLength,
        (const uint8_t*)Context->Key,
        (size_t)Context->KeyLength,      // Should be 16
        (uint8_t*)PlainText,
        &c_api_actual_plaintext_len      // Actual length written by C API
    );

    // 4. 处理 C API 返回结果
    if (c_api_ret == C_API_SUCCESS) { // 0 for success
        // 验证 C API 写入的实际长度是否与预期一致
        if (c_api_actual_plaintext_len == expected_plaintext_len_by_spec) {
            *PlainTextLength = (ULONG)c_api_actual_plaintext_len;
            status = STATUS_SUCCESS;
        } else {
            // C API 返回成功，但写入长度不符合预期规范
            KdPrint(("CryptoSM4Decrypt: C_API_SUCCESS but len mismatch. Expected %zu, Got %zu\n", 
                     expected_plaintext_len_by_spec, c_api_actual_plaintext_len));
            status = STATUS_CRYPTO_SYSTEM_INTERNAL_ERROR;
        }
    } else if (c_api_ret == C_API_ERROR_BUFFER_TOO_SMALL) { // 5
        *PlainTextLength = (ULONG)c_api_actual_plaintext_len; // C API 返回了所需大小
        status = STATUS_BUFFER_TOO_SMALL;
    } else if (c_api_ret == C_API_ERROR_INVALID_PARAM) { // 1
        status = STATUS_INVALID_PARAMETER;
    } else if (c_api_ret == C_API_ERROR_AUTH_FAILED) { // 4
        status = STATUS_DATA_AUTHENTICATION_FAILED; // CNG对应的错误码
    } else if (c_api_ret == C_API_ERROR_DECRYPTION_FAILED) { // 3 (通用解密失败)
        status = STATUS_DECRYPTION_FAILED;
    } else {
        KdPrint(("CryptoSM4Decrypt: sm4_gcm_decrypt_c_api failed with code %d\n", c_api_ret));
        status = STATUS_UNSUCCESSFUL;
    }

    return status;
}

// 实现AES算法加密
static NTSTATUS 
CryptoAESEncrypt(
    _In_ PENC_CRYPTO_CONTEXT Context,
    _In_ PVOID PlainText,
    _In_ ULONG PlainTextLength,
    _Out_ PVOID CipherText,
    _Inout_ PULONG CipherTextLength
    )
{
    NTSTATUS status = STATUS_SUCCESS;
    BCRYPT_ALG_HANDLE hAlgorithm = NULL;
    BCRYPT_KEY_HANDLE hKey = NULL;
    ULONG cbData = 0, cbKeyObject = 0, cbBlockLen = 0;
    PUCHAR pbKeyObject = NULL;
    BCRYPT_AUTHENTICATED_CIPHER_MODE_INFO authInfo;
    
    // 检查输出缓冲区大小
    if (*CipherTextLength < PlainTextLength + GCM_AUTH_TAG_LENGTH + GCM_IV_LENGTH) {
        *CipherTextLength = PlainTextLength + GCM_AUTH_TAG_LENGTH + GCM_IV_LENGTH;
        return STATUS_BUFFER_TOO_SMALL;
    }

    // 打开算法提供程序
    status = BCryptOpenAlgorithmProvider(&hAlgorithm, 
                                     BCRYPT_AES_ALGORITHM, 
                                     NULL, 
                                     0);
    if (!NT_SUCCESS(status)) {
        goto Cleanup;
    }
    
    // 设置链接模式为GCM
    status = BCryptSetProperty(hAlgorithm, 
                           BCRYPT_CHAINING_MODE, 
                           (PUCHAR)BCRYPT_CHAIN_MODE_GCM, 
                           sizeof(BCRYPT_CHAIN_MODE_GCM), 
                           0);
    if (!NT_SUCCESS(status)) {
        goto Cleanup;
    }
    
    // 获取密钥对象大小
    status = BCryptGetProperty(hAlgorithm, 
                           BCRYPT_OBJECT_LENGTH, 
                           (PUCHAR)&cbKeyObject, 
                           sizeof(ULONG), 
                           &cbData, 
                           0);
    if (!NT_SUCCESS(status)) {
        goto Cleanup;
    }
    
    // 获取块长度
    status = BCryptGetProperty(hAlgorithm, 
                           BCRYPT_BLOCK_LENGTH, 
                           (PUCHAR)&cbBlockLen, 
                           sizeof(ULONG), 
                           &cbData, 
                           0);
    if (!NT_SUCCESS(status)) {
        goto Cleanup;
    }
    
    // 分配密钥对象内存
    pbKeyObject = ExAllocatePoolWithTag(NonPagedPool, cbKeyObject, 'yekC');
    if (NULL == pbKeyObject) {
        status = STATUS_INSUFFICIENT_RESOURCES;
        goto Cleanup;
    }
    
    // 创建密钥
    status = BCryptGenerateSymmetricKey(hAlgorithm, 
                                    &hKey, 
                                    pbKeyObject, 
                                    cbKeyObject, 
                                    Context->Key, 
                                    Context->KeyLength, 
                                    0);
    if (!NT_SUCCESS(status)) {
        goto Cleanup;
    }
    
    // 准备认证信息
    BCRYPT_INIT_AUTH_MODE_INFO(authInfo);
    
    // 设置随机IV (存储在CipherText的前面)
    PUCHAR pbIV = (PUCHAR)CipherText;
    SecureZeroMemory(pbIV, GCM_IV_LENGTH);
    LARGE_INTEGER time;
    KeQuerySystemTime(&time);
    RtlCopyMemory(pbIV, &time.QuadPart, sizeof(LONGLONG));
    authInfo.pbNonce = pbIV;
    authInfo.cbNonce = GCM_IV_LENGTH;
    
    // 设置认证标签 (存储在CipherText的末尾)
    PUCHAR pbAuthTag = (PUCHAR)CipherText + GCM_IV_LENGTH + PlainTextLength;
    authInfo.pbTag = pbAuthTag;
    authInfo.cbTag = GCM_AUTH_TAG_LENGTH;
    
    // 执行加密
    status = BCryptEncrypt(hKey, 
                       (PUCHAR)PlainText, 
                       PlainTextLength, 
                       &authInfo, 
                       NULL, 
                       0, 
                       (PUCHAR)CipherText + GCM_IV_LENGTH, 
                       PlainTextLength, 
                       &cbData, 
                       0);
    
    // 设置实际使用的缓冲区大小
    *CipherTextLength = GCM_IV_LENGTH + PlainTextLength + GCM_AUTH_TAG_LENGTH;
    
Cleanup:
    if (hKey) {
        BCryptDestroyKey(hKey);
    }
    
    if (pbKeyObject) {
        ExFreePool(pbKeyObject);
    }
    
    if (hAlgorithm) {
        BCryptCloseAlgorithmProvider(hAlgorithm, 0);
    }
    
    return status;
}

// 实现AES算法解密
static NTSTATUS 
CryptoAESDecrypt(
    _In_ PENC_CRYPTO_CONTEXT Context,
    _In_ PVOID CipherText,
    _In_ ULONG CipherTextLength,
    _Out_ PVOID PlainText,
    _Inout_ PULONG PlainTextLength
    )
{
    NTSTATUS status = STATUS_SUCCESS;
    BCRYPT_ALG_HANDLE hAlgorithm = NULL;
    BCRYPT_KEY_HANDLE hKey = NULL;
    ULONG cbData = 0, cbKeyObject = 0, cbPlaintext = 0;
    PUCHAR pbKeyObject = NULL;
    BCRYPT_AUTHENTICATED_CIPHER_MODE_INFO authInfo;
    
    // 计算预期的明文大小
    if (CipherTextLength < GCM_IV_LENGTH + GCM_AUTH_TAG_LENGTH) {
        return STATUS_INVALID_PARAMETER;
    }
    
    cbPlaintext = CipherTextLength - GCM_IV_LENGTH - GCM_AUTH_TAG_LENGTH;
    
    // 检查输出缓冲区大小
    if (*PlainTextLength < cbPlaintext) {
        *PlainTextLength = cbPlaintext;
        return STATUS_BUFFER_TOO_SMALL;
    }
    
    // 打开算法提供程序
    status = BCryptOpenAlgorithmProvider(&hAlgorithm, 
                                     BCRYPT_AES_ALGORITHM, 
                                     NULL, 
                                     0);
    if (!NT_SUCCESS(status)) {
        goto Cleanup;
    }
    
    // 设置链接模式为GCM
    status = BCryptSetProperty(hAlgorithm, 
                           BCRYPT_CHAINING_MODE, 
                           (PUCHAR)BCRYPT_CHAIN_MODE_GCM, 
                           sizeof(BCRYPT_CHAIN_MODE_GCM), 
                           0);
    if (!NT_SUCCESS(status)) {
        goto Cleanup;
    }
    
    // 获取密钥对象大小
    status = BCryptGetProperty(hAlgorithm, 
                           BCRYPT_OBJECT_LENGTH, 
                           (PUCHAR)&cbKeyObject, 
                           sizeof(ULONG), 
                           &cbData, 
                           0);
    if (!NT_SUCCESS(status)) {
        goto Cleanup;
    }
    
    // 分配密钥对象内存
    pbKeyObject = ExAllocatePoolWithTag(NonPagedPool, cbKeyObject, 'yekC');
    if (NULL == pbKeyObject) {
        status = STATUS_INSUFFICIENT_RESOURCES;
        goto Cleanup;
    }
    
    // 创建密钥
    status = BCryptGenerateSymmetricKey(hAlgorithm, 
                                    &hKey, 
                                    pbKeyObject, 
                                    cbKeyObject, 
                                    Context->Key, 
                                    Context->KeyLength, 
                                    0);
    if (!NT_SUCCESS(status)) {
        goto Cleanup;
    }
    
    // 准备认证信息
    BCRYPT_INIT_AUTH_MODE_INFO(authInfo);
    
    // 获取IV (存储在CipherText的前面)
    PUCHAR pbIV = (PUCHAR)CipherText;
    authInfo.pbNonce = pbIV;
    authInfo.cbNonce = GCM_IV_LENGTH;
    
    // 获取认证标签 (存储在CipherText的末尾)
    PUCHAR pbAuthTag = (PUCHAR)CipherText + CipherTextLength - GCM_AUTH_TAG_LENGTH;
    authInfo.pbTag = pbAuthTag;
    authInfo.cbTag = GCM_AUTH_TAG_LENGTH;
    
    // 执行解密
    status = BCryptDecrypt(hKey, 
                       (PUCHAR)CipherText + GCM_IV_LENGTH, 
                       cbPlaintext, 
                       &authInfo, 
                       NULL, 
                       0, 
                       (PUCHAR)PlainText, 
                       *PlainTextLength, 
                       &cbData, 
                       0);
    
    if (NT_SUCCESS(status)) {
        *PlainTextLength = cbData;
    }
    
Cleanup:
    if (hKey) {
        BCryptDestroyKey(hKey);
    }
    
    if (pbKeyObject) {
        ExFreePool(pbKeyObject);
    }
    
    if (hAlgorithm) {
        BCryptCloseAlgorithmProvider(hAlgorithm, 0);
    }
    
    return status;
} 