{"name": "key-generator-<PERSON><PERSON>", "version": "1.0.0", "description": "HarmonyOS version of Key Generator for enterprise document encryption system", "main": "", "scripts": {"clean": "hvigor clean", "build": "hvigor assembleHap", "build:release": "hvigor assembleHap --mode release", "install:hap": "hvigor installHap", "uninstall:hap": "hvigor uninstallHap", "test": "hvigor test"}, "author": "CryptoSystem Team", "license": "MIT", "dependencies": {}, "devDependencies": {"@ohos/hvigor": "5.16.1", "@ohos/hvigor-ohos-plugin": "5.16.1", "@ohos/hypium": "1.0.18"}}