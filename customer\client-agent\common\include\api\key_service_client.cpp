#include "key_service_client.h"
#include <curl/curl.h>
#include <nlohmann/json.hpp>
#include <iostream>
#include <sstream>

namespace crypto {
namespace api {

// CURL响应回调
size_t WriteCallback(char* ptr, size_t size, size_t nmemb, std::string* data) {
    data->append(ptr, size * nmemb);
    return size * nmemb;
}

// 私有实现类
class KeyServiceClient::Impl {
public:
    explicit Impl(const KeyServiceConfig& config) : config_(config) {
        // 全局初始化CURL
        curl_global_init(CURL_GLOBAL_DEFAULT);
    }

    ~Impl() {
        // 清理CURL
        curl_global_cleanup();
    }

    // 发送HTTP请求辅助函数
    nlohmann::json SendRequest(const std::string& endpoint, 
                               const std::string& method = "GET", 
                               const nlohmann::json& payload = nullptr) {
        CURL* curl = curl_easy_init();
        if (!curl) {
            throw std::runtime_error("无法初始化CURL");
        }

        // 构建完整URL
        std::string url = config_.serviceUrl + endpoint;
        
        // 设置CURL选项
        curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
        curl_easy_setopt(curl, CURLOPT_TIMEOUT_MS, config_.timeoutMs);
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, config_.useTls ? 1L : 0L);
        
        // 设置请求头
        struct curl_slist* headers = nullptr;
        headers = curl_slist_append(headers, "Content-Type: application/json");
        if (!config_.apiKey.empty()) {
            std::string authHeader = "Authorization: Bearer " + config_.apiKey;
            headers = curl_slist_append(headers, authHeader.c_str());
        }
        curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);

        // 设置HTTP方法和请求体
        if (method == "POST" || method == "PATCH" || method == "PUT") {
            if (method == "POST") {
                curl_easy_setopt(curl, CURLOPT_POST, 1L);
            } else {
                curl_easy_setopt(curl, CURLOPT_CUSTOMREQUEST, method.c_str());
            }
            
            if (payload != nullptr) {
                std::string jsonData = payload.dump();
                curl_easy_setopt(curl, CURLOPT_POSTFIELDS, jsonData.c_str());
                curl_easy_setopt(curl, CURLOPT_POSTFIELDSIZE, jsonData.length());
            }
        } else if (method == "DELETE") {
            curl_easy_setopt(curl, CURLOPT_CUSTOMREQUEST, "DELETE");
        }

        // 设置响应回调
        std::string responseData;
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, &responseData);

        // 执行请求
        CURLcode res = curl_easy_perform(curl);
        
        // 检查响应状态
        long statusCode = 0;
        curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &statusCode);
        
        // 清理
        curl_slist_free_all(headers);
        curl_easy_cleanup(curl);

        // 处理错误
        if (res != CURLE_OK) {
            throw std::runtime_error(std::string("CURL请求失败: ") + curl_easy_strerror(res));
        }

        // 解析JSON响应
        nlohmann::json jsonResponse;
        if (!responseData.empty()) {
            try {
                jsonResponse = nlohmann::json::parse(responseData);
            } catch (const std::exception& e) {
                throw std::runtime_error(std::string("JSON解析失败: ") + e.what());
            }
        }

        // 检查错误状态码
        if (statusCode >= 400) {
            std::string errorMsg = "服务器错误: " + std::to_string(statusCode);
            if (jsonResponse.contains("message")) {
                errorMsg += " - " + jsonResponse["message"].get<std::string>();
            }
            throw std::runtime_error(errorMsg);
        }

        return jsonResponse;
    }

    // 将JSON转换为FileKey对象
    std::shared_ptr<key::FileKey> ParseFileKey(const nlohmann::json& json) {
        if (json.empty()) {
            return nullptr;
        }

        try {
            // 从JSON中提取基本密钥信息
            std::string keyId = json["keyId"];
            key::KeyType keyType = static_cast<key::KeyType>(json["keyType"].get<int>());
            key::KeyStatus keyStatus = static_cast<key::KeyStatus>(json["keyStatus"].get<int>());
            std::string parentKeyId = json["parentKeyId"];
            std::string createdAt = json["createdAt"];
            std::string updatedAt = json["updatedAt"];

            // 提取文件相关信息
            std::string fileId = json["fileId"];
            std::string filePath = json["filePath"];
            std::string algorithm = json["algorithm"];
            std::string encryptedKeyMaterial = json["encryptedKeyMaterial"];

            // 提取元数据
            key::KeyMetadata metadata;
            if (json.contains("metadata") && json["metadata"].is_object()) {
                for (auto& [key, value] : json["metadata"].items()) {
                    metadata[key] = value.get<std::string>();
                }
            }

            // 创建FileKey对象
            auto fileKey = std::make_shared<key::FileKey>(
                keyId, keyType, keyStatus, parentKeyId, createdAt, updatedAt,
                fileId, filePath, algorithm, encryptedKeyMaterial, metadata
            );

            return fileKey;
        } catch (const std::exception& e) {
            throw std::runtime_error(std::string("文件密钥解析失败: ") + e.what());
        }
    }

    KeyServiceConfig config_;
};

// 构造函数
KeyServiceClient::KeyServiceClient(const KeyServiceConfig& config)
    : pImpl_(std::make_unique<Impl>(config)) {
}

// 析构函数
KeyServiceClient::~KeyServiceClient() = default;

// 创建文件密钥
std::shared_ptr<key::FileKey> KeyServiceClient::CreateFileKey(
    const std::string& parentKeyId,
    const std::string& fileId,
    const std::string& filePath,
    const std::string& algorithm) {
    
    nlohmann::json payload = {
        {"parentKeyId", parentKeyId},
        {"fileId", fileId},
        {"filePath", filePath},
        {"algorithm", algorithm}
    };

    nlohmann::json response = pImpl_->SendRequest("/api/v1/keys/file", "POST", payload);
    return pImpl_->ParseFileKey(response);
}

// 获取文件密钥 (通过密钥ID)
std::shared_ptr<key::FileKey> KeyServiceClient::GetFileKey(const std::string& keyId) {
    nlohmann::json response = pImpl_->SendRequest("/api/v1/keys/file/" + keyId);
    return pImpl_->ParseFileKey(response);
}

// 获取文件密钥 (通过文件ID)
std::shared_ptr<key::FileKey> KeyServiceClient::GetFileKeyByFileId(const std::string& fileId) {
    nlohmann::json response = pImpl_->SendRequest("/api/v1/keys/file/by-file-id/" + fileId);
    return pImpl_->ParseFileKey(response);
}

// 获取文件密钥 (通过文件路径)
std::shared_ptr<key::FileKey> KeyServiceClient::GetFileKeyByFilePath(const std::string& filePath) {
    // URL编码文件路径
    CURL* curl = curl_easy_init();
    char* escapedPath = curl_easy_escape(curl, filePath.c_str(), static_cast<int>(filePath.length()));
    std::string encodedPath = escapedPath;
    curl_free(escapedPath);
    curl_easy_cleanup(curl);

    nlohmann::json response = pImpl_->SendRequest("/api/v1/keys/file/by-path?path=" + encodedPath);
    return pImpl_->ParseFileKey(response);
}

// 吊销密钥
bool KeyServiceClient::RevokeKey(const std::string& keyId, bool recursive) {
    try {
        std::string endpoint = "/api/v1/keys/" + keyId;
        if (recursive) {
            endpoint += "?recursive=true";
        }
        pImpl_->SendRequest(endpoint, "DELETE");
        return true;
    } catch (const std::exception& e) {
        std::cerr << "吊销密钥失败: " << e.what() << std::endl;
        return false;
    }
}

// 异步创建文件密钥
std::future<std::shared_ptr<key::FileKey>> KeyServiceClient::CreateFileKeyAsync(
    const std::string& parentKeyId,
    const std::string& fileId,
    const std::string& filePath,
    const std::string& algorithm) {
    
    return std::async(std::launch::async, [this, parentKeyId, fileId, filePath, algorithm]() {
        return this->CreateFileKey(parentKeyId, fileId, filePath, algorithm);
    });
}

// 异步获取文件密钥
std::future<std::shared_ptr<key::FileKey>> KeyServiceClient::GetFileKeyAsync(const std::string& keyId) {
    return std::async(std::launch::async, [this, keyId]() {
        return this->GetFileKey(keyId);
    });
}

} // namespace api
} // namespace crypto 