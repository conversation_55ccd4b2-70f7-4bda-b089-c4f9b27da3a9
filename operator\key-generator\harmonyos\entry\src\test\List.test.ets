import hilog from '@ohos.hilog';
import { afterAll, afterEach, beforeAll, beforeEach, describe, expect, it } from '@ohos/hypium';

export default function ListTest() {
  describe('ListTest', () => {
    beforeAll(() => {
      // input testsuit setup step，setup invoked before all testcases
      hilog.info(0x0000, 'testTag', '%{public}s', 'beforeAll called');
    });

    afterAll(() => {
      // input testsuit teardown step，teardown invoked after all testcases
      hilog.info(0x0000, 'testTag', '%{public}s', 'afterAll called');
    });

    beforeEach(() => {
      // input testcase setup step，setup invoked before each testcases
      hilog.info(0x0000, 'testTag', '%{public}s', 'beforeEach called');
    });

    afterEach(() => {
      // input testcase teardown step，teardown invoked after each testcases
      hilog.info(0x0000, 'testTag', '%{public}s', 'afterEach called');
    });

    /*
     * @tc.name: testAssertContain
     * @tc.desc: function description
     * @tc.type: FUNC
     * @tc.require: The current test interface needs to be fully verified
     * @tc.size: MediumTest
     * @tc.level: Level 1
     */
    it('testAssertContain', 0, () => {
      hilog.info(0x0000, 'testTag', '%{public}s', 'it testAssertContain begin');
      let a = 'abc';
      let b = 'b';
      // expect(a).assertContain(b);
      expect(a).assertEqual(a);
    });

    /*
     * @tc.name: testAssertEqual
     * @tc.desc: function description
     * @tc.type: FUNC
     * @tc.require: The current test interface needs to be fully verified
     * @tc.size: MediumTest
     * @tc.level: Level 1
     */
    it('testAssertEqual', 0, () => {
      hilog.info(0x0000, 'testTag', '%{public}s', 'it testAssertEqual begin');
      let a = 'abc';
      let b = 'abc';
      expect(a).assertEqual(b);
    });
  });
}
