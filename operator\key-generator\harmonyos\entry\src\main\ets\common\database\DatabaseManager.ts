import relationalStore from '@ohos.data.relationalStore';
import hilog from '@ohos.hilog';
import { CryptoAlgorithm, KeyInfo, KeyStatus, KeyType } from '../../model/KeyModels';

/**
 * 数据库管理类
 * 使用鸿蒙系统的关系型数据库(RDB)存储密钥信息
 */
export class DatabaseManager {
  private static readonly TAG = 'DatabaseManager';
  private static readonly DOMAIN = 0x0000;
  private static readonly DB_NAME = 'KeyGeneratorDB';
  private static readonly DB_VERSION = 1;

  private rdbStore?: relationalStore.RdbStore;
  private isInitialized: boolean = false;

  /**
   * 初始化数据库
   */
  async initialize(): Promise<void> {
    try {
      hilog.info(DatabaseManager.DOMAIN, DatabaseManager.TAG, 'Initializing database');

      const config: relationalStore.StoreConfig = {
        name: DatabaseManager.DB_NAME,
        securityLevel: relationalStore.SecurityLevel.S1,
        encrypt: true
      };

      this.rdbStore = await relationalStore.getRdbStore(getContext(), config);

      // 创建表结构
      await this.createTables();

      this.isInitialized = true;
      hilog.info(DatabaseManager.DOMAIN, DatabaseManager.TAG, 'Database initialized successfully');

    } catch (error) {
      hilog.error(DatabaseManager.DOMAIN, DatabaseManager.TAG,
        'Failed to initialize database: %{public}s', error.message);
      throw error;
    }
  }

  /**
   * 关闭数据库连接
   */
  close(): void {
    if (this.rdbStore) {
      this.rdbStore = undefined;
      this.isInitialized = false;
      hilog.info(DatabaseManager.DOMAIN, DatabaseManager.TAG, 'Database connection closed');
    }
  }

  /**
   * 检查数据库是否已初始化
   */
  isReady(): boolean {
    return this.isInitialized && this.rdbStore !== undefined;
  }

  /**
   * 插入密钥信息
   */
  async insertKey(keyInfo: KeyInfo): Promise<boolean> {
    if (!this.isReady()) {
      throw new Error('Database is not ready');
    }

    try {
      const valueBucket: relationalStore.ValuesBucket = {
        key_id: keyInfo.keyId,
        key_name: keyInfo.keyName,
        key_type: keyInfo.keyType,
        algorithm: keyInfo.algorithm,
        key_length: keyInfo.keyLength,
        status: keyInfo.status,
        created_time: keyInfo.createdTime.getTime(),
        effective_time: keyInfo.effectiveTime.getTime(),
        expiry_time: keyInfo.expiryTime.getTime(),
        created_by: keyInfo.createdBy,
        client_unit: keyInfo.clientUnit,
        purpose: keyInfo.purpose,
        key_value: keyInfo.keyValue,
        fingerprint: keyInfo.fingerprint,
        version: keyInfo.version,
        parent_key_id: keyInfo.parentKeyId,
        usage_count: keyInfo.usageCount,
        max_usage_count: keyInfo.maxUsageCount,
        last_used_time: keyInfo.lastUsedTime?.getTime(),
        remarks: keyInfo.remarks,
        exportable: keyInfo.exportable ? 1 : 0,
        tags: JSON.stringify(keyInfo.tags),
        metadata: JSON.stringify(keyInfo.metadata)
      };

      const rowId = await this.rdbStore!.insert('keys', valueBucket);

      hilog.info(DatabaseManager.DOMAIN, DatabaseManager.TAG,
        'Key inserted successfully: %{public}s', keyInfo.keyId);

      return rowId > 0;

    } catch (error) {
      hilog.error(DatabaseManager.DOMAIN, DatabaseManager.TAG,
        'Failed to insert key: %{public}s', error.message);
      return false;
    }
  }

  /**
   * 查询所有密钥
   */
  async getAllKeys(): Promise<KeyInfo[]> {
    if (!this.isReady()) {
      throw new Error('Database is not ready');
    }

    try {
      const predicates = new relationalStore.RdbPredicates('keys');
      const resultSet = await this.rdbStore!.query(predicates);

      const keys: KeyInfo[] = [];

      if (resultSet.goToFirstRow()) {
        do {
          const keyInfo = this.resultSetToKeyInfo(resultSet);
          keys.push(keyInfo);
        } while (resultSet.goToNextRow());
      }

      resultSet.close();

      hilog.info(DatabaseManager.DOMAIN, DatabaseManager.TAG,
        'Retrieved %{public}d keys from database', keys.length);

      return keys;

    } catch (error) {
      hilog.error(DatabaseManager.DOMAIN, DatabaseManager.TAG,
        'Failed to get all keys: %{public}s', error.message);
      return [];
    }
  }

  /**
   * 根据ID查询密钥
   */
  async getKeyById(keyId: string): Promise<KeyInfo | null> {
    if (!this.isReady()) {
      throw new Error('Database is not ready');
    }

    try {
      const predicates = new relationalStore.RdbPredicates('keys');
      predicates.equalTo('key_id', keyId);

      const resultSet = await this.rdbStore!.query(predicates);

      let keyInfo: KeyInfo | null = null;

      if (resultSet.goToFirstRow()) {
        keyInfo = this.resultSetToKeyInfo(resultSet);
      }

      resultSet.close();

      return keyInfo;

    } catch (error) {
      hilog.error(DatabaseManager.DOMAIN, DatabaseManager.TAG,
        'Failed to get key by ID: %{public}s', error.message);
      return null;
    }
  }

  /**
   * 更新密钥信息
   */
  async updateKey(keyInfo: KeyInfo): Promise<boolean> {
    if (!this.isReady()) {
      throw new Error('Database is not ready');
    }

    try {
      const valueBucket: relationalStore.ValuesBucket = {
        key_name: keyInfo.keyName,
        status: keyInfo.status,
        purpose: keyInfo.purpose,
        usage_count: keyInfo.usageCount,
        last_used_time: keyInfo.lastUsedTime?.getTime(),
        remarks: keyInfo.remarks,
        tags: JSON.stringify(keyInfo.tags),
        metadata: JSON.stringify(keyInfo.metadata)
      };

      const predicates = new relationalStore.RdbPredicates('keys');
      predicates.equalTo('key_id', keyInfo.keyId);

      const rowsAffected = await this.rdbStore!.update(valueBucket, predicates);

      hilog.info(DatabaseManager.DOMAIN, DatabaseManager.TAG,
        'Key updated successfully: %{public}s', keyInfo.keyId);

      return rowsAffected > 0;

    } catch (error) {
      hilog.error(DatabaseManager.DOMAIN, DatabaseManager.TAG,
        'Failed to update key: %{public}s', error.message);
      return false;
    }
  }

  /**
   * 删除密钥
   */
  async deleteKey(keyId: string): Promise<boolean> {
    if (!this.isReady()) {
      throw new Error('Database is not ready');
    }

    try {
      const predicates = new relationalStore.RdbPredicates('keys');
      predicates.equalTo('key_id', keyId);

      const rowsAffected = await this.rdbStore!.delete(predicates);

      hilog.info(DatabaseManager.DOMAIN, DatabaseManager.TAG,
        'Key deleted successfully: %{public}s', keyId);

      return rowsAffected > 0;

    } catch (error) {
      hilog.error(DatabaseManager.DOMAIN, DatabaseManager.TAG,
        'Failed to delete key: %{public}s', error.message);
      return false;
    }
  }

  /**
   * 创建数据库表
   */
  private async createTables(): Promise<void> {
    if (!this.rdbStore) {
      throw new Error('Database store is not initialized');
    }

    // 创建密钥信息表
    const createKeyTableSQL = `
            CREATE TABLE IF NOT EXISTS keys (
                key_id TEXT PRIMARY KEY,
                key_name TEXT NOT NULL,
                key_type INTEGER NOT NULL,
                algorithm INTEGER NOT NULL,
                key_length INTEGER NOT NULL,
                status TEXT NOT NULL,
                created_time INTEGER NOT NULL,
                effective_time INTEGER NOT NULL,
                expiry_time INTEGER NOT NULL,
                created_by TEXT NOT NULL,
                client_unit TEXT NOT NULL,
                purpose TEXT,
                key_value TEXT NOT NULL,
                fingerprint TEXT,
                version INTEGER DEFAULT 1,
                parent_key_id TEXT,
                usage_count INTEGER DEFAULT 0,
                max_usage_count INTEGER,
                last_used_time INTEGER,
                remarks TEXT,
                exportable INTEGER DEFAULT 0,
                tags TEXT,
                metadata TEXT
            )
        `;

    await this.rdbStore.executeSql(createKeyTableSQL);

    // 创建客户信息表
    const createClientTableSQL = `
            CREATE TABLE IF NOT EXISTS clients (
                client_id TEXT PRIMARY KEY,
                client_name TEXT NOT NULL,
                client_code TEXT,
                contact_person TEXT,
                contact_phone TEXT,
                contact_email TEXT,
                address TEXT,
                description TEXT,
                created_time INTEGER NOT NULL,
                last_modified INTEGER NOT NULL,
                is_active INTEGER DEFAULT 1,
                remarks TEXT
            )
        `;

    await this.rdbStore.executeSql(createClientTableSQL);

    // 创建审计日志表
    const createAuditLogTableSQL = `
            CREATE TABLE IF NOT EXISTS audit_logs (
                log_id TEXT PRIMARY KEY,
                timestamp INTEGER NOT NULL,
                level TEXT NOT NULL,
                operation TEXT NOT NULL,
                user_id TEXT NOT NULL,
                user_name TEXT,
                client_info TEXT,
                ip_address TEXT,
                resource_id TEXT,
                description TEXT,
                result TEXT,
                success INTEGER NOT NULL,
                error_code TEXT,
                session_id TEXT,
                metadata TEXT
            )
        `;

    await this.rdbStore.executeSql(createAuditLogTableSQL);

    hilog.info(DatabaseManager.DOMAIN, DatabaseManager.TAG, 'Database tables created successfully');
  }

  /**
   * 将ResultSet转换为KeyInfo对象
   */
  private resultSetToKeyInfo(resultSet: relationalStore.ResultSet): KeyInfo {
    return new KeyInfo({
      keyId: resultSet.getString(resultSet.getColumnIndex('key_id')),
      keyName: resultSet.getString(resultSet.getColumnIndex('key_name')),
      keyType: resultSet.getLong(resultSet.getColumnIndex('key_type')) as KeyType,
      algorithm: resultSet.getLong(resultSet.getColumnIndex('algorithm')) as CryptoAlgorithm,
      keyLength: resultSet.getLong(resultSet.getColumnIndex('key_length')),
      status: resultSet.getString(resultSet.getColumnIndex('status')) as KeyStatus,
      createdTime: new Date(resultSet.getLong(resultSet.getColumnIndex('created_time'))),
      effectiveTime: new Date(resultSet.getLong(resultSet.getColumnIndex('effective_time'))),
      expiryTime: new Date(resultSet.getLong(resultSet.getColumnIndex('expiry_time'))),
      createdBy: resultSet.getString(resultSet.getColumnIndex('created_by')),
      clientUnit: resultSet.getString(resultSet.getColumnIndex('client_unit')),
      purpose: resultSet.getString(resultSet.getColumnIndex('purpose')),
      keyValue: resultSet.getString(resultSet.getColumnIndex('key_value')),
      fingerprint: resultSet.getString(resultSet.getColumnIndex('fingerprint')),
      version: resultSet.getLong(resultSet.getColumnIndex('version')),
      parentKeyId: resultSet.getString(resultSet.getColumnIndex('parent_key_id')),
      usageCount: resultSet.getLong(resultSet.getColumnIndex('usage_count')),
      maxUsageCount: resultSet.getLong(resultSet.getColumnIndex('max_usage_count')),
      lastUsedTime: resultSet.getLong(resultSet.getColumnIndex('last_used_time')) ?
        new Date(resultSet.getLong(resultSet.getColumnIndex('last_used_time'))) : undefined,
      remarks: resultSet.getString(resultSet.getColumnIndex('remarks')),
      exportable: resultSet.getLong(resultSet.getColumnIndex('exportable')) === 1,
      tags: JSON.parse(resultSet.getString(resultSet.getColumnIndex('tags')) || '[]'),
      metadata: JSON.parse(resultSet.getString(resultSet.getColumnIndex('metadata')) || '{}')
    });
  }
}
