/*
 * filter_integration.c
 *
 * Cryptosystem Linux 文件过滤器集成实现
 * 将file_filter与具体操作连接起来
 */

#include "file_filter.h"
#include "filter_operations.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <pthread.h>
#include <unistd.h>
#include <fcntl.h>
#include <errno.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <dlfcn.h>
#include <limits.h>

// 原始文件操作函数指针
static int (*original_open)(const char *pathname, int flags, mode_t mode) = NULL;
static int (*original_close)(int fd) = NULL;
static ssize_t (*original_read)(int fd, void *buf, size_t count) = NULL;
static ssize_t (*original_write)(int fd, const void *buf, size_t count) = NULL;
static off_t (*original_lseek)(int fd, off_t offset, int whence) = NULL;
static int (*original_stat)(const char *pathname, struct stat *statbuf) = NULL;
static int (*original_unlink)(const char *pathname) = NULL;
static int (*original_rename)(const char *oldpath, const char *newpath) = NULL;

// 初始化标志
static int is_initialized = 0;
static pthread_mutex_t init_lock = PTHREAD_MUTEX_INITIALIZER;

// 过滤状态
static int is_filtering_enabled = 0;

// 过滤回调结构
typedef struct {
    filter_callback callback;
    void *context;
    uint32_t operation_mask;
} callback_entry;

// 回调数组
#define MAX_CALLBACKS 32
static callback_entry callbacks[MAX_CALLBACKS];
static int callback_count = 0;
static pthread_mutex_t callback_lock = PTHREAD_MUTEX_INITIALIZER;

// 保存原始函数指针
static int save_original_functions() {
    // 使用RTLD_NEXT获取下一个符号
    void *handle = RTLD_DEFAULT;
    
    original_open = dlsym(handle, "open");
    original_close = dlsym(handle, "close");
    original_read = dlsym(handle, "read");
    original_write = dlsym(handle, "write");
    original_lseek = dlsym(handle, "lseek");
    original_stat = dlsym(handle, "stat");
    original_unlink = dlsym(handle, "unlink");
    original_rename = dlsym(handle, "rename");
    
    if (!original_open || !original_close || !original_read || !original_write ||
        !original_lseek || !original_stat || !original_unlink || !original_rename) {
        return -1;
    }
    
    return 0;
}

// 触发注册的回调函数
static int trigger_callbacks(filter_operation operation, const char *path) {
    // 如果过滤未启用，直接返回
    if (!is_filtering_enabled) {
        return 0;
    }
    
    pthread_mutex_lock(&callback_lock);
    
    for (int i = 0; i < callback_count; i++) {
        // 检查回调是否匹配此操作类型
        if (callbacks[i].operation_mask & (1 << (operation - 1))) {
            int result = callbacks[i].callback(operation, path, callbacks[i].context);
            if (result != 0) {
                pthread_mutex_unlock(&callback_lock);
                return result;
            }
        }
    }
    
    pthread_mutex_unlock(&callback_lock);
    return 0;
}

// 初始化集成模块
static int integration_init(void) {
    pthread_mutex_lock(&init_lock);
    
    if (is_initialized) {
        pthread_mutex_unlock(&init_lock);
        return 0;
    }
    
    // 保存原始函数指针
    if (save_original_functions() != 0) {
        pthread_mutex_unlock(&init_lock);
        return -1;
    }
    
    // 初始化过滤操作模块
    if (filter_operations_init() != 0) {
        pthread_mutex_unlock(&init_lock);
        return -1;
    }
    
    // 清空回调数组
    memset(callbacks, 0, sizeof(callbacks));
    callback_count = 0;
    
    is_initialized = 1;
    pthread_mutex_unlock(&init_lock);
    
    return 0;
}

// 集成模块清理
static void integration_cleanup(void) {
    pthread_mutex_lock(&init_lock);
    
    if (!is_initialized) {
        pthread_mutex_unlock(&init_lock);
        return;
    }
    
    // 停用过滤
    is_filtering_enabled = 0;
    
    // 清理过滤操作模块
    filter_operations_cleanup();
    
    // 清空回调数组
    pthread_mutex_lock(&callback_lock);
    memset(callbacks, 0, sizeof(callbacks));
    callback_count = 0;
    pthread_mutex_unlock(&callback_lock);
    
    is_initialized = 0;
    pthread_mutex_unlock(&init_lock);
}

// file_filter.h API实现

filter_status filter_init(const filter_options *options) {
    // 初始化集成模块
    if (integration_init() != 0) {
        return FILTER_STATUS_ERROR;
    }
    
    // 启用过滤
    is_filtering_enabled = 1;
    
    return FILTER_STATUS_SUCCESS;
}

void filter_cleanup(void) {
    // 清理集成模块
    integration_cleanup();
}

filter_status filter_register_callback(
    uint32_t operation_mask,
    filter_callback callback,
    void *context
) {
    if (!callback || !is_initialized) {
        return FILTER_STATUS_INVALID_PARAM;
    }
    
    pthread_mutex_lock(&callback_lock);
    
    if (callback_count >= MAX_CALLBACKS) {
        pthread_mutex_unlock(&callback_lock);
        return FILTER_STATUS_ERROR;
    }
    
    callbacks[callback_count].callback = callback;
    callbacks[callback_count].context = context;
    callbacks[callback_count].operation_mask = operation_mask;
    callback_count++;
    
    pthread_mutex_unlock(&callback_lock);
    return FILTER_STATUS_SUCCESS;
}

// 拦截的文件操作函数实现

int open(const char *pathname, int flags, ...) {
    mode_t mode = 0;
    
    // 获取模式参数（如果需要）
    if (flags & O_CREAT) {
        va_list args;
        va_start(args, flags);
        mode = va_arg(args, mode_t);
        va_end(args);
    }
    
    // 如果未初始化或未启用过滤，使用原始函数
    if (!is_initialized || !is_filtering_enabled) {
        return original_open(pathname, flags, mode);
    }
    
    // 触发回调
    int result = trigger_callbacks(FILTER_OPERATION_CREATE, pathname);
    if (result != 0) {
        errno = EACCES;
        return -1;
    }
    
    // 调用过滤操作
    return filter_on_open(pathname, flags, mode);
}

int close(int fd) {
    // 如果未初始化或未启用过滤，使用原始函数
    if (!is_initialized || !is_filtering_enabled) {
        return original_close(fd);
    }
    
    // 调用过滤操作
    return filter_on_close(fd);
}

ssize_t read(int fd, void *buf, size_t count) {
    // 如果未初始化或未启用过滤，使用原始函数
    if (!is_initialized || !is_filtering_enabled) {
        return original_read(fd, buf, count);
    }
    
    // 我们需要获取文件路径来触发回调
    char path[PATH_MAX];
    char proc_path[64];
    
    snprintf(proc_path, sizeof(proc_path), "/proc/self/fd/%d", fd);
    ssize_t path_len = readlink(proc_path, path, sizeof(path) - 1);
    
    if (path_len > 0) {
        path[path_len] = '\0';
        
        // 触发回调
        int result = trigger_callbacks(FILTER_OPERATION_READ, path);
        if (result != 0) {
            errno = EACCES;
            return -1;
        }
    }
    
    // 调用过滤操作
    return filter_on_read(fd, buf, count);
}

ssize_t write(int fd, const void *buf, size_t count) {
    // 如果未初始化或未启用过滤，使用原始函数
    if (!is_initialized || !is_filtering_enabled) {
        return original_write(fd, buf, count);
    }
    
    // 我们需要获取文件路径来触发回调
    char path[PATH_MAX];
    char proc_path[64];
    
    snprintf(proc_path, sizeof(proc_path), "/proc/self/fd/%d", fd);
    ssize_t path_len = readlink(proc_path, path, sizeof(path) - 1);
    
    if (path_len > 0) {
        path[path_len] = '\0';
        
        // 触发回调
        int result = trigger_callbacks(FILTER_OPERATION_WRITE, path);
        if (result != 0) {
            errno = EACCES;
            return -1;
        }
    }
    
    // 调用过滤操作
    return filter_on_write(fd, buf, count);
}

off_t lseek(int fd, off_t offset, int whence) {
    // 如果未初始化或未启用过滤，使用原始函数
    if (!is_initialized || !is_filtering_enabled) {
        return original_lseek(fd, offset, whence);
    }
    
    // 调用过滤操作
    return filter_on_lseek(fd, offset, whence);
}

int stat(const char *pathname, struct stat *statbuf) {
    // 如果未初始化或未启用过滤，使用原始函数
    if (!is_initialized || !is_filtering_enabled) {
        return original_stat(pathname, statbuf);
    }
    
    // 触发回调
    int result = trigger_callbacks(FILTER_OPERATION_ATTRIBUTE, pathname);
    if (result != 0) {
        errno = EACCES;
        return -1;
    }
    
    // 调用过滤操作
    return filter_on_stat(pathname, statbuf);
}

int unlink(const char *pathname) {
    // 如果未初始化或未启用过滤，使用原始函数
    if (!is_initialized || !is_filtering_enabled) {
        return original_unlink(pathname);
    }
    
    // 触发回调
    int result = trigger_callbacks(FILTER_OPERATION_DELETE, pathname);
    if (result != 0) {
        errno = EACCES;
        return -1;
    }
    
    // 调用过滤操作
    return filter_on_unlink(pathname);
}

int rename(const char *oldpath, const char *newpath) {
    // 如果未初始化或未启用过滤，使用原始函数
    if (!is_initialized || !is_filtering_enabled) {
        return original_rename(oldpath, newpath);
    }
    
    // 触发回调 - 这可以看作是先删除后创建
    int result = trigger_callbacks(FILTER_OPERATION_DELETE, oldpath);
    if (result != 0) {
        errno = EACCES;
        return -1;
    }
    
    result = trigger_callbacks(FILTER_OPERATION_CREATE, newpath);
    if (result != 0) {
        errno = EACCES;
        return -1;
    }
    
    // 调用过滤操作
    return filter_on_rename(oldpath, newpath);
} 