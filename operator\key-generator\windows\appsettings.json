{"ConnectionStrings": {"DefaultConnection": "Host=***********;Port=5432;Database=cryptosystem;Username=crypto;Password=********;", "MySqlConnection": "Server=***********;Port=3306;Database=cryptosystem;Uid=crypto;Pwd=********;CharSet=utf8mb4;", "SqlServerConnection": "Server=***********;Database=cryptosystem;User Id=crypto;Password=********;TrustServerCertificate=true;"}, "DatabaseSettings": {"DatabaseType": "PostgreSQL", "UseInMemoryMode": false, "CommandTimeout": 30, "EnableRetry": true, "MaxRetryCount": 3, "RetryDelay": "00:00:05", "EnableSensitiveDataLogging": false}, "CryptoSettings": {"DefaultAlgorithm": "AES256", "EnableNationalCrypto": true, "KeyEncryptionKey": "YOUR_KEY_ENCRYPTION_KEY_HERE", "DefaultKeyLength": 256, "KeyExpiryDays": 365, "MaxKeyUsageCount": 1000000, "EnableKeyRotation": true, "KeyRotationWarningDays": 30}, "SecuritySettings": {"EnableAuditLog": true, "EnableKeyUsageLog": true, "MaxLoginAttempts": 5, "SessionTimeoutMinutes": 30, "PasswordMinLength": 8, "EnablePasswordComplexity": true, "EnableMultiFactorAuth": false, "EnableIpWhitelist": false, "IpWhitelist": []}, "BackupSettings": {"EnableAutoBackup": true, "BackupIntervalHours": 24, "BackupLocation": "./backup/keys", "MaxBackupCount": 30, "EnableBackupEncryption": true, "BackupEncryptionKey": "YOUR_BACKUP_ENCRYPTION_KEY_HERE"}, "DistributionSettings": {"MaxRetryCount": 3, "TimeoutHours": 24, "DefaultMethod": "ONLINE", "EnableOfflineDistribution": true, "EnableEmailDistribution": false, "EmailSettings": {"SmtpServer": "", "SmtpPort": 587, "Username": "", "Password": "", "EnableSsl": true, "FromAddress": "<EMAIL>", "FromName": "密钥管理系统"}}, "ApplicationSettings": {"ApplicationName": "企业密钥生成器", "Version": "1.4.0", "Environment": "Development", "EnableDemoMode": true, "DemoClientId": "CLIENT_001", "MaxConcurrentOperations": 10, "OperationTimeoutSeconds": 300}, "LogSettings": {"LogLevel": "Information", "EnableConsoleLogging": true, "EnableFileLogging": true, "LogFilePath": "./logs/keygen-{Date}.log", "MaxFileSizeMB": 10, "MaxRetainedFiles": 30, "AuditLogRetentionDays": 2555, "KeyUsageLogRetentionDays": 1095}, "UISettings": {"Theme": "Light", "Language": "zh-CN", "EnableAnimations": true, "AutoRefreshIntervalSeconds": 30, "PageSize": 20, "EnableTooltips": true, "ShowAdvancedOptions": false}, "ValidationSettings": {"EnableKeyStrengthValidation": true, "MinKeyStrengthScore": 80, "EnableClientValidation": true, "MaxClientNameLength": 200, "MaxDescriptionLength": 1000, "AllowedFileExtensions": [".key", ".pem", ".p12", ".pfx", ".jks"], "MaxFileSize": 10485760}, "PerformanceSettings": {"EnableCaching": true, "CacheExpirationMinutes": 30, "MaxCacheSize": 100, "EnableCompression": true, "ThreadPoolMinThreads": 4, "ThreadPoolMaxThreads": 16}, "DebugSettings": {"EnableDebugMode": false, "EnableDetailedErrors": false, "EnableSqlLogging": false, "EnablePerformanceCounters": true, "MockExternalServices": false}, "IntegrationSettings": {"EnableSystemManagerIntegration": true, "SystemManagerEndpoint": "http://***********:8080", "EnableClientAgentCommunication": true, "ClientAgentPort": 9090, "EnableHardwareSecurityModule": false, "HSMSettings": {"Provider": "", "ConnectionString": "", "SlotId": 0}}, "ComplianceSettings": {"EnableComplianceMode": false, "ComplianceStandard": "ISO27001", "RequireApprovalForKeyGeneration": false, "RequireDualApproval": false, "EnableKeyEscrow": false, "EscrowSettings": {"EscrowAgents": [], "RequiredAgentCount": 2}}}