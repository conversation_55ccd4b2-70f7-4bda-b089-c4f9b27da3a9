using Microsoft.EntityFrameworkCore;
using Serilog;
using CryptoSystem.SystemManager.Data;
using CryptoSystem.SystemManager.Services;
using CryptoSystem.SystemManager.ViewModels;
using CryptoSystem.SystemManager.Views;

namespace CryptoSystem.SystemManager
{
    /// <summary>
    /// 应用程序启动配置
    /// </summary>
    public static class Startup
    {
        public static IHost CreateHost(string[] args)
        {
            var builder = Host.CreateDefaultBuilder(args);
            
            // 配置Serilog
            builder.UseSerilog();
            
            builder.ConfigureServices((context, services) =>
            {
                ConfigureDatabase(services, context.Configuration);
                ConfigureServices(services);
                ConfigureViewModels(services);
                ConfigureViews(services);
            });

            return builder.Build();
        }

        private static void ConfigureDatabase(IServiceCollection services, IConfiguration configuration)
        {
            var connectionString = configuration.GetConnectionString("DefaultConnection");
            
            services.AddDbContext<SystemManagerDbContext>(options =>
            {
                options.UseNpgsql(connectionString, npgsqlOptions =>
                {
                    npgsqlOptions.CommandTimeout(60);
                    npgsqlOptions.EnableRetryOnFailure(maxRetryCount: 3);
                })
                .EnableSensitiveDataLogging(false)
                .EnableDetailedErrors(false);
            });
        }

        private static void ConfigureServices(IServiceCollection services)
        {
            // 注册核心业务服务 - 使用我们创建的完整实现
            services.AddScoped<IUserService, UserService>();
            services.AddScoped<IDeviceService, DeviceService>();
            services.AddScoped<IPolicyService, PolicyService>();
            
            // 注册配置服务
            services.AddSingleton<IConfigurationService, ConfigurationService>();
            
            // 注册其他服务
            services.AddHttpClient("SystemManager", client =>
            {
                client.Timeout = TimeSpan.FromSeconds(30);
                client.DefaultRequestHeaders.Add("User-Agent", "CryptoSystem-SystemManager/1.4.0");
            });
            
            services.AddMemoryCache(options =>
            {
                options.SizeLimit = 100;
                options.CompactionPercentage = 0.25;
            });

            // 注册后台服务
            services.AddHostedService<DeviceHeartbeatService>();
        }

        private static void ConfigureViewModels(IServiceCollection services)
        {
            // 注册ViewModels
            services.AddTransient<MainWindowViewModel>();
            services.AddTransient<UserManagementViewModel>();
            services.AddTransient<DashboardViewModel>();
            services.AddTransient<DeviceManagementViewModel>();
            services.AddTransient<PolicyManagementViewModel>();
            services.AddTransient<KeyManagementViewModel>();
            services.AddTransient<AuditLogViewModel>();
            services.AddTransient<SettingsViewModel>();
        }

        private static void ConfigureViews(IServiceCollection services)
        {
            // 注册Views
            services.AddTransient<MainWindow>();
        }
    }

    /// <summary>
    /// 配置服务实现
    /// </summary>
    public class ConfigurationService : IConfigurationService
    {
        private readonly IConfiguration _configuration;

        public ConfigurationService(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public string? GetString(string key, string? defaultValue = null) => _configuration[key] ?? defaultValue;
        public int GetInt(string key, int defaultValue = 0) => _configuration.GetValue<int>(key, defaultValue);
        public bool GetBool(string key, bool defaultValue = false) => _configuration.GetValue<bool>(key, defaultValue);
        public double? GetDouble(string key, double? defaultValue = null) => _configuration.GetValue<double?>(key, defaultValue);
        public T? GetValue<T>(string key, T? defaultValue = default) => _configuration.GetValue<T>(key, defaultValue);

        public void Set(string key, object value) => Log.Warning("试图修改只读配置项：{Key} = {Value}", key, value);
        public void SetString(string key, string value) => Set(key, value);
        public void SetInt(string key, int value) => Set(key, value);
        public void SetBool(string key, bool value) => Set(key, value);
        public void SetDouble(string key, double value) => Set(key, value);
        public bool HasKey(string key) => _configuration[key] != null;
        public void Remove(string key) => Log.Warning("试图删除只读配置项：{Key}", key);
        public void Save() => Log.Warning("配置服务不支持保存操作");
        public void Reload() => (_configuration as IConfigurationRoot)?.Reload();
    }

    /// <summary>
    /// 后台服务：设备心跳检测
    /// </summary>
    public class DeviceHeartbeatService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<DeviceHeartbeatService> _logger;

        public DeviceHeartbeatService(IServiceProvider serviceProvider, ILogger<DeviceHeartbeatService> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    using var scope = _serviceProvider.CreateScope();
                    var deviceService = scope.ServiceProvider.GetRequiredService<IDeviceService>();
                    
                    // 检查离线设备
                    var offlineDevices = await deviceService.GetOfflineDevicesAsync(1);
                    foreach (var device in offlineDevices)
                    {
                        await deviceService.SetDeviceStatusAsync(device.DeviceId, DeviceStatus.Offline, "System");
                    }

                    _logger.LogInformation("设备心跳检测完成，发现 {Count} 个离线设备", offlineDevices.Count());
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "设备心跳检测异常");
                }

                // 每5分钟检查一次
                await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken);
            }
        }
    }
} 