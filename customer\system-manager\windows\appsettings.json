{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.EntityFrameworkCore": "Warning"}}, "ConnectionStrings": {"DefaultConnection": "Host=***********;Port=5432;Database=cryptosystem;Username=crypto;Password=********;", "BackupConnection": "Host=***********;Port=5432;Database=cryptosystem;Username=crypto;Password=********;"}, "SystemManager": {"ApplicationName": "CryptoSystem 系统管理器", "Version": "1.4.0", "CompanyName": "CryptoSystem", "SupportEmail": "<EMAIL>", "UpdateUrl": "https://update.cryptosystem.com/systemmanager", "LicenseServerUrl": "https://license.cryptosystem.com"}, "UI": {"Theme": "Light", "PrimaryColor": "Indigo", "SecondaryColor": "<PERSON><PERSON>", "Language": "zh-CN", "AutoSave": true, "AutoSaveInterval": 30, "RememberWindowState": true, "ShowWelcomeScreen": true}, "Security": {"EnableAuthentication": true, "AuthenticationMode": "Database", "LdapServer": "", "LdapDomain": "", "SessionTimeoutMinutes": 480, "MaxLoginAttempts": 5, "LockoutDurationMinutes": 30, "PasswordPolicy": {"MinLength": 8, "RequireUppercase": true, "RequireLowercase": true, "RequireDigits": true, "RequireSpecialChars": true, "MaxAge": 90}, "EnableAuditLog": true, "EnableDataEncryption": true, "EncryptionAlgorithm": "SM4"}, "Database": {"Provider": "PostgreSQL", "ConnectionTimeout": 30, "CommandTimeout": 60, "MaxRetryCount": 3, "EnableSensitiveDataLogging": false, "EnableDetailedErrors": false, "MigrationsAssembly": "CryptoSystem.SystemManager", "BackupSettings": {"EnableAutoBackup": true, "BackupInterval": "Daily", "RetentionDays": 30, "BackupLocation": "Backups"}}, "Cache": {"DefaultExpiration": "01:00:00", "MaxSize": "100MB", "EnableCompression": true, "EnableEncryption": true}, "Notification": {"EnableEmailNotification": true, "SmtpServer": "smtp.company.com", "SmtpPort": 587, "SmtpUsername": "", "SmtpPassword": "", "SmtpEnableSsl": true, "FromEmail": "<EMAIL>", "FromName": "CryptoSystem 系统管理器", "EnableSystemNotification": true, "NotificationLevels": ["Warning", "Error", "Critical"]}, "DeviceManagement": {"HeartbeatInterval": 300, "OfflineThreshold": 900, "MaxOfflineHours": 24, "EnableDeviceRegistration": true, "RequireAdminApproval": true, "EnableRemoteControl": true, "EnableDeviceGrouping": true}, "PolicyManagement": {"EnablePolicyInheritance": true, "MaxPolicyDepth": 10, "EnableScheduledDeployment": true, "DeploymentRetryCount": 3, "DeploymentTimeout": 300, "EnableRollback": true, "BackupBeforeDeployment": true}, "AuditLog": {"RetentionDays": 365, "EnableCompression": true, "EnableEncryption": true, "LogLevel": "Information", "LogCategories": ["UserLogin", "UserLogout", "PolicyChange", "<PERSON>ceChange", "KeyOperation", "SystemOperation", "SecurityEvent"], "ArchiveSettings": {"EnableAutoArchive": true, "ArchiveAfterDays": 90, "ArchiveLocation": "Archives"}}, "Backup": {"EnableAutoBackup": true, "BackupLocation": "%LocalApplicationData%\\CryptoSystem\\SystemManager\\Backups", "BackupSchedule": "0 2 * * *", "RetentionDays": 30, "EnableCompression": true, "EnableEncryption": true, "BackupTypes": ["Database", "Configuration", "Logs"]}, "Integration": {"ActiveDirectory": {"Enabled": false, "Server": "", "Domain": "", "Username": "", "Password": "", "SyncInterval": 3600, "SyncAttributes": ["DisplayName", "Email", "Department", "Title"]}, "KeyGenerator": {"Enabled": true, "ServiceUrl": "http://***********:8080/api", "ApiKey": "", "EnableAutoSync": true, "SyncInterval": 300}, "ClientAgent": {"Enabled": true, "CommunicationPort": 9090, "EnableTLS": true, "CertificatePath": "", "MaxConcurrentConnections": 1000}}, "Performance": {"MaxConcurrentOperations": 50, "DatabaseConnectionPoolSize": 20, "CacheSize": "100MB", "EnableQueryOptimization": true, "EnableIndexing": true, "LogSlowQueries": true, "SlowQueryThreshold": 5000}, "Monitoring": {"EnablePerformanceMonitoring": true, "MetricsInterval": 60, "EnableHealthChecks": true, "HealthCheckInterval": 300, "EnableAlerts": true, "AlertThresholds": {"CpuUsage": 80, "MemoryUsage": 85, "DiskUsage": 90, "DatabaseConnections": 80}}, "Licensing": {"LicenseType": "Enterprise", "MaxUsers": 1000, "MaxDevices": 5000, "ExpirationDate": "2025-12-31", "Features": ["UserManagement", "DeviceManagement", "PolicyManagement", "AuditLog", "Backup", "ActiveDirectoryIntegration", "ReportGeneration"]}}