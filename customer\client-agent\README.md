# CryptoSystem 客户端模块

## 概述

CryptoSystem 客户端模块是企业级文档加密与安全管理系统的终端组件，负责文件的本地监控、加密/解密、与服务端通信以及策略执行。客户端模块支持 Windows、macOS 和 Linux 操作系统，保证了跨平台的文档安全性。

## 架构设计

客户端模块采用模块化设计，核心组件包括:

1. **核心引擎 (Core Engine)**: 负责协调各模块工作，处理文件事件，执行加密解密操作。
2. **文件系统驱动 (FS Driver)**: 监控文件系统变化，捕获文件创建、修改、删除和重命名事件。
3. **密钥管理器 (Key Manager)**: 负责密钥获取、缓存、轮换和验证。
4. **策略管理器 (Policy Manager)**: 执行访问控制和加密策略。
5. **系统钩子 (System Hooks)**: 与操作系统交互，实现屏幕水印、外设控制等功能。

## 目录结构

- `common/` - 各平台共享的核心代码和接口定义
- `windows/` - Windows 平台特定实现
- `macos/` - macOS 平台特定实现
- `linux/` - Linux 平台特定实现
- `samples/` - 示例代码和测试案例

## 系统要求

- **Windows**: Windows 10/11 (x64)
- **macOS**: macOS 10.15 (Catalina) 或更高版本
- **Linux**: 支持内核 5.0 及以上的主流发行版

## 编译指南

### Windows 平台

1. 确保已安装 Visual Studio 2019 或更高版本，以及 CMake 3.15+
2. 打开命令提示符，导航到项目目录
3. 执行以下命令:

```bash
mkdir build
cd build
cmake ..
cmake --build . --config Release
```

### macOS 平台

1. 确保已安装 Xcode 命令行工具和 CMake
2. 打开终端，导航到项目目录
3. 执行以下命令:

```bash
mkdir build
cd build
cmake ..
make
```

### Linux 平台

1. 安装必要的开发库:

```bash
sudo apt-get install build-essential cmake libssl-dev
```

2. 构建项目:

```bash
mkdir build
cd build
cmake ..
make
```

## 主要功能

- **透明加密/解密**: 对最终用户透明的文件加密解密操作
- **零知识加密**: 本地实现零知识加密，服务端无法访问明文内容
- **文件实时监控**: 实时监控文件系统变化，确保文件实时受保护
- **离线模式支持**: 无需连接服务器也能保护已加密文档
- **屏幕水印**: 显示用户标识的屏幕水印，防止截屏泄密
- **外设控制**: 根据策略控制外部设备访问权限
- **安全外发包**: 支持创建加密的外发文档包

## 排错指南

常见问题及解决方案:

1. **无法安装驱动**: 确保系统开启了测试模式或驱动已正确签名
2. **文件无法加密**: 检查用户权限和文件是否被其他进程锁定
3. **与服务器连接失败**: 验证网络连接和服务器地址配置
4. **密钥无法获取**: 确认用户已正确认证，检查密钥服务可用性

## 协作开发

请参考项目根目录的 `CONTRIBUTING.md` 文件了解代码贡献流程和规范。
