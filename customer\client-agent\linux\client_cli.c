/*
 * client_cli.c
 *
 * Cryptosystem Linux 客户端命令行工具
 * 提供用户交互界面，用于设备注册和加密控制
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <getopt.h>
#include <pwd.h>
#include <sys/types.h>
#include "driver/client_config.h"
#include "driver/key_sync_service.h"
#include "driver/policy_manager.h"

// 默认配置目录
#define DEFAULT_CONFIG_DIR "/.cryptosystem"

// 命令
typedef enum {
    CMD_HELP,           // 显示帮助
    CMD_STATUS,         // 显示状态
    CMD_REGISTER,       // 注册设备
    CMD_ENABLE,         // 启用加密
    CMD_DISABLE,        // 禁用加密
    CMD_ADD_PATH,       // 添加加密路径
    CMD_RM_PATH,        // 删除加密路径
    CMD_LIST_PATHS,     // 列出加密路径
    CMD_SYNC,           // 同步策略
    CMD_UNKNOWN         // 未知命令
} command_type;

// 命令行选项
static struct option long_options[] = {
    {"help",      no_argument,       0, 'h'},
    {"status",    no_argument,       0, 's'},
    {"register",  no_argument,       0, 'r'},
    {"enable",    no_argument,       0, 'e'},
    {"disable",   no_argument,       0, 'd'},
    {"add-path",  required_argument, 0, 'a'},
    {"rm-path",   required_argument, 0, 'm'},
    {"list-paths",no_argument,       0, 'l'},
    {"sync",      no_argument,       0, 'y'},
    {"server",    required_argument, 0, 'S'},
    {"org",       required_argument, 0, 'O'},
    {"email",     required_argument, 0, 'E'},
    {"name",      required_argument, 0, 'N'},
    {"config-dir",required_argument, 0, 'c'},
    {0, 0, 0, 0}
};

// 显示帮助
static void show_help(const char *program_name) {
    printf("Usage: %s [OPTION]...\n", program_name);
    printf("Cryptosystem Linux 客户端命令行工具\n\n");
    printf("命令选项:\n");
    printf("  -h, --help              显示帮助信息\n");
    printf("  -s, --status            显示当前状态\n");
    printf("  -r, --register          注册设备\n");
    printf("  -e, --enable            启用加密\n");
    printf("  -d, --disable           禁用加密\n");
    printf("  -a, --add-path=PATH     添加加密路径\n");
    printf("  -m, --rm-path=PATH      删除加密路径\n");
    printf("  -l, --list-paths        列出加密路径\n");
    printf("  -y, --sync              同步策略\n");
    printf("\n");
    printf("注册参数:\n");
    printf("  -S, --server=URL        服务器URL\n");
    printf("  -O, --org=ID            组织ID\n");
    printf("  -E, --email=EMAIL       用户邮箱\n");
    printf("  -N, --name=NAME         设备名称\n");
    printf("\n");
    printf("其他选项:\n");
    printf("  -c, --config-dir=DIR    配置目录路径\n");
    printf("\n");
    printf("示例:\n");
    printf("  %s --status                     显示当前状态\n", program_name);
    printf("  %s --register --server=https://example.com --org=org1 --email=<EMAIL> --name=mypc\n", program_name);
    printf("                                     注册设备\n");
    printf("  %s --add-path=/home/<USER>/docs      添加加密路径\n", program_name);
    printf("  %s --enable                        启用加密\n", program_name);
    printf("\n");
}

// 显示设备状态
static void show_status() {
    char device_id[64] = {0};
    char server_url[1024] = {0};
    int enabled = 0;
    const char *status_str = "未知";
    
    config_get_device_id(device_id, sizeof(device_id));
    config_get_server_url(server_url, sizeof(server_url));
    enabled = config_get_encryption_enabled();
    
    device_status status = config_check_device_status();
    switch (status) {
        case DEVICE_STATUS_REGISTERED:
            status_str = "已注册";
            break;
        case DEVICE_STATUS_PENDING:
            status_str = "等待批准";
            break;
        case DEVICE_STATUS_DISABLED:
            status_str = "已禁用";
            break;
        case DEVICE_STATUS_UNREGISTERED:
            status_str = "未注册";
            break;
    }
    
    printf("设备状态:\n");
    printf("  设备ID: %s\n", device_id);
    printf("  服务器: %s\n", server_url[0] ? server_url : "未设置");
    printf("  注册状态: %s\n", status_str);
    printf("  加密状态: %s\n", enabled ? "已启用" : "已禁用");
    
    // 显示策略数量
    if (status == DEVICE_STATUS_REGISTERED && server_url[0]) {
        size_t policy_count = policy_get_count();
        printf("  策略数量: %zu\n", policy_count);
    }
    
    // 显示加密路径
    char *paths[100];
    size_t path_count = 0;
    
    if (config_get_encryption_paths(paths, 100, &path_count) == CONFIG_STATUS_OK) {
        printf("\n加密路径 (%zu):\n", path_count);
        for (size_t i = 0; i < path_count; i++) {
            printf("  %s\n", paths[i]);
            free(paths[i]);
        }
    }
}

// 注册设备
static int register_device(const char *server_url, const char *org_id, 
                         const char *user_email, const char *device_name) {
    if (!server_url || !org_id || !user_email || !device_name) {
        printf("错误: 缺少必要参数\n");
        printf("请使用 --server, --org, --email 和 --name 选项指定所有必需参数\n");
        return 1;
    }
    
    printf("正在注册设备...\n");
    
    config_status status = config_register_device(server_url, org_id, user_email, device_name);
    
    if (status == CONFIG_STATUS_OK) {
        device_status dev_status = config_check_device_status();
        
        if (dev_status == DEVICE_STATUS_REGISTERED) {
            printf("设备注册成功!\n");
            
            // 初始化密钥同步服务
            char device_id[64] = {0};
            char device_key[128] = {0};
            
            config_get_device_id(device_id, sizeof(device_id));
            config_get_device_key(device_key, sizeof(device_key));
            
            key_sync_init(server_url, device_id, device_key);
            policy_init();
            policy_reload();
            
            printf("已同步策略和密钥\n");
        } else if (dev_status == DEVICE_STATUS_PENDING) {
            printf("设备注册已提交，等待管理员批准\n");
        } else {
            printf("设备注册失败\n");
            return 1;
        }
    } else {
        printf("设备注册失败，错误代码: %d\n", status);
        return 1;
    }
    
    return 0;
}

// 获取默认配置目录
static char* get_default_config_dir() {
    const char *home_dir = getenv("HOME");
    
    if (!home_dir) {
        struct passwd *pw = getpwuid(getuid());
        if (pw) {
            home_dir = pw->pw_dir;
        }
    }
    
    if (!home_dir) {
        return NULL;
    }
    
    char *config_dir = malloc(strlen(home_dir) + strlen(DEFAULT_CONFIG_DIR) + 1);
    if (!config_dir) {
        return NULL;
    }
    
    sprintf(config_dir, "%s%s", home_dir, DEFAULT_CONFIG_DIR);
    return config_dir;
}

int main(int argc, char *argv[]) {
    command_type cmd = CMD_UNKNOWN;
    char *config_dir = NULL;
    char *add_path = NULL;
    char *rm_path = NULL;
    char *server_url = NULL;
    char *org_id = NULL;
    char *user_email = NULL;
    char *device_name = NULL;
    int ret = 0;
    
    // 解析命令行选项
    int opt, option_index = 0;
    while ((opt = getopt_long(argc, argv, "hsredlya:m:S:O:E:N:c:", 
                            long_options, &option_index)) != -1) {
        switch (opt) {
            case 'h':
                cmd = CMD_HELP;
                break;
                
            case 's':
                cmd = CMD_STATUS;
                break;
                
            case 'r':
                cmd = CMD_REGISTER;
                break;
                
            case 'e':
                cmd = CMD_ENABLE;
                break;
                
            case 'd':
                cmd = CMD_DISABLE;
                break;
                
            case 'a':
                cmd = CMD_ADD_PATH;
                add_path = optarg;
                break;
                
            case 'm':
                cmd = CMD_RM_PATH;
                rm_path = optarg;
                break;
                
            case 'l':
                cmd = CMD_LIST_PATHS;
                break;
                
            case 'y':
                cmd = CMD_SYNC;
                break;
                
            case 'S':
                server_url = optarg;
                break;
                
            case 'O':
                org_id = optarg;
                break;
                
            case 'E':
                user_email = optarg;
                break;
                
            case 'N':
                device_name = optarg;
                break;
                
            case 'c':
                config_dir = optarg;
                break;
                
            case '?':
                // getopt_long 已打印错误信息
                return 1;
        }
    }
    
    // 如果没有指定命令，显示帮助
    if (cmd == CMD_UNKNOWN) {
        show_help(argv[0]);
        return 0;
    }
    
    // 如果命令是显示帮助
    if (cmd == CMD_HELP) {
        show_help(argv[0]);
        return 0;
    }
    
    // 获取配置目录
    int free_config_dir = 0;
    if (!config_dir) {
        config_dir = get_default_config_dir();
        free_config_dir = 1;
        
        if (!config_dir) {
            printf("错误: 无法确定默认配置目录\n");
            return 1;
        }
    }
    
    // 初始化配置
    if (config_init(config_dir) != CONFIG_STATUS_OK) {
        printf("错误: 无法初始化配置\n");
        if (free_config_dir) {
            free(config_dir);
        }
        return 1;
    }
    
    // 执行命令
    switch (cmd) {
        case CMD_STATUS:
            show_status();
            break;
            
        case CMD_REGISTER:
            ret = register_device(server_url, org_id, user_email, device_name);
            break;
            
        case CMD_ENABLE:
            if (config_set_encryption_enabled(1) == CONFIG_STATUS_OK) {
                printf("加密已启用\n");
            } else {
                printf("无法启用加密\n");
                ret = 1;
            }
            break;
            
        case CMD_DISABLE:
            if (config_set_encryption_enabled(0) == CONFIG_STATUS_OK) {
                printf("加密已禁用\n");
            } else {
                printf("无法禁用加密\n");
                ret = 1;
            }
            break;
            
        case CMD_ADD_PATH:
            if (config_add_encryption_path(add_path) == CONFIG_STATUS_OK) {
                printf("已添加加密路径: %s\n", add_path);
            } else {
                printf("无法添加加密路径\n");
                ret = 1;
            }
            break;
            
        case CMD_RM_PATH:
            if (config_remove_encryption_path(rm_path) == CONFIG_STATUS_OK) {
                printf("已删除加密路径: %s\n", rm_path);
            } else {
                printf("无法删除加密路径 (可能不存在)\n");
                ret = 1;
            }
            break;
            
        case CMD_LIST_PATHS: {
            char *paths[100];
            size_t path_count = 0;
            
            if (config_get_encryption_paths(paths, 100, &path_count) == CONFIG_STATUS_OK) {
                printf("加密路径 (%zu):\n", path_count);
                for (size_t i = 0; i < path_count; i++) {
                    printf("  %s\n", paths[i]);
                    free(paths[i]);
                }
            } else {
                printf("无法获取加密路径\n");
                ret = 1;
            }
            break;
        }
            
        case CMD_SYNC: {
            char device_id[64] = {0};
            char device_key[128] = {0};
            char server[1024] = {0};
            
            config_get_device_id(device_id, sizeof(device_id));
            config_get_device_key(device_key, sizeof(device_key));
            config_get_server_url(server, sizeof(server));
            
            if (server[0] == '\0') {
                printf("错误: 未设置服务器URL\n");
                ret = 1;
                break;
            }
            
            if (key_sync_init(server, device_id, device_key) != SYNC_STATUS_OK) {
                printf("错误: 无法初始化密钥同步服务\n");
                ret = 1;
                break;
            }
            
            if (policy_init() != 0) {
                printf("错误: 无法初始化策略管理器\n");
                ret = 1;
                break;
            }
            
            if (policy_reload() == 0) {
                size_t count = policy_get_count();
                printf("成功同步 %zu 条策略\n", count);
            } else {
                printf("策略同步失败\n");
                ret = 1;
            }
            
            key_sync_cleanup();
            policy_cleanup();
            break;
        }
            
        default:
            show_help(argv[0]);
            break;
    }
    
    // 清理资源
    config_cleanup();
    
    if (free_config_dir) {
        free(config_dir);
    }
    
    return ret;
} 