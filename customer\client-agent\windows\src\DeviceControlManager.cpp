#include "DeviceControlManager.h"
#include <chrono>
#include <ctime>
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <regex>
#include <cfgmgr32.h>
#include <initguid.h>
#include <devguid.h>
#include <winioctl.h>

#pragma comment(lib, "cfgmgr32.lib")

// Windows消息定义
const UINT WM_DEVICE_CHANGE_NOTIFY = WM_USER + 100;

// 单例访问
DeviceControlManager& DeviceControlManager::getInstance() {
    static DeviceControlManager instance;
    return instance;
}

DeviceControlManager::DeviceControlManager() :
    m_initialized(false),
    m_hwndNotification(NULL),
    m_hDevNotify(NULL),
    m_deviceCallback(nullptr) {
    
    // 设置默认策略
    m_defaultPolicies[DeviceType::USB_STORAGE] = DeviceAccess::READ_ONLY;
    m_defaultPolicies[DeviceType::CDROM] = DeviceAccess::ALLOW;
    m_defaultPolicies[DeviceType::FLOPPY] = DeviceAccess::DENY;
    m_defaultPolicies[DeviceType::NETWORK_ADAPTER] = DeviceAccess::ALLOW;
    m_defaultPolicies[DeviceType::BLUETOOTH] = DeviceAccess::DENY;
    m_defaultPolicies[DeviceType::PRINTER] = DeviceAccess::ALLOW;
    m_defaultPolicies[DeviceType::SCANNER] = DeviceAccess::ALLOW;
    m_defaultPolicies[DeviceType::CAMERA] = DeviceAccess::ALLOW;
    m_defaultPolicies[DeviceType::AUDIO] = DeviceAccess::ALLOW;
    m_defaultPolicies[DeviceType::MOBILE_PHONE] = DeviceAccess::READ_ONLY;
    m_defaultPolicies[DeviceType::CUSTOM] = DeviceAccess::DENY;
    m_defaultPolicies[DeviceType::UNKNOWN] = DeviceAccess::DENY;
}

DeviceControlManager::~DeviceControlManager() {
    shutdown();
}

bool DeviceControlManager::initialize() {
    if (m_initialized) {
        return true;
    }
    
    // 加载策略配置
    if (!loadPolicies()) {
        // 加载失败，但继续使用默认策略
    }
    
    // 扫描当前连接的设备
    if (!scanForDevices()) {
        return false;
    }
    
    m_initialized = true;
    return true;
}

void DeviceControlManager::shutdown() {
    if (!m_initialized) {
        return;
    }
    
    // 停止设备监控
    stopMonitoring();
    
    // 保存策略
    savePolicies();
    
    // 清理设备列表
    {
        std::lock_guard<std::mutex> lock(m_devicesMutex);
        m_connectedDevices.clear();
    }
    
    m_initialized = false;
}

bool DeviceControlManager::startMonitoring(HWND hwndNotification) {
    if (!m_initialized) {
        return false;
    }
    
    if (m_hDevNotify) {
        // 已经在监控中
        return true;
    }
    
    m_hwndNotification = hwndNotification;
    
    // 注册设备通知
    DEV_BROADCAST_DEVICEINTERFACE notificationFilter = {};
    notificationFilter.dbcc_size = sizeof(notificationFilter);
    notificationFilter.dbcc_devicetype = DBT_DEVTYP_DEVICEINTERFACE;
    
    // 监控所有USB设备
    notificationFilter.dbcc_classguid = GUID_DEVINTERFACE_DISK;
    
    m_hDevNotify = RegisterDeviceNotification(
        m_hwndNotification,
        &notificationFilter,
        DEVICE_NOTIFY_WINDOW_HANDLE
    );
    
    if (!m_hDevNotify) {
        return false;
    }
    
    return true;
}

bool DeviceControlManager::stopMonitoring() {
    if (!m_hDevNotify) {
        return true; // 已经停止
    }
    
    if (!UnregisterDeviceNotification(m_hDevNotify)) {
        return false;
    }
    
    m_hDevNotify = NULL;
    m_hwndNotification = NULL;
    
    return true;
}

void DeviceControlManager::setDefaultPolicy(DeviceType type, DeviceAccess access) {
    m_defaultPolicies[type] = access;
}

void DeviceControlManager::setDevicePolicy(const std::wstring& deviceId, DeviceAccess access) {
    m_devicePolicies[deviceId] = access;
    
    // 如果设备已连接，更新其访问权限
    {
        std::lock_guard<std::mutex> lock(m_devicesMutex);
        auto it = m_connectedDevices.find(deviceId);
        if (it != m_connectedDevices.end()) {
            it->second->currentAccess = access;
        }
    }
    
    // 立即应用策略
    if (access == DeviceAccess::DENY) {
        blockDeviceAccess(deviceId, true);
    }
}

void DeviceControlManager::addToWhitelist(const std::wstring& deviceId) {
    // 检查是否已在白名单中
    if (std::find(m_whitelist.begin(), m_whitelist.end(), deviceId) != m_whitelist.end()) {
        return;
    }
    
    // 从黑名单移除（如果存在）
    removeFromBlacklist(deviceId);
    
    // 添加到白名单
    m_whitelist.push_back(deviceId);
}

void DeviceControlManager::addToBlacklist(const std::wstring& deviceId) {
    // 检查是否已在黑名单中
    if (std::find(m_blacklist.begin(), m_blacklist.end(), deviceId) != m_blacklist.end()) {
        return;
    }
    
    // 从白名单移除（如果存在）
    removeFromWhitelist(deviceId);
    
    // 添加到黑名单
    m_blacklist.push_back(deviceId);
    
    // 如果设备已连接，则阻止访问
    blockDeviceAccess(deviceId, true);
}

void DeviceControlManager::removeFromWhitelist(const std::wstring& deviceId) {
    auto it = std::find(m_whitelist.begin(), m_whitelist.end(), deviceId);
    if (it != m_whitelist.end()) {
        m_whitelist.erase(it);
    }
}

void DeviceControlManager::removeFromBlacklist(const std::wstring& deviceId) {
    auto it = std::find(m_blacklist.begin(), m_blacklist.end(), deviceId);
    if (it != m_blacklist.end()) {
        m_blacklist.erase(it);
    }
}

bool DeviceControlManager::isWhitelisted(const std::wstring& deviceId) const {
    return std::find(m_whitelist.begin(), m_whitelist.end(), deviceId) != m_whitelist.end();
}

bool DeviceControlManager::isBlacklisted(const std::wstring& deviceId) const {
    return std::find(m_blacklist.begin(), m_blacklist.end(), deviceId) != m_blacklist.end();
}

std::vector<DeviceInfo> DeviceControlManager::getConnectedDevices() const {
    std::lock_guard<std::mutex> lock(m_devicesMutex);
    
    std::vector<DeviceInfo> devices;
    devices.reserve(m_connectedDevices.size());
    
    for (const auto& pair : m_connectedDevices) {
        devices.push_back(*(pair.second));
    }
    
    return devices;
}

std::vector<DeviceInfo> DeviceControlManager::getConnectedDevicesByType(DeviceType type) const {
    std::lock_guard<std::mutex> lock(m_devicesMutex);
    
    std::vector<DeviceInfo> devices;
    
    for (const auto& pair : m_connectedDevices) {
        if (pair.second->type == type) {
            devices.push_back(*(pair.second));
        }
    }
    
    return devices;
}

std::shared_ptr<DeviceInfo> DeviceControlManager::getDeviceInfo(const std::wstring& deviceId) const {
    std::lock_guard<std::mutex> lock(m_devicesMutex);
    
    auto it = m_connectedDevices.find(deviceId);
    if (it != m_connectedDevices.end()) {
        return it->second;
    }
    
    return nullptr;
}

void DeviceControlManager::setDeviceCallback(DeviceCallbackType callback) {
    m_deviceCallback = callback;
}

bool DeviceControlManager::allowAccess(const std::wstring& deviceId, bool isWriteAccess) {
    // 获取设备信息
    auto deviceInfo = getDeviceInfo(deviceId);
    if (!deviceInfo) {
        return false; // 设备未连接
    }
    
    // 检查黑名单
    if (isBlacklisted(deviceId)) {
        logDeviceEvent(deviceId, isWriteAccess ? L"WRITE_ACCESS_DENIED_BLACKLIST" : L"READ_ACCESS_DENIED_BLACKLIST", false);
        return false;
    }
    
    // 检查白名单
    if (isWhitelisted(deviceId)) {
        logDeviceEvent(deviceId, isWriteAccess ? L"WRITE_ACCESS_ALLOWED_WHITELIST" : L"READ_ACCESS_ALLOWED_WHITELIST", true);
        return true;
    }
    
    // 检查设备特定策略
    auto it = m_devicePolicies.find(deviceId);
    if (it != m_devicePolicies.end()) {
        DeviceAccess access = it->second;
        
        if (access == DeviceAccess::DENY) {
            logDeviceEvent(deviceId, isWriteAccess ? L"WRITE_ACCESS_DENIED_POLICY" : L"READ_ACCESS_DENIED_POLICY", false);
            return false;
        }
        
        if (access == DeviceAccess::READ_ONLY && isWriteAccess) {
            logDeviceEvent(deviceId, L"WRITE_ACCESS_DENIED_POLICY_READONLY", false);
            return false;
        }
        
        if (access == DeviceAccess::FORCE_ENCRYPT && isWriteAccess) {
            // 这里应该执行加密逻辑，但简化实现暂时允许访问
            logDeviceEvent(deviceId, L"WRITE_ACCESS_ALLOWED_WITH_ENCRYPTION", true);
            return true;
        }
        
        logDeviceEvent(deviceId, isWriteAccess ? L"WRITE_ACCESS_ALLOWED_POLICY" : L"READ_ACCESS_ALLOWED_POLICY", true);
        return true;
    }
    
    // 应用默认策略
    DeviceType type = deviceInfo->type;
    auto defaultIt = m_defaultPolicies.find(type);
    if (defaultIt != m_defaultPolicies.end()) {
        DeviceAccess access = defaultIt->second;
        
        if (access == DeviceAccess::DENY) {
            logDeviceEvent(deviceId, isWriteAccess ? L"WRITE_ACCESS_DENIED_DEFAULT" : L"READ_ACCESS_DENIED_DEFAULT", false);
            return false;
        }
        
        if (access == DeviceAccess::READ_ONLY && isWriteAccess) {
            logDeviceEvent(deviceId, L"WRITE_ACCESS_DENIED_DEFAULT_READONLY", false);
            return false;
        }
        
        if (access == DeviceAccess::FORCE_ENCRYPT && isWriteAccess) {
            // 简化实现
            logDeviceEvent(deviceId, L"WRITE_ACCESS_ALLOWED_WITH_ENCRYPTION_DEFAULT", true);
            return true;
        }
        
        logDeviceEvent(deviceId, isWriteAccess ? L"WRITE_ACCESS_ALLOWED_DEFAULT" : L"READ_ACCESS_ALLOWED_DEFAULT", true);
        return true;
    }
    
    // 未找到任何策略，默认拒绝
    logDeviceEvent(deviceId, isWriteAccess ? L"WRITE_ACCESS_DENIED_NO_POLICY" : L"READ_ACCESS_DENIED_NO_POLICY", false);
    return false;
}

bool DeviceControlManager::handleDeviceMessage(HWND hwnd, UINT message, WPARAM wParam, LPARAM lParam) {
    if (message != WM_DEVICECHANGE) {
        return false;
    }
    
    switch (wParam) {
        case DBT_DEVICEARRIVAL: {
            // 设备连接
            DEV_BROADCAST_HDR* pHdr = (DEV_BROADCAST_HDR*)lParam;
            if (pHdr->dbch_devicetype == DBT_DEVTYP_DEVICEINTERFACE) {
                DEV_BROADCAST_DEVICEINTERFACE* pDevInterface = (DEV_BROADCAST_DEVICEINTERFACE*)pHdr;
                std::wstring deviceId(pDevInterface->dbcc_name);
                handleDeviceArrival(deviceId);
                return true;
            }
            break;
        }
        
        case DBT_DEVICEREMOVECOMPLETE: {
            // 设备断开
            DEV_BROADCAST_HDR* pHdr = (DEV_BROADCAST_HDR*)lParam;
            if (pHdr->dbch_devicetype == DBT_DEVTYP_DEVICEINTERFACE) {
                DEV_BROADCAST_DEVICEINTERFACE* pDevInterface = (DEV_BROADCAST_DEVICEINTERFACE*)pHdr;
                std::wstring deviceId(pDevInterface->dbcc_name);
                handleDeviceRemoval(deviceId);
                return true;
            }
            break;
        }
    }
    
    return false;
}

bool DeviceControlManager::loadPolicies() {
    // 在实际实现中，应该从文件或注册表加载策略
    // 在此示例中，使用硬编码的默认策略
    return true;
}

bool DeviceControlManager::savePolicies() {
    // 在实际实现中，应该保存策略到文件或注册表
    return true;
}

bool DeviceControlManager::scanForDevices() {
    // 获取当前已连接的设备
    
    // 获取所有磁盘设备
    HDEVINFO hDevInfo = SetupDiGetClassDevs(&GUID_DEVINTERFACE_DISK, NULL, NULL, DIGCF_PRESENT | DIGCF_DEVICEINTERFACE);
    if (hDevInfo == INVALID_HANDLE_VALUE) {
        return false;
    }
    
    SP_DEVICE_INTERFACE_DATA deviceInterfaceData = {};
    deviceInterfaceData.cbSize = sizeof(SP_DEVICE_INTERFACE_DATA);
    
    // 枚举所有磁盘接口
    for (DWORD i = 0; SetupDiEnumDeviceInterfaces(hDevInfo, NULL, &GUID_DEVINTERFACE_DISK, i, &deviceInterfaceData); i++) {
        // 获取设备路径所需大小
        DWORD requiredSize = 0;
        SetupDiGetDeviceInterfaceDetail(hDevInfo, &deviceInterfaceData, NULL, 0, &requiredSize, NULL);
        
        // 分配内存
        PSP_DEVICE_INTERFACE_DETAIL_DATA pDeviceInterfaceDetailData = (PSP_DEVICE_INTERFACE_DETAIL_DATA)malloc(requiredSize);
        if (!pDeviceInterfaceDetailData) {
            continue;
        }
        
        pDeviceInterfaceDetailData->cbSize = sizeof(SP_DEVICE_INTERFACE_DETAIL_DATA);
        
        // 获取设备详细信息
        SP_DEVINFO_DATA deviceInfoData = {};
        deviceInfoData.cbSize = sizeof(SP_DEVINFO_DATA);
        
        if (SetupDiGetDeviceInterfaceDetail(hDevInfo, &deviceInterfaceData, pDeviceInterfaceDetailData, requiredSize, NULL, &deviceInfoData)) {
            std::wstring deviceId(pDeviceInterfaceDetailData->DevicePath);
            
            // 创建设备信息
            DeviceInfo info = createDeviceInfo(deviceId);
            info.isConnected = true;
            
            // 获取当前时间
            auto now = std::chrono::system_clock::now();
            auto time = std::chrono::system_clock::to_time_t(now);
            std::wstringstream ss;
            tm localTime;
            localtime_s(&localTime, &time);
            ss << std::put_time(&localTime, L"%Y-%m-%d %H:%M:%S");
            info.lastConnectTime = ss.str();
            
            // 添加到连接设备列表
            std::lock_guard<std::mutex> lock(m_devicesMutex);
            m_connectedDevices[deviceId] = std::make_shared<DeviceInfo>(info);
        }
        
        free(pDeviceInterfaceDetailData);
    }
    
    SetupDiDestroyDeviceInfoList(hDevInfo);
    return true;
}

DeviceInfo DeviceControlManager::createDeviceInfo(const std::wstring& deviceId) {
    DeviceInfo info;
    info.deviceId = deviceId;
    info.type = determineDeviceType(deviceId);
    
    // 使用正则表达式从设备ID提取实例ID
    std::wregex instanceIdRegex(L"\\\\([^\\\\#]+)#([^\\\\#]+)#");
    std::wsmatch match;
    if (std::regex_search(deviceId, match, instanceIdRegex) && match.size() > 2) {
        info.instanceId = match[1].str() + L"#" + match[2].str();
    } else {
        info.instanceId = L"Unknown";
    }
    
    // 设置默认访问权限
    auto it = m_devicePolicies.find(deviceId);
    if (it != m_devicePolicies.end()) {
        info.currentAccess = it->second;
    } else {
        auto defaultIt = m_defaultPolicies.find(info.type);
        if (defaultIt != m_defaultPolicies.end()) {
            info.currentAccess = defaultIt->second;
        } else {
            info.currentAccess = DeviceAccess::DENY; // 默认拒绝
        }
    }
    
    // 获取设备描述、制造商和序列号
    // 在实际实现中应该从系统API获取这些信息
    info.description = L"Unknown Device";
    info.manufacturer = L"Unknown Manufacturer";
    info.serialNumber = L"Unknown";
    
    return info;
}

DeviceType DeviceControlManager::determineDeviceType(const std::wstring& deviceId) {
    // 根据设备ID判断设备类型
    std::wstring lowerDeviceId = deviceId;
    std::transform(lowerDeviceId.begin(), lowerDeviceId.end(), lowerDeviceId.begin(), ::towlower);
    
    if (lowerDeviceId.find(L"usbstor") != std::wstring::npos) {
        return DeviceType::USB_STORAGE;
    } else if (lowerDeviceId.find(L"cdrom") != std::wstring::npos) {
        return DeviceType::CDROM;
    } else if (lowerDeviceId.find(L"floppy") != std::wstring::npos) {
        return DeviceType::FLOPPY;
    } else {
        // 默认为未知类型
        return DeviceType::UNKNOWN;
    }
}

void DeviceControlManager::handleDeviceArrival(const std::wstring& deviceId) {
    // 获取设备信息
    DeviceInfo info = createDeviceInfo(deviceId);
    info.isConnected = true;
    
    // 设置当前时间为连接时间
    auto now = std::chrono::system_clock::now();
    auto time = std::chrono::system_clock::to_time_t(now);
    std::wstringstream ss;
    tm localTime;
    localtime_s(&localTime, &time);
    ss << std::put_time(&localTime, L"%Y-%m-%d %H:%M:%S");
    info.lastConnectTime = ss.str();
    
    // 添加到连接设备列表
    std::shared_ptr<DeviceInfo> pInfo = std::make_shared<DeviceInfo>(info);
    {
        std::lock_guard<std::mutex> lock(m_devicesMutex);
        m_connectedDevices[deviceId] = pInfo;
    }
    
    // 记录事件
    logDeviceEvent(deviceId, L"CONNECTED", true);
    
    // 如果设备在黑名单中，立即阻止访问
    if (isBlacklisted(deviceId)) {
        blockDeviceAccess(deviceId, true);
    }
    
    // 触发回调
    if (m_deviceCallback) {
        m_deviceCallback(*pInfo, true);
    }
}

void DeviceControlManager::handleDeviceRemoval(const std::wstring& deviceId) {
    // 查找设备
    std::shared_ptr<DeviceInfo> pInfo;
    {
        std::lock_guard<std::mutex> lock(m_devicesMutex);
        auto it = m_connectedDevices.find(deviceId);
        if (it != m_connectedDevices.end()) {
            pInfo = it->second;
            m_connectedDevices.erase(it);
        }
    }
    
    // 记录事件
    logDeviceEvent(deviceId, L"DISCONNECTED", true);
    
    // 触发回调
    if (pInfo && m_deviceCallback) {
        DeviceInfo info = *pInfo;
        info.isConnected = false;
        m_deviceCallback(info, false);
    }
}

bool DeviceControlManager::blockDeviceAccess(const std::wstring& deviceId, bool isWriteAccess) {
    // 在实际实现中，这里应该使用Windows API将设备设置为只读或完全禁用
    
    // 为简化示例，此处仅记录日志
    logDeviceEvent(deviceId, isWriteAccess ? L"BLOCK_WRITE_ACCESS" : L"BLOCK_ALL_ACCESS", true);
    
    return true;
}

void DeviceControlManager::logDeviceEvent(const std::wstring& deviceId, const std::wstring& action, bool success) {
    // 创建日志记录
    // 在实际实现中，应该写入系统日志或通过审计系统记录
    
    // 获取当前时间
    auto now = std::chrono::system_clock::now();
    auto time = std::chrono::system_clock::to_time_t(now);
    std::wstringstream ss;
    tm localTime;
    localtime_s(&localTime, &time);
    ss << std::put_time(&localTime, L"%Y-%m-%d %H:%M:%S");
    
    // 获取设备类型和描述
    std::wstring typeStr = L"UNKNOWN";
    std::wstring description = L"Unknown Device";
    
    auto deviceInfo = getDeviceInfo(deviceId);
    if (deviceInfo) {
        switch (deviceInfo->type) {
            case DeviceType::USB_STORAGE: typeStr = L"USB_STORAGE"; break;
            case DeviceType::CDROM: typeStr = L"CDROM"; break;
            case DeviceType::FLOPPY: typeStr = L"FLOPPY"; break;
            case DeviceType::NETWORK_ADAPTER: typeStr = L"NETWORK_ADAPTER"; break;
            case DeviceType::BLUETOOTH: typeStr = L"BLUETOOTH"; break;
            case DeviceType::PRINTER: typeStr = L"PRINTER"; break;
            case DeviceType::SCANNER: typeStr = L"SCANNER"; break;
            case DeviceType::CAMERA: typeStr = L"CAMERA"; break;
            case DeviceType::AUDIO: typeStr = L"AUDIO"; break;
            case DeviceType::MOBILE_PHONE: typeStr = L"MOBILE_PHONE"; break;
            case DeviceType::CUSTOM: typeStr = L"CUSTOM"; break;
            default: break;
        }
        
        description = deviceInfo->description;
    }
    
    // 格式化日志消息
    std::wstring logMessage = ss.str() + L" - " + 
                            (success ? L"SUCCESS" : L"FAILURE") + L" - " + 
                            action + L" - " + 
                            typeStr + L" - " + 
                            description + L" - " + 
                            deviceId;
    
    // 在实际实现中，这里应该将日志保存到文件或发送到审计服务器
    // std::wcout << logMessage << std::endl;
} 