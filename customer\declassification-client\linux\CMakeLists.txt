cmake_minimum_required(VERSION 3.16)
project(DeclassificationClient)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置编译选项
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -O2")
set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -g -DDEBUG")
set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O3 -DNDEBUG")

# 查找依赖库
find_package(PkgConfig REQUIRED)
find_package(OpenSSL REQUIRED)
find_package(Qt5 REQUIRED COMPONENTS Core Widgets Network)

# 包含头文件目录
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/src
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../common/include
    ${OPENSSL_INCLUDE_DIR}
)

# 源文件
set(SOURCES
    src/main.cpp
    src/declassification_service.cpp
    src/file_processor.cpp
    src/crypto_manager.cpp
    src/audit_logger.cpp
    src/policy_manager.cpp
    src/security_controller.cpp
    src/ui/main_window.cpp
    src/ui/task_manager_widget.cpp
    src/ui/file_list_widget.cpp
    src/ui/progress_dialog.cpp
    src/network/client_connector.cpp
    src/network/secure_sender.cpp
    src/utils/file_utils.cpp
    src/utils/string_utils.cpp
    
    # 通用组件
    ../common/src/api/crypto_utils.cpp
    ../common/src/api/encoding_utils.c
    ../common/src/platform/linux/platform_utils_linux.c
)

# 头文件
set(HEADERS
    include/declassification_service.h
    include/file_processor.h
    include/crypto_manager.h
    include/audit_logger.h
    include/policy_manager.h
    include/security_controller.h
    include/ui/main_window.h
    include/ui/task_manager_widget.h
    include/ui/file_list_widget.h
    include/ui/progress_dialog.h
    include/network/client_connector.h
    include/network/secure_sender.h
    include/utils/file_utils.h
    include/utils/string_utils.h
    include/models/task_models.h
    include/models/file_models.h
    include/models/security_models.h
    
    # 通用组件头文件
    ../common/include/api/crypto_utils.h
    ../common/include/api/encoding_utils.h
    ../common/include/platform/platform_utils.h
)

# 创建可执行文件
add_executable(${PROJECT_NAME} ${SOURCES} ${HEADERS})

# 链接库
target_link_libraries(${PROJECT_NAME}
    Qt5::Core
    Qt5::Widgets
    Qt5::Network
    OpenSSL::SSL
    OpenSSL::Crypto
    pthread
    dl
)

# 设置输出目录
set_target_properties(${PROJECT_NAME} PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# 安装配置
install(TARGETS ${PROJECT_NAME}
    RUNTIME DESTINATION bin
)

# 安装配置文件
install(FILES config/declassification.conf
    DESTINATION etc/cryptosystem
)

# 安装桌面文件
install(FILES resources/declassification-client.desktop
    DESTINATION share/applications
)

# 安装图标
install(FILES resources/declassification-client.png
    DESTINATION share/icons/hicolor/64x64/apps
)

# 打包配置
set(CPACK_PACKAGE_NAME "cryptosystem-declassification-client")
set(CPACK_PACKAGE_VERSION "1.0.0")
set(CPACK_PACKAGE_DESCRIPTION "CryptoSystem Declassification Client for Linux")
set(CPACK_PACKAGE_VENDOR "CryptoSystem")
set(CPACK_GENERATOR "DEB;RPM")

include(CPack) 