import Foundation
import SwiftUI
import Combine

/// 脱密服务核心类
/// 负责管理脱密任务的完整生命周期
@MainActor
class DeclassificationService: ObservableObject {
    
    // MARK: - Published Properties
    @Published var tasks: [DeclassificationTask] = []
    @Published var isLoading = false
    @Published var lastError: Error?
    @Published var statistics = OperationStatistics.empty
    
    // MARK: - Private Properties
    private var taskQueue: DispatchQueue
    private var processingTasks: Set<UUID> = []
    private let maxConcurrentTasks = 3
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Dependencies
    private let securityManager = SecurityManager.shared
    private let networkManager = NetworkManager.shared
    private let auditLogger = AuditLogger.shared
    private let configurationManager = ConfigurationManager.shared
    
    // MARK: - Initialization
    init() {
        self.taskQueue = DispatchQueue(label: "declassification.processing", qos: .userInitiated, attributes: .concurrent)
        setupPeriodicRefresh()
        loadTasks()
    }
    
    // MARK: - Public Methods
    
    /// 创建新的脱密任务
    func createTask(name: String, files: [URL], sendMethod: SendMethod, recipients: String, notes: String) async {
        isLoading = true
        defer { isLoading = false }
        
        do {
            // 验证输入
            guard !name.isEmpty, !files.isEmpty else {
                throw DeclassificationError.invalidInput("任务名称和文件列表不能为空")
            }
            
            // 验证文件权限
            for file in files {
                guard await securityManager.hasPermission(for: file) else {
                    throw DeclassificationError.permissionDenied("没有权限访问文件: \(file.lastPathComponent)")
                }
            }
            
            // 解析收件人列表
            let recipientList = recipients.split(separator: ",").map { String($0).trimmingCharacters(in: .whitespaces) }
            let sendConfig = SendConfiguration(
                method: sendMethod,
                recipients: recipientList,
                subject: "脱密文件：\(name)",
                message: notes
            )
            
            // 创建任务
            let task = DeclassificationTask(
                name: name,
                files: files,
                sendConfiguration: sendConfig,
                createdBy: getCurrentUser(),
                notes: notes
            )
            
            // 添加到任务列表
            tasks.append(task)
            
            // 记录审计日志
            await auditLogger.logEvent(
                type: .taskEvent,
                message: "创建脱密任务",
                details: [
                    "taskId": task.id.uuidString,
                    "taskName": name,
                    "fileCount": String(files.count),
                    "sendMethod": sendMethod.rawValue
                ]
            )
            
            // 保存任务
            saveTasks()
            
            // 自动开始处理
            await startProcessing(taskId: task.id)
            
        } catch {
            lastError = error
            await auditLogger.logEvent(
                type: .errorEvent,
                message: "创建任务失败",
                details: ["error": error.localizedDescription]
            )
        }
    }
    
    /// 开始处理指定任务
    func startProcessing(taskId: UUID) async {
        guard let taskIndex = tasks.firstIndex(where: { $0.id == taskId }) else { return }
        guard !processingTasks.contains(taskId) else { return }
        guard processingTasks.count < maxConcurrentTasks else { return }
        
        processingTasks.insert(taskId)
        tasks[taskIndex].updateStatus(.processing)
        
        await auditLogger.logEvent(
            type: .taskEvent,
            message: "开始处理脱密任务",
            details: ["taskId": taskId.uuidString]
        )
        
        // 在后台队列中处理任务
        Task.detached { [weak self] in
            await self?.processTask(taskId: taskId)
        }
    }
    
    /// 取消任务处理
    func cancelTask(taskId: UUID) async {
        guard let taskIndex = tasks.firstIndex(where: { $0.id == taskId }) else { return }
        
        processingTasks.remove(taskId)
        tasks[taskIndex].updateStatus(.cancelled)
        
        await auditLogger.logEvent(
            type: .taskEvent,
            message: "取消脱密任务",
            details: ["taskId": taskId.uuidString]
        )
        
        saveTasks()
    }
    
    /// 删除任务
    func deleteTask(taskId: UUID) async {
        guard let taskIndex = tasks.firstIndex(where: { $0.id == taskId }) else { return }
        
        // 如果任务正在处理，先取消
        if processingTasks.contains(taskId) {
            await cancelTask(taskId: taskId)
        }
        
        let task = tasks[taskIndex]
        tasks.remove(at: taskIndex)
        
        await auditLogger.logEvent(
            type: .taskEvent,
            message: "删除脱密任务",
            details: [
                "taskId": taskId.uuidString,
                "taskName": task.name
            ]
        )
        
        saveTasks()
    }
    
    /// 刷新任务列表
    func refreshTasks() async {
        isLoading = true
        defer { isLoading = false }
        
        loadTasks()
        updateStatistics()
    }
    
    /// 清除错误状态
    func clearError() {
        lastError = nil
    }
    
    /// 获取任务详情
    func getTaskDetails(taskId: UUID) -> DeclassificationTask? {
        return tasks.first { $0.id == taskId }
    }
    
    /// 重试失败的任务
    func retryTask(taskId: UUID) async {
        guard let taskIndex = tasks.firstIndex(where: { $0.id == taskId }) else { return }
        guard tasks[taskIndex].status == .failed else { return }
        
        // 重置任务状态
        tasks[taskIndex].updateStatus(.pending)
        tasks[taskIndex].errorMessage = nil
        
        // 重置文件状态
        for fileId in tasks[taskIndex].files.map(\.id) {
            tasks[taskIndex].updateFileStatus(fileId, status: .waiting)
        }
        
        await auditLogger.logEvent(
            type: .taskEvent,
            message: "重试失败任务",
            details: ["taskId": taskId.uuidString]
        )
        
        saveTasks()
        
        // 重新开始处理
        await startProcessing(taskId: taskId)
    }
    
    // MARK: - Private Methods
    
    private func processTask(taskId: UUID) async {
        await MainActor.run {
            guard let taskIndex = tasks.firstIndex(where: { $0.id == taskId }) else { return }
            tasks[taskIndex].updateStatus(.processing)
        }
        
        do {
            let task = await MainActor.run { tasks.first { $0.id == taskId } }
            guard let task = task else { return }
            
            // 处理每个文件
            for file in task.files {
                try await processFile(taskId: taskId, fileId: file.id)
            }
            
            // 发送处理完成的文件
            try await sendFiles(taskId: taskId)
            
            // 更新任务状态为完成
            await MainActor.run {
                guard let taskIndex = tasks.firstIndex(where: { $0.id == taskId }) else { return }
                tasks[taskIndex].updateStatus(.completed)
                processingTasks.remove(taskId)
            }
            
            await auditLogger.logEvent(
                type: .taskEvent,
                message: "任务处理完成",
                details: ["taskId": taskId.uuidString]
            )
            
        } catch {
            await MainActor.run {
                guard let taskIndex = tasks.firstIndex(where: { $0.id == taskId }) else { return }
                tasks[taskIndex].updateStatus(.failed)
                tasks[taskIndex].errorMessage = error.localizedDescription
                processingTasks.remove(taskId)
            }
            
            await auditLogger.logEvent(
                type: .errorEvent,
                message: "任务处理失败",
                details: [
                    "taskId": taskId.uuidString,
                    "error": error.localizedDescription
                ]
            )
        }
        
        await MainActor.run {
            saveTasks()
            updateStatistics()
        }
    }
    
    private func processFile(taskId: UUID, fileId: UUID) async throws {
        await MainActor.run {
            guard let taskIndex = tasks.firstIndex(where: { $0.id == taskId }) else { return }
            tasks[taskIndex].updateFileStatus(fileId, status: .decrypting)
        }
        
        // 模拟文件解密处理
        try await Task.sleep(nanoseconds: 1_000_000_000) // 1秒延迟
        
        await MainActor.run {
            guard let taskIndex = tasks.firstIndex(where: { $0.id == taskId }) else { return }
            tasks[taskIndex].updateFileStatus(fileId, status: .decrypted)
        }
        
        // 模拟文件打包处理
        try await Task.sleep(nanoseconds: 500_000_000) // 0.5秒延迟
        
        await MainActor.run {
            guard let taskIndex = tasks.firstIndex(where: { $0.id == taskId }) else { return }
            tasks[taskIndex].updateFileStatus(fileId, status: .packaged)
        }
        
        await auditLogger.logEvent(
            type: .taskEvent,
            message: "文件处理完成",
            details: [
                "taskId": taskId.uuidString,
                "fileId": fileId.uuidString
            ]
        )
    }
    
    private func sendFiles(taskId: UUID) async throws {
        let task = await MainActor.run { tasks.first { $0.id == taskId } }
        guard let task = task else { return }
        
        // 更新文件状态为发送中
        for fileId in task.files.map(\.id) {
            await MainActor.run {
                guard let taskIndex = tasks.firstIndex(where: { $0.id == taskId }) else { return }
                tasks[taskIndex].updateFileStatus(fileId, status: .sending)
            }
        }
        
        // 根据发送方式处理
        switch task.sendConfiguration.method {
        case .email:
            try await sendViaEmail(task: task)
        case .ftp:
            try await sendViaFTP(task: task)
        case .cloud:
            try await sendViaCloud(task: task)
        case .usb:
            try await sendViaUSB(task: task)
        case .manual:
            try await sendViaManual(task: task)
        }
        
        // 更新文件状态为已发送
        for fileId in task.files.map(\.id) {
            await MainActor.run {
                guard let taskIndex = tasks.firstIndex(where: { $0.id == taskId }) else { return }
                tasks[taskIndex].updateFileStatus(fileId, status: .sent)
            }
        }
        
        await auditLogger.logEvent(
            type: .taskEvent,
            message: "文件发送完成",
            details: [
                "taskId": taskId.uuidString,
                "method": task.sendConfiguration.method.rawValue,
                "recipients": task.sendConfiguration.recipients.joined(separator: ",")
            ]
        )
    }
    
    private func sendViaEmail(task: DeclassificationTask) async throws {
        // 实现邮件发送逻辑
        try await Task.sleep(nanoseconds: 2_000_000_000) // 2秒延迟模拟发送
        
        await auditLogger.logEvent(
            type: .taskEvent,
            message: "邮件发送完成",
            details: [
                "taskId": task.id.uuidString,
                "recipients": task.sendConfiguration.recipients.joined(separator: ",")
            ]
        )
    }
    
    private func sendViaFTP(task: DeclassificationTask) async throws {
        // 实现FTP发送逻辑
        try await Task.sleep(nanoseconds: 3_000_000_000) // 3秒延迟模拟上传
        
        await auditLogger.logEvent(
            type: .taskEvent,
            message: "FTP上传完成",
            details: ["taskId": task.id.uuidString]
        )
    }
    
    private func sendViaCloud(task: DeclassificationTask) async throws {
        // 实现云存储发送逻辑
        try await Task.sleep(nanoseconds: 2_500_000_000) // 2.5秒延迟模拟上传
        
        await auditLogger.logEvent(
            type: .taskEvent,
            message: "云存储上传完成",
            details: ["taskId": task.id.uuidString]
        )
    }
    
    private func sendViaUSB(task: DeclassificationTask) async throws {
        // 实现USB设备发送逻辑
        try await Task.sleep(nanoseconds: 1_500_000_000) // 1.5秒延迟模拟复制
        
        await auditLogger.logEvent(
            type: .taskEvent,
            message: "USB设备复制完成",
            details: ["taskId": task.id.uuidString]
        )
    }
    
    private func sendViaManual(task: DeclassificationTask) async throws {
        // 实现手动提取逻辑
        try await Task.sleep(nanoseconds: 500_000_000) // 0.5秒延迟模拟准备
        
        await auditLogger.logEvent(
            type: .taskEvent,
            message: "手动提取准备完成",
            details: ["taskId": task.id.uuidString]
        )
    }
    
    private func setupPeriodicRefresh() {
        Timer.publish(every: 30, on: .main, in: .common)
            .autoconnect()
            .sink { [weak self] _ in
                Task { @MainActor in
                    await self?.refreshTasks()
                }
            }
            .store(in: &cancellables)
    }
    
    private func loadTasks() {
        // 从本地存储加载任务
        guard let data = UserDefaults.standard.data(forKey: "declassification_tasks") else { return }
        
        do {
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601
            let loadedTasks = try decoder.decode([DeclassificationTask].self, from: data)
            tasks = loadedTasks
        } catch {
            print("加载任务失败: \(error)")
        }
    }
    
    private func saveTasks() {
        do {
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            let data = try encoder.encode(tasks)
            UserDefaults.standard.set(data, forKey: "declassification_tasks")
        } catch {
            print("保存任务失败: \(error)")
        }
    }
    
    private func updateStatistics() {
        let totalTasks = tasks.count
        let completedTasks = tasks.filter { $0.status == .completed }.count
        let failedTasks = tasks.filter { $0.status == .failed }.count
        let processingTasks = tasks.filter { $0.status == .processing }.count
        
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        let todayTasks = tasks.filter { calendar.isDate($0.createdAt, inSameDayAs: today) }.count
        
        let totalFilesProcessed = tasks.reduce(0) { $0 + $1.completedFiles }
        let totalDataTransferred = tasks.reduce(0) { result, task in
            result + task.files.reduce(0) { $0 + $1.size }
        }
        
        let averageProcessingTime: TimeInterval = 45 // 假设平均处理时间
        let successRate = totalTasks > 0 ? Double(completedTasks) / Double(totalTasks) * 100 : 0
        
        statistics = OperationStatistics(
            totalTasks: totalTasks,
            completedTasks: completedTasks,
            failedTasks: failedTasks,
            processingTasks: processingTasks,
            todayTasks: todayTasks,
            totalFilesProcessed: totalFilesProcessed,
            totalDataTransferred: totalDataTransferred,
            averageProcessingTime: averageProcessingTime,
            successRate: successRate
        )
    }
    
    private func getCurrentUser() -> String {
        return NSFullUserName()
    }
    
    // MARK: - Computed Properties
    var totalTasks: Int { tasks.count }
    var completedTasks: Int { tasks.filter { $0.status == .completed }.count }
    var processingTasks: Int { tasks.filter { $0.status == .processing }.count }
    var failedTasks: Int { tasks.filter { $0.status == .failed }.count }
    var todayTasks: Int {
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        return tasks.filter { calendar.isDate($0.createdAt, inSameDayAs: today) }.count
    }
    var successRate: Int {
        guard totalTasks > 0 else { return 0 }
        return Int(Double(completedTasks) / Double(totalTasks) * 100)
    }
}

// MARK: - Error Types
enum DeclassificationError: LocalizedError {
    case invalidInput(String)
    case permissionDenied(String)
    case processingFailed(String)
    case networkError(String)
    case securityError(String)
    
    var errorDescription: String? {
        switch self {
        case .invalidInput(let message):
            return "输入错误: \(message)"
        case .permissionDenied(let message):
            return "权限不足: \(message)"
        case .processingFailed(let message):
            return "处理失败: \(message)"
        case .networkError(let message):
            return "网络错误: \(message)"
        case .securityError(let message):
            return "安全错误: \(message)"
        }
    }
} 