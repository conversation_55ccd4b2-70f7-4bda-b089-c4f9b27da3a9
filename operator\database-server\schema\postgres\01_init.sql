-- =====================================================
-- 企业文档加密系统 - 数据库初始化脚本 (PostgreSQL版本)
-- 版本: 1.4.0
-- 创建时间: 2025-01-20
-- 支持数据库: PostgreSQL
-- =====================================================

-- =====================================================
-- 1. 基础配置表
-- =====================================================

-- 系统配置表
CREATE TABLE sys_config (
    config_id VARCHAR(50) PRIMARY KEY,
    config_name VARCHAR(100) NOT NULL,
    config_value TEXT,
    config_type VARCHAR(20) DEFAULT 'STRING',
    is_encrypted BOOLEAN DEFAULT FALSE,
    description TEXT,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(50),
    updated_by VARCHAR(50),
    is_active BOOLEAN DEFAULT TRUE
);

-- 系统配置表注释
COMMENT ON TABLE sys_config IS '系统配置表';
COMMENT ON COLUMN sys_config.config_id IS '配置ID';
COMMENT ON COLUMN sys_config.config_name IS '配置名称';
COMMENT ON COLUMN sys_config.config_value IS '配置值';
COMMENT ON COLUMN sys_config.config_type IS '配置类型(STRING,NUMBER,BOOLEAN,JSON)';
COMMENT ON COLUMN sys_config.is_encrypted IS '是否加密存储';

-- 算法配置表
CREATE TABLE crypto_algorithms (
    algorithm_id SERIAL PRIMARY KEY,
    algorithm_code VARCHAR(20) UNIQUE NOT NULL,
    algorithm_name VARCHAR(50) NOT NULL,
    algorithm_type VARCHAR(20) NOT NULL,
    key_length INT NOT NULL,
    is_national_crypto BOOLEAN DEFAULT FALSE,
    is_enabled BOOLEAN DEFAULT TRUE,
    security_level INT DEFAULT 3,
    description TEXT,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 算法配置表注释
COMMENT ON TABLE crypto_algorithms IS '加密算法配置表';
COMMENT ON COLUMN crypto_algorithms.algorithm_id IS '算法ID';
COMMENT ON COLUMN crypto_algorithms.algorithm_code IS '算法代码(AES256,SM4,RSA2048等)';
COMMENT ON COLUMN crypto_algorithms.algorithm_name IS '算法名称';
COMMENT ON COLUMN crypto_algorithms.algorithm_type IS '算法类型(SYMMETRIC,ASYMMETRIC)';

-- =====================================================
-- 2. 客户单位管理
-- =====================================================

-- 客户单位表
CREATE TABLE clients (
    client_id VARCHAR(50) PRIMARY KEY,
    client_name VARCHAR(200) NOT NULL,
    client_code VARCHAR(50) UNIQUE NOT NULL,
    contact_person VARCHAR(100),
    contact_phone VARCHAR(20),
    contact_email VARCHAR(100),
    address TEXT,
    industry VARCHAR(50),
    client_level VARCHAR(20) DEFAULT 'STANDARD',
    license_type VARCHAR(50),
    max_users INT DEFAULT 100,
    max_devices INT DEFAULT 50,
    contract_start_date DATE,
    contract_end_date DATE,
    is_active BOOLEAN DEFAULT TRUE,
    status VARCHAR(20) DEFAULT 'ACTIVE',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(50),
    updated_by VARCHAR(50),
    remarks TEXT
);

-- 客户单位表注释
COMMENT ON TABLE clients IS '客户单位表';
COMMENT ON COLUMN clients.client_id IS '客户ID';
COMMENT ON COLUMN clients.client_name IS '客户名称';
COMMENT ON COLUMN clients.client_code IS '客户代码';
COMMENT ON COLUMN clients.client_level IS '客户等级(VIP,STANDARD,BASIC)';
COMMENT ON COLUMN clients.status IS '状态(ACTIVE,SUSPENDED,EXPIRED)';

-- 客户单位用户表
CREATE TABLE client_users (
    user_id VARCHAR(50) PRIMARY KEY,
    client_id VARCHAR(50) NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    user_display_name VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    phone VARCHAR(20),
    department VARCHAR(100),
    position VARCHAR(100),
    user_role VARCHAR(50) DEFAULT 'USER',
    password_hash VARCHAR(255),
    salt VARCHAR(100),
    last_login_time TIMESTAMP,
    login_count INT DEFAULT 0,
    is_locked BOOLEAN DEFAULT FALSE,
    lock_reason TEXT,
    password_expire_date DATE,
    is_active BOOLEAN DEFAULT TRUE,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(50),
    updated_by VARCHAR(50),
    
    FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE
);

-- 客户用户表注释
COMMENT ON TABLE client_users IS '客户单位用户表';
COMMENT ON COLUMN client_users.user_role IS '用户角色(ADMIN,MANAGER,USER)';

-- =====================================================
-- 3. 密钥管理核心表
-- =====================================================

-- 主密钥表
CREATE TABLE master_keys (
    key_id VARCHAR(50) PRIMARY KEY,
    key_name VARCHAR(200) NOT NULL,
    client_id VARCHAR(50) NOT NULL,
    algorithm_id INT NOT NULL,
    key_length INT NOT NULL,
    key_data_encrypted BYTEA NOT NULL,
    key_hash VARCHAR(128) NOT NULL UNIQUE,
    key_version INT DEFAULT 1,
    generation_method VARCHAR(50) DEFAULT 'SYSTEM',
    effective_date TIMESTAMP NOT NULL,
    expiration_date TIMESTAMP,
    key_status VARCHAR(20) DEFAULT 'PENDING',
    usage_policy TEXT,
    max_usage_count BIGINT,
    current_usage_count BIGINT DEFAULT 0,
    last_used_time TIMESTAMP,
    revocation_reason TEXT,
    revoked_time TIMESTAMP,
    destroyed_time TIMESTAMP,
    backup_status VARCHAR(20) DEFAULT 'NONE',
    backup_location TEXT,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(50),
    updated_by VARCHAR(50),
    description TEXT,
    
    FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE,
    FOREIGN KEY (algorithm_id) REFERENCES crypto_algorithms(algorithm_id)
);

-- 主密钥表注释
COMMENT ON TABLE master_keys IS '主密钥表';
COMMENT ON COLUMN master_keys.key_status IS '密钥状态(PENDING,ACTIVE,SUSPENDED,REVOKED,EXPIRED,DESTROYED)';
COMMENT ON COLUMN master_keys.generation_method IS '生成方式(SYSTEM,IMPORT,MANUAL)';

-- 工作密钥表
CREATE TABLE work_keys (
    key_id VARCHAR(50) PRIMARY KEY,
    key_name VARCHAR(200) NOT NULL,
    master_key_id VARCHAR(50) NOT NULL,
    client_id VARCHAR(50) NOT NULL,
    work_key_type VARCHAR(50) NOT NULL,
    algorithm_id INT NOT NULL,
    key_length INT NOT NULL,
    key_data_encrypted BYTEA NOT NULL,
    key_hash VARCHAR(128) NOT NULL UNIQUE,
    key_version INT DEFAULT 1,
    derivation_method VARCHAR(50) DEFAULT 'DERIVED',
    derivation_parameters TEXT,
    effective_date TIMESTAMP NOT NULL,
    expiration_date TIMESTAMP,
    key_status VARCHAR(20) DEFAULT 'PENDING',
    usage_scope TEXT,
    max_usage_count BIGINT,
    current_usage_count BIGINT DEFAULT 0,
    last_used_time TIMESTAMP,
    device_binding TEXT,
    ip_restrictions TEXT,
    time_restrictions TEXT,
    revocation_reason TEXT,
    revoked_time TIMESTAMP,
    destroyed_time TIMESTAMP,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(50),
    updated_by VARCHAR(50),
    description TEXT,
    
    FOREIGN KEY (master_key_id) REFERENCES master_keys(key_id) ON DELETE CASCADE,
    FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE,
    FOREIGN KEY (algorithm_id) REFERENCES crypto_algorithms(algorithm_id)
);

-- 工作密钥表注释
COMMENT ON TABLE work_keys IS '工作密钥表';
COMMENT ON COLUMN work_keys.work_key_type IS '工作密钥类型(FILE_ENCRYPTION,DB_ENCRYPTION,COMM_ENCRYPTION,DIGITAL_SIGNATURE)';
COMMENT ON COLUMN work_keys.derivation_method IS '派生方式(DERIVED,RANDOM)';

-- =====================================================
-- 4. 密钥分发管理
-- =====================================================

-- 密钥分发记录表
CREATE TABLE key_distributions (
    distribution_id VARCHAR(50) PRIMARY KEY,
    key_id VARCHAR(50) NOT NULL,
    key_type VARCHAR(20) NOT NULL,
    client_id VARCHAR(50) NOT NULL,
    target_client_id VARCHAR(50),
    target_user_id VARCHAR(50),
    target_device_id VARCHAR(50),
    distribution_method VARCHAR(50) NOT NULL,
    distribution_status VARCHAR(20) DEFAULT 'PENDING',
    distribution_time TIMESTAMP,
    scheduled_time TIMESTAMP,
    retry_count INT DEFAULT 0,
    max_retries INT DEFAULT 3,
    last_error_message TEXT,
    distribution_package_path TEXT,
    verification_code VARCHAR(100),
    expiration_time TIMESTAMP,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(50),
    updated_by VARCHAR(50),
    remarks TEXT,
    
    FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE
);

-- 密钥分发表注释
COMMENT ON TABLE key_distributions IS '密钥分发记录表';
COMMENT ON COLUMN key_distributions.key_type IS '密钥类型(MASTER,WORK)';
COMMENT ON COLUMN key_distributions.distribution_status IS '分发状态(PENDING,SENT,DELIVERED,CONFIRMED,FAILED,CANCELLED)';

-- 密钥接收确认表
CREATE TABLE key_receipts (
    receipt_id VARCHAR(50) PRIMARY KEY,
    distribution_id VARCHAR(50) NOT NULL,
    recipient_type VARCHAR(20) NOT NULL,
    recipient_id VARCHAR(50) NOT NULL,
    receipt_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    receipt_status VARCHAR(20) DEFAULT 'RECEIVED',
    verification_result BOOLEAN,
    installation_status VARCHAR(20),
    error_message TEXT,
    device_info TEXT,
    client_version VARCHAR(50),
    signature_verification BOOLEAN,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    remarks TEXT,
    
    FOREIGN KEY (distribution_id) REFERENCES key_distributions(distribution_id) ON DELETE CASCADE
);

-- 密钥接收表注释
COMMENT ON TABLE key_receipts IS '密钥接收确认表';
COMMENT ON COLUMN key_receipts.recipient_type IS '接收方类型(CLIENT,USER,DEVICE)';

-- =====================================================
-- 5. 审计日志表
-- =====================================================

-- 审计日志表
CREATE TABLE audit_logs (
    log_id VARCHAR(50) PRIMARY KEY,
    client_id VARCHAR(50),
    user_id VARCHAR(50),
    event_type VARCHAR(50) NOT NULL,
    event_category VARCHAR(50) NOT NULL,
    event_level VARCHAR(20) DEFAULT 'INFO',
    event_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    event_source VARCHAR(100),
    object_type VARCHAR(50),
    object_id VARCHAR(50),
    object_name VARCHAR(200),
    action VARCHAR(100) NOT NULL,
    result VARCHAR(20) DEFAULT 'SUCCESS',
    error_code VARCHAR(50),
    error_message TEXT,
    request_ip VARCHAR(45),
    user_agent VARCHAR(500),
    session_id VARCHAR(100),
    request_id VARCHAR(100),
    details JSONB,
    risk_level VARCHAR(20) DEFAULT 'LOW',
    is_sensitive BOOLEAN DEFAULT FALSE,
    retention_period INT DEFAULT 2555,
    archived BOOLEAN DEFAULT FALSE,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE
);

-- 审计日志表注释
COMMENT ON TABLE audit_logs IS '审计日志表';
COMMENT ON COLUMN audit_logs.event_level IS '事件级别(DEBUG,INFO,WARN,ERROR,FATAL)';
COMMENT ON COLUMN audit_logs.risk_level IS '风险级别(LOW,MEDIUM,HIGH,CRITICAL)';

-- 密钥使用日志表
CREATE TABLE key_usage_logs (
    usage_id VARCHAR(50) PRIMARY KEY,
    key_id VARCHAR(50) NOT NULL,
    key_type VARCHAR(20) NOT NULL,
    client_id VARCHAR(50) NOT NULL,
    user_id VARCHAR(50),
    device_id VARCHAR(50),
    usage_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    usage_type VARCHAR(50) NOT NULL,
    operation VARCHAR(50) NOT NULL,
    file_path TEXT,
    file_size BIGINT,
    file_hash VARCHAR(128),
    encryption_algorithm VARCHAR(50),
    usage_result VARCHAR(20) DEFAULT 'SUCCESS',
    error_message TEXT,
    request_ip VARCHAR(45),
    processing_time_ms INT,
    data_volume BIGINT,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE
);

-- 密钥使用日志表注释
COMMENT ON TABLE key_usage_logs IS '密钥使用日志表';
COMMENT ON COLUMN key_usage_logs.usage_type IS '使用类型(ENCRYPT,DECRYPT,SIGN,VERIFY)';
COMMENT ON COLUMN key_usage_logs.operation IS '操作类型(FILE_ENCRYPT,FILE_DECRYPT,CREATE_SIGNATURE,VERIFY_SIGNATURE)';

-- =====================================================
-- 6. 创建索引
-- =====================================================

-- 客户用户表索引
CREATE INDEX idx_client_users_client_id ON client_users(client_id);
CREATE INDEX idx_client_users_username ON client_users(username);

-- 主密钥表索引
CREATE INDEX idx_master_keys_client_id ON master_keys(client_id);
CREATE INDEX idx_master_keys_status ON master_keys(key_status);
CREATE INDEX idx_master_keys_expiration ON master_keys(expiration_date);
CREATE INDEX idx_master_keys_client_status ON master_keys(client_id, key_status);

-- 工作密钥表索引
CREATE INDEX idx_work_keys_master_key ON work_keys(master_key_id);
CREATE INDEX idx_work_keys_client_id ON work_keys(client_id);
CREATE INDEX idx_work_keys_type ON work_keys(work_key_type);
CREATE INDEX idx_work_keys_status ON work_keys(key_status);
CREATE INDEX idx_work_keys_expiration ON work_keys(expiration_date);
CREATE INDEX idx_work_keys_master_status ON work_keys(master_key_id, key_status);

-- 密钥分发表索引
CREATE INDEX idx_key_distributions_client_status ON key_distributions(client_id, distribution_status);

-- 审计日志表索引
CREATE INDEX idx_audit_logs_client_time ON audit_logs(client_id, event_time);
CREATE INDEX idx_audit_logs_event_type ON audit_logs(event_type);
CREATE INDEX idx_audit_logs_user_time ON audit_logs(user_id, event_time);

-- 密钥使用日志表索引
CREATE INDEX idx_key_usage_key_time ON key_usage_logs(key_id, usage_time);
CREATE INDEX idx_key_usage_client_time ON key_usage_logs(client_id, usage_time);

-- =====================================================
-- 7. 初始化基础数据
-- =====================================================

-- 插入基础算法配置
INSERT INTO crypto_algorithms (algorithm_code, algorithm_name, algorithm_type, key_length, is_national_crypto, security_level, description) VALUES
('AES256', 'AES-256', 'SYMMETRIC', 256, FALSE, 4, 'AES 256位对称加密算法'),
('AES128', 'AES-128', 'SYMMETRIC', 128, FALSE, 3, 'AES 128位对称加密算法'),
('SM4', 'SM4', 'SYMMETRIC', 128, TRUE, 4, '国密SM4对称加密算法'),
('RSA2048', 'RSA-2048', 'ASYMMETRIC', 2048, FALSE, 4, 'RSA 2048位非对称加密算法'),
('RSA4096', 'RSA-4096', 'ASYMMETRIC', 4096, FALSE, 5, 'RSA 4096位非对称加密算法'),
('SM2', 'SM2', 'ASYMMETRIC', 256, TRUE, 4, '国密SM2非对称加密算法'),
('ECC256', 'ECC-P256', 'ASYMMETRIC', 256, FALSE, 4, 'ECC P-256椭圆曲线算法');

-- 插入基础系统配置
INSERT INTO sys_config (config_id, config_name, config_value, config_type, description) VALUES
('system.default_algorithm', '默认加密算法', 'AES256', 'STRING', '系统默认使用的加密算法'),
('key.master_expiry_days', '主密钥过期天数', '1095', 'NUMBER', '主密钥默认过期天数(3年)'),
('key.work_expiry_days', '工作密钥过期天数', '365', 'NUMBER', '工作密钥默认过期天数(1年)'),
('audit.log_retention_days', '审计日志保留天数', '2555', 'NUMBER', '审计日志保留天数(7年)'),
('system.max_retry_count', '最大重试次数', '3', 'NUMBER', '系统操作最大重试次数'),
('security.password_complexity', '密码复杂度要求', 'true', 'BOOLEAN', '是否启用密码复杂度验证'); 