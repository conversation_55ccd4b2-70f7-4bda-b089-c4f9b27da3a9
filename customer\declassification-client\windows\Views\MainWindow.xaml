<Window x:Class="CryptoSystem.DeclassificationClient.Views.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:sys="clr-namespace:System;assembly=mscorlib" Title="文档脱密客户端 - CryptoSystem v1.4.0" Height="800" Width="1200" MinHeight="600" MinWidth="900" WindowStartupLocation="CenterScreen" Icon="/Resources/app_icon.ico">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 顶部工具栏 -->
        <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryMid" Padding="16,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 左侧标题和导航 -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <materialDesign:PackIcon Kind="Security" Width="32" Height="32" Foreground="White" Margin="0,0,10,0"/>
                    <TextBlock Text="文档脱密客户端" FontSize="18" FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>

                <!-- 中间搜索框 -->
                <materialDesign:Card Grid.Column="1" Margin="20,0" MaxWidth="400" Background="White" Opacity="0.9">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBox Grid.Column="0" materialDesign:HintAssist.Hint="搜索任务或文件..." materialDesign:TextFieldAssist.HasClearButton="True" BorderThickness="0" Margin="10,5"/>
                        <Button Grid.Column="1" Style="{StaticResource MaterialDesignIconButton}" Margin="5">
                            <materialDesign:PackIcon Kind="Magnify"/>
                        </Button>
                    </Grid>
                </materialDesign:Card>

                <!-- 右侧用户信息和操作 -->
                <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button Style="{StaticResource MaterialDesignIconButton}" Margin="5" ToolTip="通知">
                        <materialDesign:PackIcon Kind="Bell" Foreground="White"/>
                    </Button>
                    <Button Style="{StaticResource MaterialDesignIconButton}" Margin="5" ToolTip="设置">
                        <materialDesign:PackIcon Kind="Settings" Foreground="White"/>
                    </Button>
                    <Button Style="{StaticResource MaterialDesignIconButton}" Margin="5" ToolTip="帮助">
                        <materialDesign:PackIcon Kind="Help" Foreground="White"/>
                    </Button>
                    <Separator Margin="10,0" Background="White" Width="1" Height="24"/>
                    <StackPanel Orientation="Horizontal" Margin="10,0">
                        <materialDesign:PackIcon Kind="Account" Foreground="White" VerticalAlignment="Center" Margin="0,0,5,0"/>
                        <TextBlock Text="{Binding CurrentUser.DisplayName, FallbackValue='用户'}" Foreground="White" VerticalAlignment="Center" FontWeight="Medium"/>
                    </StackPanel>
                    <Button Style="{StaticResource MaterialDesignIconButton}" Margin="5" ToolTip="退出登录">
                        <materialDesign:PackIcon Kind="Logout" Foreground="White"/>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:ColorZone>

        <!-- 主内容区域 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧导航菜单 -->
            <materialDesign:Card Grid.Column="0" Margin="10,10,5,10" materialDesign:ShadowAssist.ShadowDepth="Depth2">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel>
                        <!-- 快速操作 -->
                        <Expander Header="快速操作" IsExpanded="True" Margin="10">
                            <StackPanel Margin="10,5">
                                <Button Content="新建脱密任务" HorizontalAlignment="Stretch" Margin="0,5" Style="{StaticResource MaterialDesignRaisedButton}"/>
                                <Button Content="导入文件" HorizontalAlignment="Stretch" Margin="0,5" Style="{StaticResource MaterialDesignOutlinedButton}"/>
                            </StackPanel>
                        </Expander>

                        <!-- 导航菜单 -->
                        <Expander Header="功能菜单" IsExpanded="True" Margin="10">
                            <StackPanel Margin="5">
                                <ListBox x:Name="NavigationListBox" SelectionMode="Single">
                                    <ListBoxItem>
                                        <StackPanel Orientation="Horizontal" Margin="5">
                                            <materialDesign:PackIcon Kind="Dashboard" VerticalAlignment="Center" Margin="0,0,10,0"/>
                                            <TextBlock Text="仪表板" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </ListBoxItem>
                                    <ListBoxItem IsSelected="True">
                                        <StackPanel Orientation="Horizontal" Margin="5">
                                            <materialDesign:PackIcon Kind="FileDocument" VerticalAlignment="Center" Margin="0,0,10,0"/>
                                            <TextBlock Text="脱密任务" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </ListBoxItem>
                                    <ListBoxItem>
                                        <StackPanel Orientation="Horizontal" Margin="5">
                                            <materialDesign:PackIcon Kind="Package" VerticalAlignment="Center" Margin="0,0,10,0"/>
                                            <TextBlock Text="安全包管理" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </ListBoxItem>
                                    <ListBoxItem>
                                        <StackPanel Orientation="Horizontal" Margin="5">
                                            <materialDesign:PackIcon Kind="FileChart" VerticalAlignment="Center" Margin="0,0,10,0"/>
                                            <TextBlock Text="审计日志" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </ListBoxItem>
                                    <ListBoxItem>
                                        <StackPanel Orientation="Horizontal" Margin="5">
                                            <materialDesign:PackIcon Kind="Cog" VerticalAlignment="Center" Margin="0,0,10,0"/>
                                            <TextBlock Text="系统设置" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </ListBoxItem>
                                </ListBox>
                            </StackPanel>
                        </Expander>

                        <!-- 统计信息 -->
                        <Expander Header="统计信息" IsExpanded="True" Margin="10">
                            <StackPanel Margin="10,5">
                                <materialDesign:Card Margin="0,5" Padding="10">
                                    <StackPanel>
                                        <TextBlock Text="今日任务" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <TextBlock Text="{Binding TodayTaskCount, FallbackValue='5'}" FontSize="20" Foreground="{StaticResource PrimaryBrush}"/>
                                    </StackPanel>
                                </materialDesign:Card>
                                <materialDesign:Card Margin="0,5" Padding="10">
                                    <StackPanel>
                                        <TextBlock Text="处理中" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <TextBlock Text="{Binding ProcessingTaskCount, FallbackValue='2'}" FontSize="20" Foreground="Orange"/>
                                    </StackPanel>
                                </materialDesign:Card>
                                <materialDesign:Card Margin="0,5" Padding="10">
                                    <StackPanel>
                                        <TextBlock Text="已完成" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <TextBlock Text="{Binding CompletedTaskCount, FallbackValue='12'}" FontSize="20" Foreground="Green"/>
                                    </StackPanel>
                                </materialDesign:Card>
                            </StackPanel>
                        </Expander>
                    </StackPanel>
                </ScrollViewer>
            </materialDesign:Card>

            <!-- 分隔条 -->
            <GridSplitter Grid.Column="1" Width="5" HorizontalAlignment="Center" VerticalAlignment="Stretch" Background="LightGray"/>

            <!-- 主内容区域 -->
            <materialDesign:Card Grid.Column="2" Margin="5,10,10,10" materialDesign:ShadowAssist.ShadowDepth="Depth2">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 内容标题栏 -->
                    <Border Grid.Row="0" Background="{DynamicResource PrimaryHueMidBrush}" Padding="20,15">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <StackPanel Grid.Column="0" Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="FileDocument" Foreground="White" Width="24" Height="24" VerticalAlignment="Center" Margin="0,0,10,0"/>
                                <TextBlock Text="脱密任务管理" Foreground="White" FontSize="18" FontWeight="Bold" VerticalAlignment="Center"/>
                            </StackPanel>
                            <StackPanel Grid.Column="1" Orientation="Horizontal">
                                <Button Style="{StaticResource MaterialDesignIconButton}" Margin="5" ToolTip="刷新">
                                    <materialDesign:PackIcon Kind="Refresh" Foreground="White"/>
                                </Button>
                                <Button Style="{StaticResource MaterialDesignIconButton}" Margin="5" ToolTip="导出">
                                    <materialDesign:PackIcon Kind="Export" Foreground="White"/>
                                </Button>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- 主内容 -->
                    <ContentPresenter Grid.Row="1" x:Name="MainContentPresenter" Margin="20">
                        <!-- 默认显示脱密任务列表 -->
                        <ScrollViewer VerticalScrollBarVisibility="Auto">
                            <StackPanel>
                                <!-- 任务过滤器 -->
                                <materialDesign:Card Margin="0,0,0,20" Padding="20">
                                    <StackPanel>
                                        <TextBlock Text="任务过滤" FontWeight="Bold" Margin="0,0,0,10"/>
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>
                                            <ComboBox Grid.Column="0" materialDesign:HintAssist.Hint="任务状态" Margin="0,0,10,0">
                                                <ComboBoxItem Content="全部状态"/>
                                                <ComboBoxItem Content="待处理"/>
                                                <ComboBoxItem Content="处理中"/>
                                                <ComboBoxItem Content="已完成"/>
                                                <ComboBoxItem Content="失败"/>
                                            </ComboBox>
                                            <DatePicker Grid.Column="1" materialDesign:HintAssist.Hint="创建日期" Margin="0,0,10,0"/>
                                            <Button Grid.Column="2" Style="{StaticResource MaterialDesignRaisedButton}" Content="筛选"/>
                                        </Grid>
                                    </StackPanel>
                                </materialDesign:Card>

                                <!-- 任务列表 -->
                                <DataGrid x:Name="TaskDataGrid" AutoGenerateColumns="False" CanUserAddRows="False" CanUserDeleteRows="False" IsReadOnly="True" materialDesign:DataGridAssist.CellPadding="13" materialDesign:DataGridAssist.ColumnHeaderPadding="8">
                                    <DataGrid.Columns>
                                        <DataGridTextColumn Header="任务名称" Width="200"/>
                                        <DataGridTextColumn Header="申请人" Width="100"/>
                                        <DataGridTextColumn Header="收件人" Width="150"/>
                                        <DataGridTextColumn Header="状态" Width="100"/>
                                        <DataGridTextColumn Header="创建时间" Width="150"/>
                                        <DataGridTextColumn Header="进度" Width="80"/>
                                        <DataGridTemplateColumn Header="操作" Width="150">
                                            <DataGridTemplateColumn.CellTemplate>
                                                <DataTemplate>
                                                    <StackPanel Orientation="Horizontal">
                                                        <Button Style="{StaticResource MaterialDesignIconButton}" ToolTip="查看详情" Margin="2">
                                                            <materialDesign:PackIcon Kind="Eye"/>
                                                        </Button>
                                                        <Button Style="{StaticResource MaterialDesignIconButton}" ToolTip="编辑" Margin="2">
                                                            <materialDesign:PackIcon Kind="Edit"/>
                                                        </Button>
                                                        <Button Style="{StaticResource MaterialDesignIconButton}" ToolTip="删除" Margin="2">
                                                            <materialDesign:PackIcon Kind="Delete"/>
                                                        </Button>
                                                    </StackPanel>
                                                </DataTemplate>
                                            </DataGridTemplateColumn.CellTemplate>
                                        </DataGridTemplateColumn>
                                    </DataGrid.Columns>
                                </DataGrid>
                            </StackPanel>
                        </ScrollViewer>
                    </ContentPresenter>
                </Grid>
            </materialDesign:Card>
        </Grid>

        <!-- 底部状态栏 -->
        <materialDesign:ColorZone Grid.Row="2" Mode="PrimaryDark" Padding="16,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Information" Foreground="White" VerticalAlignment="Center" Margin="0,0,5,0"/>
                    <TextBlock Text="就绪" Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="20,0">
                    <materialDesign:PackIcon Kind="Server" Foreground="White" VerticalAlignment="Center" Margin="0,0,5,0"/>
                    <TextBlock Text="已连接" Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Clock" Foreground="White" VerticalAlignment="Center" Margin="0,0,5,0"/>
                    <TextBlock Text="{Binding Source={x:Static sys:DateTime.Now}, StringFormat=yyyy-MM-dd HH:mm:ss}" Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </materialDesign:ColorZone>
    </Grid>
</Window>