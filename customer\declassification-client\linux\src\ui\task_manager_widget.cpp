#include "ui/widgets/task_manager_widget.h"

#include <QTreeWidget>
#include <QHeaderView>
#include <QVBoxLayout>
#include <QMenu>
#include <QMessageBox>
#include <QDateTime>

namespace DeclassificationClient::UI {

TaskManagerWidget::TaskManagerWidget(QWidget* parent) : QWidget(parent) {
    setupUI();
    setupConnections();
}

TaskManagerWidget::~TaskManagerWidget() = default;

void TaskManagerWidget::updateTasks(const std::vector<Models::DeclassificationTask>& tasks) {
    taskTreeWidget_->clear();
    for (const auto& task : tasks) {
        auto* item = new QTreeWidgetItem(taskTreeWidget_);
        updateTaskItem(item, task);
        taskTreeWidget_->addTopLevelItem(item);
    }
}

void TaskManagerWidget::showContextMenu(const QPoint& point) {
    QTreeWidgetItem* item = taskTreeWidget_->itemAt(point);
    
    // Disable actions if no item is selected
    bool itemSelected = (item != nullptr);
    contextMenu_->actions().at(0)->setEnabled(itemSelected); // View Details
    contextMenu_->actions().at(2)->setEnabled(itemSelected); // Process Task
    contextMenu_->actions().at(3)->setEnabled(itemSelected); // Cancel Task
    contextMenu_->actions().at(5)->setEnabled(itemSelected); // Delete Task

    contextMenu_->exec(taskTreeWidget_->mapToGlobal(point));
}

void TaskManagerWidget::processSelectedTask() {
    // Implementation will be added later
}

void TaskManagerWidget::cancelSelectedTask() {
    // Implementation will be added later
}

void TaskManagerWidget::deleteSelectedTask() {
    QTreeWidgetItem* currentItem = taskTreeWidget_->currentItem();
    if (!currentItem) {
        return;
    }

    QMessageBox::StandardButton reply;
    reply = QMessageBox::question(this, tr("Delete Task"), 
                                  tr("Are you sure you want to delete this task?"),
                                  QMessageBox::Yes | QMessageBox::No);

    if (reply == QMessageBox::Yes) {
        QString taskId = currentItem->data(0, Qt::UserRole).toString();
        emit taskDeletionRequested(taskId);
    }
}

void TaskManagerWidget::viewTaskDetails() {
    // Implementation will be added later
}

bool TaskManagerWidget::isTaskSelected() const {
    return taskTreeWidget_->currentItem() != nullptr;
}

void TaskManagerWidget::onTaskSelectionChanged(QTreeWidgetItem* current, QTreeWidgetItem* previous) {
    if (current) {
        QString taskId = current->data(0, Qt::UserRole).toString();
        emit taskSelected(taskId);
    } else {
        emit taskSelected(""); // Emit empty string when no item is selected
    }
}

void TaskManagerWidget::setupUI() {
    auto* layout = new QVBoxLayout(this);
    layout->setContentsMargins(0, 0, 0, 0);

    taskTreeWidget_ = new QTreeWidget(this);
    taskTreeWidget_->setColumnCount(4);
    taskTreeWidget_->setHeaderLabels({tr("Task Name"), tr("Status"), tr("Progress"), tr("Created At")});
    taskTreeWidget_->header()->setSectionResizeMode(0, QHeaderView::Stretch);
    taskTreeWidget_->setSortingEnabled(true);

    layout->addWidget(taskTreeWidget_);
    setLayout(layout);

    // Context Menu
    taskTreeWidget_->setContextMenuPolicy(Qt::CustomContextMenu);
    contextMenu_ = new QMenu(this);
    contextMenu_->addAction(tr("View Details..."), this, &TaskManagerWidget::viewTaskDetails);
    contextMenu_->addSeparator();
    contextMenu_->addAction(tr("Process Task"), this, &TaskManagerWidget::processSelectedTask);
    contextMenu_->addAction(tr("Cancel Task"), this, &TaskManagerWidget::cancelSelectedTask);
    contextMenu_->addSeparator();
    contextMenu_->addAction(tr("Delete Task"), this, &TaskManagerWidget::deleteSelectedTask);
}

void TaskManagerWidget::setupConnections() {
    connect(taskTreeWidget_, &QTreeWidget::customContextMenuRequested, this, &TaskManagerWidget::showContextMenu);
    connect(taskTreeWidget_, &QTreeWidget::currentItemChanged, this, &TaskManagerWidget::onTaskSelectionChanged);
}

void TaskManagerWidget::updateTaskItem(QTreeWidgetItem* item, const Models::DeclassificationTask& task) {
    // 0: Task Name
    item->setText(0, QString::fromStdString(task.taskName));
    item->setData(0, Qt::UserRole, QString::fromStdString(task.taskId));

    // 1: Status
    QString statusText;
    QIcon statusIcon;
    switch (task.status) {
        case Models::TaskStatus::Pending:
            statusText = tr("Pending");
            statusIcon = QIcon::fromTheme("document-properties");
            break;
        case Models::TaskStatus::Processing:
            statusText = tr("Processing");
            statusIcon = QIcon::fromTheme("media-playback-start");
            break;
        case Models::TaskStatus::Completed:
            statusText = tr("Completed");
            statusIcon = QIcon::fromTheme("emblem-ok");
            break;
        case Models::TaskStatus::Failed:
            statusText = tr("Failed");
            statusIcon = QIcon::fromTheme("emblem-important");
            break;
        case Models::TaskStatus::Cancelled:
            statusText = tr("Cancelled");
            statusIcon = QIcon::fromTheme("process-stop");
            break;
    }
    item->setText(1, statusText);
    item->setIcon(1, statusIcon);

    // 2: Progress
    item->setText(2, QString::number(task.progress) + "%");

    // 3: Created At
    QDateTime dateTime = QDateTime::fromSecsSinceEpoch(task.createdAt);
    item->setText(3, dateTime.toString(Qt::ISODate));
}

} // namespace DeclassificationClient::UI