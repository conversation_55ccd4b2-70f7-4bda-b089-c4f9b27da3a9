using Microsoft.EntityFrameworkCore;
using CryptoSystem.SystemManager.Models;
using CryptoSystem.SystemManager.Data;
using System.Security.Cryptography;
using System.Text;

namespace CryptoSystem.SystemManager.Services
{
    /// <summary>
    /// 用户服务实现
    /// </summary>
    public class UserService : IUserService
    {
        private readonly SystemManagerDbContext _context;
        private readonly ILogger<UserService> _logger;

        public UserService(SystemManagerDbContext context, ILogger<UserService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<(bool Success, User? User, string Message)> AuthenticateAsync(string username, string password)
        {
            try
            {
                var user = await _context.Users
                    .Include(u => u.Department)
                    .FirstOrDefaultAsync(u => u.Username == username && u.Status != UserStatus.Deleted);

                if (user == null)
                {
                    await RecordLoginFailureAsync(username, "Unknown");
                    return (false, null, "用户不存在");
                }

                if (user.Status == UserStatus.Disabled)
                {
                    return (false, null, "账户已禁用");
                }

                if (user.Status == UserStatus.Locked)
                {
                    if (user.LockedUntil.HasValue && user.LockedUntil > DateTime.UtcNow)
                    {
                        return (false, null, $"账户已锁定，解锁时间：{user.LockedUntil:yyyy-MM-dd HH:mm:ss}");
                    }
                    else
                    {
                        // 自动解锁
                        user.Status = UserStatus.Active;
                        user.LockedUntil = null;
                        await _context.SaveChangesAsync();
                    }
                }

                if (!VerifyPassword(password, user.PasswordHash))
                {
                    await RecordLoginFailureAsync(username, "Unknown");
                    return (false, null, "密码错误");
                }

                // 检查密码是否已过期
                if (user.PasswordExpiryDate.HasValue && user.PasswordExpiryDate < DateTime.UtcNow)
                {
                    return (false, user, "密码已过期，请联系管理员重置");
                }

                // 更新最后登录时间
                user.LastLoginTime = DateTime.UtcNow;
                await _context.SaveChangesAsync();
                await RecordSuccessfulLoginAsync(user.UserId, "Unknown");

                _logger.LogInformation("用户 {Username} 登录成功", username);
                return (true, user, "登录成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "用户认证失败：{Username}", username);
                return (false, null, "认证服务异常");
            }
        }

        public async Task<IEnumerable<User>> GetAllUsersAsync(bool includeDeleted = false)
        {
            var query = _context.Users.Include(u => u.Department);
            
            if (!includeDeleted)
            {
                query = query.Where(u => u.Status != UserStatus.Deleted);
            }

            return await query.OrderBy(u => u.Username).ToListAsync();
        }

        public async Task<User?> GetUserByIdAsync(string userId)
        {
            return await _context.Users
                .Include(u => u.Department)
                .FirstOrDefaultAsync(u => u.UserId == userId && u.Status != UserStatus.Deleted);
        }

        public async Task<User?> GetUserByUsernameAsync(string username)
        {
            return await _context.Users
                .Include(u => u.Department)
                .FirstOrDefaultAsync(u => u.Username == username && u.Status != UserStatus.Deleted);
        }

        public async Task<(IEnumerable<User> Users, int TotalCount)> GetUsersPagedAsync(
            int pageIndex, int pageSize, string? searchKeyword = null,
            string? departmentId = null, UserRole? role = null, UserStatus? status = null)
        {
            var query = _context.Users.Include(u => u.Department)
                .Where(u => u.Status != UserStatus.Deleted);

            // 搜索关键词过滤
            if (!string.IsNullOrWhiteSpace(searchKeyword))
            {
                query = query.Where(u => u.Username.Contains(searchKeyword) ||
                                    u.DisplayName.Contains(searchKeyword) ||
                                    u.Email.Contains(searchKeyword));
            }

            // 部门过滤
            if (!string.IsNullOrWhiteSpace(departmentId))
            {
                query = query.Where(u => u.DepartmentId == departmentId);
            }

            // 角色过滤
            if (role.HasValue)
            {
                query = query.Where(u => u.Role == role.Value);
            }

            // 状态过滤
            if (status.HasValue)
            {
                query = query.Where(u => u.Status == status.Value);
            }

            var totalCount = await query.CountAsync();
            var users = await query
                .OrderBy(u => u.Username)
                .Skip(pageIndex * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return (users, totalCount);
        }

        public async Task<(bool Success, string Message)> CreateUserAsync(User user, string password, string createdBy)
        {
            try
            {
                // 验证用户名唯一性
                if (await _context.Users.AnyAsync(u => u.Username == user.Username))
                {
                    return (false, "用户名已存在");
                }

                // 验证邮箱唯一性
                if (!string.IsNullOrWhiteSpace(user.Email) && 
                    await _context.Users.AnyAsync(u => u.Email == user.Email))
                {
                    return (false, "邮箱地址已存在");
                }

                // 验证密码策略
                var passwordValidation = await ValidatePasswordPolicyAsync(password);
                if (!passwordValidation.IsValid)
                {
                    return (false, $"密码不符合策略要求：{string.Join(", ", passwordValidation.Errors)}");
                }

                // 设置用户属性
                user.UserId = Guid.NewGuid().ToString("N");
                user.PasswordHash = HashPassword(password);
                user.Status = UserStatus.Active;
                user.CreatedTime = DateTime.UtcNow;
                user.CreatedBy = createdBy;
                user.PasswordExpiryDate = DateTime.UtcNow.AddDays(90); // 90天后过期
                user.MustChangePassword = true;

                _context.Users.Add(user);
                await _context.SaveChangesAsync();

                _logger.LogInformation("用户 {Username} 创建成功，创建者：{CreatedBy}", user.Username, createdBy);
                return (true, "用户创建成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建用户失败：{Username}", user.Username);
                return (false, "创建用户时发生异常");
            }
        }

        public async Task<(bool Success, string Message)> UpdateUserAsync(User user, string modifiedBy)
        {
            try
            {
                var existingUser = await _context.Users.FindAsync(user.UserId);
                if (existingUser == null)
                {
                    return (false, "用户不存在");
                }

                // 验证用户名唯一性（排除当前用户）
                if (await _context.Users.AnyAsync(u => u.Username == user.Username && u.UserId != user.UserId))
                {
                    return (false, "用户名已存在");
                }

                // 验证邮箱唯一性（排除当前用户）
                if (!string.IsNullOrWhiteSpace(user.Email) && 
                    await _context.Users.AnyAsync(u => u.Email == user.Email && u.UserId != user.UserId))
                {
                    return (false, "邮箱地址已存在");
                }

                // 更新字段
                existingUser.Username = user.Username;
                existingUser.DisplayName = user.DisplayName;
                existingUser.Email = user.Email;
                existingUser.Phone = user.Phone;
                existingUser.Role = user.Role;
                existingUser.DepartmentId = user.DepartmentId;
                existingUser.Remark = user.Remark;
                existingUser.ModifiedTime = DateTime.UtcNow;
                existingUser.ModifiedBy = modifiedBy;

                await _context.SaveChangesAsync();

                _logger.LogInformation("用户 {Username} 更新成功，修改者：{ModifiedBy}", user.Username, modifiedBy);
                return (true, "用户更新成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新用户失败：{Username}", user.Username);
                return (false, "更新用户时发生异常");
            }
        }

        public async Task<(bool Success, string Message)> DeleteUserAsync(string userId, string deletedBy)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                {
                    return (false, "用户不存在");
                }

                // 软删除
                user.Status = UserStatus.Deleted;
                user.ModifiedTime = DateTime.UtcNow;
                user.ModifiedBy = deletedBy;

                await _context.SaveChangesAsync();

                _logger.LogInformation("用户 {Username} 删除成功，删除者：{DeletedBy}", user.Username, deletedBy);
                return (true, "用户删除成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除用户失败：{UserId}", userId);
                return (false, "删除用户时发生异常");
            }
        }

        public async Task<(bool Success, string Message)> SetUserEnabledAsync(string userId, bool enabled, string operatedBy)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                {
                    return (false, "用户不存在");
                }

                user.Status = enabled ? UserStatus.Active : UserStatus.Disabled;
                user.ModifiedTime = DateTime.UtcNow;
                user.ModifiedBy = operatedBy;
                await _context.SaveChangesAsync();

                var operation = enabled ? "启用" : "禁用";
                _logger.LogInformation("用户 {Username} {Operation}成功，操作者：{OperatedBy}", user.Username, operation, operatedBy);
                return (true, $"用户{operation}成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设置用户状态失败：{UserId}", userId);
                return (false, "设置用户状态时发生异常");
            }
        }

        public async Task<(bool Success, string Message)> ResetPasswordAsync(string userId, string newPassword, bool mustChangePassword, string operatedBy)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                {
                    return (false, "用户不存在");
                }

                // 验证密码策略
                var passwordValidation = await ValidatePasswordPolicyAsync(newPassword);
                if (!passwordValidation.IsValid)
                {
                    return (false, $"密码不符合策略要求：{string.Join(", ", passwordValidation.Errors)}");
                }

                user.PasswordHash = HashPassword(newPassword);
                user.MustChangePassword = mustChangePassword;
                user.PasswordExpiryDate = DateTime.UtcNow.AddDays(90);
                user.ModifiedTime = DateTime.UtcNow;
                user.ModifiedBy = operatedBy;
                user.FailedLoginAttempts = 0; // 重置失败次数

                await _context.SaveChangesAsync();

                _logger.LogInformation("用户 {Username} 密码重置成功，操作者：{OperatedBy}", user.Username, operatedBy);
                return (true, "密码重置成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重置密码失败：{UserId}", userId);
                return (false, "重置密码时发生异常");
            }
        }

        public async Task<(bool Success, string Message)> ChangePasswordAsync(string userId, string oldPassword, string newPassword)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                {
                    return (false, "用户不存在");
                }

                if (!VerifyPassword(oldPassword, user.PasswordHash))
                {
                    return (false, "原密码错误");
                }

                // 验证密码策略
                var passwordValidation = await ValidatePasswordPolicyAsync(newPassword);
                if (!passwordValidation.IsValid)
                {
                    return (false, $"密码不符合策略要求：{string.Join(", ", passwordValidation.Errors)}");
                }

                user.PasswordHash = HashPassword(newPassword);
                user.MustChangePassword = false;
                user.PasswordExpiryDate = DateTime.UtcNow.AddDays(90);
                user.ModifiedTime = DateTime.UtcNow;
                user.ModifiedBy = userId;

                await _context.SaveChangesAsync();

                _logger.LogInformation("用户 {Username} 修改密码成功", user.Username);
                return (true, "密码修改成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "修改密码失败：{UserId}", userId);
                return (false, "修改密码时发生异常");
            }
        }

        public async Task<(bool Success, string Message)> LockUserAsync(string userId, TimeSpan lockoutDuration, string reason, string operatedBy)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                {
                    return (false, "用户不存在");
                }

                user.Status = UserStatus.Locked;
                user.LockedUntil = DateTime.UtcNow.Add(lockoutDuration);
                user.ModifiedTime = DateTime.UtcNow;
                user.ModifiedBy = operatedBy;
                user.Remark = $"锁定原因：{reason}";

                await _context.SaveChangesAsync();

                _logger.LogInformation("用户 {Username} 锁定成功，锁定时长：{Duration}，原因：{Reason}，操作者：{OperatedBy}", 
                    user.Username, lockoutDuration, reason, operatedBy);
                return (true, "用户锁定成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "锁定用户失败：{UserId}", userId);
                return (false, "锁定用户时发生异常");
            }
        }

        public async Task<(bool Success, string Message)> UnlockUserAsync(string userId, string operatedBy)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                {
                    return (false, "用户不存在");
                }

                user.Status = UserStatus.Active;
                user.LockedUntil = null;
                user.FailedLoginAttempts = 0;
                user.ModifiedTime = DateTime.UtcNow;
                user.ModifiedBy = operatedBy;

                await _context.SaveChangesAsync();

                _logger.LogInformation("用户 {Username} 解锁成功，操作者：{OperatedBy}", user.Username, operatedBy);
                return (true, "用户解锁成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "解锁用户失败：{UserId}", userId);
                return (false, "解锁用户时发生异常");
            }
        }

        public async Task<(int SuccessCount, int FailedCount, List<string> ErrorMessages)> BatchImportUsersAsync(IEnumerable<User> users, string defaultPassword, string importedBy)
        {
            var successCount = 0;
            var failedCount = 0;
            var errorMessages = new List<string>();

            foreach (var user in users)
            {
                try
                {
                    var result = await CreateUserAsync(user, defaultPassword, importedBy);
                    if (result.Success)
                    {
                        successCount++;
                    }
                    else
                    {
                        failedCount++;
                        errorMessages.Add($"导入用户 {user.Username} 失败: {result.Message}");
                    }
                }
                catch (Exception ex)
                {
                    failedCount++;
                    errorMessages.Add($"导入用户 {user.Username} 失败: {ex.Message}");
                }
            }

            _logger.LogInformation("批量导入用户完成，成功：{SuccessCount}，失败：{FailedCount}", successCount, failedCount);
            return (successCount, failedCount, errorMessages);
        }

        public async Task<(int SuccessCount, int FailedCount, List<string> ErrorMessages)> BatchDeleteUsersAsync(IEnumerable<string> userIds, string deletedBy)
        {
            var successCount = 0;
            var failedCount = 0;
            var errorMessages = new List<string>();

            foreach (var userId in userIds)
            {
                try
                {
                    var result = await DeleteUserAsync(userId, deletedBy);
                    if (result.Success)
                    {
                        successCount++;
                    }
                    else
                    {
                        failedCount++;
                        errorMessages.Add($"删除用户 {userId} 失败: {result.Message}");
                    }
                }
                catch (Exception ex)
                {
                    failedCount++;
                    errorMessages.Add($"删除用户 {userId} 失败: {ex.Message}");
                }
            }

            _logger.LogInformation("批量删除用户完成，成功：{SuccessCount}，失败：{FailedCount}", successCount, failedCount);
            return (successCount, failedCount, errorMessages);
        }

        public async Task<UserStatistics> GetUserStatisticsAsync()
        {
            var totalUsers = await _context.Users.CountAsync(u => u.Status != UserStatus.Deleted);
            var activeUsers = await _context.Users.CountAsync(u => u.Status == UserStatus.Active);
            var disabledUsers = await _context.Users.CountAsync(u => u.Status == UserStatus.Disabled);
            var lockedUsers = await _context.Users.CountAsync(u => u.Status == UserStatus.Locked);
            var recentActiveUsers = await _context.Users.CountAsync(u => u.LastLoginTime >= DateTime.UtcNow.AddDays(-30));
            var passwordExpiringUsers = await _context.Users.CountAsync(u => u.PasswordExpiryDate <= DateTime.UtcNow.AddDays(7));

            var usersByRole = await _context.Users
                .Where(u => u.Status != UserStatus.Deleted)
                .GroupBy(u => u.Role)
                .ToDictionaryAsync(g => g.Key, g => g.Count());

            var usersByStatus = await _context.Users
                .Where(u => u.Status != UserStatus.Deleted)
                .GroupBy(u => u.Status)
                .ToDictionaryAsync(g => g.Key, g => g.Count());

            var usersByDepartment = await _context.Users
                .Where(u => u.Status != UserStatus.Deleted && u.DepartmentId != null)
                .GroupBy(u => u.DepartmentId)
                .ToDictionaryAsync(g => g.Key!, g => g.Count());

            return new UserStatistics
            {
                TotalUsers = totalUsers,
                ActiveUsers = activeUsers,
                DisabledUsers = disabledUsers,
                LockedUsers = lockedUsers,
                RecentActiveUsers = recentActiveUsers,
                PasswordExpiringUsers = passwordExpiringUsers,
                UsersByRole = usersByRole,
                UsersByStatus = usersByStatus,
                UsersByDepartment = usersByDepartment
            };
        }

        public async Task<bool> IsUsernameAvailableAsync(string username, string? excludeUserId = null)
        {
            var query = _context.Users.Where(u => u.Username == username);
            if (!string.IsNullOrEmpty(excludeUserId))
            {
                query = query.Where(u => u.UserId != excludeUserId);
            }
            return !await query.AnyAsync();
        }

        public async Task<bool> IsEmailAvailableAsync(string email, string? excludeUserId = null)
        {
            var query = _context.Users.Where(u => u.Email == email);
            if (!string.IsNullOrEmpty(excludeUserId))
            {
                query = query.Where(u => u.UserId != excludeUserId);
            }
            return !await query.AnyAsync();
        }

        public async Task<bool> RecordLoginFailureAsync(string username, string ipAddress)
        {
            try
            {
                var user = await _context.Users.FirstOrDefaultAsync(u => u.Username == username);
                if (user != null)
                {
                    user.FailedLoginAttempts++;
                    user.LastFailedLoginTime = DateTime.UtcNow;

                    // 检查是否需要锁定账户（连续失败5次）
                    if (user.FailedLoginAttempts >= 5)
                    {
                        user.Status = UserStatus.Locked;
                        user.LockedUntil = DateTime.UtcNow.AddMinutes(30); // 锁定30分钟
                        await _context.SaveChangesAsync();
                        return true; // 需要锁定
                    }

                    await _context.SaveChangesAsync();
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "记录登录失败异常：{Username}", username);
                return false;
            }
        }

        public async Task RecordSuccessfulLoginAsync(string userId, string ipAddress)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user != null)
                {
                    user.FailedLoginAttempts = 0; // 重置失败次数
                    user.LastLoginTime = DateTime.UtcNow;
                    await _context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "记录成功登录异常：{UserId}", userId);
            }
        }

        public async Task<IEnumerable<Device>> GetUserDevicesAsync(string userId)
        {
            return await _context.UserDevices
                .Where(ud => ud.UserId == userId)
                .Include(ud => ud.Device)
                .Select(ud => ud.Device)
                .ToListAsync();
        }

        public async Task<IEnumerable<Policy>> GetUserPoliciesAsync(string userId)
        {
            return await _context.UserPolicies
                .Where(up => up.UserId == userId)
                .Include(up => up.Policy)
                .Select(up => up.Policy)
                .ToListAsync();
        }

        public async Task<(int CreatedCount, int UpdatedCount, int ErrorCount, List<string> ErrorMessages)> SyncLdapUsersAsync(string syncBy)
        {
            // LDAP同步功能暂不实现，返回空结果
            return (0, 0, 0, new List<string> { "LDAP同步功能暂未实现" });
        }

        public async Task<(bool IsValid, List<string> Errors)> ValidatePasswordPolicyAsync(string password)
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(password))
            {
                errors.Add("密码不能为空");
                return (false, errors);
            }

            if (password.Length < 8)
                errors.Add("密码长度至少8位");

            if (!password.Any(char.IsUpper))
                errors.Add("密码必须包含大写字母");

            if (!password.Any(char.IsLower))
                errors.Add("密码必须包含小写字母");

            if (!password.Any(char.IsDigit))
                errors.Add("密码必须包含数字");

            if (!password.Any(c => "!@#$%^&*()_+-=[]{}|;:,.<>?".Contains(c)))
                errors.Add("密码必须包含特殊字符");

            return (errors.Count == 0, errors);
        }

        public async Task<IEnumerable<User>> GetUsersWithExpiringPasswordsAsync(int daysBeforeExpiry = 7)
        {
            var expiryDate = DateTime.UtcNow.AddDays(daysBeforeExpiry);
            return await _context.Users
                .Where(u => u.Status == UserStatus.Active && 
                           u.PasswordExpiryDate.HasValue && 
                           u.PasswordExpiryDate <= expiryDate)
                .ToListAsync();
        }

        #region 私有辅助方法

        /// <summary>
        /// 哈希密码
        /// </summary>
        private static string HashPassword(string password)
        {
            using var sha256 = SHA256.Create();
            var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password));
            return Convert.ToBase64String(hashedBytes);
        }

        /// <summary>
        /// 验证密码
        /// </summary>
        private static bool VerifyPassword(string password, string hashedPassword)
        {
            var hashOfInput = HashPassword(password);
            return hashOfInput.Equals(hashedPassword);
        }

        #endregion
    }
} 