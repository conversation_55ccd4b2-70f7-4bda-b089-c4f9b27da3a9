using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using System.Windows.Threading;
using Microsoft.Extensions.Logging;
using KeyGenerator.Models;
using KeyGenerator.Services;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Windows;
using KeyGenerator.Views;
using CommunityToolkit.Mvvm.Messaging;
using KeyGenerator.Messages;

namespace KeyGenerator.ViewModels
{
    /// <summary>
    /// 密钥管理视图模型 (简化版)
    /// </summary>
    public partial class KeyManagementViewModel(
        ILogger<KeyManagementViewModel> logger,
        IDatabaseService databaseService) : ViewModelBase()
    {
        private readonly Dispatcher _dispatcher = Dispatcher.CurrentDispatcher;

        public ObservableCollection<MasterKeyEntity> AllKeys { get; } = new ObservableCollection<MasterKeyEntity>();
        public ObservableCollection<MasterKeyEntity> FilteredKeys { get; } = new ObservableCollection<MasterKeyEntity>();

        public ICommand RefreshCommand { get; }
        public ICommand EditKeyCommand { get; }
        public ICommand DeleteKeyCommand { get; }
        public ICommand DistributeKeyCommand { get; }

        // Constructor body for primary constructor
        {
            RefreshCommand = new AsyncRelayCommand(RefreshAsync);
            EditKeyCommand = new AsyncRelayCommand<MasterKeyEntity>(EditKeyAsync, (key) => key != null);
            DeleteKeyCommand = new AsyncRelayCommand<MasterKeyEntity>(DeleteKeyAsync, (key) => key != null);
            DistributeKeyCommand = new RelayCommand<MasterKeyEntity>(DistributeKey, (key) => key != null);

            _ = InitializeAsync();
        }

        [ObservableProperty]
        private string _searchText = "";

        [ObservableProperty]
        private MasterKeyEntity? _selectedKey;

        [ObservableProperty]
        private string _statusText = "就绪";
        
        public int TotalCount => AllKeys.Count;

        private void DistributeKey(MasterKeyEntity? key)
        {
            if (key == null) return;
            logger.LogInformation("请求分发密钥 {KeyId}", key.KeyId);
            StatusText = $"准备分发密钥: {key.KeyName}";
            WeakReferenceMessenger.Default.Send(new NavigationRequestMessage("KeyDistribution", key.KeyId));
        }

        private async Task EditKeyAsync(MasterKeyEntity? key)
        {
            if (key == null) return; // Add null check for key
            try
            {
                StatusText = $"正在编辑密钥: {key.KeyName}";
                
                // 创建编辑对话框
                var editDialog = new KeyEditDialog(key)
                {
                    Owner = Application.Current.MainWindow
                };
                
                var result = editDialog.ShowDialog();
                
                if (result == true && editDialog.EditedKey != null)
                {
                    // 确认修改操作
                    var confirmResult = MessageBox.Show(
                        $"确定要保存对密钥 '{key.KeyName}' 的修改吗？\n\n" +
                        $"修改内容:\n" +
                        $"• 过期日期: {key.ExpirationDate:yyyy-MM-dd} → {editDialog.EditedKey.ExpirationDate:yyyy-MM-dd}\n" +
                        $"• 客户信息: {key.ClientName} → {editDialog.EditedKey.ClientName}\n" +
                        $"• 备注信息: {key.Remarks} → {editDialog.EditedKey.Remarks}",
                        "确认修改", 
                        MessageBoxButton.YesNo, 
                        MessageBoxImage.Question);

                    if (confirmResult == MessageBoxResult.Yes)
                    {
                        // 记录修改前的信息用于审计
                        var originalKey = new MasterKeyEntity
                        {
                            KeyId = key.KeyId,
                            KeyName = key.KeyName,
                            ClientName = key.ClientName,
                            ExpirationDate = key.ExpirationDate,
                            Remarks = key.Remarks,
                            UsagePolicy = key.UsagePolicy ?? string.Empty, // 确保 UsagePolicy 被赋值
                        };

                        // 更新密钥信息
                        key.ClientName = editDialog.EditedKey.ClientName;
                        key.ExpirationDate = editDialog.EditedKey.ExpirationDate;
                        key.Remarks = editDialog.EditedKey.Remarks;
                        key.UpdatedTime = DateTime.Now;

                        // 保存到数据库
                        var updateSuccess = await databaseService.UpdateMasterKeyAsync(key);
                        
                        if (updateSuccess)
                        {
                            // 记录操作日志
                            await LogKeyModificationAsync(originalKey, key);
                            
                            // 刷新界面
                            await _dispatcher.InvokeAsync(() =>
                            {
                                ApplyFilters();
                            });
                            
                            StatusText = $"密钥 {key.KeyName} 修改成功";
                            
                            // TODO: 通知系统管理器密钥已更新
                            logger.LogInformation("密钥修改成功: {KeyId}", key.KeyId);
                        }
                        else
                        {
                            StatusText = "密钥修改失败：数据库更新失败";
                            logger.LogError("密钥修改失败：数据库更新失败 {KeyId}", key.KeyId);
                        }
                    }
                }
                else
                {
                    StatusText = "密钥编辑已取消";
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "编辑密钥失败");
                StatusText = $"编辑失败: {ex.Message}";
                MessageBox.Show($"编辑密钥时发生错误：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LogKeyModificationAsync(MasterKeyEntity originalKey, MasterKeyEntity modifiedKey)
        {
            try
            {
                var auditLog = new AuditLogEntity
                {
                    LogId = Guid.NewGuid().ToString(),
                    EventTime = DateTime.Now,
                    EventAction = "密钥修改",
                    TargetResourceType = "主密钥",
                    TargetResourceId = originalKey.KeyId,
                    OperatorId = Environment.UserName,
                    OperatorName = Environment.UserName,
                    EventDescription = $"密钥修改详情:\n" +
                             $"密钥ID: {originalKey.KeyId}\n" +
                             $"密钥名称: {originalKey.KeyName}\n" +
                             $"客户名称: {originalKey.ClientName} → {modifiedKey.ClientName}\n" +
                             $"过期日期: {originalKey.ExpirationDate:yyyy-MM-dd HH:mm:ss} → {modifiedKey.ExpirationDate:yyyy-MM-dd HH:mm:ss}\n" +
                             $"备注信息: {originalKey.Remarks} → {modifiedKey.Remarks}",
                    EventResult = "成功",
                    ErrorMessage = null
                };

                await databaseService.SaveAuditLogAsync(auditLog);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "记录密钥修改日志失败");
            }
        }

        private async Task DeleteKeyAsync(MasterKeyEntity? key)
        {
            if (key == null) return;
            try
            {
                var result = MessageBox.Show(
                    $"确定要删除密钥 '{key.KeyName}' (客户: {key.ClientName}) 吗？\n\n此操作不可恢复！", 
                    "确认删除", 
                    MessageBoxButton.YesNo, 
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    StatusText = $"正在删除密钥: {key.KeyName}";
                    
                    await databaseService.DeleteKeyAsync(key.KeyId);
                    
                    await _dispatcher.InvokeAsync(() =>
                    {
                        AllKeys.Remove(key);
                        ApplyFilters();
                    });
                    
                    StatusText = $"密钥 {key.KeyName} 已删除";
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "删除密钥失败");
                StatusText = $"删除失败: {ex.Message}";
            }
        }

        #region 私有方法

        private async Task InitializeAsync()
        {
            try
            {
                await RefreshAsync();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "初始化密钥管理页面失败的主要构造函数中");
            }
        }

        private async Task RefreshAsync()
        {
            try
            {
                StatusText = "正在加载密钥...";
                var keys = await databaseService.GetAllMasterKeysAsync();
                
                await _dispatcher.InvokeAsync(() =>
                {
                    AllKeys.Clear();
                    foreach (var key in keys)
                    {
                        AllKeys.Add(key);
                    }
                    ApplyFilters();
                });
                StatusText = "密钥列表已刷新";
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "刷新密钥列表失败");
                StatusText = $"加载失败: {ex.Message}";
            }
        }

        private void ApplyFilters()
        {
            var query = AllKeys.AsQueryable();

            if (!string.IsNullOrWhiteSpace(SearchText))
            {
                query = query.Where(key =>
                    (key.KeyName != null && key.KeyName.Contains(SearchText, StringComparison.OrdinalIgnoreCase)) ||
                    (key.ClientName != null && key.ClientName.Contains(SearchText, StringComparison.OrdinalIgnoreCase)) ||
                    (key.Remarks != null && key.Remarks.Contains(SearchText, StringComparison.OrdinalIgnoreCase)) ||
                    (key.KeyId != null && key.KeyId.Contains(SearchText, StringComparison.OrdinalIgnoreCase)));
            }

            var filteredList = query.ToList();

            _dispatcher.Invoke(() =>
            {
                FilteredKeys.Clear();
                foreach (var key in filteredList)
                {
                    FilteredKeys.Add(key);
                }
            });
        }

        partial void OnSearchTextChanged(string value)
        {
            ApplyFilters();
        }

        #endregion
    }
} 