<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>WinExe</OutputType>
        <TargetFramework>net9.0-windows</TargetFramework>
        <UseWPF>true</UseWPF>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <AssemblyName>CryptoSystem.SystemManager</AssemblyName>
        <RootNamespace>CryptoSystem.SystemManager</RootNamespace>
        <ApplicationIcon>Resources\Icons\app.ico</ApplicationIcon>
        <StartupObject>CryptoSystem.SystemManager.App</StartupObject>
        <Version>1.4.0</Version>
        <Company>CryptoSystem</Company>
        <Product>CryptoSystem System Manager</Product>
        <Description>CryptoSystem 系统管理器</Description>
        <Copyright>Copyright © 2025</Copyright>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
        <DebugType>full</DebugType>
        <DebugSymbols>true</DebugSymbols>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
        <DebugType>pdbonly</DebugType>
        <DebugSymbols>true</DebugSymbols>
        <Optimize>true</Optimize>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />
        <PackageReference Include="LiveCharts.Wpf" Version="0.9.7" />
        <PackageReference Include="MaterialDesignThemes" Version="5.1.0" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.0" />
        <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
        <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.2" />
        <PackageReference Include="Serilog.Extensions.Hosting" Version="8.0.0" />
        <PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />
        <PackageReference Include="System.DirectoryServices.AccountManagement" Version="9.0.0" />
    </ItemGroup>

    <ItemGroup>
        <None Remove="Resources\**" />
        <Resource Include="Resources\**" />
    </ItemGroup>

    <ItemGroup>
        <Compile Update="Properties\Settings.Designer.cs">
            <DesignTimeSharedInput>True</DesignTimeSharedInput>
            <AutoGen>True</AutoGen>
            <DependentUpon>Settings.settings</DependentUpon>
        </Compile>
    </ItemGroup>

    <ItemGroup>
        <None Update="Properties\Settings.settings">
            <Generator>SettingsSingleFileGenerator</Generator>
            <LastGenOutput>Settings.Designer.cs</LastGenOutput>
        </None>
        <None Update="appsettings.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="appsettings.Development.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
    </ItemGroup>

</Project> 