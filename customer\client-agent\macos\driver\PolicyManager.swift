import Foundation
import os.log

/// 策略管理器 - 管理文件加密策略
class PolicyManager {
    static let shared = PolicyManager()
    
    private let logger = OSLog(subsystem: "com.cryptosystem.policy", category: "PolicyManager")
    
    // 加密策略配置
    private var encryptionPolicies: [EncryptionPolicy] = []
    
    // 默认策略
    private var defaultPolicy: EncryptionPolicy = EncryptionPolicy.defaultPolicy()
    
    private init() {
        os_log("PolicyManager 初始化", log: logger, type: .info)
        loadPolicies()
    }
    
    // MARK: - 策略加载
    
    private func loadPolicies() {
        // 从配置文件或服务器加载策略
        // 这里先使用默认策略
        encryptionPolicies = [
            // 文档文件需要加密
            EncryptionPolicy(
                name: "文档加密策略",
                description: "对文档文件进行加密",
                fileExtensions: ["doc", "docx", "pdf", "txt", "rtf", "pages"],
                pathPatterns: [],
                enabled: true,
                priority: 100
            ),
            
            // 表格文件需要加密
            EncryptionPolicy(
                name: "表格加密策略", 
                description: "对表格文件进行加密",
                fileExtensions: ["xls", "xlsx", "csv", "numbers"],
                pathPatterns: [],
                enabled: true,
                priority: 90
            ),
            
            // 演示文件需要加密
            EncryptionPolicy(
                name: "演示加密策略",
                description: "对演示文件进行加密", 
                fileExtensions: ["ppt", "pptx", "key"],
                pathPatterns: [],
                enabled: true,
                priority: 80
            ),
            
            // 机密目录下的所有文件
            EncryptionPolicy(
                name: "机密目录策略",
                description: "机密目录下的所有文件需要加密",
                fileExtensions: [],
                pathPatterns: [
                    "*/机密/*",
                    "*/Confidential/*",
                    "*/Secret/*",
                    "*/私密/*"
                ],
                enabled: true,
                priority: 200
            )
        ]
        
        os_log("已加载 %d 个加密策略", log: logger, type: .info, encryptionPolicies.count)
    }
    
    // MARK: - 策略判断
    
    /// 判断文件是否应该加密
    func shouldEncryptFile(at path: String) -> Bool {
        let url = URL(fileURLWithPath: path)
        let fileName = url.lastPathComponent
        let fileExtension = url.pathExtension.lowercased()
        
        // 按优先级排序检查策略
        let sortedPolicies = encryptionPolicies.sorted { $0.priority > $1.priority }
        
        for policy in sortedPolicies {
            if !policy.enabled {
                continue
            }
            
            // 检查文件扩展名
            if !policy.fileExtensions.isEmpty {
                if policy.fileExtensions.contains(fileExtension) {
                    os_log("文件 %{public}@ 匹配策略: %{public}@ (扩展名)", 
                           log: logger, type: .debug, fileName, policy.name)
                    return true
                }
            }
            
            // 检查路径模式
            if !policy.pathPatterns.isEmpty {
                for pattern in policy.pathPatterns {
                    if matchesPattern(path: path, pattern: pattern) {
                        os_log("文件 %{public}@ 匹配策略: %{public}@ (路径)", 
                               log: logger, type: .debug, fileName, policy.name)
                        return true
                    }
                }
            }
        }
        
        // 检查默认策略
        if defaultPolicy.enabled {
            if !defaultPolicy.fileExtensions.isEmpty && defaultPolicy.fileExtensions.contains(fileExtension) {
                os_log("文件 %{public}@ 匹配默认策略", log: logger, type: .debug, fileName)
                return true
            }
        }
        
        return false
    }
    
    /// 简单的通配符模式匹配
    private func matchesPattern(path: String, pattern: String) -> Bool {
        // 简化的通配符匹配实现
        // 支持 * 通配符
        let regexPattern = pattern
            .replacingOccurrences(of: "*", with: ".*")
            .replacingOccurrences(of: "?", with: ".")
        
        do {
            let regex = try NSRegularExpression(pattern: regexPattern, options: .caseInsensitive)
            let range = NSRange(location: 0, length: path.count)
            return regex.firstMatch(in: path, options: [], range: range) != nil
        } catch {
            os_log("正则表达式错误: %{public}@", log: logger, type: .error, error.localizedDescription)
            return false
        }
    }
    
    // MARK: - 策略管理
    
    /// 添加策略
    func addPolicy(_ policy: EncryptionPolicy) {
        encryptionPolicies.append(policy)
        savePolicies()
        
        // 发送策略变更通知
        NotificationCenter.default.post(name: NSNotification.Name("PolicyDidChangeNotification"), object: self)
    }
    
    /// 移除策略
    func removePolicy(withName name: String) {
        encryptionPolicies.removeAll { $0.name == name }
        savePolicies()
        
        // 发送策略变更通知
        NotificationCenter.default.post(name: NSNotification.Name("PolicyDidChangeNotification"), object: self)
    }
    
    /// 更新策略
    func updatePolicy(_ policy: EncryptionPolicy) {
        if let index = encryptionPolicies.firstIndex(where: { $0.name == policy.name }) {
            encryptionPolicies[index] = policy
            savePolicies()
            
            // 发送策略变更通知
            NotificationCenter.default.post(name: NSNotification.Name("PolicyDidChangeNotification"), object: self)
        }
    }
    
    /// 获取所有策略
    func getAllPolicies() -> [EncryptionPolicy] {
        return encryptionPolicies
    }
    
    /// 设置默认策略
    func setDefaultPolicy(_ policy: EncryptionPolicy) {
        defaultPolicy = policy
        savePolicies()
    }
    
    /// 获取默认策略
    func getDefaultPolicy() -> EncryptionPolicy {
        return defaultPolicy
    }
    
    // MARK: - 持久化
    
    private func savePolicies() {
        // 实际实现应该保存到安全存储或配置文件
        // 这里简化处理，使用UserDefaults
        do {
            let encoder = JSONEncoder()
            let data = try encoder.encode(encryptionPolicies)
            UserDefaults.standard.set(data, forKey: "encryption_policies")
            
            let defaultData = try encoder.encode(defaultPolicy)
            UserDefaults.standard.set(defaultData, forKey: "default_policy")
            
            os_log("策略已保存", log: logger, type: .debug)
        } catch {
            os_log("保存策略失败: %{public}@", log: logger, type: .error, error.localizedDescription)
        }
    }
    
    private func loadPoliciesFromStorage() {
        // 从持久存储加载策略
        if let data = UserDefaults.standard.data(forKey: "encryption_policies") {
            do {
                let decoder = JSONDecoder()
                encryptionPolicies = try decoder.decode([EncryptionPolicy].self, from: data)
                os_log("从存储加载了 %d 个策略", log: logger, type: .info, encryptionPolicies.count)
            } catch {
                os_log("加载策略失败: %{public}@", log: logger, type: .error, error.localizedDescription)
            }
        }
        
        if let defaultData = UserDefaults.standard.data(forKey: "default_policy") {
            do {
                let decoder = JSONDecoder()
                defaultPolicy = try decoder.decode(EncryptionPolicy.self, from: defaultData)
                os_log("从存储加载了默认策略", log: logger, type: .info)
            } catch {
                os_log("加载默认策略失败: %{public}@", log: logger, type: .error, error.localizedDescription)
            }
        }
    }
}

/// 加密策略定义
struct EncryptionPolicy: Codable {
    let name: String
    let description: String
    let fileExtensions: [String]  // 文件扩展名列表
    let pathPatterns: [String]    // 路径模式列表
    let enabled: Bool
    let priority: Int             // 优先级，数值越大优先级越高
    
    /// 创建默认策略
    static func defaultPolicy() -> EncryptionPolicy {
        return EncryptionPolicy(
            name: "默认策略",
            description: "默认的文件加密策略",
            fileExtensions: ["doc", "docx", "pdf", "txt"],
            pathPatterns: [],
            enabled: false,  // 默认策略默认关闭
            priority: 0
        )
    }
} 