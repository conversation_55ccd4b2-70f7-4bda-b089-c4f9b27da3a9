#include "api/encoding_utils.h"
#include <string.h>

/* Base64编码表和解码表 */
static const char base64_table[] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";

static const int base64_decode_table[256] = {
    -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
    -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
    -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 62, -1, -1, -1, 63,
    52, 53, 54, 55, 56, 57, 58, 59, 60, 61, -1, -1, -1, -1, -1, -1,
    -1,  0,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14,
    15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, -1, -1, -1, -1, -1,
    -1, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40,
    41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, -1, -1, -1, -1, -1,
    -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
    -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
    -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
    -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
    -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
    -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
    -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,
    -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1
};

size_t encoding_base64_encode(
    const uint8_t* data,
    size_t data_len,
    char* output,
    size_t output_size)
{
    if (!data || data_len == 0) {
        return 0;
    }
    
    // 计算编码后的长度
    size_t encoded_len = ((data_len + 2) / 3) * 4;
    
    // 如果只需要返回长度
    if (!output || output_size < encoded_len + 1) {
        return encoded_len;
    }
    
    size_t i, j;
    for (i = 0, j = 0; i < data_len; i += 3, j += 4) {
        uint32_t octet_a = i < data_len ? data[i] : 0;
        uint32_t octet_b = i + 1 < data_len ? data[i + 1] : 0;
        uint32_t octet_c = i + 2 < data_len ? data[i + 2] : 0;
        
        uint32_t triple = (octet_a << 16) + (octet_b << 8) + octet_c;
        
        output[j] = base64_table[(triple >> 18) & 0x3F];
        output[j + 1] = base64_table[(triple >> 12) & 0x3F];
        output[j + 2] = base64_table[(triple >> 6) & 0x3F];
        output[j + 3] = base64_table[triple & 0x3F];
    }
    
    // 添加填充符
    if (data_len % 3 == 1) {
        output[encoded_len - 2] = '=';
        output[encoded_len - 1] = '=';
    } else if (data_len % 3 == 2) {
        output[encoded_len - 1] = '=';
    }
    
    // 添加结尾null字符
    output[encoded_len] = '\0';
    
    return encoded_len;
}

size_t encoding_base64_decode(
    const char* base64_str,
    size_t str_len,
    uint8_t* output,
    size_t output_size)
{
    if (!base64_str) {
        return 0;
    }
    
    // 如果长度为0，使用strlen计算
    if (str_len == 0) {
        str_len = strlen(base64_str);
    }
    
    if (str_len == 0) {
        return 0;
    }
    
    // 计算解码后的最大长度
    size_t padding = 0;
    if (str_len >= 1 && base64_str[str_len - 1] == '=') padding++;
    if (str_len >= 2 && base64_str[str_len - 2] == '=') padding++;
    
    size_t decoded_len = (str_len * 3) / 4 - padding;
    
    // 如果只需要返回长度
    if (!output || output_size < decoded_len) {
        return decoded_len;
    }
    
    size_t i, j;
    int val;
    for (i = 0, j = 0; i < str_len; i += 4, j += 3) {
        uint32_t sextet_a = i < str_len ? base64_decode_table[(unsigned char)base64_str[i]] : -1;
        uint32_t sextet_b = i + 1 < str_len ? base64_decode_table[(unsigned char)base64_str[i + 1]] : -1;
        uint32_t sextet_c = i + 2 < str_len ? base64_decode_table[(unsigned char)base64_str[i + 2]] : -1;
        uint32_t sextet_d = i + 3 < str_len ? base64_decode_table[(unsigned char)base64_str[i + 3]] : -1;
        
        // 检查无效字符
        if (sextet_a == -1 || sextet_b == -1 || 
            (i + 2 < str_len && base64_str[i + 2] != '=' && sextet_c == -1) || 
            (i + 3 < str_len && base64_str[i + 3] != '=' && sextet_d == -1)) {
            return 0;
        }
        
        // 前两个字节总是有效的
        uint32_t triple = (sextet_a << 18) + (sextet_b << 12);
        
        // 但是后面可能是填充符
        if (i + 2 < str_len && base64_str[i + 2] != '=') {
            triple += sextet_c << 6;
        }
        if (i + 3 < str_len && base64_str[i + 3] != '=') {
            triple += sextet_d;
        }
        
        // 第一个字节总是有效的
        if (j < decoded_len) {
            output[j] = (triple >> 16) & 0xFF;
        }
        // 检查第二个字节是否有效
        if (j + 1 < decoded_len) {
            output[j + 1] = (triple >> 8) & 0xFF;
        }
        // 检查第三个字节是否有效
        if (j + 2 < decoded_len) {
            output[j + 2] = triple & 0xFF;
        }
    }
    
    return decoded_len;
}

size_t encoding_hex_encode(
    const uint8_t* data,
    size_t data_len,
    char* output,
    size_t output_size,
    bool uppercase)
{
    if (!data || data_len == 0) {
        return 0;
    }
    
    // 计算编码后的长度
    size_t encoded_len = data_len * 2;
    
    // 如果只需要返回长度
    if (!output || output_size < encoded_len + 1) {
        return encoded_len;
    }
    
    // 选择大小写
    const char* hex_chars = uppercase ? "0123456789ABCDEF" : "0123456789abcdef";
    
    // 编码
    for (size_t i = 0; i < data_len; i++) {
        output[i * 2] = hex_chars[(data[i] >> 4) & 0x0F];
        output[i * 2 + 1] = hex_chars[data[i] & 0x0F];
    }
    
    // 添加结尾null字符
    output[encoded_len] = '\0';
    
    return encoded_len;
}

size_t encoding_hex_decode(
    const char* hex_str,
    size_t str_len,
    uint8_t* output,
    size_t output_size)
{
    if (!hex_str) {
        return 0;
    }
    
    // 如果长度为0，使用strlen计算
    if (str_len == 0) {
        str_len = strlen(hex_str);
    }
    
    if (str_len == 0 || str_len % 2 != 0) {
        return 0;  // 十六进制字符串长度必须是偶数
    }
    
    // 计算解码后的长度
    size_t decoded_len = str_len / 2;
    
    // 如果只需要返回长度
    if (!output || output_size < decoded_len) {
        return decoded_len;
    }
    
    // 解码
    for (size_t i = 0; i < decoded_len; i++) {
        char high = hex_str[i * 2];
        char low = hex_str[i * 2 + 1];
        
        // 解析高位
        uint8_t high_val;
        if (high >= '0' && high <= '9') {
            high_val = high - '0';
        } else if (high >= 'a' && high <= 'f') {
            high_val = high - 'a' + 10;
        } else if (high >= 'A' && high <= 'F') {
            high_val = high - 'A' + 10;
        } else {
            return 0;  // 无效字符
        }
        
        // 解析低位
        uint8_t low_val;
        if (low >= '0' && low <= '9') {
            low_val = low - '0';
        } else if (low >= 'a' && low <= 'f') {
            low_val = low - 'a' + 10;
        } else if (low >= 'A' && low <= 'F') {
            low_val = low - 'A' + 10;
        } else {
            return 0;  // 无效字符
        }
        
        output[i] = (high_val << 4) | low_val;
    }
    
    return decoded_len;
} 