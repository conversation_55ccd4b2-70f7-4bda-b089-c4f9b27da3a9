using System;
using System.IO;
using System.Windows;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using KeyGenerator.Services;
using KeyGenerator.ViewModels;
using KeyGenerator.Views;
using KeyGenerator.Utils;
using System.Linq; // Added for FirstOrDefault

namespace KeyGenerator
{
    /// <summary>
    /// 密钥生成器应用程序主入口
    /// 专注于密钥生成和管理功能
    /// </summary>
    public partial class App : Application
    {
        private readonly ServiceProvider _serviceProvider;
        private readonly ILogger<App> _logger;

        public App()
        {
            try
            {
                var services = new ServiceCollection();
                ConfigureServices(services);
                _serviceProvider = services.BuildServiceProvider();
                _logger = _serviceProvider.GetRequiredService<ILogger<App>>();
                
                _logger.LogInformation("密钥生成器应用程序服务容器初始化完成");
            }
            catch (Exception ex)
            {
                // 记录详细的启动错误
                LogStartupError(ex);
                MessageBox.Show($"应用程序初始化失败: {ex.Message}\n\n详细信息已记录到err.log文件中。", 
                                "启动错误", MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown(-1);
                throw; // 确保构造函数失败时正确抛出异常
            }
        }

        private static void ConfigureServices(IServiceCollection services)
        {
            try
            {
                // 确保日志目录存在
                var logDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs");
                if (!Directory.Exists(logDirectory))
                {
                    Directory.CreateDirectory(logDirectory);
                }

                // 配置服务
                var configuration = new ConfigurationBuilder()
                    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                    .Build();

                services.AddSingleton<IConfiguration>(configuration);
                
                // 日志服务 - 添加文件日志
                services.AddLogging(builder =>
                {
                    builder.AddConsole();
                    
                    // 添加文件日志
                    var logFilePath = Path.Combine(logDirectory, $"keygen-{DateTime.Now:yyyy-MM-dd}.log");
                    builder.AddProvider(new FileLoggerProvider(logFilePath));
                    
                    builder.SetMinimumLevel(LogLevel.Information); // 生产环境使用Information级别
                });

                // 注册基础服务 - 按依赖顺序注册
                services.AddSingleton<IDatabaseService, DatabaseService>();
                services.AddSingleton<IAuditService, AuditService>();
                services.AddSingleton<ISecurityService, SecurityService>();
                
                // 注册业务服务 - 核心密钥管理功能
                services.AddSingleton<IKeyGenerationService, KeyGenerationService>();
                services.AddSingleton<IKeyManagementService, KeyManagementService>();
                services.AddSingleton<IKeyDistributionService, KeyDistributionService>();

                // ViewModels - 只注册核心功能的ViewModels
                services.AddTransient<KeyGenerationViewModel>();
                services.AddTransient<KeyManagementViewModel>();
                services.AddTransient<KeyDistributionViewModel>();
                services.AddTransient<AuditLogViewModel>();
                services.AddTransient<SettingsViewModel>();
                services.AddTransient<MainWindowViewModel>();
                
                // Views
                services.AddTransient<MainWindow>();
                
                // 验证关键服务注册
                ValidateServiceRegistrations(services);
            }
            catch (Exception ex)
            {
                LogStartupError(ex);
                throw; // 重新抛出异常以让上层捕获
            }
        }

        /// <summary>
        /// 验证关键服务是否正确注册
        /// </summary>
        private static void ValidateServiceRegistrations(IServiceCollection services)
        {
            var requiredServices = new[]
            {
                typeof(IDatabaseService),
                typeof(IAuditService),
                typeof(ISecurityService),
                typeof(IKeyGenerationService),
                typeof(IKeyManagementService),
                typeof(MainWindowViewModel)
            };

            foreach (var serviceType in requiredServices)
            {
                var serviceDescriptor = services.FirstOrDefault(s => s.ServiceType == serviceType);
                if (serviceDescriptor == null)
                {
                    throw new InvalidOperationException($"必需的服务 {serviceType.Name} 未注册");
                }
            }
        }

        protected override void OnStartup(StartupEventArgs e)
        {
            try
            {
                base.OnStartup(e);
                
                _logger.LogInformation("密钥生成器应用程序启动 - 版本: 1.0.0");

                // 启动主窗口
                var mainWindow = _serviceProvider.GetRequiredService<MainWindow>();
                var mainWindowViewModel = _serviceProvider.GetRequiredService<MainWindowViewModel>();
                mainWindow.DataContext = mainWindowViewModel;
                
                mainWindow.Show();
                
                _logger.LogInformation("主窗口启动完成");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "应用程序启动失败");
                LogStartupError(ex);
                MessageBox.Show($"应用程序启动失败: {ex.Message}\n详细信息: {ex}", 
                                "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                Current.Shutdown();
            }
        }

        protected override void OnExit(ExitEventArgs e)
        {
            try
            {
                _logger?.LogInformation("密钥生成器应用程序退出");
                _serviceProvider?.Dispose();
            }
            catch (Exception ex)
            {
                LogStartupError(ex);
            }
            finally
            {
                base.OnExit(e);
            }
        }

        /// <summary>
        /// 获取服务实例 - 用于紧急情况下的服务访问
        /// </summary>
        public static T GetService<T>() where T : notnull
        {
            return ((App)Current)._serviceProvider.GetRequiredService<T>();
        }
        
        /// <summary>
        /// 记录启动错误到文件
        /// </summary>
        private static void LogStartupError(Exception ex)
        {
            try
            {
                // 确保错误日志目录存在
                var logDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs");
                if (!Directory.Exists(logDirectory))
                {
                    Directory.CreateDirectory(logDirectory);
                }

                // 记录详细异常信息到文件
                var errorLogPath = Path.Combine(logDirectory, "startup-errors.log");
                string errorLog = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 密钥生成器启动错误\r\n" +
                                 $"异常类型: {ex.GetType().FullName}\r\n" +
                                 $"异常消息: {ex.Message}\r\n" +
                                 $"堆栈跟踪:\r\n{ex.StackTrace}\r\n";
                
                // 记录内部异常
                var innerEx = ex.InnerException;
                while (innerEx != null)
                {
                    errorLog += $"\r\n内部异常类型: {innerEx.GetType().FullName}\r\n" +
                               $"内部异常消息: {innerEx.Message}\r\n" +
                               $"内部异常堆栈跟踪:\r\n{innerEx.StackTrace}\r\n";
                    innerEx = innerEx.InnerException;
                }
                
                File.AppendAllText(errorLogPath, errorLog + "\r\n" + new string('-', 80) + "\r\n\r\n");
            }
            catch
            {
                // 记录日志失败，无法处理
                // 在这种情况下，只能依赖用户看到的消息框
            }
        }
    }
}