/*
 * perf_crypto_cache.c
 * 
 * Windows MiniFilter驱动加密上下文缓存管理实现
 * 解决重复创建加密上下文的性能瓶颈
 */

#include <fltKernel.h>
#include <ntstrsafe.h>
#include "perf_optimization.h"
#include "crypto.h"

// 外部全局变量
extern PERF_MANAGER g_PerfManager;

// 超时常量 (100纳秒为单位)
#define PERF_CRYPTO_CONTEXT_TIMEOUT_100NS (PERF_CRYPTO_CONTEXT_TIMEOUT_SEC * 10000000LL)

// 前向声明
static PPERF_CRYPTO_CACHE_ENTRY
PerfAllocateCryptoCacheEntry(
    VOID
    );

static VOID
PerfFreeCryptoCacheEntry(
    _In_ PPERF_CRYPTO_CACHE_ENTRY Entry
    );

static BOOLEAN
PerfIsContextExpired(
    _In_ PPERF_CRYPTO_CACHE_ENTRY Entry
    );

static NTSTATUS
PerfEvictExpiredContexts(
    VOID
    );

static BOOLEAN
PerfMatchCryptoContext(
    _In_ PPERF_CRYPTO_CACHE_ENTRY Entry,
    _In_ ENC_ALGORITHM_TYPE Algorithm,
    _In_ ENC_MODE_TYPE Mode,
    _In_ ULONG KeyVersion
    );

//
// 初始化加密上下文缓存
//
NTSTATUS
PerfInitializeCryptoCache(
    VOID
    )
{
    PPERF_CRYPTO_CACHE cache = &g_PerfManager.CryptoCache;

    PERF_DEBUG_PRINT("Initializing crypto context cache...");

    // 初始化缓存结构
    InitializeListHead(&cache->FreeList);
    InitializeListHead(&cache->UsedList);
    KeInitializeSpinLock(&cache->SpinLock);
    
    cache->TotalCount = 0;
    cache->FreeCount = 0;
    cache->UsedCount = 0;
    cache->LookupRequests = 0;
    cache->LookupHits = 0;
    cache->LookupMisses = 0;
    cache->CreateRequests = 0;
    cache->EvictRequests = 0;

    PERF_DEBUG_PRINT("Crypto context cache initialized");
    return STATUS_SUCCESS;
}

//
// 清理加密上下文缓存
//
VOID
PerfCleanupCryptoCache(
    VOID
    )
{
    PPERF_CRYPTO_CACHE cache = &g_PerfManager.CryptoCache;
    PLIST_ENTRY listEntry;
    PPERF_CRYPTO_CACHE_ENTRY entry;
    KIRQL oldIrql;

    PERF_DEBUG_PRINT("Cleaning up crypto context cache...");

    KeAcquireSpinLock(&cache->SpinLock, &oldIrql);

    // 清理空闲列表
    while (!IsListEmpty(&cache->FreeList)) {
        listEntry = RemoveHeadList(&cache->FreeList);
        entry = CONTAINING_RECORD(listEntry, PERF_CRYPTO_CACHE_ENTRY, ListEntry);
        PerfFreeCryptoCacheEntry(entry);
    }

    // 清理使用列表
    while (!IsListEmpty(&cache->UsedList)) {
        listEntry = RemoveHeadList(&cache->UsedList);
        entry = CONTAINING_RECORD(listEntry, PERF_CRYPTO_CACHE_ENTRY, ListEntry);
        PerfFreeCryptoCacheEntry(entry);
    }

    cache->TotalCount = 0;
    cache->FreeCount = 0;
    cache->UsedCount = 0;

    KeReleaseSpinLock(&cache->SpinLock, oldIrql);

    PERF_DEBUG_PRINT("Crypto context cache cleaned up");
}

//
// 获取加密上下文
//
NTSTATUS
PerfGetCryptoContext(
    _In_ ENC_ALGORITHM_TYPE Algorithm,
    _In_ ENC_MODE_TYPE Mode,
    _In_ ULONG KeyVersion,
    _Out_ PENC_CRYPTO_CONTEXT *CryptoContext
    )
{
    NTSTATUS status = STATUS_SUCCESS;
    PPERF_CRYPTO_CACHE cache = &g_PerfManager.CryptoCache;
    PPERF_CRYPTO_CACHE_ENTRY entry = NULL;
    PLIST_ENTRY listEntry;
    KIRQL oldIrql;
    BOOLEAN found = FALSE;

    if (CryptoContext == NULL) {
        return STATUS_INVALID_PARAMETER;
    }

    *CryptoContext = NULL;

    KeAcquireSpinLock(&cache->SpinLock, &oldIrql);

    __try {
        cache->LookupRequests++;

        // 首先尝试在使用列表中查找匹配的上下文
        for (listEntry = cache->UsedList.Flink; 
             listEntry != &cache->UsedList; 
             listEntry = listEntry->Flink) {
            
            entry = CONTAINING_RECORD(listEntry, PERF_CRYPTO_CACHE_ENTRY, ListEntry);
            
            if (PerfMatchCryptoContext(entry, Algorithm, Mode, KeyVersion) &&
                !PerfIsContextExpired(entry)) {
                
                // 找到匹配且未过期的上下文
                entry->ReferenceCount++;
                KeQuerySystemTime(&entry->LastUsed);
                *CryptoContext = entry->CryptoContext;
                
                cache->LookupHits++;
                found = TRUE;
                
                PERF_DEBUG_PRINT("Crypto context cache hit: alg=%d, mode=%d, key=%lu", 
                               Algorithm, Mode, KeyVersion);
                break;
            }
        }

        if (!found) {
            // 尝试在空闲列表中查找匹配的上下文
            for (listEntry = cache->FreeList.Flink; 
                 listEntry != &cache->FreeList; 
                 listEntry = listEntry->Flink) {
                
                entry = CONTAINING_RECORD(listEntry, PERF_CRYPTO_CACHE_ENTRY, ListEntry);
                
                if (PerfMatchCryptoContext(entry, Algorithm, Mode, KeyVersion) &&
                    !PerfIsContextExpired(entry)) {
                    
                    // 找到匹配且未过期的上下文，从空闲列表移到使用列表
                    RemoveEntryList(&entry->ListEntry);
                    InsertTailList(&cache->UsedList, &entry->ListEntry);
                    
                    entry->InUse = TRUE;
                    entry->ReferenceCount = 1;
                    KeQuerySystemTime(&entry->LastUsed);
                    *CryptoContext = entry->CryptoContext;
                    
                    cache->FreeCount--;
                    cache->UsedCount++;
                    cache->LookupHits++;
                    found = TRUE;
                    
                    PERF_DEBUG_PRINT("Crypto context moved from free to used: alg=%d, mode=%d, key=%lu", 
                                   Algorithm, Mode, KeyVersion);
                    break;
                }
            }
        }

        if (!found) {
            cache->LookupMisses++;
        }
    }
    __finally {
        KeReleaseSpinLock(&cache->SpinLock, oldIrql);
    }

    // 如果没有找到缓存的上下文，创建新的
    if (!found) {
        status = CryptoCreateContext(Algorithm, Mode, KeyVersion, CryptoContext);
        if (NT_SUCCESS(status)) {
            PERF_DEBUG_PRINT("New crypto context created: alg=%d, mode=%d, key=%lu", 
                           Algorithm, Mode, KeyVersion);
        }
    }

    return status;
}

//
// 释放加密上下文
//
VOID
PerfReleaseCryptoContext(
    _In_ PENC_CRYPTO_CONTEXT CryptoContext
    )
{
    PPERF_CRYPTO_CACHE cache = &g_PerfManager.CryptoCache;
    PPERF_CRYPTO_CACHE_ENTRY entry = NULL;
    PLIST_ENTRY listEntry;
    KIRQL oldIrql;
    BOOLEAN found = FALSE;

    if (CryptoContext == NULL) {
        return;
    }

    KeAcquireSpinLock(&cache->SpinLock, &oldIrql);

    __try {
        // 在使用列表中查找该上下文
        for (listEntry = cache->UsedList.Flink; 
             listEntry != &cache->UsedList; 
             listEntry = listEntry->Flink) {
            
            entry = CONTAINING_RECORD(listEntry, PERF_CRYPTO_CACHE_ENTRY, ListEntry);
            
            if (entry->CryptoContext == CryptoContext) {
                entry->ReferenceCount--;
                
                if (entry->ReferenceCount == 0) {
                    // 没有引用了，考虑是否缓存或释放
                    if (cache->FreeCount < PERF_CRYPTO_CONTEXT_CACHE_SIZE) {
                        // 移动到空闲列表
                        RemoveEntryList(&entry->ListEntry);
                        InsertTailList(&cache->FreeList, &entry->ListEntry);
                        
                        entry->InUse = FALSE;
                        KeQuerySystemTime(&entry->LastUsed);
                        
                        cache->UsedCount--;
                        cache->FreeCount++;
                        
                        PERF_DEBUG_PRINT("Crypto context moved to free list");
                    } else {
                        // 缓存已满，直接释放
                        RemoveEntryList(&entry->ListEntry);
                        PerfFreeCryptoCacheEntry(entry);
                        
                        cache->UsedCount--;
                        cache->TotalCount--;
                        
                        PERF_DEBUG_PRINT("Crypto context cache full, direct free");
                    }
                }
                
                found = TRUE;
                break;
            }
        }
        
        if (!found) {
            // 如果没有在缓存中找到，直接销毁
            CryptoDestroyContext(CryptoContext);
            PERF_DEBUG_PRINT("Crypto context not in cache, direct destroy");
        }
    }
    __finally {
        KeReleaseSpinLock(&cache->SpinLock, oldIrql);
    }
}

//
// 缓存新的加密上下文
//
NTSTATUS
PerfCacheCryptoContext(
    _In_ PENC_CRYPTO_CONTEXT CryptoContext,
    _In_ ENC_ALGORITHM_TYPE Algorithm,
    _In_ ENC_MODE_TYPE Mode,
    _In_ ULONG KeyVersion
    )
{
    NTSTATUS status = STATUS_SUCCESS;
    PPERF_CRYPTO_CACHE cache = &g_PerfManager.CryptoCache;
    PPERF_CRYPTO_CACHE_ENTRY entry;
    KIRQL oldIrql;

    if (CryptoContext == NULL) {
        return STATUS_INVALID_PARAMETER;
    }

    KeAcquireSpinLock(&cache->SpinLock, &oldIrql);

    __try {
        cache->CreateRequests++;

        // 检查缓存是否已满
        if (cache->TotalCount >= PERF_CRYPTO_CONTEXT_CACHE_SIZE) {
            // 尝试驱逐过期的上下文
            KeReleaseSpinLock(&cache->SpinLock, oldIrql);
            PerfEvictExpiredContexts();
            KeAcquireSpinLock(&cache->SpinLock, &oldIrql);
            
            if (cache->TotalCount >= PERF_CRYPTO_CONTEXT_CACHE_SIZE) {
                // 仍然满了，不缓存
                status = STATUS_INSUFFICIENT_RESOURCES;
                __leave;
            }
        }

        // 分配新的缓存条目
        entry = PerfAllocateCryptoCacheEntry();
        if (entry == NULL) {
            status = STATUS_INSUFFICIENT_RESOURCES;
            __leave;
        }

        // 初始化条目
        entry->CryptoContext = CryptoContext;
        entry->Algorithm = Algorithm;
        entry->Mode = Mode;
        entry->KeyVersion = KeyVersion;
        entry->InUse = TRUE;
        entry->ReferenceCount = 1;
        KeQuerySystemTime(&entry->LastUsed);

        // 添加到使用列表
        InsertTailList(&cache->UsedList, &entry->ListEntry);
        cache->UsedCount++;
        cache->TotalCount++;

        PERF_DEBUG_PRINT("Crypto context cached: alg=%d, mode=%d, key=%lu", 
                       Algorithm, Mode, KeyVersion);
    }
    __finally {
        KeReleaseSpinLock(&cache->SpinLock, oldIrql);
    }

    return status;
}

//
// 获取加密上下文缓存统计信息
//
VOID
PerfGetCryptoCacheStats(
    _Out_ PULONG TotalCount,
    _Out_ PULONG FreeCount,
    _Out_ PULONG UsedCount,
    _Out_ PULONG LookupHits,
    _Out_ PULONG LookupMisses
    )
{
    PPERF_CRYPTO_CACHE cache = &g_PerfManager.CryptoCache;
    KIRQL oldIrql;

    KeAcquireSpinLock(&cache->SpinLock, &oldIrql);
    
    if (TotalCount) *TotalCount = cache->TotalCount;
    if (FreeCount) *FreeCount = cache->FreeCount;
    if (UsedCount) *UsedCount = cache->UsedCount;
    if (LookupHits) *LookupHits = cache->LookupHits;
    if (LookupMisses) *LookupMisses = cache->LookupMisses;
    
    KeReleaseSpinLock(&cache->SpinLock, oldIrql);
}

//
// 分配加密上下文缓存条目
//
static PPERF_CRYPTO_CACHE_ENTRY
PerfAllocateCryptoCacheEntry(
    VOID
    )
{
    PPERF_CRYPTO_CACHE_ENTRY entry;

    entry = (PPERF_CRYPTO_CACHE_ENTRY)ExAllocatePoolWithTag(
        NonPagedPool,
        sizeof(PERF_CRYPTO_CACHE_ENTRY),
        'eccE'
    );

    if (entry != NULL) {
        RtlZeroMemory(entry, sizeof(PERF_CRYPTO_CACHE_ENTRY));
    }

    return entry;
}

//
// 释放加密上下文缓存条目
//
static VOID
PerfFreeCryptoCacheEntry(
    _In_ PPERF_CRYPTO_CACHE_ENTRY Entry
    )
{
    if (Entry != NULL) {
        if (Entry->CryptoContext != NULL) {
            CryptoDestroyContext(Entry->CryptoContext);
        }
        ExFreePool(Entry);
    }
}

//
// 检查上下文是否过期
//
static BOOLEAN
PerfIsContextExpired(
    _In_ PPERF_CRYPTO_CACHE_ENTRY Entry
    )
{
    LARGE_INTEGER currentTime;
    LARGE_INTEGER expireTime;

    KeQuerySystemTime(&currentTime);
    expireTime.QuadPart = Entry->LastUsed.QuadPart + PERF_CRYPTO_CONTEXT_TIMEOUT_100NS;

    return (currentTime.QuadPart > expireTime.QuadPart);
}

//
// 驱逐过期的上下文
//
static NTSTATUS
PerfEvictExpiredContexts(
    VOID
    )
{
    PPERF_CRYPTO_CACHE cache = &g_PerfManager.CryptoCache;
    PLIST_ENTRY listEntry, nextEntry;
    PPERF_CRYPTO_CACHE_ENTRY entry;
    KIRQL oldIrql;
    ULONG evictedCount = 0;

    KeAcquireSpinLock(&cache->SpinLock, &oldIrql);

    __try {
        cache->EvictRequests++;

        // 检查空闲列表中的过期上下文
        listEntry = cache->FreeList.Flink;
        while (listEntry != &cache->FreeList) {
            nextEntry = listEntry->Flink;
            entry = CONTAINING_RECORD(listEntry, PERF_CRYPTO_CACHE_ENTRY, ListEntry);
            
            if (PerfIsContextExpired(entry)) {
                RemoveEntryList(&entry->ListEntry);
                PerfFreeCryptoCacheEntry(entry);
                
                cache->FreeCount--;
                cache->TotalCount--;
                evictedCount++;
            }
            
            listEntry = nextEntry;
        }

        // 检查使用列表中引用计数为0且过期的上下文
        listEntry = cache->UsedList.Flink;
        while (listEntry != &cache->UsedList) {
            nextEntry = listEntry->Flink;
            entry = CONTAINING_RECORD(listEntry, PERF_CRYPTO_CACHE_ENTRY, ListEntry);
            
            if (entry->ReferenceCount == 0 && PerfIsContextExpired(entry)) {
                RemoveEntryList(&entry->ListEntry);
                PerfFreeCryptoCacheEntry(entry);
                
                cache->UsedCount--;
                cache->TotalCount--;
                evictedCount++;
            }
            
            listEntry = nextEntry;
        }
    }
    __finally {
        KeReleaseSpinLock(&cache->SpinLock, oldIrql);
    }

    if (evictedCount > 0) {
        PERF_DEBUG_PRINT("Evicted %lu expired crypto contexts", evictedCount);
    }

    return STATUS_SUCCESS;
}

//
// 匹配加密上下文
//
static BOOLEAN
PerfMatchCryptoContext(
    _In_ PPERF_CRYPTO_CACHE_ENTRY Entry,
    _In_ ENC_ALGORITHM_TYPE Algorithm,
    _In_ ENC_MODE_TYPE Mode,
    _In_ ULONG KeyVersion
    )
{
    return (Entry->Algorithm == Algorithm &&
            Entry->Mode == Mode &&
            Entry->KeyVersion == KeyVersion);
} 