#pragma once

#include <stddef.h>
#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * 统一的编码解码工具接口
 * 提供跨平台和跨语言一致的编码解码实现
 */

/**
 * Base64编码
 * 
 * @param data 输入数据
 * @param data_len 输入数据长度
 * @param output 输出缓冲区，如果为NULL则只返回所需大小
 * @param output_size 输出缓冲区大小
 * @return 编码后的字符串长度（不包括结尾NULL），如果失败返回0
 */
size_t encoding_base64_encode(
    const uint8_t* data,
    size_t data_len,
    char* output,
    size_t output_size);

/**
 * Base64解码
 * 
 * @param base64_str Base64编码的字符串
 * @param str_len 字符串长度，如果为0则使用strlen
 * @param output 输出缓冲区，如果为NULL则只返回所需大小
 * @param output_size 输出缓冲区大小
 * @return 解码后的数据长度，如果失败返回0
 */
size_t encoding_base64_decode(
    const char* base64_str,
    size_t str_len,
    uint8_t* output,
    size_t output_size);

/**
 * 十六进制编码
 * 
 * @param data 输入数据
 * @param data_len 输入数据长度
 * @param output 输出缓冲区，如果为NULL则只返回所需大小
 * @param output_size 输出缓冲区大小
 * @param uppercase 是否使用大写字母
 * @return 编码后的字符串长度（不包括结尾NULL），如果失败返回0
 */
size_t encoding_hex_encode(
    const uint8_t* data,
    size_t data_len,
    char* output,
    size_t output_size,
    bool uppercase);

/**
 * 十六进制解码
 * 
 * @param hex_str 十六进制编码的字符串
 * @param str_len 字符串长度，如果为0则使用strlen
 * @param output 输出缓冲区，如果为NULL则只返回所需大小
 * @param output_size 输出缓冲区大小
 * @return 解码后的数据长度，如果失败返回0
 */
size_t encoding_hex_decode(
    const char* hex_str,
    size_t str_len,
    uint8_t* output,
    size_t output_size);

#ifdef __cplusplus
}
#endif

/* C++ 包装器 (只在C++编译环境中可用) */
#ifdef __cplusplus
#include <string>
#include <vector>

namespace crypto {
namespace encoding {

/**
 * Base64编码
 * 
 * @param data 要编码的二进制数据
 * @return Base64编码的字符串
 */
inline std::string Base64Encode(const std::vector<uint8_t>& data) {
    if (data.empty()) {
        return "";
    }
    
    // 计算所需缓冲区大小
    size_t output_size = encoding_base64_encode(data.data(), data.size(), nullptr, 0);
    if (output_size == 0) {
        return "";
    }
    
    // 分配缓冲区并编码
    std::string result;
    result.resize(output_size);
    encoding_base64_encode(data.data(), data.size(), &result[0], output_size + 1);
    return result;
}

/**
 * Base64解码
 * 
 * @param base64String Base64编码的字符串
 * @return 解码后的二进制数据
 */
inline std::vector<uint8_t> Base64Decode(const std::string& base64String) {
    if (base64String.empty()) {
        return {};
    }
    
    // 计算所需缓冲区大小
    size_t output_size = encoding_base64_decode(base64String.c_str(), base64String.length(), nullptr, 0);
    if (output_size == 0) {
        return {};
    }
    
    // 分配缓冲区并解码
    std::vector<uint8_t> result(output_size);
    encoding_base64_decode(base64String.c_str(), base64String.length(), result.data(), output_size);
    return result;
}

/**
 * 十六进制编码
 * 
 * @param data 要编码的二进制数据
 * @param uppercase 是否使用大写字母
 * @return 十六进制编码的字符串
 */
inline std::string HexEncode(const std::vector<uint8_t>& data, bool uppercase = false) {
    if (data.empty()) {
        return "";
    }
    
    // 计算所需缓冲区大小
    size_t output_size = encoding_hex_encode(data.data(), data.size(), nullptr, 0, uppercase);
    if (output_size == 0) {
        return "";
    }
    
    // 分配缓冲区并编码
    std::string result;
    result.resize(output_size);
    encoding_hex_encode(data.data(), data.size(), &result[0], output_size + 1, uppercase);
    return result;
}

/**
 * 十六进制解码
 * 
 * @param hexString 十六进制编码的字符串
 * @return 解码后的二进制数据
 */
inline std::vector<uint8_t> HexDecode(const std::string& hexString) {
    if (hexString.empty()) {
        return {};
    }
    
    // 计算所需缓冲区大小
    size_t output_size = encoding_hex_decode(hexString.c_str(), hexString.length(), nullptr, 0);
    if (output_size == 0) {
        return {};
    }
    
    // 分配缓冲区并解码
    std::vector<uint8_t> result(output_size);
    encoding_hex_decode(hexString.c_str(), hexString.length(), result.data(), output_size);
    return result;
}

} // namespace encoding
} // namespace crypto

#endif /* __cplusplus */ 