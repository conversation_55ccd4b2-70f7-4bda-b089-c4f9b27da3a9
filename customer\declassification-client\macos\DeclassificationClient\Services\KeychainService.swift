import Foundation
import Security

/// 钥匙串服务
/// 负责在macOS钥匙串中安全存储和管理敏感数据
class KeychainService {
    
    // MARK: - Singleton
    static let shared = KeychainService()
    
    // MARK: - Private Properties
    private let serviceName = "com.cryptosystem.declassification.client"
    private let accessGroup: String?
    
    // MARK: - Initialization
    private init() {
        // 在实际应用中，可以设置访问组以在应用间共享钥匙串项目
        self.accessGroup = nil
    }
    
    // MARK: - Public Methods
    
    /// 存储数据到钥匙串
    func setItem(data: Data, identifier: String) throws {
        // 首先尝试更新现有项目
        let updateQuery = buildQuery(identifier: identifier)
        let updateAttributes: [String: Any] = [
            kSecValueData as String: data
        ]
        
        let updateStatus = SecItemUpdate(updateQuery as CFDictionary, updateAttributes as CFDictionary)
        
        if updateStatus == errSecItemNotFound {
            // 如果项目不存在，创建新项目
            var addQuery = updateQuery
            addQuery[kSecValueData as String] = data
            
            let addStatus = SecItemAdd(addQuery as CFDictionary, nil)
            
            guard addStatus == errSecSuccess else {
                throw KeychainError.itemAddFailed(addStatus)
            }
        } else if updateStatus != errSecSuccess {
            throw KeychainError.itemUpdateFailed(updateStatus)
        }
    }
    
    /// 从钥匙串获取数据
    func getItem(identifier: String) throws -> Data? {
        var query = buildQuery(identifier: identifier)
        query[kSecReturnData as String] = true
        query[kSecMatchLimit as String] = kSecMatchLimitOne
        
        var item: CFTypeRef?
        let status = SecItemCopyMatching(query as CFDictionary, &item)
        
        guard status == errSecSuccess else {
            if status == errSecItemNotFound {
                return nil
            }
            throw KeychainError.itemQueryFailed(status)
        }
        
        return item as? Data
    }
    
    /// 删除钥匙串项目
    func deleteItem(identifier: String) throws {
        let query = buildQuery(identifier: identifier)
        let status = SecItemDelete(query as CFDictionary)
        
        guard status == errSecSuccess || status == errSecItemNotFound else {
            throw KeychainError.itemDeleteFailed(status)
        }
    }
    
    /// 检查项目是否存在
    func itemExists(identifier: String) -> Bool {
        do {
            return try getItem(identifier: identifier) != nil
        } catch {
            return false
        }
    }
    
    /// 存储字符串到钥匙串
    func setString(_ string: String, identifier: String) throws {
        guard let data = string.data(using: .utf8) else {
            throw KeychainError.stringEncodingFailed
        }
        try setItem(data: data, identifier: identifier)
    }
    
    /// 从钥匙串获取字符串
    func getString(identifier: String) throws -> String? {
        guard let data = try getItem(identifier: identifier) else {
            return nil
        }
        
        guard let string = String(data: data, encoding: .utf8) else {
            throw KeychainError.stringDecodingFailed
        }
        
        return string
    }
    
    /// 存储凭据（用户名和密码）
    func setCredentials(username: String, password: String, server: String) throws {
        let credentialsQuery: [String: Any] = [
            kSecClass as String: kSecClassInternetPassword,
            kSecAttrService as String: serviceName,
            kSecAttrServer as String: server,
            kSecAttrAccount as String: username,
            kSecValueData as String: password.data(using: .utf8)!,
            kSecAttrAccessible as String: kSecAttrAccessibleWhenUnlockedThisDeviceOnly
        ]
        
        // 首先尝试删除现有凭据
        let deleteQuery: [String: Any] = [
            kSecClass as String: kSecClassInternetPassword,
            kSecAttrService as String: serviceName,
            kSecAttrServer as String: server,
            kSecAttrAccount as String: username
        ]
        
        SecItemDelete(deleteQuery as CFDictionary)
        
        // 添加新凭据
        let status = SecItemAdd(credentialsQuery as CFDictionary, nil)
        
        guard status == errSecSuccess else {
            throw KeychainError.credentialsStoreFailed(status)
        }
    }
    
    /// 获取凭据
    func getCredentials(username: String, server: String) throws -> String? {
        let query: [String: Any] = [
            kSecClass as String: kSecClassInternetPassword,
            kSecAttrService as String: serviceName,
            kSecAttrServer as String: server,
            kSecAttrAccount as String: username,
            kSecReturnData as String: true,
            kSecMatchLimit as String: kSecMatchLimitOne
        ]
        
        var item: CFTypeRef?
        let status = SecItemCopyMatching(query as CFDictionary, &item)
        
        guard status == errSecSuccess else {
            if status == errSecItemNotFound {
                return nil
            }
            throw KeychainError.credentialsQueryFailed(status)
        }
        
        guard let passwordData = item as? Data,
              let password = String(data: passwordData, encoding: .utf8) else {
            throw KeychainError.credentialsDecodeFailed
        }
        
        return password
    }
    
    /// 删除凭据
    func deleteCredentials(username: String, server: String) throws {
        let query: [String: Any] = [
            kSecClass as String: kSecClassInternetPassword,
            kSecAttrService as String: serviceName,
            kSecAttrServer as String: server,
            kSecAttrAccount as String: username
        ]
        
        let status = SecItemDelete(query as CFDictionary)
        
        guard status == errSecSuccess || status == errSecItemNotFound else {
            throw KeychainError.credentialsDeleteFailed(status)
        }
    }
    
    /// 获取所有存储的凭据
    func getAllCredentials() throws -> [Credential] {
        let query: [String: Any] = [
            kSecClass as String: kSecClassInternetPassword,
            kSecAttrService as String: serviceName,
            kSecReturnAttributes as String: true,
            kSecReturnData as String: true,
            kSecMatchLimit as String: kSecMatchLimitAll
        ]
        
        var items: CFTypeRef?
        let status = SecItemCopyMatching(query as CFDictionary, &items)
        
        guard status == errSecSuccess else {
            if status == errSecItemNotFound {
                return []
            }
            throw KeychainError.credentialsQueryFailed(status)
        }
        
        guard let itemsArray = items as? [[String: Any]] else {
            return []
        }
        
        var credentials: [Credential] = []
        
        for item in itemsArray {
            guard let server = item[kSecAttrServer as String] as? String,
                  let username = item[kSecAttrAccount as String] as? String,
                  let passwordData = item[kSecValueData as String] as? Data,
                  let password = String(data: passwordData, encoding: .utf8) else {
                continue
            }
            
            let credential = Credential(
                server: server,
                username: username,
                password: password
            )
            credentials.append(credential)
        }
        
        return credentials
    }
    
    /// 清除所有钥匙串项目
    func clearAllItems() throws {
        // 清除通用密码
        let genericPasswordQuery: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: serviceName
        ]
        
        SecItemDelete(genericPasswordQuery as CFDictionary)
        
        // 清除互联网密码
        let internetPasswordQuery: [String: Any] = [
            kSecClass as String: kSecClassInternetPassword,
            kSecAttrService as String: serviceName
        ]
        
        SecItemDelete(internetPasswordQuery as CFDictionary)
    }
    
    /// 导出钥匙串数据（用于备份）
    func exportData() throws -> [String: Any] {
        var exportData: [String: Any] = [:]
        
        // 导出通用密码
        let genericQuery: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: serviceName,
            kSecReturnAttributes as String: true,
            kSecMatchLimit as String: kSecMatchLimitAll
        ]
        
        var genericItems: CFTypeRef?
        let genericStatus = SecItemCopyMatching(genericQuery as CFDictionary, &genericItems)
        
        if genericStatus == errSecSuccess,
           let items = genericItems as? [[String: Any]] {
            exportData["genericPasswords"] = items
        }
        
        // 导出互联网密码（不包含密码数据，只包含元数据）
        let internetQuery: [String: Any] = [
            kSecClass as String: kSecClassInternetPassword,
            kSecAttrService as String: serviceName,
            kSecReturnAttributes as String: true,
            kSecMatchLimit as String: kSecMatchLimitAll
        ]
        
        var internetItems: CFTypeRef?
        let internetStatus = SecItemCopyMatching(internetQuery as CFDictionary, &internetItems)
        
        if internetStatus == errSecSuccess,
           let items = internetItems as? [[String: Any]] {
            exportData["internetPasswords"] = items
        }
        
        return exportData
    }
    
    // MARK: - Private Methods
    
    private func buildQuery(identifier: String) -> [String: Any] {
        var query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: serviceName,
            kSecAttrAccount as String: identifier,
            kSecAttrAccessible as String: kSecAttrAccessibleWhenUnlockedThisDeviceOnly
        ]
        
        if let accessGroup = accessGroup {
            query[kSecAttrAccessGroup as String] = accessGroup
        }
        
        return query
    }
    
    private func statusMessage(for status: OSStatus) -> String {
        switch status {
        case errSecSuccess:
            return "操作成功"
        case errSecItemNotFound:
            return "项目未找到"
        case errSecDuplicateItem:
            return "项目已存在"
        case errSecParam:
            return "参数错误"
        case errSecAllocate:
            return "内存分配失败"
        case errSecNotAvailable:
            return "服务不可用"
        case errSecAuthFailed:
            return "认证失败"
        case errSecDecode:
            return "解码失败"
        case errSecInteractionNotAllowed:
            return "用户交互不被允许"
        default:
            return "未知错误 (\(status))"
        }
    }
}

// MARK: - Supporting Types

struct Credential {
    let server: String
    let username: String
    let password: String
    
    var displayName: String {
        return "\(username)@\(server)"
    }
}

// MARK: - Error Types

enum KeychainError: LocalizedError {
    case itemAddFailed(OSStatus)
    case itemUpdateFailed(OSStatus)
    case itemQueryFailed(OSStatus)
    case itemDeleteFailed(OSStatus)
    case stringEncodingFailed
    case stringDecodingFailed
    case credentialsStoreFailed(OSStatus)
    case credentialsQueryFailed(OSStatus)
    case credentialsDeleteFailed(OSStatus)
    case credentialsDecodeFailed
    
    var errorDescription: String? {
        switch self {
        case .itemAddFailed(let status):
            return "添加钥匙串项目失败: \(statusDescription(status))"
        case .itemUpdateFailed(let status):
            return "更新钥匙串项目失败: \(statusDescription(status))"
        case .itemQueryFailed(let status):
            return "查询钥匙串项目失败: \(statusDescription(status))"
        case .itemDeleteFailed(let status):
            return "删除钥匙串项目失败: \(statusDescription(status))"
        case .stringEncodingFailed:
            return "字符串编码失败"
        case .stringDecodingFailed:
            return "字符串解码失败"
        case .credentialsStoreFailed(let status):
            return "存储凭据失败: \(statusDescription(status))"
        case .credentialsQueryFailed(let status):
            return "查询凭据失败: \(statusDescription(status))"
        case .credentialsDeleteFailed(let status):
            return "删除凭据失败: \(statusDescription(status))"
        case .credentialsDecodeFailed:
            return "凭据解码失败"
        }
    }
    
    private func statusDescription(_ status: OSStatus) -> String {
        switch status {
        case errSecSuccess:
            return "操作成功"
        case errSecItemNotFound:
            return "项目未找到"
        case errSecDuplicateItem:
            return "项目已存在"
        case errSecParam:
            return "参数错误"
        case errSecAllocate:
            return "内存分配失败"
        case errSecNotAvailable:
            return "服务不可用"
        case errSecAuthFailed:
            return "认证失败"
        case errSecDecode:
            return "解码失败"
        case errSecInteractionNotAllowed:
            return "用户交互不被允许"
        default:
            return "未知错误 (状态码: \(status))"
        }
    }
} 