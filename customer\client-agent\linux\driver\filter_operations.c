/*
 * filter_operations.c
 *
 * Cryptosystem Linux 文件操作过滤器实现
 * 处理文件读写操作的加解密流程
 */

#include "file_filter.h"
#include "crypto_manager.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <errno.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <pthread.h>
#include <limits.h>

// 文件操作缓存结构
typedef struct {
    int fd;                 // 文件描述符
    char path[PATH_MAX];    // 文件路径
    int is_encrypted;       // 是否已加密
    int is_writing;         // 是否正在写入
    uint8_t iv[16];         // 当前IV
    uint32_t key_version;   // 密钥版本
    crypto_algorithm algo;  // 加密算法
    crypto_mode mode;       // 加密模式
} file_context;

// 文件上下文缓存
#define MAX_OPEN_FILES 256
static file_context file_contexts[MAX_OPEN_FILES];
static pthread_mutex_t context_lock = PTHREAD_MUTEX_INITIALIZER;

// 检查是否应该加密文件（基于扩展名和路径）
static int should_encrypt_file(const char *path) {
    // 检查文件是否在受保护路径内
    bool is_protected = 0;
    if (filter_is_path_protected(path, &is_protected) != FILTER_STATUS_SUCCESS || !is_protected) {
        return 0;
    }
    
    // 检查文件扩展名
    const char *encrypted_extensions[] = {
        ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx",
        ".pdf", ".txt", ".csv", ".rtf", ".zip", ".rar",
        ".jpg", ".jpeg", ".png", ".gif", ".dwg", ".dxf",
        ".psd", ".ai", ".java", ".c", ".cpp", ".py", ".js",
        NULL  // 终止标记
    };
    
    const char *dot = strrchr(path, '.');
    if (!dot) {
        return 0; // 没有扩展名
    }
    
    for (int i = 0; encrypted_extensions[i] != NULL; i++) {
        if (strcasecmp(dot, encrypted_extensions[i]) == 0) {
            return 1;
        }
    }
    
    return 0;
}

// 获取空闲文件上下文槽位
static file_context* get_free_context() {
    pthread_mutex_lock(&context_lock);
    
    for (int i = 0; i < MAX_OPEN_FILES; i++) {
        if (file_contexts[i].fd == 0) {
            pthread_mutex_unlock(&context_lock);
            return &file_contexts[i];
        }
    }
    
    pthread_mutex_unlock(&context_lock);
    return NULL;
}

// 通过文件描述符查找上下文
static file_context* find_context_by_fd(int fd) {
    pthread_mutex_lock(&context_lock);
    
    for (int i = 0; i < MAX_OPEN_FILES; i++) {
        if (file_contexts[i].fd == fd) {
            pthread_mutex_unlock(&context_lock);
            return &file_contexts[i];
        }
    }
    
    pthread_mutex_unlock(&context_lock);
    return NULL;
}

// 通过路径查找上下文
static file_context* find_context_by_path(const char *path) {
    pthread_mutex_lock(&context_lock);
    
    for (int i = 0; i < MAX_OPEN_FILES; i++) {
        if (file_contexts[i].fd != 0 && strcmp(file_contexts[i].path, path) == 0) {
            pthread_mutex_unlock(&context_lock);
            return &file_contexts[i];
        }
    }
    
    pthread_mutex_unlock(&context_lock);
    return NULL;
}

// 释放文件上下文
static void free_context(file_context *ctx) {
    if (ctx) {
        pthread_mutex_lock(&context_lock);
        memset(ctx, 0, sizeof(file_context));
        pthread_mutex_unlock(&context_lock);
    }
}

// 读取文件加密头信息
static int read_file_header(int fd, file_context *ctx) {
    // 保存当前文件位置
    off_t current_pos = lseek(fd, 0, SEEK_CUR);
    if (current_pos == (off_t)-1) {
        return -1;
    }
    
    // 移到文件开头
    if (lseek(fd, 0, SEEK_SET) == (off_t)-1) {
        return -1;
    }
    
    // 读取文件头标识
    char signature[4];
    if (read(fd, signature, 4) != 4 || memcmp(signature, "ENCF", 4) != 0) {
        // 不是加密文件
        lseek(fd, current_pos, SEEK_SET);
        ctx->is_encrypted = 0;
        return 0;
    }
    
    // 读取IV
    if (read(fd, ctx->iv, 16) != 16) {
        lseek(fd, current_pos, SEEK_SET);
        return -1;
    }
    
    // 读取密钥版本
    uint32_t key_version_network;
    if (read(fd, &key_version_network, sizeof(key_version_network)) != sizeof(key_version_network)) {
        lseek(fd, current_pos, SEEK_SET);
        return -1;
    }
    ctx->key_version = ntohl(key_version_network);
    
    // 读取算法信息
    uint8_t algo, mode;
    if (read(fd, &algo, 1) != 1 || read(fd, &mode, 1) != 1) {
        lseek(fd, current_pos, SEEK_SET);
        return -1;
    }
    
    ctx->algo = algo;
    ctx->mode = mode;
    ctx->is_encrypted = 1;
    
    // 恢复文件位置
    if (lseek(fd, current_pos, SEEK_SET) == (off_t)-1) {
        return -1;
    }
    
    return 0;
}

// 初始化操作回调
int filter_operations_init(void) {
    // 初始化文件上下文数组
    memset(file_contexts, 0, sizeof(file_contexts));
    
    // 初始化加密模块
    if (crypto_init() != CRYPTO_SUCCESS) {
        return -1;
    }
    
    return 0;
}

// 清理操作
void filter_operations_cleanup(void) {
    // 清理文件上下文
    pthread_mutex_lock(&context_lock);
    
    for (int i = 0; i < MAX_OPEN_FILES; i++) {
        if (file_contexts[i].fd != 0) {
            close(file_contexts[i].fd);
            memset(&file_contexts[i], 0, sizeof(file_context));
        }
    }
    
    pthread_mutex_unlock(&context_lock);
    
    // 清理加密模块
    crypto_cleanup();
}

// 文件打开回调
int filter_on_open(const char *path, int flags, mode_t mode) {
    // 检查是否在受保护路径内
    bool should_handle = 0;
    if (filter_is_path_protected(path, &should_handle) != FILTER_STATUS_SUCCESS || !should_handle) {
        // 不在受保护路径内，直接原样打开
        return open(path, flags, mode);
    }
    
    // 打开文件
    int fd = open(path, flags, mode);
    if (fd == -1) {
        return -1;
    }
    
    // 分配文件上下文
    file_context *ctx = get_free_context();
    if (!ctx) {
        close(fd);
        errno = EMFILE;
        return -1;
    }
    
    // 初始化上下文
    ctx->fd = fd;
    strncpy(ctx->path, path, PATH_MAX - 1);
    ctx->path[PATH_MAX - 1] = '\0';
    ctx->is_writing = (flags & (O_WRONLY | O_RDWR)) ? 1 : 0;
    
    // 检查文件是否已加密
    read_file_header(fd, ctx);
    
    // 如果是新创建的文件且应该加密
    if ((flags & O_CREAT) && should_encrypt_file(path) && !ctx->is_encrypted) {
        ctx->is_encrypted = 1;
        ctx->algo = ALGORITHM_AES;
        ctx->mode = MODE_GCM;
        
        // 获取默认密钥版本
        uint32_t default_key_version = 0;
        if (filter_get_default_key_version(&default_key_version) != FILTER_STATUS_SUCCESS || default_key_version == 0) {
            default_key_version = 1; // 使用版本1作为默认
        }
        ctx->key_version = default_key_version;
        
        // 生成随机IV
        if (crypto_generate_random(ctx->iv, 16) != CRYPTO_SUCCESS) {
            free_context(ctx);
            close(fd);
            errno = EIO;
            return -1;
        }
    }
    
    return fd;
}

// 文件关闭回调
int filter_on_close(int fd) {
    // 查找上下文
    file_context *ctx = find_context_by_fd(fd);
    if (!ctx) {
        // 没有上下文，直接关闭文件
        return close(fd);
    }
    
    // 如果文件已修改且需要加密
    if (ctx->is_writing && ctx->is_encrypted) {
        // 这里应该处理可能需要的文件加密操作
        // 如果文件内容被修改，可能需要重新加密整个文件
        
        // 在实际实现中，应该将内存缓冲区中的修改刷新到磁盘，
        // 并确保文件头正确设置
    }
    
    // 释放上下文并关闭文件
    free_context(ctx);
    return close(fd);
}

// 文件读取回调
ssize_t filter_on_read(int fd, void *buf, size_t count) {
    // 查找上下文
    file_context *ctx = find_context_by_fd(fd);
    if (!ctx || !ctx->is_encrypted) {
        // 没有上下文或非加密文件，直接读取
        return read(fd, buf, count);
    }
    
    // 获取当前文件位置
    off_t current_pos = lseek(fd, 0, SEEK_CUR);
    if (current_pos == (off_t)-1) {
        return -1;
    }
    
    // 计算文件头大小
    const size_t header_size = 4 + 16 + 4 + 2; // 签名 + IV + 密钥版本 + 算法信息
    
    // 调整读取位置，跳过文件头
    off_t data_pos = current_pos + header_size;
    if (lseek(fd, data_pos, SEEK_SET) == (off_t)-1) {
        return -1;
    }
    
    // 读取加密数据
    uint8_t *encrypted_data = malloc(count + 16); // 额外空间用于认证标签
    if (!encrypted_data) {
        errno = ENOMEM;
        return -1;
    }
    
    ssize_t bytes_read = read(fd, encrypted_data, count);
    if (bytes_read <= 0) {
        free(encrypted_data);
        return bytes_read;
    }
    
    // 解密数据
    size_t decrypted_len = count;
    
    int crypto_result = crypto_decrypt(
        ctx->algo,
        ctx->mode,
        ctx->key_version,
        ctx->iv,
        encrypted_data,
        bytes_read,
        buf,
        &decrypted_len
    );
    
    free(encrypted_data);
    
    if (crypto_result != CRYPTO_SUCCESS) {
        errno = EIO;
        return -1;
    }
    
    // 更新IV用于下一次读取
    if (bytes_read >= 16) {
        memcpy(ctx->iv, encrypted_data + (bytes_read - 16), 16);
    }
    
    // 恢复文件位置
    if (lseek(fd, current_pos + decrypted_len, SEEK_SET) == (off_t)-1) {
        return -1;
    }
    
    return decrypted_len;
}

// 文件写入回调
ssize_t filter_on_write(int fd, const void *buf, size_t count) {
    // 查找上下文
    file_context *ctx = find_context_by_fd(fd);
    if (!ctx || !ctx->is_encrypted) {
        // 没有上下文或非加密文件，直接写入
        return write(fd, buf, count);
    }
    
    // 获取当前文件位置
    off_t current_pos = lseek(fd, 0, SEEK_CUR);
    if (current_pos == (off_t)-1) {
        return -1;
    }
    
    // 计算文件头大小
    const size_t header_size = 4 + 16 + 4 + 2; // 签名 + IV + 密钥版本 + 算法信息
    
    // 如果是文件开头，写入文件头
    if (current_pos == 0) {
        // 写入文件头标识
        const char *header = "ENCF";
        if (write(fd, header, 4) != 4) {
            return -1;
        }
        
        // 写入IV
        if (write(fd, ctx->iv, 16) != 16) {
            return -1;
        }
        
        // 写入密钥版本
        uint32_t key_version_network = htonl(ctx->key_version);
        if (write(fd, &key_version_network, 4) != 4) {
            return -1;
        }
        
        // 写入算法信息
        uint8_t algo_info = ctx->algo;
        uint8_t mode_info = ctx->mode;
        if (write(fd, &algo_info, 1) != 1 || write(fd, &mode_info, 1) != 1) {
            return -1;
        }
    } else if (current_pos < header_size) {
        // 不允许在文件头中间位置写入
        errno = EINVAL;
        return -1;
    }
    
    // 加密数据
    uint8_t *encrypted_data = malloc(count + 16 + 16); // 额外空间用于IV和认证标签
    if (!encrypted_data) {
        errno = ENOMEM;
        return -1;
    }
    
    size_t encrypted_len = count + 16 + 16;
    
    int crypto_result = crypto_encrypt(
        ctx->algo,
        ctx->mode,
        ctx->key_version,
        ctx->iv,
        buf,
        count,
        encrypted_data,
        &encrypted_len
    );
    
    if (crypto_result != CRYPTO_SUCCESS) {
        free(encrypted_data);
        errno = EIO;
        return -1;
    }
    
    // 写入加密数据
    ssize_t bytes_written = write(fd, encrypted_data, encrypted_len);
    
    if (bytes_written > 0) {
        // 更新IV用于下一次写入
        if (encrypted_len >= 16) {
            memcpy(ctx->iv, encrypted_data + (encrypted_len - 16), 16);
        }
    }
    
    free(encrypted_data);
    
    if (bytes_written <= 0) {
        return -1;
    }
    
    // 返回原始数据长度
    return count;
}

// 文件定位回调
off_t filter_on_lseek(int fd, off_t offset, int whence) {
    // 查找上下文
    file_context *ctx = find_context_by_fd(fd);
    if (!ctx || !ctx->is_encrypted) {
        // 没有上下文或非加密文件，直接定位
        return lseek(fd, offset, whence);
    }
    
    // 计算文件头大小
    const size_t header_size = 4 + 16 + 4 + 2; // 签名 + IV + 密钥版本 + 算法信息
    
    // 获取文件当前大小
    struct stat st;
    if (fstat(fd, &st) == -1) {
        return -1;
    }
    
    // 根据起始位置不同进行转换
    off_t new_offset;
    
    switch (whence) {
        case SEEK_SET:
            // 从文件开头计算，需要加上文件头大小
            new_offset = offset + header_size;
            break;
            
        case SEEK_CUR:
            // 从当前位置计算，不需要额外调整
            new_offset = lseek(fd, 0, SEEK_CUR) + offset;
            break;
            
        case SEEK_END:
            // 从文件末尾计算，需要考虑文件头大小
            new_offset = st.st_size + offset;
            break;
            
        default:
            errno = EINVAL;
            return -1;
    }
    
    // 确保不在文件头内部
    if (new_offset < header_size) {
        new_offset = header_size;
    }
    
    // 执行定位
    return lseek(fd, new_offset, SEEK_SET);
}

// 文件状态回调
int filter_on_stat(const char *path, struct stat *statbuf) {
    // 执行原始stat操作
    int result = stat(path, statbuf);
    if (result == -1) {
        return -1;
    }
    
    // 检查是否在受保护路径内
    bool should_handle = 0;
    if (filter_is_path_protected(path, &should_handle) != FILTER_STATUS_SUCCESS || !should_handle) {
        return result;
    }
    
    // 检查文件是否已加密
    bool is_encrypted = 0;
    if (filter_is_file_encrypted(path, &is_encrypted) == FILTER_STATUS_SUCCESS && is_encrypted) {
        // 如果是加密文件，减去文件头大小
        const size_t header_size = 4 + 16 + 4 + 2; // 签名 + IV + 密钥版本 + 算法信息
        
        // 调整文件大小
        if (statbuf->st_size > header_size) {
            statbuf->st_size -= header_size;
        } else {
            statbuf->st_size = 0;
        }
    }
    
    return result;
}

// 文件删除回调
int filter_on_unlink(const char *path) {
    // 释放该路径对应的所有上下文
    file_context *ctx = find_context_by_path(path);
    while (ctx) {
        int fd = ctx->fd;
        free_context(ctx);
        close(fd);
        
        // 查找下一个相同路径的上下文
        ctx = find_context_by_path(path);
    }
    
    // 执行删除操作
    return unlink(path);
}

// 文件重命名回调
int filter_on_rename(const char *oldpath, const char *newpath) {
    // 更新所有相关上下文的路径
    pthread_mutex_lock(&context_lock);
    
    for (int i = 0; i < MAX_OPEN_FILES; i++) {
        if (file_contexts[i].fd != 0 && strcmp(file_contexts[i].path, oldpath) == 0) {
            strncpy(file_contexts[i].path, newpath, PATH_MAX - 1);
            file_contexts[i].path[PATH_MAX - 1] = '\0';
        }
    }
    
    pthread_mutex_unlock(&context_lock);
    
    // 执行重命名操作
    return rename(oldpath, newpath);
} 