using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using KeyGenerator.Models;
using KeyGenerator.Services;
using System.Text;
using Newtonsoft.Json;

namespace KeyGenerator.Services
{
    /// <summary>
    /// 密钥生成服务实现
    /// 专注于为客户单位提供AES-256密钥生成服务
    /// </summary>
    public class KeyGenerationService(
        ILogger<KeyGenerationService> logger,
        IConfiguration configuration,
        ISecurityService securityService,
        IDatabaseService databaseService,
        IAuditService auditService) : IKeyGenerationService
    {
        private readonly ILogger<KeyGenerationService> _logger = logger;
        private readonly IConfiguration _configuration = configuration;
        private readonly ISecurityService _securityService = securityService;
        private readonly IDatabaseService _databaseService = databaseService;
        private readonly IAuditService _auditService = auditService;
        private readonly Random _random = new();

        /// <summary>
        /// 生成主密钥 - 系统核心功能
        /// </summary>
        public async Task<KeyGenerationResult> GenerateMasterKeyAsync(KeyGenerationRequest request)
        {
            var keyId = string.Empty;
            try
            {
                _logger.LogInformation("开始生成主密钥 - 客户: {ClientName}, 算法: AES-256", request.ClientName);
                await LogAuditAsync("KEY_GENERATION_START", request.ClientId, $"开始为客户 {request.ClientName} 生成主密钥");

                // 验证请求参数
                var (isValid, errorMessage) = ValidateRequest(request);
                if (!isValid)
                {
                    _logger.LogWarning("密钥生成请求验证失败 - 客户: {ClientName}, 错误: {Error}", request.ClientName, errorMessage);
                    await LogAuditAsync("KEY_GENERATION_VALIDATION_FAILED", request.ClientId, $"请求验证失败: {errorMessage}");
                    
                    return new KeyGenerationResult
                    {
                        KeyId = string.Empty,
                        KeyData = [],
                        KeyHash = string.Empty,
                        ErrorMessage = errorMessage,
                        Status = KeyStatus.Failed
                    };
                }

                // 生成密钥ID
                keyId = GenerateKeyId("MK", request.ClientId);
                _logger.LogDebug("生成密钥ID: {KeyId}", keyId);

                // 使用AES-256生成密钥数据
                var keyData = GenerateSecureKeyData();
                
                // 加密密钥数据用于存储
                var encryptedKeyData = await _securityService.EncryptKeyDataAsync(keyData);
                
                // 计算密钥哈希用于完整性验证
                var keyHash = ComputeKeyHash(keyData);

                // 构建结果
                var result = new KeyGenerationResult
                {
                    KeyId = keyId,
                    KeyData = encryptedKeyData,
                    KeyHash = keyHash,
                    GenerationTime = DateTime.UtcNow,
                    Status = KeyStatus.Active,
                    Algorithm = "AES-256",
                    ClientId = request.ClientId,
                    ClientName = request.ClientName
                };

                _logger.LogInformation("主密钥生成成功 - 密钥ID: {KeyId}, 客户: {ClientName}", keyId, request.ClientName);
                await LogAuditAsync("KEY_GENERATION_SUCCESS", keyId, $"主密钥生成成功 - 客户: {request.ClientName}");
                
                return result;
            }
            catch (CryptographicException ex)
            {
                _logger.LogError(ex, "密钥生成过程中发生加密错误 - 密钥ID: {KeyId}", keyId);
                await LogAuditAsync("KEY_GENERATION_CRYPTO_ERROR", keyId, $"加密错误: {ex.Message}");
                
                return new KeyGenerationResult
                {
                    KeyId = keyId,
                    KeyData = [],
                    KeyHash = string.Empty,
                    ErrorMessage = "密钥生成过程中发生加密错误，请重试",
                    Status = KeyStatus.Failed
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成主密钥时发生未知错误 - 密钥ID: {KeyId}, 错误: {ErrorMessage}", keyId, ex.Message);
                await LogAuditAsync("KEY_GENERATION_ERROR", keyId, $"生成失败: {ex.Message}");
                
                return new KeyGenerationResult
                {
                    KeyId = keyId,
                    KeyData = [],
                    KeyHash = string.Empty,
                    ErrorMessage = $"密钥生成失败: {ex.Message}",
                    Status = KeyStatus.Failed
                };
            }
        }

        /// <summary>
        /// 获取支持的算法列表
        /// 专注于企业级加密标准
        /// </summary>
        public List<CryptoAlgorithm> GetSupportedAlgorithms()
        {
            _logger.LogDebug("获取支持的算法列表");
            return [
                CryptoAlgorithm.AES256,    // 主要推荐算法
                CryptoAlgorithm.AES128,    // 兼容性算法
                CryptoAlgorithm.SM4        // 国密算法支持
            ];
        }

        /// <summary>
        /// 获取密钥强度信息
        /// </summary>
        public KeyStrengthInfo GetKeyStrengthInfo(int keyLength)
        {
            _logger.LogDebug("获取密钥强度信息 - 密钥长度: {KeyLength}", keyLength);
            
            return keyLength switch
            {
                128 => new KeyStrengthInfo 
                { 
                    Strength = "中等", 
                    Description = "适用于一般商业用途",
                    RecommendedUse = "标准办公文档加密"
                },
                256 => new KeyStrengthInfo 
                { 
                    Strength = "高强度", 
                    Description = "适用于高敏感数据保护",
                    RecommendedUse = "核心机密文档加密（推荐）"
                },
                512 => new KeyStrengthInfo 
                { 
                    Strength = "军用级", 
                    Description = "最高级别安全保护",
                    RecommendedUse = "极高敏感数据保护"
                },
                _ => new KeyStrengthInfo 
                { 
                    Strength = "自定义", 
                    Description = "非标准密钥长度",
                    RecommendedUse = "特殊用途"
                }
            };
        }

        #region 私有方法

        /// <summary>
        /// 验证请求参数
        /// 确保所有必需的参数都正确提供
        /// </summary>
        private static (bool IsValid, string ErrorMessage) ValidateRequest(KeyGenerationRequest request)
        {
            if (string.IsNullOrWhiteSpace(request.ClientId))
                return (false, "客户ID不能为空");

            if (string.IsNullOrWhiteSpace(request.ClientName))
                return (false, "客户名称不能为空");

            if (request.KeyLength < 128)
                return (false, "密钥长度不能小于128位，建议使用256位");

            if (request.KeyLength > 512)
                return (false, "密钥长度不应超过512位");

            if (request.EffectiveDate >= request.ExpirationDate)
                return (false, "生效时间必须早于过期时间");

            if (request.ExpirationDate <= DateTime.UtcNow)
                return (false, "过期时间必须为未来时间");

            // 验证客户ID格式（应为字母数字组合）
            if (!System.Text.RegularExpressions.Regex.IsMatch(request.ClientId, @"^[a-zA-Z0-9_-]+$"))
                return (false, "客户ID格式无效，只能包含字母、数字、下划线和连字符");

            return (true, string.Empty);
        }

        /// <summary>
        /// 生成密钥ID
        /// 格式: 前缀_客户ID_时间戳_随机数
        /// </summary>
        private string GenerateKeyId(string prefix, string clientId)
        {
            var timestamp = DateTime.UtcNow.ToString("yyyyMMddHHmmss");
            var random = _random.Next(1000, 9999);
            var keyId = $"{prefix}_{clientId}_{timestamp}_{random}";
            
            _logger.LogDebug("生成密钥ID: {KeyId} (前缀: {Prefix}, 客户: {ClientId})", keyId, prefix, clientId);
            return keyId;
        }

        /// <summary>
        /// 生成安全的AES-256密钥数据
        /// 使用系统级加密随机数生成器
        /// </summary>
        private byte[] GenerateSecureKeyData()
        {
            const int AES_256_KEY_SIZE = 32; // 256位 = 32字节
            var keyData = new byte[AES_256_KEY_SIZE];

            using (var rng = RandomNumberGenerator.Create())
            {
                rng.GetBytes(keyData);
            }

            _logger.LogDebug("生成AES-256密钥数据成功，密钥长度: {Length} 字节", keyData.Length);
            return keyData;
        }

        /// <summary>
        /// 计算密钥哈希值
        /// 用于密钥完整性验证
        /// </summary>
        private static string ComputeKeyHash(byte[] keyData)
        {
            var hash = SHA256.HashData(keyData);
            var hashString = Convert.ToHexString(hash).ToLowerInvariant();
            return hashString;
        }

        /// <summary>
        /// 记录审计日志
        /// 所有关键操作都必须记录审计信息
        /// </summary>
        private async Task LogAuditAsync(string action, string resourceId, string description)
        {
            try
            {
                await _auditService.LogAsync(new AuditLogEntity
                {
                    EventAction = action,
                    TargetResourceId = resourceId,
                    EventDescription = description,
                    EventTime = DateTime.UtcNow,
                    UserId = "SYSTEM", // 密钥生成器是系统级操作
                    EventCategory = "KeyGeneration", // 暂定为 KeyGeneration，可根据需要调整
                    LogId = Guid.NewGuid().ToString(),
                    LogType = "System",
                    EventResult = action.Contains("SUCCESS") || action.Contains("START") ? "Success" : "Failed",
                    OperatorName = "KeyGeneratorService",
                    OperatorIp = "127.0.0.1",
                    SeverityLevel = "Information"
                });

                _logger.LogDebug("审计日志记录成功: {Action} - {ResourceId}", action, resourceId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "记录审计日志失败 - Action: {Action}, ResourceId: {ResourceId}", action, resourceId);
                // 审计日志失败不应该阻止主要操作，但需要记录错误
            }
        }

        #endregion
    }

    /// <summary>
    /// 密钥强度信息
    /// </summary>
    public class KeyStrengthInfo
    {
        public string Strength { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string RecommendedUse { get; set; } = string.Empty;
    }
} 