#pragma once

#include <string>
#include <memory>
#include <vector>
#include <mutex>
#include <thread>
#include <atomic>
#include <windows.h>
#include <d2d1.h>
#include <dwrite.h>
#include <wincodec.h>

// 通过链接库使用Direct2D和DirectWrite
#pragma comment(lib, "d2d1.lib")
#pragma comment(lib, "dwrite.lib")

// 水印颜色结构（RGBA格式）
struct WatermarkColor {
    float r; // 红色分量 (0.0 - 1.0)
    float g; // 绿色分量 (0.0 - 1.0)
    float b; // 蓝色分量 (0.0 - 1.0)
    float a; // 透明度 (0.0 - 1.0)
};

// 水印配置结构
struct WatermarkConfig {
    std::wstring contentTemplate;  // 水印内容模板（支持变量占位符）
    float opacity;                 // 透明度 (0.0 - 1.0)
    float fontSize;                // 字体大小
    float rotation;                // 旋转角度
    WatermarkColor color;          // 水印颜色
    int refreshInterval;           // 刷新间隔（毫秒）
    
    // 构造函数，设置默认值
    WatermarkConfig() 
        : contentTemplate(L"机密文件 - {username} - {datetime}")
        , opacity(0.3f)
        , fontSize(16.0f)
        , rotation(30.0f)
        , color({0.8f, 0.0f, 0.0f, 1.0f})  // 默认红色
        , refreshInterval(60000)  // 默认每分钟刷新一次
    {}
};

// 水印管理器类 - 单例模式
class WatermarkManager {
public:
    // 获取单例实例
    static WatermarkManager& getInstance() {
        static WatermarkManager instance;
        return instance;
    }
    
    // 禁止复制和移动
    WatermarkManager(const WatermarkManager&) = delete;
    WatermarkManager& operator=(const WatermarkManager&) = delete;
    WatermarkManager(WatermarkManager&&) = delete;
    WatermarkManager& operator=(WatermarkManager&&) = delete;
    
    // 初始化水印管理器
    bool initialize();
    
    // 关闭水印管理器
    void shutdown();
    
    // 启用水印
    bool enable();
    
    // 禁用水印
    void disable();
    
    // 设置水印配置
    void setConfig(const WatermarkConfig& config);
    
    // 获取当前水印配置
    WatermarkConfig getConfig() const;
    
private:
    // 私有构造函数
    WatermarkManager();
    
    // 析构函数
    ~WatermarkManager();
    
    // 水印绘制窗口过程
    static LRESULT CALLBACK WatermarkWndProc(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam);
    
    // 创建水印窗口
    bool createWatermarkWindow();
    
    // 初始化Direct2D资源
    bool initializeDirect2D();
    
    // 更新水印内容（替换变量）
    std::wstring updateWatermarkContent();
    
    // 绘制水印
    void drawWatermark();
    
    // 刷新水印线程函数
    void refreshThreadFunc();
    
private:
    // 配置与状态
    WatermarkConfig m_config;
    std::atomic<bool> m_initialized;
    std::atomic<bool> m_enabled;
    std::mutex m_configMutex;
    
    // 窗口和绘图相关
    HWND m_watermarkHwnd;
    HINSTANCE m_hInstance;
    
    // Direct2D资源
    ID2D1Factory* m_pD2DFactory;
    ID2D1HwndRenderTarget* m_pRenderTarget;
    ID2D1SolidColorBrush* m_pTextBrush;
    IDWriteFactory* m_pDWriteFactory;
    IDWriteTextFormat* m_pTextFormat;
    
    // 刷新线程相关
    std::unique_ptr<std::thread> m_refreshThread;
    std::atomic<bool> m_threadRunning;
    
    // 用户相关信息（用于水印）
    std::wstring m_username;
    std::wstring m_computerName;
    std::wstring m_ipAddress;
}; 