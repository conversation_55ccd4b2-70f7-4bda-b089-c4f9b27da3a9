<UserControl x:Class="KeyGenerator.Views.SettingsView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:KeyGenerator.Views"
    xmlns:viewmodels="clr-namespace:KeyGenerator.ViewModels" mc:Ignorable="d" d:DataContext="{d:DesignInstance Type=viewmodels:SettingsViewModel, IsDesignTimeCreatable=False}" d:DesignHeight="600" d:DesignWidth="800" Background="#F5F5F5">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <TextBlock Grid.Row="0" Text="系统设置" FontSize="24" FontWeight="Bold" Margin="0,0,0,20"/>

        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <GroupBox Header="数据库连接" Margin="0,0,0,10">
                    <StackPanel Margin="10">
                        <Label Content="连接字符串:"/>
                        <TextBox Text="{Binding AppSettings.ConnectionStrings.DefaultConnection, Mode=TwoWay}" MinLines="3" TextWrapping="Wrap"/>
                    </StackPanel>
                </GroupBox>

                <GroupBox Header="日志设置" Margin="0,0,0,10">
                    <StackPanel Margin="10">
                        <Label Content="默认日志级别:"/>
                        <TextBox Text="{Binding AppSettings.Logging.LogLevel.Default, Mode=TwoWay}"/>
                        <Label Content="Microsoft.Hosting.Lifetime 日志级别:" Margin="0,5,0,0"/>
                        <TextBox Text="{Binding AppSettings.Logging.LogLevel.MicrosoftHostingLifetime, Mode=TwoWay}"/>
                    </StackPanel>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>

        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,10,0,0">
            <Button Content="加载" Command="{Binding LoadSettingsCommand}" Margin="0,0,10,0"/>
            <Button Content="保存" Command="{Binding SaveSettingsCommand}" Style="{StaticResource AccentButtonStyle}"/>
        </StackPanel>

        <TextBlock Grid.Row="2" Text="{Binding StatusText}" VerticalAlignment="Center" FontStyle="Italic"/>
    </Grid>
</UserControl> 