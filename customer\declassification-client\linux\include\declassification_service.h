#pragma once

#include <vector>
#include <string>
#include <optional>
#include "models/task_models.h"

namespace DeclassificationClient {

class DeclassificationService {
public:
    DeclassificationService();
    ~DeclassificationService();
    
    bool initialize();
    void shutdown();
    
    std::vector<Models::DeclassificationTask> getTasks() const;
    std::optional<Models::DeclassificationTask> getTask(const std::string& taskId) const;
    bool createTask(const Models::DeclassificationTask& task);
    bool updateTask(const Models::DeclassificationTask& task);
    bool deleteTask(const std::string& taskId);

private:
    // This is a mock implementation
    mutable std::vector<Models::DeclassificationTask> tasks_; 
    void loadInitialData();
};

} // namespace DeclassificationClient 