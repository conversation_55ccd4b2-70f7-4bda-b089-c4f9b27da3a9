using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using CryptoSystem.SystemManager.Models;
using CryptoSystem.SystemManager.Services;

namespace CryptoSystem.SystemManager.ViewModels
{
    public partial class UserManagementViewModel : ObservableObject
    {
        private readonly IUserService _userService;

        [ObservableProperty]
        private ObservableCollection<User> users = new();

        [ObservableProperty]
        private string statusMessage = string.Empty;

        public UserManagementViewModel(IUserService userService)
        {
            _userService = userService;
        }

        [RelayCommand]
        private async Task LoadUsersAsync()
        {
            StatusMessage = "正在加载用户列表...";
            var userList = await _userService.GetAllUsersAsync();
            Users.Clear();
            foreach (var user in userList)
            {
                Users.Add(user);
            }
            StatusMessage = $"已加载 {Users.Count} 个用户";
        }
    }
} 