using CryptoSystem.SystemManager.Models;

namespace CryptoSystem.SystemManager.Services
{
    /// <summary>
    /// 用户服务接口
    /// </summary>
    public interface IUserService
    {
        /// <summary>
        /// 用户认证
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="password">密码</param>
        /// <returns>认证结果和用户信息</returns>
        Task<(bool Success, User? User, string Message)> AuthenticateAsync(string username, string password);

        /// <summary>
        /// 获取所有用户
        /// </summary>
        /// <param name="includeDeleted">是否包含已删除用户</param>
        /// <returns>用户列表</returns>
        Task<IEnumerable<User>> GetAllUsersAsync(bool includeDeleted = false);

        /// <summary>
        /// 根据ID获取用户
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户信息</returns>
        Task<User?> GetUserByIdAsync(string userId);

        /// <summary>
        /// 根据用户名获取用户
        /// </summary>
        /// <param name="username">用户名</param>
        /// <returns>用户信息</returns>
        Task<User?> GetUserByUsernameAsync(string username);

        /// <summary>
        /// 分页查询用户
        /// </summary>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <param name="searchKeyword">搜索关键词</param>
        /// <param name="departmentId">部门ID筛选</param>
        /// <param name="role">角色筛选</param>
        /// <param name="status">状态筛选</param>
        /// <returns>分页结果</returns>
        Task<(IEnumerable<User> Users, int TotalCount)> GetUsersPagedAsync(
            int pageIndex, 
            int pageSize, 
            string? searchKeyword = null,
            string? departmentId = null,
            UserRole? role = null,
            UserStatus? status = null);

        /// <summary>
        /// 创建用户
        /// </summary>
        /// <param name="user">用户信息</param>
        /// <param name="password">初始密码</param>
        /// <param name="createdBy">创建者</param>
        /// <returns>创建结果</returns>
        Task<(bool Success, string Message)> CreateUserAsync(User user, string password, string createdBy);

        /// <summary>
        /// 更新用户信息
        /// </summary>
        /// <param name="user">用户信息</param>
        /// <param name="modifiedBy">修改者</param>
        /// <returns>更新结果</returns>
        Task<(bool Success, string Message)> UpdateUserAsync(User user, string modifiedBy);

        /// <summary>
        /// 删除用户（软删除）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="deletedBy">删除者</param>
        /// <returns>删除结果</returns>
        Task<(bool Success, string Message)> DeleteUserAsync(string userId, string deletedBy);

        /// <summary>
        /// 启用/禁用用户
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="enabled">是否启用</param>
        /// <param name="operatedBy">操作者</param>
        /// <returns>操作结果</returns>
        Task<(bool Success, string Message)> SetUserEnabledAsync(string userId, bool enabled, string operatedBy);

        /// <summary>
        /// 重置用户密码
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="newPassword">新密码</param>
        /// <param name="mustChangePassword">是否强制下次登录修改密码</param>
        /// <param name="operatedBy">操作者</param>
        /// <returns>重置结果</returns>
        Task<(bool Success, string Message)> ResetPasswordAsync(string userId, string newPassword, bool mustChangePassword, string operatedBy);

        /// <summary>
        /// 修改用户密码
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="oldPassword">旧密码</param>
        /// <param name="newPassword">新密码</param>
        /// <returns>修改结果</returns>
        Task<(bool Success, string Message)> ChangePasswordAsync(string userId, string oldPassword, string newPassword);

        /// <summary>
        /// 锁定用户账户
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="lockoutDuration">锁定时长</param>
        /// <param name="reason">锁定原因</param>
        /// <param name="operatedBy">操作者</param>
        /// <returns>锁定结果</returns>
        Task<(bool Success, string Message)> LockUserAsync(string userId, TimeSpan lockoutDuration, string reason, string operatedBy);

        /// <summary>
        /// 解锁用户账户
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="operatedBy">操作者</param>
        /// <returns>解锁结果</returns>
        Task<(bool Success, string Message)> UnlockUserAsync(string userId, string operatedBy);

        /// <summary>
        /// 批量导入用户
        /// </summary>
        /// <param name="users">用户列表</param>
        /// <param name="defaultPassword">默认密码</param>
        /// <param name="importedBy">导入者</param>
        /// <returns>导入结果</returns>
        Task<(int SuccessCount, int FailedCount, List<string> ErrorMessages)> BatchImportUsersAsync(IEnumerable<User> users, string defaultPassword, string importedBy);

        /// <summary>
        /// 批量删除用户
        /// </summary>
        /// <param name="userIds">用户ID列表</param>
        /// <param name="deletedBy">删除者</param>
        /// <returns>删除结果</returns>
        Task<(int SuccessCount, int FailedCount, List<string> ErrorMessages)> BatchDeleteUsersAsync(IEnumerable<string> userIds, string deletedBy);

        /// <summary>
        /// 获取用户统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        Task<UserStatistics> GetUserStatisticsAsync();

        /// <summary>
        /// 验证用户名是否可用
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="excludeUserId">排除的用户ID（用于更新时检查）</param>
        /// <returns>是否可用</returns>
        Task<bool> IsUsernameAvailableAsync(string username, string? excludeUserId = null);

        /// <summary>
        /// 验证邮箱是否可用
        /// </summary>
        /// <param name="email">邮箱地址</param>
        /// <param name="excludeUserId">排除的用户ID（用于更新时检查）</param>
        /// <returns>是否可用</returns>
        Task<bool> IsEmailAvailableAsync(string email, string? excludeUserId = null);

        /// <summary>
        /// 记录登录失败
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="ipAddress">IP地址</param>
        /// <returns>是否需要锁定账户</returns>
        Task<bool> RecordLoginFailureAsync(string username, string ipAddress);

        /// <summary>
        /// 记录成功登录
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="ipAddress">IP地址</param>
        /// <returns>任务</returns>
        Task RecordSuccessfulLoginAsync(string userId, string ipAddress);

        /// <summary>
        /// 获取用户的设备列表
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>设备列表</returns>
        Task<IEnumerable<Device>> GetUserDevicesAsync(string userId);

        /// <summary>
        /// 获取用户的策略列表
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>策略列表</returns>
        Task<IEnumerable<Policy>> GetUserPoliciesAsync(string userId);

        /// <summary>
        /// 同步AD/LDAP用户
        /// </summary>
        /// <param name="syncBy">同步操作者</param>
        /// <returns>同步结果</returns>
        Task<(int CreatedCount, int UpdatedCount, int ErrorCount, List<string> ErrorMessages)> SyncLdapUsersAsync(string syncBy);

        /// <summary>
        /// 检查密码策略
        /// </summary>
        /// <param name="password">密码</param>
        /// <returns>检查结果</returns>
        Task<(bool IsValid, List<string> Errors)> ValidatePasswordPolicyAsync(string password);

        /// <summary>
        /// 获取即将过期的用户列表
        /// </summary>
        /// <param name="daysBeforeExpiry">到期前天数</param>
        /// <returns>用户列表</returns>
        Task<IEnumerable<User>> GetUsersWithExpiringPasswordsAsync(int daysBeforeExpiry = 7);
    }

    /// <summary>
    /// 用户统计信息
    /// </summary>
    public class UserStatistics
    {
        /// <summary>
        /// 总用户数
        /// </summary>
        public int TotalUsers { get; set; }

        /// <summary>
        /// 活动用户数
        /// </summary>
        public int ActiveUsers { get; set; }

        /// <summary>
        /// 禁用用户数
        /// </summary>
        public int DisabledUsers { get; set; }

        /// <summary>
        /// 锁定用户数
        /// </summary>
        public int LockedUsers { get; set; }

        /// <summary>
        /// 最近7天登录用户数
        /// </summary>
        public int RecentActiveUsers { get; set; }

        /// <summary>
        /// 密码即将过期用户数
        /// </summary>
        public int PasswordExpiringUsers { get; set; }

        /// <summary>
        /// 按角色分组的用户数
        /// </summary>
        public Dictionary<UserRole, int> UsersByRole { get; set; } = new();

        /// <summary>
        /// 按状态分组的用户数
        /// </summary>
        public Dictionary<UserStatus, int> UsersByStatus { get; set; } = new();

        /// <summary>
        /// 按部门分组的用户数
        /// </summary>
        public Dictionary<string, int> UsersByDepartment { get; set; } = new();
    }
} 