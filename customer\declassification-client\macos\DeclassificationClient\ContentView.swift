import SwiftUI

struct ContentView: View {
    @EnvironmentObject var configurationManager: ConfigurationManager
    @EnvironmentObject var auditLogger: AuditLogger
    @StateObject private var declassificationService = DeclassificationService()
    
    @State private var selectedTab = 0
    @State private var searchText = ""
    @State private var showingNewTaskDialog = false
    @State private var showingSettings = false
    
    var body: some View {
        NavigationSplitView {
            // 侧边栏
            SidebarView(selectedTab: $selectedTab)
                .frame(minWidth: 200, idealWidth: 250, maxWidth: 300)
        } detail: {
            // 主内容区域
            Group {
                switch selectedTab {
                case 0:
                    TaskListView()
                        .environmentObject(declassificationService)
                case 1:
                    AuditLogView()
                        .environmentObject(auditLogger)
                case 2:
                    StatisticsView()
                        .environmentObject(declassificationService)
                default:
                    TaskListView()
                        .environmentObject(declassificationService)
                }
            }
            .frame(minWidth: 600, minHeight: 400)
        }
        .navigationTitle("脱密客户端")
        .toolbar {
            ToolbarContent()
        }
        .sheet(isPresented: $showingNewTaskDialog) {
            NewTaskDialog()
                .environmentObject(declassificationService)
        }
        .onReceive(NotificationCenter.default.publisher(for: .createNewTask)) { _ in
            showingNewTaskDialog = true
        }
        .onReceive(NotificationCenter.default.publisher(for: .refreshTaskList)) { _ in
            Task {
                await declassificationService.refreshTasks()
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: .exportAuditLog)) { _ in
            exportAuditLog()
        }
        .alert("错误", isPresented: .constant(declassificationService.lastError != nil)) {
            Button("确定") {
                declassificationService.clearError()
            }
        } message: {
            Text(declassificationService.lastError?.localizedDescription ?? "")
        }
    }
    
    private func exportAuditLog() {
        let savePanel = NSSavePanel()
        savePanel.title = "导出审计日志"
        savePanel.nameFieldStringValue = "audit_log_\(Date().formatted(.iso8601.year().month().day())).json"
        savePanel.allowedContentTypes = [.json]
        
        if savePanel.runModal() == .OK {
            Task {
                await auditLogger.exportLogs(to: savePanel.url!)
            }
        }
    }
}

struct SidebarView: View {
    @Binding var selectedTab: Int
    
    var body: some View {
        List(selection: $selectedTab) {
            Section("主要功能") {
                NavigationLink(destination: EmptyView()) {
                    Label("脱密任务", systemImage: "lock.open")
                }
                .tag(0)
                
                NavigationLink(destination: EmptyView()) {
                    Label("审计日志", systemImage: "doc.text")
                }
                .tag(1)
                
                NavigationLink(destination: EmptyView()) {
                    Label("统计报表", systemImage: "chart.bar")
                }
                .tag(2)
            }
            
            Section("系统") {
                NavigationLink(destination: EmptyView()) {
                    Label("系统设置", systemImage: "gear")
                }
                .tag(3)
                
                NavigationLink(destination: EmptyView()) {
                    Label("帮助文档", systemImage: "questionmark.circle")
                }
                .tag(4)
            }
        }
        .listStyle(.sidebar)
        .navigationTitle("功能菜单")
    }
}

struct ToolbarContent: ToolbarContent {
    var body: some ToolbarContent {
        ToolbarItemGroup(placement: .primaryAction) {
            Button {
                NotificationCenter.default.post(name: .createNewTask, object: nil)
            } label: {
                Label("新建任务", systemImage: "plus")
            }
            .help("创建新的脱密任务")
            
            Button {
                NotificationCenter.default.post(name: .refreshTaskList, object: nil)
            } label: {
                Label("刷新", systemImage: "arrow.clockwise")
            }
            .help("刷新任务列表")
            
            Button {
                NotificationCenter.default.post(name: .exportAuditLog, object: nil)
            } label: {
                Label("导出日志", systemImage: "square.and.arrow.up")
            }
            .help("导出审计日志")
        }
        
        ToolbarItemGroup(placement: .status) {
            HStack {
                Image(systemName: "circle.fill")
                    .foregroundColor(.green)
                    .font(.caption)
                Text("系统正常")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
}

struct NewTaskDialog: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var declassificationService: DeclassificationService
    
    @State private var taskName = ""
    @State private var selectedFiles: [URL] = []
    @State private var sendMethod: SendMethod = .email
    @State private var recipients = ""
    @State private var notes = ""
    
    var body: some View {
        VStack(spacing: 20) {
            VStack(alignment: .leading, spacing: 12) {
                Text("新建脱密任务")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                GroupBox("任务信息") {
                    VStack(alignment: .leading, spacing: 12) {
                        TextField("任务名称", text: $taskName)
                            .textFieldStyle(.roundedBorder)
                        
                        HStack {
                            Text("发送方式")
                            Spacer()
                            Picker("发送方式", selection: $sendMethod) {
                                ForEach(SendMethod.allCases, id: \.self) { method in
                                    Text(method.displayName).tag(method)
                                }
                            }
                            .pickerStyle(.segmented)
                            .frame(width: 200)
                        }
                        
                        TextField("收件人", text: $recipients)
                            .textFieldStyle(.roundedBorder)
                        
                        TextField("备注", text: $notes, axis: .vertical)
                            .textFieldStyle(.roundedBorder)
                            .frame(height: 60)
                    }
                    .padding()
                }
                
                GroupBox("文件选择") {
                    VStack(alignment: .leading, spacing: 12) {
                        HStack {
                            Button("选择文件") {
                                selectFiles()
                            }
                            .buttonStyle(.borderedProminent)
                            
                            Spacer()
                            
                            Text("已选择 \(selectedFiles.count) 个文件")
                                .foregroundColor(.secondary)
                        }
                        
                        if !selectedFiles.isEmpty {
                            List(selectedFiles, id: \.self) { file in
                                HStack {
                                    Image(systemName: "doc.fill")
                                        .foregroundColor(.blue)
                                    Text(file.lastPathComponent)
                                    Spacer()
                                    Button("移除") {
                                        selectedFiles.removeAll { $0 == file }
                                    }
                                    .buttonStyle(.plain)
                                    .foregroundColor(.red)
                                }
                            }
                            .frame(height: 100)
                        }
                    }
                    .padding()
                }
            }
            
            HStack {
                Button("取消") {
                    dismiss()
                }
                .buttonStyle(.plain)
                
                Spacer()
                
                Button("创建任务") {
                    createTask()
                }
                .buttonStyle(.borderedProminent)
                .disabled(taskName.isEmpty || selectedFiles.isEmpty)
            }
        }
        .padding()
        .frame(width: 500, height: 400)
    }
    
    private func selectFiles() {
        let panel = NSOpenPanel()
        panel.allowsMultipleSelection = true
        panel.canChooseDirectories = false
        panel.canChooseFiles = true
        
        if panel.runModal() == .OK {
            selectedFiles = panel.urls
        }
    }
    
    private func createTask() {
        Task {
            await declassificationService.createTask(
                name: taskName,
                files: selectedFiles,
                sendMethod: sendMethod,
                recipients: recipients,
                notes: notes
            )
            dismiss()
        }
    }
}

struct AuditLogView: View {
    @EnvironmentObject var auditLogger: AuditLogger
    @State private var searchText = ""
    
    var body: some View {
        VStack {
            HStack {
                TextField("搜索日志...", text: $searchText)
                    .textFieldStyle(.roundedBorder)
                    .frame(maxWidth: 300)
                Spacer()
            }
            .padding()
            
            List(auditLogger.recentLogs, id: \.id) { log in
                AuditLogRow(log: log)
            }
            .listStyle(.inset)
        }
        .navigationTitle("审计日志")
    }
}

struct AuditLogRow: View {
    let log: AuditLog
    
    var body: some View {
        HStack {
            Image(systemName: log.type.iconName)
                .foregroundColor(log.type.color)
                .frame(width: 20)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(log.message)
                    .font(.headline)
                
                if let details = log.details, !details.isEmpty {
                    Text(details.map { "\($0.key): \($0.value)" }.joined(separator: ", "))
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
            
            Text(log.timestamp.formatted(.dateTime.hour().minute().second()))
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(.vertical, 4)
    }
}

struct StatisticsView: View {
    @EnvironmentObject var declassificationService: DeclassificationService
    
    var body: some View {
        ScrollView {
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3)) {
                StatCard(title: "总任务数", value: "\(declassificationService.totalTasks)", color: .blue)
                StatCard(title: "已完成", value: "\(declassificationService.completedTasks)", color: .green)
                StatCard(title: "处理中", value: "\(declassificationService.processingTasks)", color: .orange)
                StatCard(title: "失败任务", value: "\(declassificationService.failedTasks)", color: .red)
                StatCard(title: "本日处理", value: "\(declassificationService.todayTasks)", color: .purple)
                StatCard(title: "成功率", value: "\(declassificationService.successRate)%", color: .mint)
            }
            .padding()
        }
        .navigationTitle("统计报表")
    }
}

struct StatCard: View {
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text(value)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(color)
        }
        .frame(maxWidth: .infinity, minHeight: 80)
        .background(color.opacity(0.1))
        .cornerRadius(8)
    }
}

#Preview {
    ContentView()
        .environmentObject(ConfigurationManager.shared)
        .environmentObject(AuditLogger.shared)
} 