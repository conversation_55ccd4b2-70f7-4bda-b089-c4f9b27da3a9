#include "ui/main_window.h"
#include "declassification_service.h"
#include "ui/widgets/task_manager_widget.h"
#include "ui/widgets/file_list_widget.h"
#include "models/task_models.h"
#include "models/file_models.h"

#include <QCloseEvent>
#include <QMessageBox>
#include <QApplication>
#include <QDesktopWidget>
#include <QIcon>
#include <QDebug>
#include <QTimer> // Added for refreshTimer_
#include <QDateTime> // For logging timestamp
#include <QRandomGenerator> // For simulating results

namespace DeclassificationClient::UI {

MainWindow::MainWindow(DeclassificationService* service, QWidget* parent)
    : QMainWindow(parent), service_(service) {
    initializeUI();
    
    processingTimer_ = new QTimer(this);
    processingTimer_->setSingleShot(true);
    connect(processingTimer_, &QTimer::timeout, this, &MainWindow::onTaskProcessingFinished);
}

MainWindow::~MainWindow() {
    saveSettings();
}

void MainWindow::closeEvent(QCloseEvent* event) {
    // 退出前的确认或清理
    QMainWindow::closeEvent(event);
}

void MainWindow::changeEvent(QEvent* event) {
    // 处理窗口状态变化，例如最小化到托盘
    QMainWindow::changeEvent(event);
}

// Slots
void MainWindow::createNewTask() {
    qDebug() << "Slot: createNewTask triggered";
}

void MainWindow::openFiles() {
    qDebug() << "Slot: openFiles triggered";
}

void MainWindow::processTask() {
    if (!taskManager_->isTaskSelected()) {
        QMessageBox::warning(this, tr("No Task Selected"), tr("Please select a task from the list before processing."));
        return;
    }

    if (fileList_->getRowCount() == 0) {
        QMessageBox::warning(this, tr("No Files"), tr("There are no files in the list to process. Please add files first."));
        return;
    }

    onLogMessage(tr("Starting task processing..."));
    
    processTaskAction_->setEnabled(false);
    cancelTaskAction_->setEnabled(true); // Assuming cancel is now possible
    statusLabel_->setText(tr("Processing..."));
    progressBar_->setRange(0, 0); // Indeterminate progress bar
    
    for (int i = 0; i < fileList_->getRowCount(); ++i) {
        fileList_->updateFileStatus(i, tr("Processing..."), QIcon::fromTheme("view-refresh"));
    }

    // Simulate processing time
    processingTimer_->start(3000);
}

void MainWindow::cancelTask() {
    if (processingTimer_ && processingTimer_->isActive()) {
        processingTimer_->stop();
        onLogMessage(tr("Task processing cancelled by user."));

        for (int i = 0; i < fileList_->getRowCount(); ++i) {
            fileList_->updateFileStatus(i, tr("Cancelled"), QIcon::fromTheme("dialog-close"));
        }

        processTaskAction_->setEnabled(true);
        cancelTaskAction_->setEnabled(false);
        statusLabel_->setText(tr("Ready"));
        progressBar_->setRange(0, 100);
        progressBar_->setValue(0);
    }
}

void MainWindow::deleteTask() {
    qDebug() << "Slot: deleteTask triggered";
}

void MainWindow::generateSecurePackage() {
    qDebug() << "Slot: generateSecurePackage triggered";
}

void MainWindow::viewLogs() {
    qDebug() << "Slot: viewLogs triggered";
}

void MainWindow::showSettings() {
    qDebug() << "Slot: showSettings triggered";
}

void MainWindow::showAbout() {
    qDebug() << "Slot: showAbout triggered";
}

void MainWindow::exitApplication() {
    qDebug() << "Slot: exitApplication triggered";
    QApplication::quit();
}

void MainWindow::systemTrayActivated(QSystemTrayIcon::ActivationReason reason) {
    qDebug() << "Slot: systemTrayActivated triggered";
}

void MainWindow::showContextMenu(const QPoint& point) {
    qDebug() << "Slot: showContextMenu triggered";
}

void MainWindow::refreshData() {
    qDebug() << "Slot: refreshData triggered";
}

void MainWindow::updateStatusBar() {
    qDebug() << "Slot: updateStatusBar triggered";
}

void MainWindow::selectionChanged() {
    qDebug() << "Slot: selectionChanged triggered";
}

void MainWindow::onTaskSelected(const QString& taskId) {
    qDebug() << "Slot: onTaskSelected triggered with taskId:" << taskId;

    // Create dummy data based on taskId
    std::vector<Models::File> files;
    if (taskId == "task-001") {
        files.push_back({"report-q4.docx", 54321, Models::FileStatus::Decrypted, "/path/to/report-q4.docx"});
    } else if (taskId == "task-002") {
        files.push_back({"spec-v1.pdf", 123456, Models::FileStatus::Encrypted, "/path/to/spec-v1.pdf"});
        files.push_back({"spec-v2.pdf", 234567, Models::FileStatus::Encrypted, "/path/to/spec-v2.pdf"});
    } else if (taskId == "task-003") {
        files.push_back({"budget.xlsx", 78901, Models::FileStatus::Encrypted, "/path/to/budget.xlsx"});
    }
    
    fileList_->updateFiles(files);
}

void MainWindow::onLogMessage(const QString& message) {
    if (logView_) {
        QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
        logView_->append(QString("[%1] %2").arg(timestamp, message));
    }
}

void MainWindow::onTaskProcessingFinished() {
    onLogMessage(tr("Task processing finished."));
    
    int successCount = 0;
    int failCount = 0;

    for (int i = 0; i < fileList_->getRowCount(); ++i) {
        bool success = QRandomGenerator::global()->bounded(100) > 20; // 80% success rate
        if (success) {
            fileList_->updateFileStatus(i, tr("Decrypted"), QIcon::fromTheme("emblem-ok"));
            successCount++;
        } else {
            fileList_->updateFileStatus(i, tr("Failed"), QIcon::fromTheme("emblem-important"));
            failCount++;
        }
    }
    
    onLogMessage(tr("Processing summary: %1 succeeded, %2 failed.").arg(successCount).arg(failCount));

    processTaskAction_->setEnabled(true);
    cancelTaskAction_->setEnabled(false);
    statusLabel_->setText(tr("Ready"));
    progressBar_->setRange(0, 100);
    progressBar_->setValue(0);
}

void MainWindow::handleDeleteTask(const QString& taskId) {
    if (service_->deleteTask(taskId.toStdString())) {
        onLogMessage(tr("Task '%1' deleted successfully.").arg(taskId));
        refreshTaskList();
    } else {
        onLogMessage(tr("Error deleting task '%1'.").arg(taskId));
        QMessageBox::warning(this, tr("Deletion Failed"), tr("Could not find or delete task '%1'.").arg(taskId));
    }
}

// Private Methods
void MainWindow::initializeUI() {
    setupWindowProperties();
    createMenuBar();
    createToolBar();
    createStatusBar();
    createCentralWidget();
    createDockWidgets();
    createSystemTray();
    
    // Connect signals and slots
    connectSignals();
    
    // Load data and settings
    refreshTaskList();
    loadSettings();
}

void MainWindow::createMenuBar() {
    fileMenu_ = menuBar()->addMenu(tr("&File"));
    taskMenu_ = menuBar()->addMenu(tr("&Task"));
    toolsMenu_ = menuBar()->addMenu(tr("&Tools"));
    menuBar()->addStretch();
    helpMenu_ = menuBar()->addMenu(tr("&Help"));

    // File Menu Actions
    openFilesAction_ = new QAction(QIcon::fromTheme("document-open", QIcon(":/icons/open.png")), tr("&Open Files..."), this);
    openFilesAction_->setShortcut(QKeySequence::Open);
    fileMenu_->addAction(openFilesAction_);
    
    fileMenu_->addSeparator();
    
    generatePackageAction_ = new QAction(QIcon(":/icons/package.png"), tr("&Generate Secure Package..."), this);
    fileMenu_->addAction(generatePackageAction_);
    
    fileMenu_->addSeparator();

    settingsAction_ = new QAction(QIcon::fromTheme("preferences-system", QIcon(":/icons/settings.png")), tr("&Settings..."), this);
    fileMenu_->addAction(settingsAction_);
    
    fileMenu_->addSeparator();
    
    QAction* exitAction = new QAction(tr("E&xit"), this);
    exitAction->setShortcut(QKeySequence::Quit);
    connect(exitAction, &QAction::triggered, this, &MainWindow::exitApplication);
    fileMenu_->addAction(exitAction);

    // Task Menu Actions
    newTaskAction_ = new QAction(QIcon::fromTheme("document-new", QIcon(":/icons/new.png")), tr("&New Task..."), this);
    taskMenu_->addAction(newTaskAction_);
    
    taskMenu_->addSeparator();
    
    processTaskAction_ = new QAction(QIcon::fromTheme("media-playback-start", QIcon(":/icons/start.png")), tr("&Process Selected Task"), this);
    taskMenu_->addAction(processTaskAction_);
    
    cancelTaskAction_ = new QAction(QIcon::fromTheme("media-playback-stop", QIcon(":/icons/stop.png")), tr("&Cancel Selected Task"), this);
    cancelTaskAction_->setEnabled(false); // Initially disabled
    taskMenu_->addAction(cancelTaskAction_);
    
    deleteTaskAction_ = new QAction(QIcon::fromTheme("edit-delete", QIcon(":/icons/delete.png")), tr("&Delete Selected Task"), this);
    taskMenu_->addAction(deleteTaskAction_);

    // Tools Menu Actions
    viewLogsAction_ = new QAction(QIcon(":/icons/log.png"), tr("&View Logs"), this);
    toolsMenu_->addAction(viewLogsAction_);

    // Help Menu Actions
    aboutAction_ = new QAction(tr("&About..."), this);
    helpMenu_->addAction(aboutAction_);
}

void MainWindow::createToolBar() {
    mainToolBar_ = addToolBar(tr("Main"));
    mainToolBar_->setMovable(false);
    mainToolBar_->setIconSize(QSize(24, 24));
    
    mainToolBar_->addAction(newTaskAction_);
    mainToolBar_->addAction(openFilesAction_);
    mainToolBar_->addSeparator();
    mainToolBar_->addAction(processTaskAction_);
    mainToolBar_->addAction(cancelTaskAction_);
    mainToolBar_->addAction(deleteTaskAction_);
    mainToolBar_->addSeparator();
    mainToolBar_->addAction(generatePackageAction_);
}

void MainWindow::createStatusBar() {
    statusBar()->showMessage(tr("Ready"), 2000);

    statusLabel_ = new QLabel(tr("Ready"));
    statusBar()->addPermanentWidget(statusLabel_);

    progressBar_ = new QProgressBar(this);
    progressBar_->setRange(0, 100);
    progressBar_->setValue(0);
    progressBar_->setTextVisible(false);
    progressBar_->setFixedSize(200, 16);
    statusBar()->addPermanentWidget(progressBar_);

    taskCountLabel_ = new QLabel(tr("Tasks: 0"));
    statusBar()->addPermanentWidget(taskCountLabel_);
}

void MainWindow::createCentralWidget() {
    centralTabs_ = new QTabWidget(this);
    centralTabs_->setTabPosition(QTabWidget::North);

    taskManager_ = new TaskManagerWidget(this);
    fileList_ = new FileListWidget(this);

    centralTabs_->addTab(taskManager_, tr("Task Manager"));
    centralTabs_->addTab(fileList_, tr("File List"));

    setCentralWidget(centralTabs_);
}

void MainWindow::createDockWidgets() {
    logDock_ = new QDockWidget(tr("Log Output"), this);
    logView_ = new QTextEdit(this);
    logView_->setReadOnly(true);
    logDock_->setWidget(logView_);
    addDockWidget(Qt::BottomDockWidgetArea, logDock_);
}

void MainWindow::createSystemTray() {
    // 创建系统托盘
}

void MainWindow::connectSignals() {
    // File Menu
    connect(openFilesAction_, &QAction::triggered, this, &MainWindow::openFiles);
    connect(generatePackageAction_, &QAction::triggered, this, &MainWindow::generateSecurePackage);
    connect(settingsAction_, &QAction::triggered, this, &MainWindow::showSettings);

    // Task Menu
    connect(newTaskAction_, &QAction::triggered, this, &MainWindow::createNewTask);
    connect(processTaskAction_, &QAction::triggered, this, &MainWindow::processTask);
    connect(cancelTaskAction_, &QAction::triggered, this, &MainWindow::cancelTask);
    connect(deleteTaskAction_, &QAction::triggered, this, &MainWindow::deleteTask); // This seems to be a different delete action from the menu

    // Tools Menu
    connect(viewLogsAction_, &QAction::triggered, this, &MainWindow::viewLogs);

    // Help Menu
    connect(aboutAction_, &QAction::triggered, this, &MainWindow::showAbout);

    // Widget connections
    connect(taskManager_, &TaskManagerWidget::taskSelected, this, &MainWindow::onTaskSelected);
    connect(taskManager_, &TaskManagerWidget::taskDeletionRequested, this, &MainWindow::handleDeleteTask);
    connect(fileList_, &FileListWidget::logMessage, this, &MainWindow::onLogMessage);

    // Timer for status updates
    refreshTimer_ = new QTimer(this);
    connect(refreshTimer_, &QTimer::timeout, this, &MainWindow::updateStatusBar);
    refreshTimer_->start(5000); // Update every 5 seconds
}

void MainWindow::setupWindowProperties() {
    setWindowTitle(tr("Declassification Client"));
    setWindowIcon(QIcon(":/icons/app.png")); // Assuming icon is in resource file

    // 初始尺寸
    resize(1280, 800);

    // 窗口居中
    const QRect availableGeometry = QApplication::desktop()->availableGeometry(this);
    setGeometry(
        QStyle::alignedRect(
            Qt::LeftToRight,
            Qt::AlignCenter,
            size(),
            availableGeometry
        )
    );
}

void MainWindow::loadSettings() {
    // 加载设置
}

void MainWindow::saveSettings() {
    // 保存设置
}

void MainWindow::initializeSampleData() {
    // This method is now replaced by refreshTaskList()
}

void MainWindow::refreshTaskList() {
    auto tasks = service_->getTasks();
    taskManager_->updateTasks(tasks);
    onLogMessage(tr("Task list refreshed. Found %1 tasks.").arg(tasks.size()));
}

} // namespace DeclassificationClient::UI 