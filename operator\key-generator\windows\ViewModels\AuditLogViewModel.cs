using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using KeyGenerator.Models;
using KeyGenerator.Services;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows;

namespace KeyGenerator.ViewModels
{
    public partial class AuditLogViewModel : ViewModelBase
    {
        private readonly ILogger<AuditLogViewModel> _logger;
        private readonly IAuditService _auditService;

        [ObservableProperty]
        private ObservableCollection<AuditLogEntity> _auditLogs = new();

        [ObservableProperty]
        private bool _isLoading = true;

        [ObservableProperty]
        private string _statusText = "正在加载审计日志...";

        [ObservableProperty]
        private DateTime _filterFrom = DateTime.Now.AddDays(-7);

        [ObservableProperty]
        private DateTime _filterTo = DateTime.Now;

        [ObservableProperty]
        private string _filterUser = string.Empty;

        public AuditLogViewModel(ILogger<AuditLogViewModel> logger, IAuditService auditService)
        {
            _logger = logger;
            _auditService = auditService;
            LoadLogsCommand = new AsyncRelayCommand(LoadLogsAsync);
        }

        public IAsyncRelayCommand LoadLogsCommand { get; }

        private async Task LoadLogsAsync()
        {
            IsLoading = true;
            StatusText = "正在从数据库加载审计日志...";
            _logger.LogInformation("开始加载审计日志");

            try
            {
                var logs = await _auditService.GetLogsAsync(FilterFrom, FilterTo, FilterUser);
                Application.Current.Dispatcher.Invoke(() =>
                {
                    AuditLogs.Clear();
                    foreach (var log in logs)
                    {
                        AuditLogs.Add(log);
                    }
                    StatusText = $"加载了 {AuditLogs.Count} 条日志记录。";
                });
            }
            catch (Exception ex)
            {
                StatusText = "加载审计日志失败。";
                _logger.LogError(ex, "加载审计日志时发生错误");
            }
            finally
            {
                IsLoading = false;
            }
        }
    }
} 