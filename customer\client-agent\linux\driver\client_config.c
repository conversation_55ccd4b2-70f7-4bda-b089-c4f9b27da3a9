/*
 * client_config.c
 *
 * Cryptosystem Linux 客户端配置实现
 * 管理客户端配置和设备注册
 */

#include "client_config.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <pthread.h>
#include <json-c/json.h>
#include <curl/curl.h>
#include <openssl/rand.h>
#include <openssl/sha.h>
#include <uuid/uuid.h>
#include <syslog.h>

// 常量定义
#define CONFIG_FILENAME "cryptosystem.json"
#define DEVICE_ID_LENGTH 36  // UUID长度
#define DEVICE_KEY_LENGTH 64
#define MAX_PATH_LENGTH 1024
#define MAX_ENCRYPTION_PATHS 100

// 客户端配置
static struct {
    char config_dir[MAX_PATH_LENGTH];      // 配置目录
    char config_path[MAX_PATH_LENGTH];     // 配置文件路径
    char device_id[DEVICE_ID_LENGTH + 1];  // 设备ID
    char device_key[DEVICE_KEY_LENGTH + 1];// 设备密钥
    char server_url[MAX_PATH_LENGTH];      // 服务器URL
    int encryption_enabled;                // 是否启用加密
    char **encryption_paths;               // 加密路径列表
    size_t encryption_path_count;          // 加密路径数量
    device_status status;                  // 设备状态
    pthread_mutex_t lock;                  // 互斥锁
    int initialized;                       // 是否已初始化
} client_config = {
    .encryption_enabled = 0,
    .encryption_paths = NULL,
    .encryption_path_count = 0,
    .status = DEVICE_STATUS_UNREGISTERED,
    .initialized = 0
};

// 响应数据结构
typedef struct {
    char *data;
    size_t size;
} response_data;

// CURL写回调函数
static size_t write_callback(void *contents, size_t size, size_t nmemb, void *userp) {
    size_t realsize = size * nmemb;
    response_data *resp = (response_data *)userp;
    
    // 扩展缓冲区
    char *ptr = realloc(resp->data, resp->size + realsize + 1);
    if (!ptr) {
        return 0;  // 内存分配失败
    }
    
    resp->data = ptr;
    memcpy(&(resp->data[resp->size]), contents, realsize);
    resp->size += realsize;
    resp->data[resp->size] = 0;
    
    return realsize;
}

// 生成随机设备密钥
static void generate_device_key(char *key, size_t length) {
    unsigned char buffer[32];
    RAND_bytes(buffer, sizeof(buffer));
    
    for (size_t i = 0; i < sizeof(buffer) && i * 2 < length - 1; i++) {
        sprintf(&key[i * 2], "%02x", buffer[i]);
    }
    key[length - 1] = '\0';
}

// 创建配置目录
static config_status create_config_directory(const char *dir) {
    struct stat st = {0};
    
    if (stat(dir, &st) == -1) {
        if (mkdir(dir, 0700) != 0) {
            return CONFIG_STATUS_IO_ERROR;
        }
    }
    
    return CONFIG_STATUS_OK;
}

// 加载配置
static config_status load_config(void) {
    FILE *file = fopen(client_config.config_path, "r");
    if (!file) {
        return CONFIG_STATUS_NOT_FOUND;
    }
    
    // 读取文件内容
    fseek(file, 0, SEEK_END);
    long file_size = ftell(file);
    fseek(file, 0, SEEK_SET);
    
    if (file_size <= 0) {
        fclose(file);
        return CONFIG_STATUS_INVALID;
    }
    
    char *content = malloc(file_size + 1);
    if (!content) {
        fclose(file);
        return CONFIG_STATUS_ERROR;
    }
    
    size_t read_size = fread(content, 1, file_size, file);
    fclose(file);
    
    if (read_size != file_size) {
        free(content);
        return CONFIG_STATUS_IO_ERROR;
    }
    
    content[file_size] = '\0';
    
    // 解析JSON
    json_object *json = json_tokener_parse(content);
    free(content);
    
    if (!json) {
        return CONFIG_STATUS_INVALID;
    }
    
    // 读取配置项
    json_object *obj;
    
    if (json_object_object_get_ex(json, "deviceId", &obj)) {
        const char *value = json_object_get_string(obj);
        strncpy(client_config.device_id, value, sizeof(client_config.device_id) - 1);
    }
    
    if (json_object_object_get_ex(json, "deviceKey", &obj)) {
        const char *value = json_object_get_string(obj);
        strncpy(client_config.device_key, value, sizeof(client_config.device_key) - 1);
    }
    
    if (json_object_object_get_ex(json, "serverUrl", &obj)) {
        const char *value = json_object_get_string(obj);
        strncpy(client_config.server_url, value, sizeof(client_config.server_url) - 1);
    }
    
    if (json_object_object_get_ex(json, "encryptionEnabled", &obj)) {
        client_config.encryption_enabled = json_object_get_boolean(obj);
    }
    
    if (json_object_object_get_ex(json, "status", &obj)) {
        const char *value = json_object_get_string(obj);
        if (strcmp(value, "registered") == 0) {
            client_config.status = DEVICE_STATUS_REGISTERED;
        } else if (strcmp(value, "pending") == 0) {
            client_config.status = DEVICE_STATUS_PENDING;
        } else if (strcmp(value, "disabled") == 0) {
            client_config.status = DEVICE_STATUS_DISABLED;
        } else {
            client_config.status = DEVICE_STATUS_UNREGISTERED;
        }
    }
    
    // 读取加密路径
    if (json_object_object_get_ex(json, "encryptionPaths", &obj) && 
        json_object_is_type(obj, json_type_array)) {
        
        // 清理已有路径
        for (size_t i = 0; i < client_config.encryption_path_count; i++) {
            free(client_config.encryption_paths[i]);
        }
        
        // 分配新路径
        size_t path_count = json_object_array_length(obj);
        
        if (path_count > 0) {
            client_config.encryption_paths = realloc(client_config.encryption_paths, 
                                                   path_count * sizeof(char*));
            
            if (!client_config.encryption_paths) {
                json_object_put(json);
                return CONFIG_STATUS_ERROR;
            }
            
            client_config.encryption_path_count = 0;
            
            for (size_t i = 0; i < path_count; i++) {
                json_object *path_obj = json_object_array_get_idx(obj, i);
                const char *path = json_object_get_string(path_obj);
                
                client_config.encryption_paths[i] = strdup(path);
                if (client_config.encryption_paths[i]) {
                    client_config.encryption_path_count++;
                }
            }
        } else {
            free(client_config.encryption_paths);
            client_config.encryption_paths = NULL;
            client_config.encryption_path_count = 0;
        }
    }
    
    json_object_put(json);
    return CONFIG_STATUS_OK;
}

/**
 * 初始化客户端配置
 */
config_status config_init(const char *config_dir) {
    if (client_config.initialized) {
        return CONFIG_STATUS_OK;
    }
    
    if (!config_dir) {
        return CONFIG_STATUS_INVALID;
    }
    
    // 初始化互斥锁
    if (pthread_mutex_init(&client_config.lock, NULL) != 0) {
        return CONFIG_STATUS_ERROR;
    }
    
    // 初始化配置
    strncpy(client_config.config_dir, config_dir, sizeof(client_config.config_dir) - 1);
    
    // 确保配置目录存在
    if (create_config_directory(config_dir) != CONFIG_STATUS_OK) {
        pthread_mutex_destroy(&client_config.lock);
        return CONFIG_STATUS_IO_ERROR;
    }
    
    // 设置配置文件路径
    snprintf(client_config.config_path, sizeof(client_config.config_path),
            "%s/%s", config_dir, CONFIG_FILENAME);
    
    // 尝试加载配置
    config_status status = load_config();
    
    // 如果配置不存在，生成新的设备ID
    if (status == CONFIG_STATUS_NOT_FOUND) {
        uuid_t uuid;
        uuid_generate(uuid);
        uuid_unparse_lower(uuid, client_config.device_id);
        
        client_config.server_url[0] = '\0';
        client_config.encryption_enabled = 0;
        client_config.status = DEVICE_STATUS_UNREGISTERED;
        
        // 生成随机设备密钥
        generate_device_key(client_config.device_key, sizeof(client_config.device_key));
        
        // 确保加密路径为空
        client_config.encryption_paths = NULL;
        client_config.encryption_path_count = 0;
        
        // 保存新配置
        if (config_save() != CONFIG_STATUS_OK) {
            pthread_mutex_destroy(&client_config.lock);
            return CONFIG_STATUS_IO_ERROR;
        }
        
        status = CONFIG_STATUS_OK;
    } else if (status != CONFIG_STATUS_OK) {
        pthread_mutex_destroy(&client_config.lock);
        return status;
    }
    
    client_config.initialized = 1;
    return CONFIG_STATUS_OK;
}

/**
 * 清理客户端配置
 */
void config_cleanup(void) {
    if (!client_config.initialized) {
        return;
    }
    
    pthread_mutex_lock(&client_config.lock);
    
    // 释放路径内存
    for (size_t i = 0; i < client_config.encryption_path_count; i++) {
        free(client_config.encryption_paths[i]);
    }
    
    free(client_config.encryption_paths);
    client_config.encryption_paths = NULL;
    client_config.encryption_path_count = 0;
    
    pthread_mutex_unlock(&client_config.lock);
    pthread_mutex_destroy(&client_config.lock);
    
    client_config.initialized = 0;
}

/**
 * 保存配置到文件
 */
config_status config_save(void) {
    if (!client_config.initialized) {
        return CONFIG_STATUS_ERROR;
    }
    
    pthread_mutex_lock(&client_config.lock);
    
    // 创建JSON对象
    json_object *json = json_object_new_object();
    if (!json) {
        pthread_mutex_unlock(&client_config.lock);
        return CONFIG_STATUS_ERROR;
    }
    
    // 添加配置项
    json_object_object_add(json, "deviceId", json_object_new_string(client_config.device_id));
    json_object_object_add(json, "deviceKey", json_object_new_string(client_config.device_key));
    json_object_object_add(json, "serverUrl", json_object_new_string(client_config.server_url));
    json_object_object_add(json, "encryptionEnabled", json_object_new_boolean(client_config.encryption_enabled));
    
    // 设置状态
    const char *status_str = "unregistered";
    switch (client_config.status) {
        case DEVICE_STATUS_REGISTERED:
            status_str = "registered";
            break;
        case DEVICE_STATUS_PENDING:
            status_str = "pending";
            break;
        case DEVICE_STATUS_DISABLED:
            status_str = "disabled";
            break;
        default:
            status_str = "unregistered";
            break;
    }
    json_object_object_add(json, "status", json_object_new_string(status_str));
    
    // 添加加密路径
    json_object *paths_array = json_object_new_array();
    for (size_t i = 0; i < client_config.encryption_path_count; i++) {
        json_object_array_add(paths_array, json_object_new_string(client_config.encryption_paths[i]));
    }
    json_object_object_add(json, "encryptionPaths", paths_array);
    
    // 写入文件
    const char *json_str = json_object_to_json_string_ext(json, JSON_C_TO_STRING_PRETTY);
    
    FILE *file = fopen(client_config.config_path, "w");
    if (!file) {
        json_object_put(json);
        pthread_mutex_unlock(&client_config.lock);
        return CONFIG_STATUS_IO_ERROR;
    }
    
    size_t written = fwrite(json_str, 1, strlen(json_str), file);
    fclose(file);
    
    json_object_put(json);
    pthread_mutex_unlock(&client_config.lock);
    
    if (written != strlen(json_str)) {
        return CONFIG_STATUS_IO_ERROR;
    }
    
    return CONFIG_STATUS_OK;
}

/**
 * 检查设备注册状态
 */
device_status config_check_device_status(void) {
    if (!client_config.initialized) {
        return DEVICE_STATUS_UNREGISTERED;
    }
    
    pthread_mutex_lock(&client_config.lock);
    device_status status = client_config.status;
    pthread_mutex_unlock(&client_config.lock);
    
    return status;
}

/**
 * 注册设备
 */
config_status config_register_device(const char *server_url, const char *org_id,
                                   const char *user_email, const char *device_name) {
    if (!client_config.initialized || !server_url || !org_id || !user_email || !device_name) {
        return CONFIG_STATUS_INVALID;
    }
    
    pthread_mutex_lock(&client_config.lock);
    
    // 更新服务器URL
    strncpy(client_config.server_url, server_url, sizeof(client_config.server_url) - 1);
    
    // 构建注册请求
    json_object *request = json_object_new_object();
    json_object_object_add(request, "deviceId", json_object_new_string(client_config.device_id));
    json_object_object_add(request, "deviceName", json_object_new_string(device_name));
    json_object_object_add(request, "orgId", json_object_new_string(org_id));
    json_object_object_add(request, "userEmail", json_object_new_string(user_email));
    
    // 获取系统信息
    char hostname[256];
    gethostname(hostname, sizeof(hostname));
    json_object_object_add(request, "hostname", json_object_new_string(hostname));
    
    // 系统类型
    #ifdef __linux__
        json_object_object_add(request, "osType", json_object_new_string("Linux"));
    #else
        json_object_object_add(request, "osType", json_object_new_string("Unknown"));
    #endif
    
    // 发送注册请求
    CURL *curl = curl_easy_init();
    if (!curl) {
        json_object_put(request);
        pthread_mutex_unlock(&client_config.lock);
        return CONFIG_STATUS_ERROR;
    }
    
    char url[MAX_PATH_LENGTH];
    snprintf(url, sizeof(url), "%s/api/device/register", server_url);
    
    struct curl_slist *headers = NULL;
    headers = curl_slist_append(headers, "Content-Type: application/json");
    
    response_data resp = { .data = malloc(1), .size = 0 };
    if (!resp.data) {
        curl_easy_cleanup(curl);
        curl_slist_free_all(headers);
        json_object_put(request);
        pthread_mutex_unlock(&client_config.lock);
        return CONFIG_STATUS_ERROR;
    }
    resp.data[0] = '\0';
    
    const char *json_str = json_object_to_json_string(request);
    
    curl_easy_setopt(curl, CURLOPT_URL, url);
    curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
    curl_easy_setopt(curl, CURLOPT_POSTFIELDS, json_str);
    curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, write_callback);
    curl_easy_setopt(curl, CURLOPT_WRITEDATA, (void *)&resp);
    curl_easy_setopt(curl, CURLOPT_TIMEOUT, 30L);
    
    config_status status = CONFIG_STATUS_ERROR;
    CURLcode res = curl_easy_perform(curl);
    
    if (res == CURLE_OK) {
        long http_code = 0;
        curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &http_code);
        
        if (http_code == 200 || http_code == 202) {
            // 解析响应
            json_object *response = json_tokener_parse(resp.data);
            if (response) {
                json_object *status_obj;
                if (json_object_object_get_ex(response, "status", &status_obj)) {
                    const char *status_str = json_object_get_string(status_obj);
                    if (strcmp(status_str, "approved") == 0) {
                        client_config.status = DEVICE_STATUS_REGISTERED;
                    } else if (strcmp(status_str, "pending") == 0) {
                        client_config.status = DEVICE_STATUS_PENDING;
                    } else {
                        client_config.status = DEVICE_STATUS_UNREGISTERED;
                    }
                }
                
                json_object *key_obj;
                if (json_object_object_get_ex(response, "deviceKey", &key_obj)) {
                    const char *key = json_object_get_string(key_obj);
                    strncpy(client_config.device_key, key, sizeof(client_config.device_key) - 1);
                }
                
                json_object_put(response);
                status = CONFIG_STATUS_OK;
            }
        }
    }
    
    // 保存配置
    if (status == CONFIG_STATUS_OK) {
        config_save();
    }
    
    // 清理
    curl_easy_cleanup(curl);
    curl_slist_free_all(headers);
    json_object_put(request);
    free(resp.data);
    
    pthread_mutex_unlock(&client_config.lock);
    return status;
}

/**
 * 获取设备ID
 */
config_status config_get_device_id(char *buffer, size_t size) {
    if (!client_config.initialized || !buffer || size == 0) {
        return CONFIG_STATUS_INVALID;
    }
    
    pthread_mutex_lock(&client_config.lock);
    strncpy(buffer, client_config.device_id, size - 1);
    buffer[size - 1] = '\0';
    pthread_mutex_unlock(&client_config.lock);
    
    return CONFIG_STATUS_OK;
}

/**
 * 获取设备密钥
 */
config_status config_get_device_key(char *buffer, size_t size) {
    if (!client_config.initialized || !buffer || size == 0) {
        return CONFIG_STATUS_INVALID;
    }
    
    pthread_mutex_lock(&client_config.lock);
    strncpy(buffer, client_config.device_key, size - 1);
    buffer[size - 1] = '\0';
    pthread_mutex_unlock(&client_config.lock);
    
    return CONFIG_STATUS_OK;
}

/**
 * 获取服务器URL
 */
config_status config_get_server_url(char *buffer, size_t size) {
    if (!client_config.initialized || !buffer || size == 0) {
        return CONFIG_STATUS_INVALID;
    }
    
    pthread_mutex_lock(&client_config.lock);
    strncpy(buffer, client_config.server_url, size - 1);
    buffer[size - 1] = '\0';
    pthread_mutex_unlock(&client_config.lock);
    
    return CONFIG_STATUS_OK;
}

/**
 * 设置启用加密
 */
config_status config_set_encryption_enabled(int enabled) {
    if (!client_config.initialized) {
        return CONFIG_STATUS_ERROR;
    }
    
    pthread_mutex_lock(&client_config.lock);
    client_config.encryption_enabled = enabled ? 1 : 0;
    pthread_mutex_unlock(&client_config.lock);
    
    return config_save();
}

/**
 * 获取是否启用加密
 */
int config_get_encryption_enabled(void) {
    if (!client_config.initialized) {
        return -1;
    }
    
    pthread_mutex_lock(&client_config.lock);
    int enabled = client_config.encryption_enabled;
    pthread_mutex_unlock(&client_config.lock);
    
    return enabled;
}

/**
 * 设置加密路径
 */
config_status config_add_encryption_path(const char *path) {
    if (!client_config.initialized || !path) {
        return CONFIG_STATUS_INVALID;
    }
    
    pthread_mutex_lock(&client_config.lock);
    
    // 检查路径是否已存在
    for (size_t i = 0; i < client_config.encryption_path_count; i++) {
        if (strcmp(client_config.encryption_paths[i], path) == 0) {
            pthread_mutex_unlock(&client_config.lock);
            return CONFIG_STATUS_OK;  // 已存在
        }
    }
    
    // 检查是否超过最大数量
    if (client_config.encryption_path_count >= MAX_ENCRYPTION_PATHS) {
        pthread_mutex_unlock(&client_config.lock);
        return CONFIG_STATUS_ERROR;
    }
    
    // 扩展数组
    char **new_paths = realloc(client_config.encryption_paths, 
                             (client_config.encryption_path_count + 1) * sizeof(char*));
    if (!new_paths) {
        pthread_mutex_unlock(&client_config.lock);
        return CONFIG_STATUS_ERROR;
    }
    
    client_config.encryption_paths = new_paths;
    client_config.encryption_paths[client_config.encryption_path_count] = strdup(path);
    
    if (!client_config.encryption_paths[client_config.encryption_path_count]) {
        pthread_mutex_unlock(&client_config.lock);
        return CONFIG_STATUS_ERROR;
    }
    
    client_config.encryption_path_count++;
    
    pthread_mutex_unlock(&client_config.lock);
    
    return config_save();
}

/**
 * 移除加密路径
 */
config_status config_remove_encryption_path(const char *path) {
    if (!client_config.initialized || !path) {
        return CONFIG_STATUS_INVALID;
    }
    
    pthread_mutex_lock(&client_config.lock);
    
    // 查找路径
    size_t index = (size_t)-1;
    for (size_t i = 0; i < client_config.encryption_path_count; i++) {
        if (strcmp(client_config.encryption_paths[i], path) == 0) {
            index = i;
            break;
        }
    }
    
    // 路径不存在
    if (index == (size_t)-1) {
        pthread_mutex_unlock(&client_config.lock);
        return CONFIG_STATUS_NOT_FOUND;
    }
    
    // 释放内存
    free(client_config.encryption_paths[index]);
    
    // 移动其他路径
    for (size_t i = index; i < client_config.encryption_path_count - 1; i++) {
        client_config.encryption_paths[i] = client_config.encryption_paths[i + 1];
    }
    
    client_config.encryption_path_count--;
    
    pthread_mutex_unlock(&client_config.lock);
    
    return config_save();
}

/**
 * 获取加密路径列表
 */
config_status config_get_encryption_paths(char **paths, size_t max_paths, size_t *count) {
    if (!client_config.initialized || !paths || !count || max_paths == 0) {
        return CONFIG_STATUS_INVALID;
    }
    
    pthread_mutex_lock(&client_config.lock);
    
    *count = (client_config.encryption_path_count < max_paths) ? 
              client_config.encryption_path_count : max_paths;
    
    for (size_t i = 0; i < *count; i++) {
        paths[i] = strdup(client_config.encryption_paths[i]);
        if (!paths[i]) {
            // 释放已分配内存
            for (size_t j = 0; j < i; j++) {
                free(paths[j]);
            }
            pthread_mutex_unlock(&client_config.lock);
            return CONFIG_STATUS_ERROR;
        }
    }
    
    pthread_mutex_unlock(&client_config.lock);
    
    return CONFIG_STATUS_OK;
} 