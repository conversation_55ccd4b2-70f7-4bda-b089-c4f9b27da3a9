/*
 * perf_optimization.h
 * 
 * Windows MiniFilter驱动性能优化模块
 * 提供高性能的缓冲区管理、上下文缓存和密钥管理
 */

#pragma once

#include <fltKernel.h>
#include "crypto.h"

// 调试输出宏
#if DBG
#define PERF_DEBUG_PRINT(_string, ...) \
    DbgPrint("PERF: " _string "\n", ##__VA_ARGS__)
#else
#define PERF_DEBUG_PRINT(_string, ...) ((int)0)
#endif

// 性能优化配置常量
#define PERF_BUFFER_POOL_SIZE_4K    4096
#define PERF_BUFFER_POOL_SIZE_16K   16384
#define PERF_BUFFER_POOL_SIZE_64K   65536
#define PERF_BUFFER_POOL_SIZE_256K  262144

#define PERF_BUFFER_POOL_COUNT_4K   32
#define PERF_BUFFER_POOL_COUNT_16K  16
#define PERF_BUFFER_POOL_COUNT_64K  8
#define PERF_BUFFER_POOL_COUNT_256K 4

#define PERF_CRYPTO_CONTEXT_CACHE_SIZE  64
#define PERF_CRYPTO_CONTEXT_TIMEOUT_SEC 30

#define PERF_KEY_HASH_TABLE_SIZE    64
#define PERF_MDL_CACHE_SIZE         32

// 缓冲区池类型
typedef enum _PERF_BUFFER_SIZE_TYPE {
    BufferSize4K = 0,
    BufferSize16K,
    BufferSize64K,
    BufferSize256K,
    BufferSizeMax
} PERF_BUFFER_SIZE_TYPE;

// 缓冲区池条目
typedef struct _PERF_BUFFER_ENTRY {
    LIST_ENTRY ListEntry;
    PVOID Buffer;
    ULONG Size;
    BOOLEAN InUse;
    LARGE_INTEGER LastUsed;
} PERF_BUFFER_ENTRY, *PPERF_BUFFER_ENTRY;

// 缓冲区池管理结构
typedef struct _PERF_BUFFER_POOL {
    LIST_ENTRY FreeList;
    LIST_ENTRY UsedList;
    KSPIN_LOCK SpinLock;
    ULONG TotalCount;
    ULONG FreeCount;
    ULONG UsedCount;
    ULONG Size;
    
    // 统计信息
    ULONG AllocRequests;
    ULONG AllocHits;
    ULONG AllocMisses;
    ULONG FreeRequests;
} PERF_BUFFER_POOL, *PPERF_BUFFER_POOL;

// 加密上下文缓存条目
typedef struct _PERF_CRYPTO_CACHE_ENTRY {
    LIST_ENTRY ListEntry;
    PENC_CRYPTO_CONTEXT CryptoContext;
    ENC_ALGORITHM_TYPE Algorithm;
    ENC_MODE_TYPE Mode;
    ULONG KeyVersion;
    LARGE_INTEGER LastUsed;
    BOOLEAN InUse;
    ULONG ReferenceCount;
} PERF_CRYPTO_CACHE_ENTRY, *PPERF_CRYPTO_CACHE_ENTRY;

// 加密上下文缓存管理
typedef struct _PERF_CRYPTO_CACHE {
    LIST_ENTRY FreeList;
    LIST_ENTRY UsedList;
    KSPIN_LOCK SpinLock;
    ULONG TotalCount;
    ULONG FreeCount;
    ULONG UsedCount;
    
    // 统计信息
    ULONG LookupRequests;
    ULONG LookupHits;
    ULONG LookupMisses;
    ULONG CreateRequests;
    ULONG EvictRequests;
} PERF_CRYPTO_CACHE, *PPERF_CRYPTO_CACHE;

// 密钥哈希表条目
typedef struct _PERF_KEY_HASH_ENTRY {
    LIST_ENTRY ListEntry;
    ULONG KeyVersion;
    UCHAR Key[64];
    ULONG KeyLength;
    LARGE_INTEGER LastUsed;
    ULONG AccessCount;
} PERF_KEY_HASH_ENTRY, *PPERF_KEY_HASH_ENTRY;

// 密钥哈希表桶
typedef struct _PERF_KEY_HASH_BUCKET {
    LIST_ENTRY ListHead;
    KSPIN_LOCK SpinLock;
    ULONG EntryCount;
} PERF_KEY_HASH_BUCKET, *PPERF_KEY_HASH_BUCKET;

// 密钥哈希表管理
typedef struct _PERF_KEY_HASH_TABLE {
    PERF_KEY_HASH_BUCKET Buckets[PERF_KEY_HASH_TABLE_SIZE];
    ULONG TotalEntries;
    
    // 统计信息
    ULONG LookupRequests;
    ULONG LookupHits;
    ULONG LookupMisses;
    ULONG InsertRequests;
    ULONG DeleteRequests;
} PERF_KEY_HASH_TABLE, *PPERF_KEY_HASH_TABLE;

// MDL缓存条目
typedef struct _PERF_MDL_CACHE_ENTRY {
    LIST_ENTRY ListEntry;
    PMDL Mdl;
    ULONG Size;
    BOOLEAN InUse;
    LARGE_INTEGER LastUsed;
} PERF_MDL_CACHE_ENTRY, *PPERF_MDL_CACHE_ENTRY;

// MDL缓存管理
typedef struct _PERF_MDL_CACHE {
    LIST_ENTRY FreeList;
    LIST_ENTRY UsedList;
    KSPIN_LOCK SpinLock;
    ULONG TotalCount;
    ULONG FreeCount;
    ULONG UsedCount;
    
    // 统计信息
    ULONG AllocRequests;
    ULONG AllocHits;
    ULONG AllocMisses;
    ULONG FreeRequests;
} PERF_MDL_CACHE, *PPERF_MDL_CACHE;

// 全局性能统计
typedef struct _PERF_GLOBAL_STATS {
    // I/O统计
    LARGE_INTEGER TotalReadOperations;
    LARGE_INTEGER TotalWriteOperations;
    LARGE_INTEGER TotalBytesRead;
    LARGE_INTEGER TotalBytesWritten;
    
    // 加密统计
    LARGE_INTEGER TotalEncryptOperations;
    LARGE_INTEGER TotalDecryptOperations;
    LARGE_INTEGER TotalEncryptBytes;
    LARGE_INTEGER TotalDecryptBytes;
    
    // 性能统计
    LARGE_INTEGER TotalEncryptTime;
    LARGE_INTEGER TotalDecryptTime;
    LARGE_INTEGER AverageEncryptTime;
    LARGE_INTEGER AverageDecryptTime;
    
    // 缓存统计
    ULONG BufferPoolHitRate;
    ULONG CryptoContextHitRate;
    ULONG KeyLookupHitRate;
    ULONG MdlCacheHitRate;
    
    // 错误统计
    ULONG EncryptErrors;
    ULONG DecryptErrors;
    ULONG MemoryErrors;
    ULONG ContextErrors;
} PERF_GLOBAL_STATS, *PPERF_GLOBAL_STATS;

// 性能优化管理器
typedef struct _PERF_MANAGER {
    // 缓冲区池数组
    PERF_BUFFER_POOL BufferPools[BufferSizeMax];
    
    // 加密上下文缓存
    PERF_CRYPTO_CACHE CryptoCache;
    
    // 密钥哈希表
    PERF_KEY_HASH_TABLE KeyHashTable;
    
    // MDL缓存
    PERF_MDL_CACHE MdlCache;
    
    // 全局统计
    PERF_GLOBAL_STATS GlobalStats;
    
    // 初始化状态
    BOOLEAN Initialized;
    
    // 同步对象
    ERESOURCE Resource;
    
    // 清理工作项
    WORK_QUEUE_ITEM CleanupWorkItem;
    KTIMER CleanupTimer;
    KDPC CleanupDpc;
    
} PERF_MANAGER, *PPERF_MANAGER;

// 函数声明

//
// 性能管理器初始化和清理
//
NTSTATUS
PerfInitialize(
    VOID
    );

VOID
PerfCleanup(
    VOID
    );

//
// 缓冲区池管理
//
NTSTATUS
PerfAllocateBuffer(
    _In_ ULONG Size,
    _Out_ PVOID *Buffer,
    _Out_ PERF_BUFFER_SIZE_TYPE *BufferType
    );

VOID
PerfFreeBuffer(
    _In_ PVOID Buffer,
    _In_ PERF_BUFFER_SIZE_TYPE BufferType
    );

//
// 加密上下文缓存管理
//
NTSTATUS
PerfGetCryptoContext(
    _In_ ENC_ALGORITHM_TYPE Algorithm,
    _In_ ENC_MODE_TYPE Mode,
    _In_ ULONG KeyVersion,
    _Out_ PENC_CRYPTO_CONTEXT *CryptoContext
    );

VOID
PerfReleaseCryptoContext(
    _In_ PENC_CRYPTO_CONTEXT CryptoContext
    );

//
// 密钥哈希表管理
//
NTSTATUS
PerfLookupKey(
    _In_ ULONG KeyVersion,
    _Out_ PUCHAR Key,
    _Inout_ PULONG KeyLength
    );

NTSTATUS
PerfInsertKey(
    _In_ ULONG KeyVersion,
    _In_ PUCHAR Key,
    _In_ ULONG KeyLength
    );

NTSTATUS
PerfDeleteKey(
    _In_ ULONG KeyVersion
    );

//
// MDL缓存管理
//
NTSTATUS
PerfAllocateMdl(
    _In_ PVOID Buffer,
    _In_ ULONG Size,
    _Out_ PMDL *Mdl
    );

VOID
PerfFreeMdl(
    _In_ PMDL Mdl
    );

//
// 性能统计
//
VOID
PerfUpdateIOStats(
    _In_ BOOLEAN IsRead,
    _In_ ULONG BytesTransferred
    );

VOID
PerfUpdateCryptoStats(
    _In_ BOOLEAN IsEncrypt,
    _In_ ULONG BytesProcessed,
    _In_ LARGE_INTEGER ProcessingTime
    );

VOID
PerfGetGlobalStats(
    _Out_ PPERF_GLOBAL_STATS Stats
    );

//
// 内部辅助函数
//
PERF_BUFFER_SIZE_TYPE
PerfGetBufferSizeType(
    _In_ ULONG Size
    );

ULONG
PerfHashKeyVersion(
    _In_ ULONG KeyVersion
    );

VOID
PerfCleanupExpiredEntries(
    VOID
    );

// 性能优化宏
#define PERF_START_TIMER(timer) \
    KeQuerySystemTime(&(timer))

#define PERF_END_TIMER(start_timer, end_timer, elapsed) \
    do { \
        KeQuerySystemTime(&(end_timer)); \
        (elapsed).QuadPart = (end_timer).QuadPart - (start_timer).QuadPart; \
    } while(0)

#define PERF_UPDATE_COUNTER(counter) \
    InterlockedIncrement64(&(counter).QuadPart)

#define PERF_UPDATE_BYTES(counter, bytes) \
    InterlockedAdd64(&(counter).QuadPart, (bytes))

// 调试和日志宏
#ifdef DBG
#define PERF_DEBUG_PRINT(format, ...) \
    DbgPrint("[PERF] " format "\n", __VA_ARGS__)
#else
#define PERF_DEBUG_PRINT(format, ...)
#endif

// 性能检查宏
#define PERF_CHECK_THRESHOLD(value, threshold, message) \
    do { \
        if ((value) > (threshold)) { \
            PERF_DEBUG_PRINT("Performance threshold exceeded: %s (%lu > %lu)", \
                           (message), (ULONG)(value), (ULONG)(threshold)); \
        } \
    } while(0) 

// 清理过期条目的函数
VOID
PerfCleanupExpiredEntries(
    VOID
    );

// 缓冲区池相关函数
NTSTATUS
PerfInitializeAllBufferPools(
    VOID
    );

VOID
PerfCleanupAllBufferPools(
    VOID
    );

VOID
PerfGetBufferPoolStats(
    _In_ PERF_BUFFER_SIZE_TYPE BufferType,
    _Out_ PULONG TotalCount,
    _Out_ PULONG FreeCount,
    _Out_ PULONG UsedCount,
    _Out_ PULONG AllocHits,
    _Out_ PULONG AllocMisses
    );

// 加密上下文缓存相关函数
NTSTATUS
PerfInitializeCryptoCache(
    VOID
    );

VOID
PerfCleanupCryptoCache(
    VOID
    );

NTSTATUS
PerfCacheCryptoContext(
    _In_ PENC_CRYPTO_CONTEXT CryptoContext,
    _In_ ENC_ALGORITHM_TYPE Algorithm,
    _In_ ENC_MODE_TYPE Mode,
    _In_ ULONG KeyVersion
    );

VOID
PerfGetCryptoCacheStats(
    _Out_ PULONG TotalCount,
    _Out_ PULONG FreeCount,
    _Out_ PULONG UsedCount,
    _Out_ PULONG LookupHits,
    _Out_ PULONG LookupMisses
    );

// 密钥哈希表相关函数
NTSTATUS
PerfInitializeKeyHashTable(
    VOID
    );

VOID
PerfCleanupKeyHashTable(
    VOID
    );

VOID
PerfGetKeyHashStats(
    _Out_ PULONG TotalEntries,
    _Out_ PULONG LookupHits,
    _Out_ PULONG LookupMisses,
    _Out_ PULONG InsertRequests,
    _Out_ PULONG DeleteRequests
    );

// MDL缓存相关函数
NTSTATUS
PerfInitializeMdlCache(
    VOID
    );

VOID
PerfCleanupMdlCache(
    VOID
    );

VOID
PerfGetMdlCacheStats(
    _Out_ PULONG TotalCount,
    _Out_ PULONG FreeCount,
    _Out_ PULONG UsedCount,
    _Out_ PULONG AllocHits,
    _Out_ PULONG AllocMisses
    );

// 调试和诊断宏
#if DBG
#define PERF_DEBUG_PRINT(fmt, ...) \
    DbgPrint("[PERF] %s:%d " fmt "\n", __FUNCTION__, __LINE__, ##__VA_ARGS__)
#else
#define PERF_DEBUG_PRINT(fmt, ...) ((void)0)
#endif

#endif // _PERF_OPTIMIZATION_H_