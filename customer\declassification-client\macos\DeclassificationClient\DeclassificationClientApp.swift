import SwiftUI
import Foundation

@main
struct DeclassificationClientApp: App {
    @StateObject private var configurationManager = ConfigurationManager.shared
    @StateObject private var auditLogger = AuditLogger.shared
    
    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(configurationManager)
                .environmentObject(auditLogger)
                .onAppear {
                    setupApplication()
                }
        }
        .windowStyle(.hiddenTitleBar)
        .windowToolbarStyle(.unified)
        .commands {
            AppCommands()
        }
        
        Settings {
            SettingsView()
                .environmentObject(configurationManager)
        }
    }
    
    private func setupApplication() {
        // 初始化应用程序
        Task {
            await configurationManager.loadConfiguration()
            await auditLogger.logEvent(
                type: .systemEvent,
                message: "应用程序启动",
                details: ["version": Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0"]
            )
        }
    }
}

struct AppCommands: Commands {
    var body: some Commands {
        CommandGroup(replacing: .appInfo) {
            Button("关于脱密客户端") {
                NSApplication.shared.orderFrontStandardAboutPanel(
                    options: [
                        NSApplication.AboutPanelOptionKey.credits: NSAttributedString(
                            string: "企业级文档脱密解决方案\n专业、安全、高效",
                            attributes: [NSAttributedString.Key.font: NSFont.systemFont(ofSize: 11)]
                        ),
                        NSApplication.AboutPanelOptionKey.applicationName: "脱密客户端",
                        NSApplication.AboutPanelOptionKey.applicationVersion: "1.0.0",
                        NSApplication.AboutPanelOptionKey.version: "Build 1001"
                    ]
                )
            }
        }
        
        CommandGroup(replacing: .newItem) {
            Button("新建脱密任务") {
                // 触发新建任务
                NotificationCenter.default.post(name: .createNewTask, object: nil)
            }
            .keyboardShortcut("n", modifiers: .command)
        }
        
        CommandGroup(after: .toolbar) {
            Button("刷新任务列表") {
                NotificationCenter.default.post(name: .refreshTaskList, object: nil)
            }
            .keyboardShortcut("r", modifiers: .command)
            
            Divider()
            
            Button("导出审计日志") {
                NotificationCenter.default.post(name: .exportAuditLog, object: nil)
            }
            .keyboardShortcut("e", modifiers: [.command, .shift])
        }
    }
}

extension Notification.Name {
    static let createNewTask = Notification.Name("createNewTask")
    static let refreshTaskList = Notification.Name("refreshTaskList")
    static let exportAuditLog = Notification.Name("exportAuditLog")
} 