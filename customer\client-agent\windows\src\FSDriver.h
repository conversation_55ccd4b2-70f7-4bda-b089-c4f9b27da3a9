﻿#pragma once

#include <string>
#include <memory>
#include <thread>
#include <atomic>
#include <windows.h> // Required for Windows API types

#include "CoreEngine.h" // Needs to know about the engine to call it

class FSDriver {
public:
    // Constructor takes the directory to monitor and a shared pointer to the engine
    FSDriver(const std::wstring& directoryPath, std::shared_ptr<CoreEngine> engine);
    ~FSDriver();

    // Starts the monitoring thread
    bool startMonitoring();

    // Stops the monitoring thread
    void stopMonitoring();

    // Prevent copying and assignment
    FSDriver(const FSDriver&) = delete;
    FSDriver& operator=(const FSDriver&) = delete;

private:
    // The actual monitoring loop that runs in a separate thread
    void monitorLoop();
    
    // 执行完整目录扫描，用于处理潜在的缓冲区溢出情况
    void performFullDirectoryScan();
    
    // 递归扫描目录并通知引擎
    void scanDirectory(const std::wstring& path);

    std::wstring m_directoryPath;
    std::shared_ptr<CoreEngine> m_engine;
    HANDLE m_hDirectory = INVALID_HANDLE_VALUE;
    HANDLE m_hStopEvent = nullptr;
    std::thread m_monitorThread;
    std::atomic<bool> m_isRunning{false};
    
    // 存储上一个重命名文件的新名称，用于关联RENAMED_OLD和RENAMED_NEW事件
    std::wstring m_lastRenamedFile;

    // Buffer for ReadDirectoryChangesW
    static const DWORD BUFFER_SIZE = 4096;
    BYTE m_buffer[BUFFER_SIZE];
    OVERLAPPED m_overlapped = {0};
}; 