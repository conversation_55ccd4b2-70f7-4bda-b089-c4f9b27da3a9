#ifndef AUDIT_SERVICE_H
#define AUDIT_SERVICE_H

#include <stdio.h>
#include <stdlib.h>
#include <time.h>
#include <pthread.h>
#include <stdbool.h>
#include <curl/curl.h>
#include <json-c/json.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * 审计事件类型
 */
typedef enum {
    AUDIT_FILE_ENCRYPT = 1,
    AUDIT_FILE_DECRYPT = 2,
    AUDIT_KEY_SYNC = 3,
    AUDIT_POLICY_UPDATE = 4,
    AUDIT_USER_LOGIN = 5,
    AUDIT_USER_LOGOUT = 6,
    AUDIT_CONFIG_CHANGE = 7,
    AUDIT_ERROR = 8
} audit_event_type_t;

/**
 * 审计事件结构
 */
typedef struct {
    audit_event_type_t type;
    char timestamp[32];
    char user_id[64];
    char device_id[64];
    char file_path[512];
    char details[1024];
    char client_version[32];
    int result_code;
} audit_event_t;

/**
 * 审计服务配置
 */
typedef struct {
    char server_url[256];
    char api_key[128];
    char device_id[64];
    int batch_size;
    int upload_interval;
    int max_local_logs;
    bool enabled;
    int timeout_seconds;
    int retry_count;
} audit_config_t;

/**
 * 审计统计信息
 */
typedef struct {
    int total_logs;
    int upload_success;
    int upload_failed;
    int local_logs;
    time_t last_upload;
    time_t service_start_time;
} audit_statistics_t;

/**
 * 审计服务句柄
 */
typedef struct audit_service audit_service_t;

/**
 * 初始化审计服务
 * @param config 审计配置
 * @return 成功返回服务句柄，失败返回NULL
 */
audit_service_t* audit_service_init(const audit_config_t* config);

/**
 * 启动审计服务
 * @param service 审计服务句柄
 * @return 成功返回0，失败返回-1
 */
int audit_service_start(audit_service_t* service);

/**
 * 停止审计服务
 * @param service 审计服务句柄
 * @return 成功返回0，失败返回-1
 */
int audit_service_stop(audit_service_t* service);

/**
 * 记录审计事件
 * @param service 审计服务句柄
 * @param event 审计事件
 * @return 成功返回0，失败返回-1
 */
int audit_service_log_event(audit_service_t* service, const audit_event_t* event);

/**
 * 立即上传所有日志
 * @param service 审计服务句柄
 * @return 成功返回0，失败返回-1
 */
int audit_service_force_upload(audit_service_t* service);

/**
 * 清空本地日志
 * @param service 审计服务句柄
 * @return 成功返回0，失败返回-1
 */
int audit_service_clear_logs(audit_service_t* service);

/**
 * 获取审计统计信息
 * @param service 审计服务句柄
 * @param stats 统计信息结构
 * @return 成功返回0，失败返回-1
 */
int audit_service_get_statistics(audit_service_t* service, audit_statistics_t* stats);

/**
 * 创建审计事件
 * @param type 事件类型
 * @param user_id 用户ID
 * @param device_id 设备ID
 * @param file_path 文件路径
 * @param details 详细信息
 * @param result_code 结果代码
 * @param event 输出事件结构
 */
void audit_create_event(audit_event_type_t type, const char* user_id, 
                       const char* device_id, const char* file_path,
                       const char* details, int result_code, audit_event_t* event);

/**
 * 销毁审计服务
 * @param service 审计服务句柄
 */
void audit_service_destroy(audit_service_t* service);

/**
 * 获取事件类型字符串
 * @param type 事件类型
 * @return 事件类型字符串
 */
const char* audit_get_event_type_string(audit_event_type_t type);

#ifdef __cplusplus
}
#endif

#endif // AUDIT_SERVICE_H 