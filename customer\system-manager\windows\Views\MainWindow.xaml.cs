using System;
using System.Windows;
using Microsoft.Extensions.DependencyInjection;
using CryptoSystem.SystemManager.ViewModels;

namespace CryptoSystem.SystemManager.Views
{
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();
            
            // 获取依赖注入容器并设置ViewModel
            var app = (App)Application.Current;
            var viewModel = app.Services.GetRequiredService<MainWindowViewModel>();
            DataContext = viewModel;
        }

        private async void Window_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                var viewModel = (MainWindowViewModel)DataContext;
                await viewModel.InitializeAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化失败：{ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}