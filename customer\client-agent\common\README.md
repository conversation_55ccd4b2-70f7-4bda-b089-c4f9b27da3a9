# CryptoSystem 客户端公共组件

## 概述

此目录包含 CryptoSystem 客户端在各平台（Windows、macOS、Linux）之间共享的代码和接口。通过这些共享组件，确保核心功能在不同操作系统上的一致性，同时最大化代码复用。

## 目录结构

- `include/` - C++ 头文件和接口定义
- `src/` - 实现代码
  - `crypto/` - 密码学算法和工具
  - `network/` - 网络通信组件
  - `utils/` - 通用工具和辅助函数
  - `protocol/` - 客户端与服务器通信协议
  - `storage/` - 本地存储和缓存管理

## 主要组件

### 1. 接口定义

提供跨平台统一的接口定义，包括：

- `IFileSystemWatcher` - 文件系统监控接口
- `ICryptoProvider` - 密码学服务提供接口
- `IKeyManager` - 密钥管理接口
- `IPolicyEngine` - 策略引擎接口
- `INetworkClient` - 网络通信接口

### 2. 加密核心

实现核心加密/解密算法及相关功能：

- `BlockCipher` - 块加密实现（支持AES、SM4）
- `FileEncryptor` - 文件加密/解密操作
- `CryptoUtils` - 加密工具函数（哈希、签名、随机数）
- `ZKEProtocol` - 零知识加密协议实现

### 3. 网络通信

处理与服务器的通信：

- `SecureHttpClient` - 安全HTTP通信客户端
- `ProtoSerializer` - 消息序列化与反序列化
- `ConnectionManager` - 连接管理与重试机制
- `SyncManager` - 数据同步管理

### 4. 数据模型

定义系统核心数据模型：

- `User` - 用户信息模型
- `Document` - 文档元数据模型
- `Policy` - 安全策略模型
- `Key` - 密钥信息模型
- `Audit` - 审计日志模型

## 编码规范

共享库遵循以下规范：

1. **跨平台兼容性**: 避免平台特定的API，使用C++17标准库或第三方跨平台库
2. **异常处理**: 所有公共接口必须有明确的异常处理策略
3. **内存管理**: 优先使用智能指针和RAII模式
4. **线程安全**: 明确指定每个接口的线程安全性

## 依赖管理

共享组件使用以下第三方库：

- `OpenSSL` (或 `BoringSSL`) - 加密库
- `libcurl` - 网络请求
- `nlohmann/json` - JSON处理
- `spdlog` - 日志系统
- `GoogleTest` - 单元测试

## 构建指南

共享组件可以单独构建测试：

```bash
cd client/common
mkdir build && cd build
cmake ..
make
```

测试所有共享组件：

```bash
cd build
ctest -V
```

## 文档生成

可以使用Doxygen生成API文档：

```bash
cd client/common
doxygen Doxyfile
```

生成的文档将保存到 `docs/api` 目录。

## 开发状态

### **当前完成度: 85%** ✅ 基本完成

#### ✅ 已完成组件
- [x] 接口定义 (100%) - 完整的跨平台接口定义
- [x] 加密核心 (100%) - AES、SM4算法实现
- [x] 网络通信 (90%) - 安全HTTP客户端和同步管理
- [x] 数据模型 (100%) - 核心数据结构定义
- [x] 工具函数 (90%) - 通用辅助工具
- [x] 文档生成 (100%) - Doxygen API文档

#### 🔄 进行中组件
- [ ] 高级加密功能 (70%) - 零知识加密协议优化
- [ ] 性能优化 (60%) - 内存和CPU使用优化
- [ ] 错误处理完善 (80%) - 异常处理机制

#### 🎯 计划组件
- [ ] 缓存管理优化
- [ ] 多线程安全增强
- [ ] 平台特定优化接口

### 🚀 最新完成
- ✅ **跨平台兼容性**: 确保所有接口在各平台一致工作
- ✅ **内存管理优化**: 使用智能指针和RAII模式
- ✅ **线程安全**: 明确的线程安全性规范
- ✅ **单元测试**: 完整的测试覆盖

## 扩展指南

添加新的共享组件时，请遵循以下步骤：

1. 在 `include/` 中定义接口
2. 在 `src/` 中实现功能
3. 添加单元测试到 `tests/` 目录
4. 更新相关文档和依赖声明 