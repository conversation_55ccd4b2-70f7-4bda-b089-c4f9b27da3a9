# 文档加密系统 (Document Encryption System) V1.5

![开发状态](https://img.shields.io/badge/开发状态-75%25完成-yellow)
![平台支持](https://img.shields.io/badge/平台-Windows%20%7C%20Linux%20%7C%20macOS%20%7C%20HarmonyOS-blue)
![加密算法](https://img.shields.io/badge/加密-国密SM4%20%7C%20AES256-green)

## 📋 项目概述

本项目是一套企业级文档加密与安全管理系统，基于**透明加密**技术，实现对敏感文件的强制、透明加密保护。系统采用分布式架构，包含运营商端和客户端组件，确保数据安全的同时最大程度减少对用户工作习惯的影响。

### 🎯 核心理念

- **透明加密**: 文件系统驱动层面的实时、强制加解密
- **集中策略管控**: 统一的策略配置和分发机制
- **细粒度权限**: 基于用户/组/设备的精确权限控制
- **零知识原则**: 密钥管理遵循零知识原则
- **国密算法**: 强制采用国密SM4作为核心加密标准

## 🏗️ 系统架构

### 核心组件及开发状态

| 组件类别 | 组件名称 | Windows | Linux | macOS | HarmonyOS | 完成度 |
|---------|---------|---------|-------|-------|-----------|--------|
| **运营商组件** | 密钥生成器 | ✅ 90% | ❌ | ❌ | 🟡 45% | **70%** |
| | 数据库服务器 | ✅ 85% | ✅ 85% | ✅ 85% | ❌ | **85%** |
| **客户端组件** | 系统管理器 | 🟡 60% | ❌ | ❌ | 🟡 40% | **50%** |
| | 客户端代理 | ✅ 85% | ✅ 80% | 🟡 75% | 🟡 30% | **68%** |
| | 脱密客户端 | 🟡 70% | 🟡 65% | ✅ 95% | 🟡 35% | **66%** |

### 项目结构

```text
cryptosystem/
├── 📁 operator/                    # 运营商组件 (开发商/运营商运行)
│   ├── 🔑 key-generator/          # 密钥生成器 - 为客户提供密钥管理服务
│   │   ├── windows/               # Windows版本 (C# + WPF)
│   │   └── harmonyos/             # 鸿蒙版本 (ArkTS + ArkUI)
│   └── 🗄️ database-server/        # 数据库服务器配置 (PostgreSQL)
├── 📁 customer/                   # 客户端组件 (客户单位部署)
│   ├── 🎛️ system-manager/         # 系统管理器 - 管理控制中心
│   │   ├── windows/               # Windows版本 (C# + WPF)
│   │   └── harmonyos/             # 鸿蒙版本 (ArkTS + ArkUI)
│   ├── 🛡️ client-agent/          # 客户端代理 - 终端强制加密
│   │   ├── windows/               # Windows版本 (C++ + MiniFilter)
│   │   ├── linux/                 # Linux版本 (C + FUSE)
│   │   ├── macos/                 # macOS版本 (Swift + System Extensions)
│   │   ├── harmonyos/             # 鸿蒙版本 (ArkTS + ArkUI)
│   │   ├── common/                # 跨平台通用组件
│   │   └── samples/               # 示例代码
│   └── 📤 declassification-client/ # 脱密客户端 - 对外发送专用
│       ├── windows/               # Windows版本 (C# + WPF)
│       ├── linux/                 # Linux版本 (C++ + Qt)
│       ├── macos/                 # macOS版本 (Swift + SwiftUI) ✨ 最新完成
│       └── harmonyos/             # 鸿蒙版本 (ArkTS + ArkUI)
└── 📚 docs/                       # 项目文档
```

## 🚀 核心功能

### 1. 文件透明加解密

- **🔒 强制加密**: 基于文件系统驱动的实时加密
- **👻 透明操作**: 用户无感知的加解密过程  
- **🇨🇳 国密算法**: 强制使用SM4算法，支持AES-256备选
- **📋 策略控制**: 灵活的文件类型、路径、应用程序策略

### 2. 密钥管理体系

- **🔑 多层密钥**: 主密钥(MK) → 组织密钥(OK) → 用户/设备密钥(UK/DK) → 文件密钥(FK)
- **🚫 零知识原则**: 服务端不存储可解密用户数据的密钥明文
- **🔐 安全分发**: TLS双向认证的密钥分发机制
- **🔄 轮换机制**: 支持密钥定期更新和轮换

### 3. 智能数据可视化

- **📊 实时图表**: 用户活跃度趋势、设备状态分布等动态图表
- **📈 性能监控**: 系统性能指标实时监控和告警
- **🎯 统计分析**: 详细的操作统计和数据分析报告

### 4. 访问控制

- **🆔 身份认证**: 支持本地认证、域认证、MFA多因素认证
- **👥 权限管理**: 基于用户/组/设备的精细化权限控制
- **🏢 AD/LDAP集成**: 企业目录服务集成
- **📱 离线策略**: 支持离线工作和权限缓存

### 4. 安全审计

- **📊 全面审计**: 文件操作、管理操作、系统事件全覆盖
- **⚡ 实时监控**: 安全事件实时告警和响应
- **🔍 日志分析**: 多维度日志查询和分析
- **📈 合规报告**: 预定义报表和自定义报告功能

### 5. 终端防护

- **🛡️ 自我保护**: 防卸载、防调试、防逆向工程
- **🔌 外设管控**: USB设备精细化管控
- **🖥️ 屏幕保护**: 屏幕水印和截屏控制
- **⌨️ 行为控制**: 打印、剪贴板等操作管控

### 6. 安全外发

- **📋 脱密流程**: 受控的文件脱密审批流程
- **📦 外发包**: 带权限控制的安全外发包制作
- **📧 邮件白名单**: 可信外部邮箱白名单机制
- **🔍 审计追踪**: 外发全生命周期审计

## 🛠️ 技术栈

### 运营商组件

- **密钥生成器**: C# + WPF (Windows), ArkTS + ArkUI (HarmonyOS)
- **数据库服务器**: PostgreSQL, 支持MySQL、达梦、人大金仓

### 客户端组件

- **系统管理器**: C# + WPF (Windows), ArkTS + ArkUI (HarmonyOS)
- **客户端代理**:
  - Windows: C++ + Windows Driver Kit (WDK)
  - Linux: C + FUSE/eBPF
  - macOS: Swift + System Extensions
  - HarmonyOS: ArkTS + ArkUI + C++ NDK
- **脱密客户端**:
  - Windows: C# + WPF
  - Linux: C++ + Qt
  - macOS: Swift + SwiftUI
  - HarmonyOS: ArkTS + ArkUI

## 📋 系统要求

### 开发环境

- **Windows**: Visual Studio 2019+, Windows 10 SDK
- **Linux**: GCC 7+, CMake 3.16+
- **macOS**: Xcode 14.3+, macOS 13.0+
- **HarmonyOS**: DevEco Studio 4.0+

### 运行环境

- **操作系统**: Windows 10/11, macOS 13+, Linux (Ubuntu 18.04+), HarmonyOS 4.0+
- **CPU架构**: x86-64, ARM64
- **内存**: 最低4GB RAM，推荐8GB+
- **存储**: 最低500MB可用空间

## 🚀 快速开始

### 编译构建

#### Windows平台

```bash
# 客户端代理
cd customer/client-agent/windows
mkdir build && cd build
cmake ..
cmake --build . --config Release

# 系统管理器
cd customer/system-manager/windows
dotnet build SystemManager.csproj --configuration Release
```

#### Linux平台

```bash
# 客户端代理
cd customer/client-agent/linux
chmod +x build.sh
./build.sh

# 脱密客户端
cd customer/declassification-client/linux
chmod +x build.sh
./build.sh
```

#### macOS平台

```bash
# 客户端代理
cd customer/client-agent/macos
chmod +x build.sh
./build.sh

# 脱密客户端
cd customer/declassification-client/macos
chmod +x build.sh
./build.sh
```

#### HarmonyOS平台

```bash
# 在DevEco Studio中打开对应的HarmonyOS项目
# 或使用命令行工具
hvigor assembleHap
```

## 📚 文档导航

### 核心文档

- 📖 [用户需求文档](user_requirements.md) - 详细的功能需求和技术规范
- 📊 [组件分析报告](customer_components_analysis_report.md) - 组件完成状态分析

### 组件文档

- 🏢 [运营商组件说明](operator/README.md) - 密钥生成器和数据库服务器
- 👥 [客户端组件说明](customer/README.md) - 系统管理器、客户端代理、脱密客户端

### 平台特定文档

- 🪟 [Windows开发指南](customer/client-agent/windows/README.md)
- 🐧 [Linux开发指南](customer/client-agent/linux/README.md)  
- 🍎 [macOS开发指南](customer/client-agent/macos/README.md)
- 📱 [HarmonyOS开发指南](customer/client-agent/harmonyos/README.md)

## 📈 开发进展

### 最近更新 (2024年)

- ✅ **Windows密钥生成器核心功能完成**: 实现了密钥生成、管理和基础审计功能
- ✅ **Windows/Linux客户端代理基础实现**: 完成了文件系统驱动和加密核心功能
- ✅ **macOS脱密客户端完成**: 完整的Swift + SwiftUI实现
- 🟡 **HarmonyOS平台基础架构**: 完成了项目结构和接口定义，核心功能待实现
- ✅ **数据库架构设计**: 完整的PostgreSQL数据库schema和部署配置

### 项目里程碑

- 🎯 **核心平台基础完成**: Windows/Linux平台核心功能基本实现
- 🟡 **跨平台架构建立**: 多平台项目结构已建立，部分平台需要进一步开发
- ✅ **安全架构设计**: 完整的安全防护和审计体系设计
- 🟡 **功能完整性**: 核心功能已实现，高级功能和HarmonyOS平台需要继续开发

### 下一步计划

- 🎯 **HarmonyOS平台核心功能开发**: 完成客户端代理和系统管理器的核心实现
- 🎯 **Windows系统管理器功能完善**: 补充高级管理功能和UI优化
- 🎯 **Linux/Windows脱密客户端功能完善**: 完成剩余功能模块
- 🎯 **集成测试和性能优化**: 跨组件集成测试和性能调优
- 🎯 **生产环境部署验证**: 实际环境测试和部署优化

## ⚠️ 重要说明

### 安全注意事项

- 本系统涉及企业级安全防护，部署前请确保充分理解安全要求
- 密钥管理遵循零知识原则，请妥善保管主密钥
- 建议在生产环境部署前进行充分的安全评估

### 合规性要求

- 系统设计遵循国家网络安全法和数据安全法要求
- 支持网络安全等级保护(等保2.0)相关标准
- 强制使用国密算法确保合规性

## 📞 联系我们

- 📧 技术支持: [<EMAIL>]
- 🐛 问题反馈: [GitHub Issues]
- 📖 项目文档: [项目主页]

---

**Copyright © 2024 Document Encryption System. All rights reserved.**
