import Foundation
import Network
import Combine

/// 网络管理器
/// 负责处理所有网络通信，包括与系统管理器的连接
class NetworkManager: ObservableObject {
    
    // MARK: - Singleton
    static let shared = NetworkManager()
    
    // MARK: - Published Properties
    @Published var isConnected = false
    @Published var connectionStatus: ConnectionStatus = .disconnected
    @Published var serverInfo: ServerInfo?
    
    // MARK: - Private Properties
    private let monitor = NWPathMonitor()
    private let monitorQueue = DispatchQueue(label: "NetworkMonitor")
    private var session: URLSession
    private let configuration: URLSessionConfiguration
    
    private let auditLogger = AuditLogger.shared
    private let configManager = ConfigurationManager.shared
    
    // MARK: - Initialization
    private init() {
        // 配置URLSession
        configuration = URLSessionConfiguration.default
        configuration.timeoutIntervalForRequest = 30
        configuration.timeoutIntervalForResource = 60
        configuration.requestCachePolicy = .useProtocolCachePolicy
        
        session = URLSession(configuration: configuration)
        
        setupNetworkMonitoring()
    }
    
    // MARK: - Public Methods
    
    /// 测试与服务器的连接
    func testConnection() async -> Bool {
        let serverConfig = configManager.configuration.server
        let url = buildURL(endpoint: "/api/health")
        
        do {
            let request = createRequest(url: url, method: .GET)
            let (data, response) = try await session.data(for: request)
            
            guard let httpResponse = response as? HTTPURLResponse else {
                throw NetworkError.invalidResponse("无效的响应格式")
            }
            
            let isSuccess = httpResponse.statusCode == 200
            
            await auditLogger.logEvent(
                type: .systemEvent,
                message: "服务器连接测试",
                details: [
                    "server": serverConfig.host,
                    "port": String(serverConfig.port),
                    "status": String(httpResponse.statusCode),
                    "success": String(isSuccess)
                ]
            )
            
            if isSuccess {
                // 尝试解析服务器信息
                if let serverInfo = try? JSONDecoder().decode(ServerInfo.self, from: data) {
                    await MainActor.run {
                        self.serverInfo = serverInfo
                        self.connectionStatus = .connected
                    }
                }
            }
            
            return isSuccess
            
        } catch {
            await auditLogger.logEvent(
                type: .errorEvent,
                message: "服务器连接测试失败",
                details: [
                    "server": serverConfig.host,
                    "error": error.localizedDescription
                ]
            )
            
            await MainActor.run {
                self.connectionStatus = .error(error.localizedDescription)
            }
            
            return false
        }
    }
    
    /// 上传任务结果
    func uploadTaskResult(_ taskResult: TaskResult) async throws {
        let url = buildURL(endpoint: "/api/tasks/result")
        let request = try createJSONRequest(url: url, method: .POST, body: taskResult)
        
        let (_, response) = try await session.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              200...299 ~= httpResponse.statusCode else {
            throw NetworkError.serverError("上传任务结果失败")
        }
        
        await auditLogger.logEvent(
            type: .systemEvent,
            message: "上传任务结果",
            details: [
                "taskId": taskResult.taskId.uuidString,
                "status": taskResult.status.rawValue
            ]
        )
    }
    
    /// 下载系统配置
    func downloadSystemConfiguration() async throws -> SystemConfiguration {
        let url = buildURL(endpoint: "/api/config")
        let request = createRequest(url: url, method: .GET)
        
        let (data, response) = try await session.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              200...299 ~= httpResponse.statusCode else {
            throw NetworkError.serverError("下载配置失败")
        }
        
        let systemConfig = try JSONDecoder().decode(SystemConfiguration.self, from: data)
        
        await auditLogger.logEvent(
            type: .systemEvent,
            message: "下载系统配置",
            details: ["configVersion": systemConfig.version]
        )
        
        return systemConfig
    }
    
    /// 上传文件
    func uploadFile(from url: URL, to endpoint: String) async throws -> UploadResponse {
        let uploadURL = buildURL(endpoint: endpoint)
        var request = URLRequest(url: uploadURL)
        request.httpMethod = "POST"
        
        // 创建multipart/form-data请求
        let boundary = UUID().uuidString
        request.setValue("multipart/form-data; boundary=\(boundary)", forHTTPHeaderField: "Content-Type")
        
        let formData = try createMultipartFormData(fileURL: url, boundary: boundary)
        request.httpBody = formData
        
        let (data, response) = try await session.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              200...299 ~= httpResponse.statusCode else {
            throw NetworkError.uploadFailed("文件上传失败")
        }
        
        let uploadResponse = try JSONDecoder().decode(UploadResponse.self, from: data)
        
        await auditLogger.logEvent(
            type: .systemEvent,
            message: "文件上传完成",
            details: [
                "fileName": url.lastPathComponent,
                "fileId": uploadResponse.fileId
            ]
        )
        
        return uploadResponse
    }
    
    /// 下载文件
    func downloadFile(fileId: String, to destination: URL) async throws {
        let url = buildURL(endpoint: "/api/files/\(fileId)")
        let request = createRequest(url: url, method: .GET)
        
        let (data, response) = try await session.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              200...299 ~= httpResponse.statusCode else {
            throw NetworkError.downloadFailed("文件下载失败")
        }
        
        try data.write(to: destination)
        
        await auditLogger.logEvent(
            type: .systemEvent,
            message: "文件下载完成",
            details: [
                "fileId": fileId,
                "destination": destination.path
            ]
        )
    }
    
    /// 获取任务状态
    func getTaskStatus(taskId: UUID) async throws -> TaskStatusResponse {
        let url = buildURL(endpoint: "/api/tasks/\(taskId.uuidString)/status")
        let request = createRequest(url: url, method: .GET)
        
        let (data, response) = try await session.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              200...299 ~= httpResponse.statusCode else {
            throw NetworkError.serverError("获取任务状态失败")
        }
        
        return try JSONDecoder().decode(TaskStatusResponse.self, from: data)
    }
    
    /// 发送心跳
    func sendHeartbeat() async {
        do {
            let url = buildURL(endpoint: "/api/heartbeat")
            let heartbeat = HeartbeatRequest(
                clientId: getClientId(),
                timestamp: Date(),
                status: "active"
            )
            
            let request = try createJSONRequest(url: url, method: .POST, body: heartbeat)
            let (_, response) = try await session.data(for: request)
            
            if let httpResponse = response as? HTTPURLResponse,
               200...299 ~= httpResponse.statusCode {
                await MainActor.run {
                    self.connectionStatus = .connected
                }
            }
            
        } catch {
            await MainActor.run {
                self.connectionStatus = .error(error.localizedDescription)
            }
        }
    }
    
    // MARK: - Private Methods
    
    private func setupNetworkMonitoring() {
        monitor.pathUpdateHandler = { [weak self] path in
            DispatchQueue.main.async {
                self?.isConnected = path.status == .satisfied
                
                if path.status == .satisfied {
                    self?.connectionStatus = .connecting
                    Task {
                        _ = await self?.testConnection()
                    }
                } else {
                    self?.connectionStatus = .disconnected
                }
            }
        }
        
        monitor.start(queue: monitorQueue)
    }
    
    private func buildURL(endpoint: String) -> URL {
        let config = configManager.configuration.server
        let scheme = config.useSSL ? "https" : "http"
        let urlString = "\(scheme)://\(config.host):\(config.port)\(endpoint)"
        
        guard let url = URL(string: urlString) else {
            fatalError("无效的URL: \(urlString)")
        }
        
        return url
    }
    
    private func createRequest(url: URL, method: HTTPMethod) -> URLRequest {
        var request = URLRequest(url: url)
        request.httpMethod = method.rawValue
        request.setValue("application/json", forHTTPHeaderField: "Accept")
        request.setValue("DeclassificationClient/1.0", forHTTPHeaderField: "User-Agent")
        
        // 添加认证头
        if let apiKey = configManager.configuration.server.apiKey {
            request.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        }
        
        return request
    }
    
    private func createJSONRequest<T: Codable>(url: URL, method: HTTPMethod, body: T) throws -> URLRequest {
        var request = createRequest(url: url, method: method)
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        request.httpBody = try encoder.encode(body)
        
        return request
    }
    
    private func createMultipartFormData(fileURL: URL, boundary: String) throws -> Data {
        var formData = Data()
        
        // 添加文件数据
        formData.append("--\(boundary)\r\n".data(using: .utf8)!)
        formData.append("Content-Disposition: form-data; name=\"file\"; filename=\"\(fileURL.lastPathComponent)\"\r\n".data(using: .utf8)!)
        formData.append("Content-Type: application/octet-stream\r\n\r\n".data(using: .utf8)!)
        
        let fileData = try Data(contentsOf: fileURL)
        formData.append(fileData)
        formData.append("\r\n".data(using: .utf8)!)
        
        // 结束边界
        formData.append("--\(boundary)--\r\n".data(using: .utf8)!)
        
        return formData
    }
    
    private func getClientId() -> String {
        let key = "client_id"
        if let existingId = UserDefaults.standard.string(forKey: key) {
            return existingId
        }
        
        let newId = UUID().uuidString
        UserDefaults.standard.set(newId, forKey: key)
        return newId
    }
}

// MARK: - Supporting Types

enum ConnectionStatus: Equatable {
    case disconnected
    case connecting
    case connected
    case error(String)
    
    var displayText: String {
        switch self {
        case .disconnected:
            return "未连接"
        case .connecting:
            return "连接中..."
        case .connected:
            return "已连接"
        case .error(let message):
            return "错误: \(message)"
        }
    }
    
    var color: Color {
        switch self {
        case .disconnected:
            return .gray
        case .connecting:
            return .orange
        case .connected:
            return .green
        case .error:
            return .red
        }
    }
}

enum HTTPMethod: String {
    case GET = "GET"
    case POST = "POST"
    case PUT = "PUT"
    case DELETE = "DELETE"
    case PATCH = "PATCH"
}

struct ServerInfo: Codable {
    let name: String
    let version: String
    let apiVersion: String
    let capabilities: [String]
    let timestamp: Date
}

struct TaskResult: Codable {
    let taskId: UUID
    let status: TaskStatus
    let completedAt: Date?
    let errorMessage: String?
    let fileResults: [FileResult]
}

struct FileResult: Codable {
    let fileId: UUID
    let status: FileProcessStatus
    let outputPath: String?
    let checksum: String?
}

struct SystemConfiguration: Codable {
    let version: String
    let policies: [SecurityPolicy]
    let allowedFileTypes: [String]
    let maxFileSize: Int64
    let retentionDays: Int
}

struct SecurityPolicy: Codable {
    let id: String
    let name: String
    let description: String
    let rules: [PolicyRule]
}

struct PolicyRule: Codable {
    let type: String
    let condition: String
    let action: String
}

struct UploadResponse: Codable {
    let fileId: String
    let uploadedAt: Date
    let checksum: String
}

struct TaskStatusResponse: Codable {
    let taskId: UUID
    let status: TaskStatus
    let progress: Double
    let estimatedCompletion: Date?
    let message: String?
}

struct HeartbeatRequest: Codable {
    let clientId: String
    let timestamp: Date
    let status: String
    let systemInfo: SystemInfo?
    
    init(clientId: String, timestamp: Date, status: String) {
        self.clientId = clientId
        self.timestamp = timestamp
        self.status = status
        self.systemInfo = SystemInfo.current
    }
}

struct SystemInfo: Codable {
    let osVersion: String
    let appVersion: String
    let memoryUsage: Int64
    let diskSpace: Int64
    
    static let current = SystemInfo(
        osVersion: ProcessInfo.processInfo.operatingSystemVersionString,
        appVersion: Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0.0",
        memoryUsage: ProcessInfo.processInfo.physicalMemory,
        diskSpace: getDiskSpace()
    )
    
    private static func getDiskSpace() -> Int64 {
        do {
            let systemAttributes = try FileManager.default.attributesOfFileSystem(forPath: NSHomeDirectory())
            return systemAttributes[.systemFreeSize] as? Int64 ?? 0
        } catch {
            return 0
        }
    }
}

// MARK: - Error Types

enum NetworkError: LocalizedError {
    case noConnection
    case invalidURL(String)
    case invalidResponse(String)
    case serverError(String)
    case uploadFailed(String)
    case downloadFailed(String)
    case authenticationFailed
    case timeout
    case unknownError(String)
    
    var errorDescription: String? {
        switch self {
        case .noConnection:
            return "网络连接不可用"
        case .invalidURL(let url):
            return "无效的URL: \(url)"
        case .invalidResponse(let message):
            return "无效的响应: \(message)"
        case .serverError(let message):
            return "服务器错误: \(message)"
        case .uploadFailed(let message):
            return "上传失败: \(message)"
        case .downloadFailed(let message):
            return "下载失败: \(message)"
        case .authenticationFailed:
            return "认证失败"
        case .timeout:
            return "请求超时"
        case .unknownError(let message):
            return "未知错误: \(message)"
        }
    }
} 