using KeyGenerator.Models;
using System;

namespace KeyGenerator
{
    /// <summary>
    /// 密钥状态辅助类，提供枚举到显示文本的转换
    /// </summary>
    public static class KeyStatusHelper
    {
        /// <summary>
        /// 将KeyStatus枚举转换为显示文本
        /// </summary>
        /// <param name="status">密钥状态枚举</param>
        /// <returns>对应的显示文本</returns>
        public static string ToDisplayText(this KeyStatus status)
        {
            return status switch
            {
                KeyStatus.Active => "活动",
                KeyStatus.Pending => "待激活",
                KeyStatus.Suspended => "已暂停",
                KeyStatus.Revoked => "已吊销",
                KeyStatus.Expired => "已过期",
                KeyStatus.Destroyed => "已销毁",
                _ => status.ToString()
            };
        }
    }
} 