# Windows驱动CMake构建文件

cmake_minimum_required(VERSION 3.13)

# 设置驱动名称
set(DRIVER_NAME "encfilter")

# 设置项目
project(${DRIVER_NAME})

# 源文件
set(SOURCE_FILES
    enc_filter.c
    policy.c
    crypto.c
    context.c
    utils.c
)

# 头文件
set(HEADER_FILES
    enc_filter.h
    policy.h
    crypto.h
    utils.h
)

# 设置为Windows驱动项目
set(CMAKE_SYSTEM_NAME Windows)

# 检查是否使用WDK构建环境
if(NOT DEFINED ENV{WINDDK_VERSION})
    message(FATAL_ERROR "Windows Driver Kit环境未设置")
endif()

# 添加驱动库
add_library(${DRIVER_NAME} SHARED ${SOURCE_FILES} ${HEADER_FILES})

# 设置驱动属性
set_target_properties(${DRIVER_NAME} PROPERTIES
    SUFFIX ".sys"
    COMPILE_FLAGS "/W4 /WX /wd4214 /wd4201"
    LINK_FLAGS "/ENTRY:DriverEntry /SUBSYSTEM:NATIVE /DRIVER"
)

# 包含目录
target_include_directories(${DRIVER_NAME} PRIVATE
    $ENV{WINDDK_INC_PATH}
    $ENV{WINDDK_INC_PATH}/km
)

# 链接库
target_link_libraries(${DRIVER_NAME} PRIVATE
    $ENV{WINDDK_LIB_PATH}/km/x64/fltmgr.lib
    $ENV{WINDDK_LIB_PATH}/km/x64/ntoskrnl.lib
)

# 安装规则
install(TARGETS ${DRIVER_NAME}
    RUNTIME DESTINATION drivers
) 