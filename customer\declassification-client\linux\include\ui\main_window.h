#pragma once

#include <QMainWindow>
#include <QMenuBar>
#include <QToolBar>
#include <QStatusBar>
#include <QSplitter>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QTabWidget>
#include <QTreeWidget>
#include <QTableWidget>
#include <QListWidget>
#include <QProgressBar>
#include <QLabel>
#include <QDockWidget>
#include <QAction>
#include <QActionGroup>
#include <QTimer>
#include <QSystemTrayIcon>
#include <QMenu>
#include <memory>
#include <QTextEdit> // Add this include

namespace DeclassificationClient {

// 前向声明
class DeclassificationService;
class TaskManagerWidget;
class FileListWidget;
class ProgressDialog;

namespace UI {

/**
 * 主窗口类
 */
class MainWindow : public QMainWindow {
    Q_OBJECT

public:
    /**
     * 构造函数
     * @param service 脱密服务实例
     * @param parent 父窗口
     */
    explicit MainWindow(DeclassificationService* service, QWidget* parent = nullptr);
    
    /**
     * 析构函数
     */
    ~MainWindow();

protected:
    /**
     * 关闭事件
     */
    void closeEvent(QCloseEvent* event) override;
    
    /**
     * 窗口状态改变事件
     */
    void changeEvent(QEvent* event) override;

private slots:
    /**
     * 创建新任务
     */
    void createNewTask();
    
    /**
     * 打开文件
     */
    void openFiles();
    
    /**
     * 处理任务
     */
    void processTask();
    
    /**
     * 取消任务
     */
    void cancelTask();
    
    /**
     * 删除任务
     */
    void deleteTask();
    
    /**
     * 生成安全包
     */
    void generateSecurePackage();
    
    /**
     * 查看日志
     */
    void viewLogs();
    
    /**
     * 显示设置
     */
    void showSettings();
    
    /**
     * 显示关于
     */
    void showAbout();
    
    /**
     * 退出应用程序
     */
    void exitApplication();
    
    /**
     * 系统托盘激活
     */
    void systemTrayActivated(QSystemTrayIcon::ActivationReason reason);
    
    /**
     * 显示上下文菜单
     */
    void showContextMenu(const QPoint& point);
    
    /**
     * 刷新数据
     */
    void refreshData();
    
    /**
     * 更新状态栏
     */
    void updateStatusBar();
    
    /**
     * 选择改变
     */
    void selectionChanged();

    /**
     * 当任务被选择时
     */
    void onTaskSelected(const QString& taskId);

    /**
     * @brief Logs a message to the log view.
     * @param message The message to log.
     */
    void onLogMessage(const QString& message);

    /**
     * @brief Called when the simulated task processing is finished.
     */
    void onTaskProcessingFinished();

    /**
     * 删除任务
     */
    void handleDeleteTask(const QString& taskId);

private:
    /**
     * 初始化UI
     */
    void initializeUI();
    
    /**
     * 创建菜单栏
     */
    void createMenuBar();
    
    /**
     * 创建工具栏
     */
    void createToolBar();
    
    /**
     * 创建状态栏
     */
    void createStatusBar();
    
    /**
     * 创建中央窗口
     */
    void createCentralWidget();
    
    /**
     * 创建停靠窗口
     */
    void createDockWidgets();
    
    /**
     * 创建系统托盘
     */
    void createSystemTray();
    
    /**
     * 连接信号
     */
    void connectSignals();
    
    /**
     * 设置窗口属性
     */
    void setupWindowProperties();
    
    /**
     * 加载设置
     */
    void loadSettings();
    
    /**
     * 保存设置
     */
    void saveSettings();
    
    /**
     * 初始化示例数据
     */
    void initializeSampleData();

private:
    // 服务实例
    DeclassificationService* service_;
    
    // 菜单
    QMenu* fileMenu_;
    QMenu* taskMenu_;
    QMenu* toolsMenu_;
    QMenu* helpMenu_;
    
    // 工具栏
    QToolBar* mainToolBar_;
    QToolBar* taskToolBar_;
    
    // 状态栏
    QLabel* statusLabel_;
    QProgressBar* progressBar_;
    QLabel* taskCountLabel_;
    
    // 中央窗口
    QTabWidget* centralTabs_;
    
    // 主要窗口组件
    TaskManagerWidget* taskManager_;
    FileListWidget* fileList_;
    
    // 停靠窗口
    QDockWidget* logDock_;
    QTextEdit* logView_; // The actual log text view
    QDockWidget* statisticsDock_;
    
    // 系统托盘
    QSystemTrayIcon* systemTray_;
    QMenu* trayMenu_;
    
    // 定时器
    QTimer* refreshTimer_;
    QTimer* processingTimer_; // Timer for simulating task processing
    
    // 对话框
    std::unique_ptr<ProgressDialog> progressDialog_;
    
    // 操作
    QAction* newTaskAction_;
    QAction* openFilesAction_;
    QAction* processTaskAction_;
    QAction* cancelTaskAction_;
    QAction* deleteTaskAction_;
    QAction* generatePackageAction_;
    QAction* viewLogsAction_;
    QAction* settingsAction_;
    QAction* aboutAction_;
    QAction* exitAction_;
    
    // 状态
    bool isInitialized_;
    bool isMinimizedToTray_;
};

} // namespace UI
} // namespace DeclassificationClient 