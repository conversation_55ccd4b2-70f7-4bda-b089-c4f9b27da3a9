cmake_minimum_required(VERSION 3.10)
project(cryptosystem_client)

# 设置C++标准
set(CMAKE_CXX_STANDARD 14)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 添加源文件目录
include_directories(include)
include_directories(src)

# 核心库源文件
set(WATERMARK_LIB_SOURCES
    src/Logger.cpp
    src/WatermarkManager.cpp
)

# 创建静态库
add_library(watermark_lib STATIC ${WATERMARK_LIB_SOURCES})

# 链接Windows库
target_link_libraries(watermark_lib d2d1 dwrite iphlpapi)

# 安装目标
install(TARGETS watermark_lib DESTINATION lib)
install(FILES src/WatermarkManager.h src/Logger.h DESTINATION include)

# 复制配置文件到输出目录（如果有的话）
# configure_file(${CMAKE_SOURCE_DIR}/config.json ${CMAKE_BINARY_DIR}/config.json COPYONLY) 