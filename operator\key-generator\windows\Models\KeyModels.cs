using System;
using System.ComponentModel;

namespace KeyGenerator.Models;

/// <summary>
/// 密钥类型
/// </summary>
public enum KeyType
{
    [Description("主密钥")]
    MasterKey = 1,
    
    [Description("工作密钥")]
    WorkKey = 2,
    
    [Description("传输密钥")]
    TransportKey = 3,
    
    [Description("存储密钥")]
    StorageKey = 4
}

/// <summary>
/// 工作密钥类型
/// </summary>
public enum WorkKeyType
{
    [Description("文件加密密钥")]
    FileEncryption = 1,
    
    [Description("数据库加密密钥")]
    DatabaseEncryption = 2,
    
    [Description("通信加密密钥")]
    CommunicationEncryption = 3,
    
    [Description("数字签名密钥")]
    DigitalSignature = 4
}

/// <summary>
/// 加密算法
/// </summary>
public enum CryptoAlgorithm
{
    [Description("AES-256")]
    AES256 = 1,
    
    [Description("AES-128")]
    AES128 = 2,
    
    [Description("SM4（国密）")]
    SM4 = 3,
    
    [Description("RSA-2048")]
    RSA2048 = 4,
    
    [Description("RSA-4096")]
    RSA4096 = 5,
    
    [Description("SM2（国密）")]
    SM2 = 6,
    
    [Description("ECC P-256")]
    ECCP256 = 7,
    
    [Description("ECC P-384")]
    ECCP384 = 8
}

/// <summary>
/// 密钥状态
/// </summary>
public enum KeyStatus
{
    [Description("待激活")]
    Pending = 0,
    
    [Description("活动")]
    Active = 1,
    
    [Description("已暂停")]
    Suspended = 2,
    
    [Description("已吊销")]
    Revoked = 3,
    
    [Description("已过期")]
    Expired = 4,
    
    [Description("已销毁")]
    Destroyed = 5
}

/// <summary>
/// 密钥强度等级
/// </summary>
public enum KeyStrengthLevel
{
    [Description("弱")]
    Weak = 1,
    
    [Description("中等")]
    Medium = 2,
    
    [Description("强")]
    Strong = 3,
    
    [Description("很强")]
    VeryStrong = 4
}

/// <summary>
/// 密钥导出格式
/// </summary>
public enum KeyExportFormat
{
    [Description("PKCS#8")]
    PKCS8 = 1,
    
    [Description("PKCS#12")]
    PKCS12 = 2,
    
    [Description("PEM")]
    PEM = 3,
    
    [Description("DER")]
    DER = 4,
    
    [Description("JSON")]
    JSON = 5,
    
    [Description("原始格式")]
    Raw = 6
}

/// <summary>
/// 密钥导入格式
/// </summary>
public enum KeyImportFormat
{
    [Description("PKCS#8")]
    PKCS8 = 1,
    
    [Description("PKCS#12")]
    PKCS12 = 2,
    
    [Description("PEM")]
    PEM = 3,
    
    [Description("DER")]
    DER = 4,
    
    [Description("JSON")]
    JSON = 5,
    
    [Description("原始格式")]
    Raw = 6
}

/// <summary>
/// 密钥信息实体
/// </summary>
public record KeyInfo
{
    /// <summary>
    /// 密钥ID
    /// </summary>
    public required string KeyId { get; set; }

    /// <summary>
    /// 密钥名称
    /// </summary>
    public required string KeyName { get; set; }

    /// <summary>
    /// 密钥类型
    /// </summary>
    public KeyType KeyType { get; set; }

    /// <summary>
    /// 加密算法
    /// </summary>
    public CryptoAlgorithm Algorithm { get; set; }

    /// <summary>
    /// 密钥长度
    /// </summary>
    public int KeyLength { get; set; }

    /// <summary>
    /// 客户单位ID
    /// </summary>
    public required string ClientId { get; set; }

    /// <summary>
    /// 客户单位名称
    /// </summary>
    public required string ClientName { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; }

    /// <summary>
    /// 生效时间
    /// </summary>
    public DateTime EffectiveDate { get; set; }

    /// <summary>
    /// 过期时间
    /// </summary>
    public DateTime ExpirationDate { get; set; }

    /// <summary>
    /// 密钥状态
    /// </summary>
    public KeyStatus Status { get; set; }

    /// <summary>
    /// 创建者
    /// </summary>
    public required string CreatedBy { get; set; }

    /// <summary>
    /// 最后修改时间
    /// </summary>
    public DateTime UpdatedTime { get; set; } // 将 LastModified 重命名为 UpdatedTime

    /// <summary>
    /// 最后修改者
    /// </summary>
    public required string UpdatedBy { get; set; } // 将 LastModifiedBy 重命名为 UpdatedBy

    /// <summary>
    /// 备注信息
    /// </summary>
    public required string Description { get; set; }

    /// <summary>
    /// 密钥哈希值
    /// </summary>
    public required string KeyHash { get; set; }

    /// <summary>
    /// 是否为国密算法
    /// </summary>
    public bool IsNationalCrypto { get; set; }

    /// <summary>
    /// 使用次数
    /// </summary>
    public long UsageCount { get; set; }

    /// <summary>
    /// 最后使用时间
    /// </summary>
    public DateTime? LastUsedTime { get; set; }
}

/// <summary>
/// 密钥导入结果
/// </summary>
public record KeyImportResult
{
    /// <summary>
    /// 导入是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 导入的密钥ID
    /// </summary>
    public required string KeyId { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public required string ErrorMessage { get; set; }

    /// <summary>
    /// 导入的密钥信息
    /// </summary>
    public required KeyInfo KeyInfo { get; set; }
}

/// <summary>
/// 客户单位信息
/// </summary>
public record ClientInfo
{
    /// <summary>
    /// 客户ID
    /// </summary>
    public required string ClientId { get; set; }

    /// <summary>
    /// 客户名称
    /// </summary>
    public required string ClientName { get; set; }

    /// <summary>
    /// 联系人
    /// </summary>
    public required string ContactPerson { get; set; }

    /// <summary>
    /// 联系电话
    /// </summary>
    public required string ContactPhone { get; set; }

    /// <summary>
    /// 联系邮箱
    /// </summary>
    public required string ContactEmail { get; set; }

    /// <summary>
    /// 地址
    /// </summary>
    public required string Address { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public required string Remarks { get; set; }
} 