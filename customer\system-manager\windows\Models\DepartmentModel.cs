using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CryptoSystem.SystemManager.Models
{
    /// <summary>
    /// 部门实体
    /// </summary>
    [Table("sys_departments")]
    public class Department
    {
        /// <summary>
        /// 部门ID
        /// </summary>
        [Key]
        [Column("department_id")]
        public string DepartmentId { get; set; } = string.Empty;

        /// <summary>
        /// 部门名称
        /// </summary>
        [Required]
        [MaxLength(200)]
        [Column("department_name")]
        public string DepartmentName { get; set; } = string.Empty;

        /// <summary>
        /// 部门代码
        /// </summary>
        [MaxLength(50)]
        [Column("department_code")]
        public string DepartmentCode { get; set; } = string.Empty;

        /// <summary>
        /// 部门描述
        /// </summary>
        [MaxLength(1000)]
        [Column("description")]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 父部门ID
        /// </summary>
        [Column("parent_department_id")]
        public string? ParentDepartmentId { get; set; }

        /// <summary>
        /// 部门层级
        /// </summary>
        [Column("level")]
        public int Level { get; set; } = 1;

        /// <summary>
        /// 部门路径（用于快速查询）
        /// </summary>
        [MaxLength(1000)]
        [Column("path")]
        public string Path { get; set; } = string.Empty;

        /// <summary>
        /// 排序号
        /// </summary>
        [Column("sort_order")]
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 部门负责人ID
        /// </summary>
        [Column("manager_id")]
        public string? ManagerId { get; set; }

        /// <summary>
        /// 部门负责人姓名
        /// </summary>
        [MaxLength(100)]
        [Column("manager_name")]
        public string ManagerName { get; set; } = string.Empty;

        /// <summary>
        /// 联系电话
        /// </summary>
        [MaxLength(50)]
        [Column("phone")]
        public string Phone { get; set; } = string.Empty;

        /// <summary>
        /// 电子邮箱
        /// </summary>
        [MaxLength(200)]
        [Column("email")]
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// 办公地址
        /// </summary>
        [MaxLength(500)]
        [Column("address")]
        public string Address { get; set; } = string.Empty;

        /// <summary>
        /// 是否启用
        /// </summary>
        [Column("is_enabled")]
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        [Column("created_time")]
        public DateTime CreatedTime { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 创建者
        /// </summary>
        [MaxLength(100)]
        [Column("created_by")]
        public string CreatedBy { get; set; } = string.Empty;

        /// <summary>
        /// 最后修改时间
        /// </summary>
        [Column("last_modified_time")]
        public DateTime LastModifiedTime { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 最后修改者
        /// </summary>
        [MaxLength(100)]
        [Column("last_modified_by")]
        public string LastModifiedBy { get; set; } = string.Empty;

        /// <summary>
        /// 备注信息
        /// </summary>
        [MaxLength(1000)]
        [Column("remarks")]
        public string Remarks { get; set; } = string.Empty;

        // 导航属性
        public virtual Department? ParentDepartment { get; set; }
        public virtual ICollection<Department> ChildDepartments { get; set; } = new List<Department>();
        public virtual User? Manager { get; set; }
        public virtual ICollection<User> Users { get; set; } = new List<User>();
    }

    /// <summary>
    /// 部门统计信息
    /// </summary>
    public class DepartmentStatistics
    {
        /// <summary>
        /// 总部门数
        /// </summary>
        public int TotalDepartments { get; set; }

        /// <summary>
        /// 启用的部门数
        /// </summary>
        public int EnabledDepartments { get; set; }

        /// <summary>
        /// 禁用的部门数
        /// </summary>
        public int DisabledDepartments { get; set; }

        /// <summary>
        /// 按层级分组的部门数
        /// </summary>
        public Dictionary<int, int> DepartmentsByLevel { get; set; } = new();

        /// <summary>
        /// 最大部门层级
        /// </summary>
        public int MaxLevel { get; set; }

        /// <summary>
        /// 有用户的部门数
        /// </summary>
        public int DepartmentsWithUsers { get; set; }

        /// <summary>
        /// 空部门数（无用户）
        /// </summary>
        public int EmptyDepartments { get; set; }
    }
}