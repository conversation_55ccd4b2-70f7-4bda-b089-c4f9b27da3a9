import SwiftUI

struct AppCommands: Commands {
    @EnvironmentObject var declassificationService: DeclassificationService
    @EnvironmentObject var configurationManager: ConfigurationManager
    @EnvironmentObject var auditLogger: AuditLogger
    
    var body: some Commands {
        // 替换标准的"New"命令
        CommandGroup(replacing: .newItem) {
            Button("新建脱密任务...") {
                // 这里可以发送通知给主视图来显示新建任务对话框
                NotificationCenter.default.post(name: .showNewTaskDialog, object: nil)
            }
            .keyboardShortcut("n", modifiers: .command)
        }
        
        // 文件菜单
        CommandMenu("文件") {
            But<PERSON>("导入任务...") {
                importTasks()
            }
            .keyboardShortcut("i", modifiers: [.command, .shift])
            
            Button("导出任务...") {
                exportTasks()
            }
            .keyboardShortcut("e", modifiers: [.command, .shift])
            
            Divider()
            
            But<PERSON>("导入配置...") {
                importConfiguration()
            }
            
            Button("导出配置...") {
                exportConfiguration()
            }
            
            Divider()
            
            Button("导出审计日志...") {
                exportAuditLogs()
            }
            .keyboardShortcut("l", modifiers: [.command, .shift])
        }
        
        // 编辑菜单
        CommandGroup(after: .undoRedo) {
            Divider()
            
            Button("全选任务") {
                // 发送通知选择所有任务
                NotificationCenter.default.post(name: .selectAllTasks, object: nil)
            }
            .keyboardShortcut("a", modifiers: .command)
            
            Button("刷新任务列表") {
                Task {
                    await declassificationService.refreshTasks()
                }
            }
            .keyboardShortcut("r", modifiers: .command)
        }
        
        // 视图菜单
        CommandMenu("视图") {
            Button("显示侧边栏") {
                NSApp.keyWindow?.firstResponder?.tryToPerform(#selector(NSSplitViewController.toggleSidebar(_:)), with: nil)
            }
            .keyboardShortcut("s", modifiers: [.command, .control])
            
            Divider()
            
            Button("任务列表") {
                NotificationCenter.default.post(name: .switchToTab, object: 0)
            }
            .keyboardShortcut("1", modifiers: .command)
            
            Button("正在处理") {
                NotificationCenter.default.post(name: .switchToTab, object: 1)
            }
            .keyboardShortcut("2", modifiers: .command)
            
            Button("已完成") {
                NotificationCenter.default.post(name: .switchToTab, object: 2)
            }
            .keyboardShortcut("3", modifiers: .command)
            
            Button("失败任务") {
                NotificationCenter.default.post(name: .switchToTab, object: 3)
            }
            .keyboardShortcut("4", modifiers: .command)
            
            Divider()
            
            Button("审计日志") {
                NotificationCenter.default.post(name: .switchToTab, object: 4)
            }
            .keyboardShortcut("5", modifiers: .command)
            
            Button("系统状态") {
                NotificationCenter.default.post(name: .switchToTab, object: 5)
            }
            .keyboardShortcut("6", modifiers: .command)
        }
        
        // 任务菜单
        CommandMenu("任务") {
            Button("开始处理选中任务") {
                NotificationCenter.default.post(name: .startSelectedTask, object: nil)
            }
            .keyboardShortcut(.return, modifiers: .command)
            
            Button("取消选中任务") {
                NotificationCenter.default.post(name: .cancelSelectedTask, object: nil)
            }
            .keyboardShortcut(.delete, modifiers: .command)
            
            Button("重试失败任务") {
                NotificationCenter.default.post(name: .retrySelectedTask, object: nil)
            }
            .keyboardShortcut("t", modifiers: .command)
            
            Divider()
            
            Button("删除选中任务") {
                NotificationCenter.default.post(name: .deleteSelectedTask, object: nil)
            }
            .keyboardShortcut(.delete, modifiers: [.command, .shift])
            
            Button("清除已完成任务") {
                clearCompletedTasks()
            }
            
            Button("清除失败任务") {
                clearFailedTasks()
            }
        }
        
        // 工具菜单
        CommandMenu("工具") {
            Button("检查系统连接") {
                Task {
                    _ = await NetworkManager.shared.testConnection()
                }
            }
            
            Button("清理临时文件") {
                cleanupTemporaryFiles()
            }
            
            Button("重置密钥") {
                resetEncryptionKeys()
            }
            
            Divider()
            
            Button("系统诊断") {
                runSystemDiagnostics()
            }
            
            Button("生成诊断报告") {
                generateDiagnosticReport()
            }
        }
        
        // 帮助菜单
        CommandGroup(replacing: .help) {
            Button("脱密客户端帮助") {
                openHelp()
            }
            .keyboardShortcut("?", modifiers: .command)
            
            Button("用户手册") {
                openUserManual()
            }
            
            Button("键盘快捷键") {
                showKeyboardShortcuts()
            }
            
            Divider()
            
            Button("检查更新...") {
                checkForUpdates()
            }
            
            Button("发送反馈") {
                sendFeedback()
            }
            
            Button("关于脱密客户端") {
                showAbout()
            }
        }
        
        // 开发者菜单（仅在调试模式下显示）
        #if DEBUG
        CommandMenu("开发者") {
            Button("生成测试任务") {
                generateTestTasks()
            }
            
            Button("清除所有数据") {
                clearAllData()
            }
            
            Button("显示日志控制台") {
                showLogConsole()
            }
            
            Button("重载配置") {
                Task {
                    await configurationManager.loadConfiguration()
                }
            }
        }
        #endif
    }
    
    // MARK: - Private Methods
    
    private func importTasks() {
        let panel = NSOpenPanel()
        panel.title = "导入任务"
        panel.allowedContentTypes = [.json]
        panel.allowsMultipleSelection = false
        
        if panel.runModal() == .OK, let url = panel.url {
            // 实现任务导入逻辑
            print("导入任务从: \(url.path)")
        }
    }
    
    private func exportTasks() {
        let panel = NSSavePanel()
        panel.title = "导出任务"
        panel.nameFieldStringValue = "tasks_export_\(Date().formatted(.iso8601.day().month().year())).json"
        panel.allowedContentTypes = [.json]
        
        if panel.runModal() == .OK, let url = panel.url {
            Task {
                do {
                    let encoder = JSONEncoder()
                    encoder.dateEncodingStrategy = .iso8601
                    encoder.outputFormatting = .prettyPrinted
                    
                    let data = try encoder.encode(declassificationService.tasks)
                    try data.write(to: url)
                    
                    await auditLogger.logEvent(
                        type: .systemEvent,
                        message: "导出任务列表",
                        details: ["exportPath": url.path, "taskCount": String(declassificationService.tasks.count)]
                    )
                } catch {
                    print("导出任务失败: \(error)")
                }
            }
        }
    }
    
    private func importConfiguration() {
        let panel = NSOpenPanel()
        panel.title = "导入配置"
        panel.allowedContentTypes = [.json]
        panel.allowsMultipleSelection = false
        
        if panel.runModal() == .OK, let url = panel.url {
            Task {
                do {
                    try await configurationManager.importConfiguration(from: url)
                } catch {
                    print("导入配置失败: \(error)")
                }
            }
        }
    }
    
    private func exportConfiguration() {
        let panel = NSSavePanel()
        panel.title = "导出配置"
        panel.nameFieldStringValue = "declassification_config.json"
        panel.allowedContentTypes = [.json]
        
        if panel.runModal() == .OK, let url = panel.url {
            Task {
                do {
                    try await configurationManager.exportConfiguration(to: url)
                } catch {
                    print("导出配置失败: \(error)")
                }
            }
        }
    }
    
    private func exportAuditLogs() {
        let panel = NSSavePanel()
        panel.title = "导出审计日志"
        panel.nameFieldStringValue = "audit_logs_\(Date().formatted(.iso8601.day().month().year())).json"
        panel.allowedContentTypes = [.json]
        
        if panel.runModal() == .OK, let url = panel.url {
            Task {
                await auditLogger.exportLogs(to: url)
            }
        }
    }
    
    private func clearCompletedTasks() {
        Task {
            let completedTasks = declassificationService.tasks.filter { $0.status == .completed }
            for task in completedTasks {
                await declassificationService.deleteTask(taskId: task.id)
            }
        }
    }
    
    private func clearFailedTasks() {
        Task {
            let failedTasks = declassificationService.tasks.filter { $0.status == .failed }
            for task in failedTasks {
                await declassificationService.deleteTask(taskId: task.id)
            }
        }
    }
    
    private func cleanupTemporaryFiles() {
        Task {
            let tempDir = configurationManager.configuration.processing.tempDirectory
            do {
                let contents = try FileManager.default.contentsOfDirectory(atPath: tempDir)
                for item in contents {
                    let itemPath = (tempDir as NSString).appendingPathComponent(item)
                    try FileManager.default.removeItem(atPath: itemPath)
                }
                
                await auditLogger.logEvent(
                    type: .systemEvent,
                    message: "清理临时文件",
                    details: ["tempDirectory": tempDir, "itemsRemoved": String(contents.count)]
                )
            } catch {
                print("清理临时文件失败: \(error)")
            }
        }
    }
    
    private func resetEncryptionKeys() {
        Task {
            do {
                try await SecurityManager.shared.rotateEncryptionKey()
                
                await auditLogger.logEvent(
                    type: .securityEvent,
                    message: "重置加密密钥"
                )
            } catch {
                print("重置密钥失败: \(error)")
            }
        }
    }
    
    private func runSystemDiagnostics() {
        Task {
            await auditLogger.logEvent(
                type: .systemEvent,
                message: "运行系统诊断"
            )
            
            // 这里可以实现系统诊断逻辑
            print("运行系统诊断...")
        }
    }
    
    private func generateDiagnosticReport() {
        let panel = NSSavePanel()
        panel.title = "生成诊断报告"
        panel.nameFieldStringValue = "diagnostic_report_\(Date().formatted(.iso8601.day().month().year())).txt"
        panel.allowedContentTypes = [.plainText]
        
        if panel.runModal() == .OK, let url = panel.url {
            Task {
                let report = generateDiagnosticReportContent()
                try report.write(to: url, atomically: true, encoding: .utf8)
                
                await auditLogger.logEvent(
                    type: .systemEvent,
                    message: "生成诊断报告",
                    details: ["reportPath": url.path]
                )
            }
        }
    }
    
    private func generateDiagnosticReportContent() -> String {
        var report = "=== 脱密客户端诊断报告 ===\n"
        report += "生成时间: \(Date().formatted(.dateTime))\n\n"
        
        report += "=== 系统信息 ===\n"
        report += "操作系统: \(ProcessInfo.processInfo.operatingSystemVersionString)\n"
        report += "应用版本: \(Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "未知")\n"
        report += "内存使用: \(ProcessInfo.processInfo.physicalMemory / 1024 / 1024) MB\n\n"
        
        report += "=== 任务统计 ===\n"
        report += "总任务数: \(declassificationService.totalTasks)\n"
        report += "已完成: \(declassificationService.completedTasks)\n"
        report += "处理中: \(declassificationService.processingTasks)\n"
        report += "失败: \(declassificationService.failedTasks)\n\n"
        
        report += "=== 配置信息 ===\n"
        report += "服务器: \(configurationManager.configuration.server.host):\(configurationManager.configuration.server.port)\n"
        report += "SSL启用: \(configurationManager.configuration.server.useSSL)\n"
        report += "加密启用: \(configurationManager.configuration.security.encryptionEnabled)\n"
        report += "审计启用: \(configurationManager.configuration.security.auditEnabled)\n\n"
        
        return report
    }
    
    private func openHelp() {
        if let url = URL(string: "https://help.cryptosystem.com/declassification-client") {
            NSWorkspace.shared.open(url)
        }
    }
    
    private func openUserManual() {
        if let url = URL(string: "https://docs.cryptosystem.com/user-manual") {
            NSWorkspace.shared.open(url)
        }
    }
    
    private func showKeyboardShortcuts() {
        // 这里可以显示一个包含键盘快捷键的窗口
        print("显示键盘快捷键")
    }
    
    private func checkForUpdates() {
        // 这里可以实现更新检查逻辑
        print("检查更新...")
    }
    
    private func sendFeedback() {
        if let url = URL(string: "mailto:<EMAIL>?subject=脱密客户端反馈") {
            NSWorkspace.shared.open(url)
        }
    }
    
    private func showAbout() {
        NSApp.orderFrontStandardAboutPanel(nil)
    }
    
    #if DEBUG
    private func generateTestTasks() {
        Task {
            // 生成一些测试任务
            let testFiles = [
                URL(fileURLWithPath: "/tmp/test1.pdf"),
                URL(fileURLWithPath: "/tmp/test2.docx")
            ]
            
            await declassificationService.createTask(
                name: "测试任务 \(Date().formatted(.dateTime.hour().minute()))",
                files: testFiles,
                sendMethod: .email,
                recipients: "<EMAIL>",
                notes: "这是一个测试任务"
            )
        }
    }
    
    private func clearAllData() {
        Task {
            // 清除所有任务
            for task in declassificationService.tasks {
                await declassificationService.deleteTask(taskId: task.id)
            }
            
            // 清除配置
            await configurationManager.resetToDefault()
            
            // 清除日志
            await auditLogger.cleanupOldLogs()
            
            await auditLogger.logEvent(
                type: .systemEvent,
                message: "清除所有数据"
            )
        }
    }
    
    private func showLogConsole() {
        // 这里可以实现日志控制台窗口
        print("显示日志控制台")
    }
    #endif
}

// MARK: - Notification Names
extension Notification.Name {
    static let showNewTaskDialog = Notification.Name("showNewTaskDialog")
    static let selectAllTasks = Notification.Name("selectAllTasks")
    static let switchToTab = Notification.Name("switchToTab")
    static let startSelectedTask = Notification.Name("startSelectedTask")
    static let cancelSelectedTask = Notification.Name("cancelSelectedTask")
    static let retrySelectedTask = Notification.Name("retrySelectedTask")
    static let deleteSelectedTask = Notification.Name("deleteSelectedTask")
} 