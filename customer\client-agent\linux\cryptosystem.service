[Unit]
Description=Cryptosystem Client Agent
Documentation=file:///usr/share/doc/cryptosystem/README.md
After=network.target syslog.target
Wants=network.target

[Service]
Type=notify
ExecStart=/usr/local/bin/cryptosystem_client --daemon
ExecReload=/bin/kill -HUP $MAINPID
ExecStop=/bin/kill -TERM $MAINPID
Restart=always
RestartSec=10
User=cryptosystem
Group=cryptosystem

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/log/cryptosystem /var/lib/cryptosystem /etc/cryptosystem

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

# 环境变量
Environment=CRYPTOSYSTEM_CONFIG_DIR=/etc/cryptosystem
Environment=CRYPTOSYSTEM_LOG_DIR=/var/log/cryptosystem
Environment=CRYPTOSYSTEM_DATA_DIR=/var/lib/cryptosystem

[Install]
WantedBy=multi-user.target 