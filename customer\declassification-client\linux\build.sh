#!/bin/bash

# CryptoSystem Declassification Client Build Script for Linux
# 脱密客户端Linux构建脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目信息
PROJECT_NAME="DeclassificationClient"
PROJECT_VERSION="1.0.0"
BUILD_TYPE="Release"
BUILD_DIR="build"
INSTALL_PREFIX="/usr/local"

# 显示帮助信息
show_help() {
    echo -e "${BLUE}CryptoSystem Declassification Client Build Script${NC}"
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "OPTIONS:"
    echo "  -h, --help          Show this help message"
    echo "  -d, --debug         Build in debug mode"
    echo "  -c, --clean         Clean build directory"
    echo "  -i, --install       Install after build"
    echo "  -p, --package       Create installation package"
    echo "  -j, --jobs N        Number of parallel jobs (default: auto)"
    echo "  --prefix PATH       Installation prefix (default: /usr/local)"
    echo "  --build-dir DIR     Build directory (default: build)"
    echo ""
    echo "Examples:"
    echo "  $0                  # Build in release mode"
    echo "  $0 -d               # Build in debug mode"
    echo "  $0 -c -d            # Clean and build in debug mode"
    echo "  $0 -i               # Build and install"
    echo "  $0 -p               # Build and create package"
}

# 打印信息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# 打印警告
print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 打印错误
print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 打印成功
print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 检查依赖
check_dependencies() {
    print_info "Checking dependencies..."
    
    # 检查CMake
    if ! command -v cmake &> /dev/null; then
        print_error "CMake is not installed"
        exit 1
    fi
    
    # 检查编译器
    if ! command -v g++ &> /dev/null && ! command -v clang++ &> /dev/null; then
        print_error "C++ compiler (g++ or clang++) is not installed"
        exit 1
    fi
    
    # 检查Qt5
    if ! pkg-config --exists Qt5Core Qt5Widgets Qt5Network; then
        print_error "Qt5 development packages are not installed"
        print_info "Please install: sudo apt-get install qtbase5-dev qttools5-dev"
        exit 1
    fi
    
    # 检查OpenSSL
    if ! pkg-config --exists openssl; then
        print_error "OpenSSL development packages are not installed"
        print_info "Please install: sudo apt-get install libssl-dev"
        exit 1
    fi
    
    print_success "All dependencies are satisfied"
}

# 清理构建目录
clean_build() {
    print_info "Cleaning build directory..."
    if [ -d "$BUILD_DIR" ]; then
        rm -rf "$BUILD_DIR"
        print_success "Build directory cleaned"
    else
        print_info "Build directory does not exist"
    fi
}

# 配置构建
configure_build() {
    print_info "Configuring build..."
    
    # 创建构建目录
    mkdir -p "$BUILD_DIR"
    cd "$BUILD_DIR"
    
    # 配置CMake
    cmake -DCMAKE_BUILD_TYPE="$BUILD_TYPE" \
          -DCMAKE_INSTALL_PREFIX="$INSTALL_PREFIX" \
          -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \
          ..
    
    print_success "Build configured successfully"
}

# 编译项目
build_project() {
    print_info "Building project..."
    
    # 确定并行作业数
    if [ -z "$JOBS" ]; then
        JOBS=$(nproc)
        print_info "Using $JOBS parallel jobs"
    fi
    
    # 编译
    make -j"$JOBS"
    
    print_success "Build completed successfully"
}

# 安装项目
install_project() {
    print_info "Installing project..."
    
    # 检查是否需要sudo
    if [ "$INSTALL_PREFIX" = "/usr/local" ] || [[ "$INSTALL_PREFIX" == /usr/* ]]; then
        if [ "$EUID" -ne 0 ]; then
            print_warning "Installation requires root privileges"
            sudo make install
        else
            make install
        fi
    else
        make install
    fi
    
    print_success "Installation completed successfully"
}

# 创建安装包
create_package() {
    print_info "Creating installation package..."
    
    # 创建DEB包
    if command -v dpkg-deb &> /dev/null; then
        make package
        print_success "DEB package created"
    fi
    
    # 创建RPM包
    if command -v rpmbuild &> /dev/null; then
        make package
        print_success "RPM package created"
    fi
    
    print_success "Package creation completed"
}

# 显示构建信息
show_build_info() {
    print_info "Build Information:"
    echo "  Project: $PROJECT_NAME"
    echo "  Version: $PROJECT_VERSION"
    echo "  Build Type: $BUILD_TYPE"
    echo "  Build Directory: $BUILD_DIR"
    echo "  Install Prefix: $INSTALL_PREFIX"
    echo "  Parallel Jobs: ${JOBS:-auto}"
    echo ""
}

# 主函数
main() {
    local clean_build_flag=false
    local install_flag=false
    local package_flag=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -d|--debug)
                BUILD_TYPE="Debug"
                shift
                ;;
            -c|--clean)
                clean_build_flag=true
                shift
                ;;
            -i|--install)
                install_flag=true
                shift
                ;;
            -p|--package)
                package_flag=true
                shift
                ;;
            -j|--jobs)
                JOBS="$2"
                shift 2
                ;;
            --prefix)
                INSTALL_PREFIX="$2"
                shift 2
                ;;
            --build-dir)
                BUILD_DIR="$2"
                shift 2
                ;;
            *)
                print_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 显示构建信息
    show_build_info
    
    # 检查依赖
    check_dependencies
    
    # 清理构建目录（如果需要）
    if [ "$clean_build_flag" = true ]; then
        clean_build
    fi
    
    # 配置构建
    configure_build
    
    # 编译项目
    build_project
    
    # 安装项目（如果需要）
    if [ "$install_flag" = true ]; then
        install_project
    fi
    
    # 创建安装包（如果需要）
    if [ "$package_flag" = true ]; then
        create_package
    fi
    
    print_success "All tasks completed successfully!"
    
    # 显示输出文件
    if [ -f "bin/$PROJECT_NAME" ]; then
        print_info "Executable: $(pwd)/bin/$PROJECT_NAME"
    fi
    
    if [ "$package_flag" = true ]; then
        print_info "Packages created in: $(pwd)"
    fi
}

# 运行主函数
main "$@" 