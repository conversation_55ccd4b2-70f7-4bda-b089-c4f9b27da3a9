import SwiftUI

struct TaskListView: View {
    @EnvironmentObject var declassificationService: DeclassificationService
    @State private var selectedTask: DeclassificationTask?
    @State private var searchText = ""
    @State private var statusFilter: TaskStatus?
    @State private var sortOrder: SortOrder = .newest
    
    var filteredTasks: [DeclassificationTask] {
        var tasks = declassificationService.tasks
        
        // 按状态过滤
        if let status = statusFilter {
            tasks = tasks.filter { $0.status == status }
        }
        
        // 按搜索文本过滤
        if !searchText.isEmpty {
            tasks = tasks.filter { task in
                task.name.localizedCaseInsensitiveContains(searchText) ||
                task.createdBy.localizedCaseInsensitiveContains(searchText) ||
                task.notes?.localizedCaseInsensitiveContains(searchText) == true
            }
        }
        
        // 排序
        switch sortOrder {
        case .newest:
            tasks.sort { $0.createdAt > $1.createdAt }
        case .oldest:
            tasks.sort { $0.createdAt < $1.createdAt }
        case .name:
            tasks.sort { $0.name < $1.name }
        case .status:
            tasks.sort { $0.status.rawValue < $1.status.rawValue }
        }
        
        return tasks
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 搜索和过滤工具栏
                ToolbarView(
                    searchText: $searchText,
                    statusFilter: $statusFilter,
                    sortOrder: $sortOrder
                )
                
                // 任务列表
                List(filteredTasks, selection: $selectedTask) { task in
                    TaskRowView(task: task)
                        .tag(task)
                        .contextMenu {
                            TaskContextMenu(task: task)
                        }
                }
                .listStyle(.inset)
                .refreshable {
                    await declassificationService.refreshTasks()
                }
            }
            
            // 详情面板
            if let selectedTask = selectedTask {
                TaskDetailView(task: selectedTask)
            } else {
                EmptyDetailView()
            }
        }
        .onAppear {
            if selectedTask == nil && !declassificationService.tasks.isEmpty {
                selectedTask = declassificationService.tasks.first
            }
        }
        .onChange(of: declassificationService.tasks) { _ in
            // 如果选中的任务被删除，清除选择
            if let selected = selectedTask,
               !declassificationService.tasks.contains(selected) {
                selectedTask = nil
            }
        }
    }
}

struct ToolbarView: View {
    @Binding var searchText: String
    @Binding var statusFilter: TaskStatus?
    @Binding var sortOrder: SortOrder
    
    var body: some View {
        HStack(spacing: 12) {
            // 搜索框
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)
                TextField("搜索任务...", text: $searchText)
                    .textFieldStyle(.plain)
                
                if !searchText.isEmpty {
                    Button {
                        searchText = ""
                    } label: {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.secondary)
                    }
                    .buttonStyle(.plain)
                }
            }
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(Color(NSColor.controlBackgroundColor))
            .cornerRadius(6)
            .frame(maxWidth: 200)
            
            // 状态过滤器
            Picker("状态", selection: $statusFilter) {
                Text("全部状态").tag(TaskStatus?.none)
                ForEach(TaskStatus.allCases, id: \.self) { status in
                    Text(status.displayName).tag(status as TaskStatus?)
                }
            }
            .pickerStyle(.menu)
            .frame(width: 120)
            
            // 排序选择器
            Picker("排序", selection: $sortOrder) {
                ForEach(SortOrder.allCases, id: \.self) { order in
                    Text(order.displayName).tag(order)
                }
            }
            .pickerStyle(.menu)
            .frame(width: 100)
            
            Spacer()
            
            // 状态统计
            TaskStatsView()
        }
        .padding(.horizontal)
        .padding(.vertical, 8)
        .background(Color(NSColor.windowBackgroundColor))
    }
}

struct TaskRowView: View {
    let task: DeclassificationTask
    
    var body: some View {
        HStack(spacing: 12) {
            // 状态指示器
            VStack {
                Image(systemName: task.status.iconName)
                    .font(.title2)
                    .foregroundColor(task.status.color)
                
                if task.status == .processing {
                    ProgressView()
                        .scaleEffect(0.7)
                }
            }
            .frame(width: 30)
            
            VStack(alignment: .leading, spacing: 4) {
                // 任务名称和创建时间
                HStack {
                    Text(task.name)
                        .font(.headline)
                        .lineLimit(1)
                    
                    Spacer()
                    
                    Text(task.createdAt.formatted(.relative(presentation: .abbreviated)))
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                // 创建者和文件数量
                HStack {
                    Text("by \(task.createdBy)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Label("\(task.totalFiles)", systemImage: "doc.fill")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                // 进度条（如果正在处理）
                if task.status == .processing {
                    VStack(alignment: .leading, spacing: 2) {
                        HStack {
                            Text("进度")
                                .font(.caption2)
                            Spacer()
                            Text("\(Int(task.overallProgress * 100))%")
                                .font(.caption2)
                        }
                        .foregroundColor(.secondary)
                        
                        ProgressView(value: task.overallProgress)
                            .progressViewStyle(.linear)
                    }
                }
                
                // 发送方式
                HStack {
                    Image(systemName: task.sendConfiguration.method.iconName)
                        .font(.caption)
                    Text(task.sendConfiguration.method.displayName)
                        .font(.caption)
                    
                    if !task.sendConfiguration.recipients.isEmpty {
                        Text("→ \(task.sendConfiguration.recipients.joined(separator: ", "))")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .lineLimit(1)
                    }
                }
                .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // 文件统计
            VStack(alignment: .trailing, spacing: 2) {
                if task.completedFiles > 0 {
                    Text("\(task.completedFiles)")
                        .font(.caption)
                        .foregroundColor(.green)
                    + Text(" 完成")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
                
                if task.failedFiles > 0 {
                    Text("\(task.failedFiles)")
                        .font(.caption)
                        .foregroundColor(.red)
                    + Text(" 失败")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
                
                if task.processingFiles > 0 {
                    Text("\(task.processingFiles)")
                        .font(.caption)
                        .foregroundColor(.orange)
                    + Text(" 处理中")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(.vertical, 8)
        .contentShape(Rectangle())
    }
}

struct TaskContextMenu: View {
    let task: DeclassificationTask
    @EnvironmentObject var declassificationService: DeclassificationService
    
    var body: some View {
        Group {
            if task.status == .pending {
                Button("开始处理") {
                    Task {
                        await declassificationService.startProcessing(taskId: task.id)
                    }
                }
            }
            
            if task.status == .processing {
                Button("取消任务") {
                    Task {
                        await declassificationService.cancelTask(taskId: task.id)
                    }
                }
            }
            
            if task.status == .failed {
                Button("重试任务") {
                    Task {
                        await declassificationService.retryTask(taskId: task.id)
                    }
                }
            }
            
            Divider()
            
            Button("复制任务ID") {
                NSPasteboard.general.clearContents()
                NSPasteboard.general.setString(task.id.uuidString, forType: .string)
            }
            
            Button("导出任务信息") {
                exportTaskInfo(task)
            }
            
            Divider()
            
            Button("删除任务") {
                Task {
                    await declassificationService.deleteTask(taskId: task.id)
                }
            }
            .foregroundColor(.red)
        }
    }
    
    private func exportTaskInfo(_ task: DeclassificationTask) {
        let savePanel = NSSavePanel()
        savePanel.title = "导出任务信息"
        savePanel.nameFieldStringValue = "task_\(task.name)_info.json"
        savePanel.allowedContentTypes = [.json]
        
        if savePanel.runModal() == .OK, let url = savePanel.url {
            do {
                let encoder = JSONEncoder()
                encoder.dateEncodingStrategy = .iso8601
                encoder.outputFormatting = .prettyPrinted
                
                let data = try encoder.encode(task)
                try data.write(to: url)
            } catch {
                print("导出任务信息失败: \(error)")
            }
        }
    }
}

struct TaskStatsView: View {
    @EnvironmentObject var declassificationService: DeclassificationService
    
    var body: some View {
        HStack(spacing: 16) {
            StatItem(
                title: "总计",
                value: "\(declassificationService.totalTasks)",
                color: .blue
            )
            
            StatItem(
                title: "已完成",
                value: "\(declassificationService.completedTasks)",
                color: .green
            )
            
            StatItem(
                title: "处理中",
                value: "\(declassificationService.processingTasks)",
                color: .orange
            )
            
            StatItem(
                title: "失败",
                value: "\(declassificationService.failedTasks)",
                color: .red
            )
        }
    }
}

struct StatItem: View {
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 2) {
            Text(value)
                .font(.headline)
                .foregroundColor(color)
            
            Text(title)
                .font(.caption2)
                .foregroundColor(.secondary)
        }
    }
}

struct TaskDetailView: View {
    let task: DeclassificationTask
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 16) {
                // 任务基本信息
                TaskInfoSection(task: task)
                
                // 文件列表
                FileListSection(task: task)
                
                // 发送配置
                SendConfigSection(task: task)
                
                // 操作按钮
                TaskActionsSection(task: task)
            }
            .padding()
        }
        .frame(minWidth: 400)
        .navigationTitle(task.name)
        .navigationSubtitle(task.status.displayName)
    }
}

struct TaskInfoSection: View {
    let task: DeclassificationTask
    
    var body: some View {
        GroupBox("任务信息") {
            VStack(alignment: .leading, spacing: 8) {
                InfoRow(label: "创建时间", value: task.createdAt.formatted(.dateTime))
                InfoRow(label: "创建者", value: task.createdBy)
                InfoRow(label: "状态", value: task.status.displayName)
                
                if let completedAt = task.completedAt {
                    InfoRow(label: "完成时间", value: completedAt.formatted(.dateTime))
                }
                
                if let estimatedCompletion = task.estimatedCompletionTime {
                    InfoRow(label: "预计完成", value: estimatedCompletion.formatted(.relative(presentation: .named)))
                }
                
                if let notes = task.notes, !notes.isEmpty {
                    InfoRow(label: "备注", value: notes)
                }
                
                if let errorMessage = task.errorMessage {
                    InfoRow(label: "错误信息", value: errorMessage, valueColor: .red)
                }
            }
            .padding()
        }
    }
}

struct FileListSection: View {
    let task: DeclassificationTask
    
    var body: some View {
        GroupBox("文件列表 (\(task.files.count))") {
            LazyVStack(alignment: .leading, spacing: 8) {
                ForEach(task.files, id: \.id) { file in
                    FileRowView(file: file)
                }
            }
            .padding()
        }
    }
}

struct FileRowView: View {
    let file: FileInfo
    
    var body: some View {
        HStack {
            Image(systemName: "doc.fill")
                .foregroundColor(.blue)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(file.name)
                    .font(.headline)
                    .lineLimit(1)
                
                HStack {
                    Text(file.formattedSize)
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    HStack(spacing: 4) {
                        Circle()
                            .fill(file.status.color)
                            .frame(width: 8, height: 8)
                        Text(file.status.displayName)
                            .font(.caption)
                            .foregroundColor(file.status.color)
                    }
                }
                
                if file.progress > 0 && file.progress < 1 {
                    ProgressView(value: file.progress)
                        .progressViewStyle(.linear)
                }
                
                if let error = file.errorMessage {
                    Text(error)
                        .font(.caption)
                        .foregroundColor(.red)
                        .lineLimit(2)
                }
            }
            
            Spacer()
        }
        .padding(.vertical, 4)
    }
}

struct SendConfigSection: View {
    let task: DeclassificationTask
    
    var body: some View {
        GroupBox("发送配置") {
            VStack(alignment: .leading, spacing: 8) {
                InfoRow(label: "发送方式", value: task.sendConfiguration.method.displayName)
                
                if !task.sendConfiguration.recipients.isEmpty {
                    InfoRow(label: "收件人", value: task.sendConfiguration.recipients.joined(separator: ", "))
                }
                
                if let subject = task.sendConfiguration.subject {
                    InfoRow(label: "主题", value: subject)
                }
                
                if let message = task.sendConfiguration.message {
                    InfoRow(label: "消息", value: message)
                }
            }
            .padding()
        }
    }
}

struct TaskActionsSection: View {
    let task: DeclassificationTask
    @EnvironmentObject var declassificationService: DeclassificationService
    
    var body: some View {
        GroupBox("操作") {
            HStack {
                if task.status == .pending {
                    Button("开始处理") {
                        Task {
                            await declassificationService.startProcessing(taskId: task.id)
                        }
                    }
                    .buttonStyle(.borderedProminent)
                }
                
                if task.status == .processing {
                    Button("取消任务") {
                        Task {
                            await declassificationService.cancelTask(taskId: task.id)
                        }
                    }
                    .buttonStyle(.bordered)
                }
                
                if task.status == .failed {
                    Button("重试任务") {
                        Task {
                            await declassificationService.retryTask(taskId: task.id)
                        }
                    }
                    .buttonStyle(.borderedProminent)
                }
                
                Button("删除任务") {
                    Task {
                        await declassificationService.deleteTask(taskId: task.id)
                    }
                }
                .buttonStyle(.bordered)
                .foregroundColor(.red)
                
                Spacer()
            }
            .padding()
        }
    }
}

struct InfoRow: View {
    let label: String
    let value: String
    var valueColor: Color = .primary
    
    var body: some View {
        HStack {
            Text(label)
                .font(.caption)
                .foregroundColor(.secondary)
                .frame(width: 80, alignment: .leading)
            
            Text(value)
                .font(.body)
                .foregroundColor(valueColor)
                .textSelection(.enabled)
            
            Spacer()
        }
    }
}

struct EmptyDetailView: View {
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "tray")
                .font(.system(size: 48))
                .foregroundColor(.secondary)
            
            Text("选择一个任务以查看详细信息")
                .font(.title2)
                .foregroundColor(.secondary)
            
            Text("从左侧列表中选择任务，或创建一个新任务开始工作")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

// MARK: - Supporting Types

enum SortOrder: String, CaseIterable {
    case newest = "newest"
    case oldest = "oldest"
    case name = "name"
    case status = "status"
    
    var displayName: String {
        switch self {
        case .newest: return "最新"
        case .oldest: return "最旧"
        case .name: return "名称"
        case .status: return "状态"
        }
    }
}

#Preview {
    TaskListView()
        .environmentObject(DeclassificationService())
} 