#include "key_lifecycle_client.h"
#include <nlohmann/json.hpp>
#include <sstream>
#include <iomanip>
#include <curl/curl.h>
#include <stdexcept>
#include <iostream>

namespace crypto {
namespace api {

// 内部实现类
class KeyLifecycleClientImpl : public KeyLifecycleClient {
public:
    // 构造函数
    explicit KeyLifecycleClientImpl(const std::string& baseUrl, const std::string& apiKey, bool useTls)
        : baseUrl_(baseUrl), apiKey_(apiKey), useTls_(useTls) {
        // 确保URL以/结尾
        if (!baseUrl_.empty() && baseUrl_.back() != '/') {
            baseUrl_ += '/';
        }
    }
    
    // 析构函数
    ~KeyLifecycleClientImpl() override = default;
    
    // 实现接口方法
    KeyRotationResult RotateKey(const KeyRotationRequest& request) override {
        try {
            // 构建请求JSON
            nlohmann::json requestJson = {
                {"keyId", request.keyId},
                {"reEncryptChildren", request.reEncryptChildren}
            };
            
            if (!request.description.empty()) {
                requestJson["description"] = request.description;
            }
            
            // 发送POST请求
            std::string endpoint = "api/v1/keys/lifecycle/rotate";
            std::string response = SendRequest(endpoint, "POST", requestJson.dump());
            
            // 解析响应
            nlohmann::json responseJson = nlohmann::json::parse(response);
            
            KeyRotationResult result;
            result.oldKeyId = responseJson["oldKeyId"];
            result.newKeyId = responseJson["newKeyId"];
            result.keyType = responseJson["keyType"];
            result.childKeysReEncrypted = responseJson["childKeysReEncrypted"];
            result.completed = responseJson["completed"];
            
            return result;
        } catch (const std::exception& e) {
            std::cerr << "轮换密钥失败: " << e.what() << std::endl;
            return KeyRotationResult{};
        }
    }
    
    std::vector<key::Key> GetKeyRotationHistory(const std::string& keyId) override {
        try {
            // 发送GET请求
            std::string endpoint = "api/v1/keys/lifecycle/" + keyId + "/history";
            std::string response = SendRequest(endpoint, "GET");
            
            // 解析响应
            nlohmann::json responseJson = nlohmann::json::parse(response);
            
            std::vector<key::Key> keys;
            for (const auto& keyJson : responseJson) {
                key::Key key;
                
                // 提取基本信息
                key.keyId_ = keyJson["keyId"];
                key.keyType_ = keyJson["keyType"];
                key.status_ = ParseKeyStatus(keyJson["status"]);
                
                if (keyJson.contains("parentKeyId") && !keyJson["parentKeyId"].is_null()) {
                    key.parentKeyId_ = keyJson["parentKeyId"];
                }
                
                key.creationTimestamp_ = keyJson["creationTimestamp"];
                key.lastUpdateTimestamp_ = keyJson["lastUpdateTimestamp"];
                
                // 提取元数据
                if (keyJson.contains("metadata") && keyJson["metadata"].is_object()) {
                    for (auto& [key, value] : keyJson["metadata"].items()) {
                        key.metadata_[key] = value;
                    }
                }
                
                keys.push_back(key);
            }
            
            return keys;
        } catch (const std::exception& e) {
            std::cerr << "获取密钥轮换历史失败: " << e.what() << std::endl;
            return {};
        }
    }
    
    bool SetKeyExpiryDate(const std::string& keyId, 
                         const std::chrono::system_clock::time_point& expiryTime) override {
        try {
            // 格式化过期时间
            std::string formattedTime = FormatTimeISO8601(expiryTime);
            
            // 发送PUT请求
            std::string endpoint = "api/v1/keys/lifecycle/" + keyId + "/expiry?expiryDate=" + formattedTime;
            std::string response = SendRequest(endpoint, "PUT");
            
            // 如果没有异常，则认为设置成功
            return !response.empty();
        } catch (const std::exception& e) {
            std::cerr << "设置密钥过期时间失败: " << e.what() << std::endl;
            return false;
        }
    }
    
    int ProcessExpiredKeys() override {
        try {
            // 发送POST请求
            std::string endpoint = "api/v1/keys/lifecycle/process-expired";
            std::string response = SendRequest(endpoint, "POST");
            
            // 解析响应
            return std::stoi(response);
        } catch (const std::exception& e) {
            std::cerr << "处理过期密钥失败: " << e.what() << std::endl;
            return 0;
        }
    }
    
private:
    std::string baseUrl_;
    std::string apiKey_;
    bool useTls_;
    
    // 辅助方法 - 发送HTTP请求
    std::string SendRequest(const std::string& endpoint, const std::string& method, 
                            const std::string& body = "") {
        CURL* curl = curl_easy_init();
        std::string response;
        
        if (curl) {
            std::string url = baseUrl_ + endpoint;
            
            curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
            curl_easy_setopt(curl, CURLOPT_CUSTOMREQUEST, method.c_str());
            
            // 设置API密钥
            struct curl_slist *headers = NULL;
            headers = curl_slist_append(headers, ("X-API-Key: " + apiKey_).c_str());
            headers = curl_slist_append(headers, "Content-Type: application/json");
            curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
            
            // 设置请求体
            if (!body.empty()) {
                curl_easy_setopt(curl, CURLOPT_POSTFIELDS, body.c_str());
            }
            
            // 写回回调
            curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, [](char* ptr, size_t size, size_t nmemb, std::string* data) {
                if (data == NULL) return 0;
                data->append(ptr, size * nmemb);
                return size * nmemb;
            });
            curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response);
            
            // 执行请求
            CURLcode res = curl_easy_perform(curl);
            
            // 检查结果
            if (res != CURLE_OK) {
                curl_slist_free_all(headers);
                curl_easy_cleanup(curl);
                throw std::runtime_error(curl_easy_strerror(res));
            }
            
            // 检查HTTP状态码
            long http_code = 0;
            curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &http_code);
            if (http_code < 200 || http_code >= 300) {
                curl_slist_free_all(headers);
                curl_easy_cleanup(curl);
                throw std::runtime_error("HTTP错误: " + std::to_string(http_code) + " - " + response);
            }
            
            curl_slist_free_all(headers);
            curl_easy_cleanup(curl);
        } else {
            throw std::runtime_error("无法初始化CURL");
        }
        
        return response;
    }
    
    // 辅助方法 - 将字符串状态转换为枚举
    key::KeyStatus ParseKeyStatus(const std::string& status) {
        if (status == "ACTIVE") return key::KeyStatus::ACTIVE;
        if (status == "INACTIVE") return key::KeyStatus::INACTIVE;
        if (status == "REVOKED") return key::KeyStatus::REVOKED;
        return key::KeyStatus::INACTIVE; // 默认为非活动
    }
    
    // 辅助方法 - 格式化时间为ISO8601
    std::string FormatTimeISO8601(const std::chrono::system_clock::time_point& time) {
        // 转换为时间结构
        auto timeT = std::chrono::system_clock::to_time_t(time);
        std::tm tm = *std::gmtime(&timeT);
        
        // 获取毫秒
        auto duration = time.time_since_epoch();
        auto millis = std::chrono::duration_cast<std::chrono::milliseconds>(duration).count() % 1000;
        
        // 格式化
        std::stringstream ss;
        ss << std::put_time(&tm, "%Y-%m-%dT%H:%M:%S");
        ss << '.' << std::setfill('0') << std::setw(3) << millis;
        ss << 'Z';
        
        return ss.str();
    }
};

// 创建密钥生命周期管理客户端的工厂函数
std::unique_ptr<KeyLifecycleClient> CreateKeyLifecycleClient(
    const std::string& baseUrl, const std::string& apiKey, bool useTls) {
    return std::make_unique<KeyLifecycleClientImpl>(baseUrl, apiKey, useTls);
}

} // namespace api
} // namespace crypto