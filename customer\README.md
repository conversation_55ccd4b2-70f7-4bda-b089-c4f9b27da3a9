# 客户端组件 (Customer Components)

![组件状态](https://img.shields.io/badge/状态-部分完成-yellow)
![平台支持](https://img.shields.io/badge/平台-Windows%20%7C%20Linux%20%7C%20macOS%20%7C%20HarmonyOS-blue)
![完成度](https://img.shields.io/badge/完成度-61%25-yellow)

## 📋 概述

客户端组件是部署在客户单位的核心应用程序集合，为企业用户提供完整的文档加密、管理和安全外发解决方案。这些组件协同工作，形成一个完整的企业级文档安全防护体系。

### 🎯 核心价值
- **透明加密**: 用户无感知的文件系统级加密保护
- **集中管理**: 统一的策略配置和权限管理
- **安全外发**: 可控的文件脱密和对外发送机制
- **全面审计**: 完整的操作记录和安全事件追踪

## 🏗️ 组件架构

### 组件构成及开发状态

| 组件名称 | 核心职责 | Windows | Linux | macOS | HarmonyOS | 完成度 | 状态 |
|---------|---------|---------|-------|-------|-----------|--------|------|
| **系统管理器** | 管理控制中心 | 🟡 60% | ❌ | ❌ | 🟡 40% | 50% | 🟡 开发中 |
| **客户端代理** | 终端强制加密 | ✅ 85% | ✅ 80% | 🟡 75% | 🟡 30% | 68% | 🟡 部分完成 |
| **脱密客户端** | 对外发送专用 | 🟡 70% | 🟡 65% | ✅ 95% | 🟡 35% | 66% | 🟡 部分完成 |

### 🏆 最新完成项目
- ✅ **Windows客户端代理核心功能** - MiniFilter驱动和加密核心功能基本完成
- ✅ **Linux客户端代理基础实现** - FUSE文件系统和加密功能基本完成
- ✅ **macOS脱密客户端** - 完整的Swift + SwiftUI架构实现 🆕
- 🟡 **HarmonyOS平台架构设计** - 完成了项目结构和接口定义，核心功能开发中

### 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    客户端基础设施                              │
├─────────────────────────────────────────────────────────────┤
│  🎛️ 系统管理器 (System Manager)                             │
│  ├── 用户设备管理                                            │
│  ├── 策略配置分发                                            │
│  ├── 密钥管理                                               │
│  └── 审批流程                                               │
├─────────────────────────────────────────────────────────────┤
│  🛡️ 客户端代理 (Client Agent)                               │
│  ├── 透明加解密                                             │
│  ├── 实时监控                                               │
│  ├── 策略执行                                               │
│  └── 终端防护                                               │
├─────────────────────────────────────────────────────────────┤
│  📤 脱密客户端 (Declassification Client)                    │
│  ├── 自动脱密                                               │
│  ├── 发送控制                                               │
│  ├── 权限验证                                               │
│  └── 操作审计                                               │
└─────────────────────────────────────────────────────────────┘
                              ↕️
                    ═══════════════════════
                       安全通信协议
                    ═══════════════════════
                              ↕️
┌─────────────────────────────────────────────────────────────┐
│                    运营商基础设施                              │
│  🔑 密钥生成器 + 🗄️ 数据库服务器                           │
└─────────────────────────────────────────────────────────────┘
```

## 🎛️ 系统管理器 (System Manager)

### 核心职责
安装在客户单位管理员终端的桌面应用程序，作为整个加密系统的控制中心。

### 功能特性
- **👥 用户设备管理**: 用户账户、设备注册、组织架构管理
- **📋 策略配置**: 加密策略、权限策略配置和分发
- **🔑 密钥管理**: 接收主密钥、派生用户密钥、密钥分发和轮换
- **✅ 审批流程**: 脱密申请、外发申请的审批处理
- **📊 审计监控**: 查看审计日志、系统状态监控
- **🎮 远程控制**: 对客户端和脱密客户端的远程管理

### 平台支持
- **Windows**: C# + WPF，基础功能实现 (60%)
- **HarmonyOS**: ArkTS + ArkUI，架构设计完成，核心功能开发中 (40%)

### 技术栈
```yaml
Windows:
  - 语言: C# (.NET 6/8)
  - 框架: WPF + MVVM
  - 数据库: Entity Framework Core
  - 通信: SignalR + gRPC

HarmonyOS:
  - 语言: ArkTS (TypeScript)
  - 框架: ArkUI
  - 构建: Hvigor
  - 通信: HTTP + WebSocket
```

## 🛡️ 客户端代理 (Client Agent)

### 核心职责
安装在所有需要保护的终端计算机上的核心安全组件，具有最高安全等级的自我保护能力。

### 功能特性
- **🔒 透明加解密**: 在文件系统层面实现透明的文件加解密
- **👁️ 实时监控**: 监控文件操作、网络传输、移动设备等
- **🛡️ 强制防护**: 不可卸载、防调试、防逆向的自我保护
- **📋 策略执行**: 实时执行系统管理器下发的安全策略
- **🖥️ 终端防护**: 屏幕水印、防截屏、剪贴板控制、外设管控
- **📱 离线支持**: 支持离线工作模式和策略缓存

### 平台支持
- **Windows**: C++ + MiniFilter驱动，核心功能基本完成 (85%)
- **Linux**: C + FUSE文件系统，核心功能基本完成 (80%)
- **macOS**: Swift + System Extensions，基础实现 (75%)
- **HarmonyOS**: ArkTS + ArkUI + C++ NDK，架构设计完成 (30%)

### 技术栈
```yaml
Windows:
  - 核心: C++ + Windows Driver Kit (WDK)
  - 驱动: MiniFilter File System Driver
  - 加密: CryptoAPI + OpenSSL
  - 界面: Qt/C++

Linux:
  - 核心: C + FUSE
  - 监控: inotify + eBPF
  - 加密: OpenSSL + libgcrypt
  - 界面: Qt/GTK

macOS:
  - 核心: Swift + System Extensions
  - 监控: FSEvents + EndpointSecurity
  - 加密: CryptoKit + Security Framework
  - 界面: SwiftUI + AppKit

HarmonyOS:
  - 核心: ArkTS + C++ NDK
  - 监控: 文件监控API
  - 加密: cryptoFramework API
  - 界面: ArkUI
```

## 📤 脱密客户端 (Declassification Client)

### 核心职责
安装在客户单位指定对外发送终端的专用程序，是对外发送文件的唯一合规途径。

### 功能特性
- **🔓 自动脱密**: 自动识别和处理加密文件的脱密操作
- **📡 发送控制**: 管理文件的对外发送渠道和安全控制
- **🔐 权限验证**: 验证用户和文件的脱密发送权限
- **🛡️ 安全防护**: 防截屏、防复制、水印添加等安全措施
- **📊 操作审计**: 详细记录所有脱密和发送操作
- **📋 策略执行**: 严格按照系统管理器配置的策略执行

### 平台支持
- **Windows**: C# + WPF，基础功能实现 (70%)
- **Linux**: C++ + Qt，基础架构存在 (65%)
- **macOS**: ✨ **Swift + SwiftUI，完整实现** (95%) 🆕
- **HarmonyOS**: ArkTS + ArkUI，架构设计完成 (35%)

### 🏆 macOS版本亮点
最新完成的macOS版本采用现代化架构：
- **Swift + SwiftUI**: 原生macOS用户体验
- **模块化设计**: 清晰的服务层和数据模型
- **安全集成**: 深度集成macOS安全框架
- **专业品质**: 完整的构建、配置和文档体系

### 技术栈
```yaml
Windows:
  - 语言: C# (.NET 6/8)
  - 框架: WPF + MVVM
  - 加密: System.Security.Cryptography
  - 文件处理: .NET File APIs

Linux:
  - 语言: C++
  - 框架: Qt 5.15+
  - 加密: OpenSSL
  - 文件处理: POSIX APIs

macOS: ✨ 新完成
  - 语言: Swift 5.7+
  - 框架: SwiftUI + Combine
  - 加密: CryptoKit + Security Framework
  - 文件处理: Foundation APIs
  - 构建: Xcode 14.3+

HarmonyOS:
  - 语言: ArkTS (TypeScript)
  - 框架: ArkUI
  - 加密: cryptoFramework API
  - 文件处理: fileio API
```

## 🚀 快速开始

### 环境要求

#### 开发环境
- **Windows**: Visual Studio 2019+, Windows 10 SDK, CMake 3.16+
- **Linux**: GCC 7+, CMake 3.16+, Qt 5.15+
- **macOS**: Xcode 14.3+, macOS 13.0+, Swift 5.7+
- **HarmonyOS**: DevEco Studio 4.0+, Node.js 16+

#### 运行环境
- **操作系统**: Windows 10/11, macOS 13+, Ubuntu 18.04+, HarmonyOS 4.0+
- **内存**: 最低4GB RAM，推荐8GB+
- **存储**: 最低2GB可用空间
- **权限**: 管理员权限 (驱动安装需要)

### 构建说明

#### Windows平台
```bash
# 系统管理器
cd customer/system-manager/windows
dotnet build SystemManager.csproj --configuration Release

# 客户端代理
cd customer/client-agent/windows
mkdir build && cd build
cmake .. -DCMAKE_BUILD_TYPE=Release
cmake --build . --config Release

# 脱密客户端
cd customer/declassification-client/windows
dotnet build DeclassificationClient.csproj --configuration Release
```

#### Linux平台
```bash
# 客户端代理
cd customer/client-agent/linux
chmod +x build.sh
./build.sh

# 脱密客户端
cd customer/declassification-client/linux
chmod +x build.sh
./build.sh
```

#### macOS平台
```bash
# 客户端代理
cd customer/client-agent/macos
chmod +x build.sh
./build.sh

# 脱密客户端 ✨ 新完成
cd customer/declassification-client/macos
chmod +x build.sh
./build.sh

# 或使用Xcode
open DeclassificationClient.xcodeproj
```

#### HarmonyOS平台
```bash
# 所有组件统一构建方式
cd customer/*/harmonyos
npm install
hvigor assembleHap
```

## 📂 目录结构

```
customer/
├── 🎛️ system-manager/        # 系统管理器
│   ├── windows/               # Windows版本 (C# + WPF) - 60%
│   └── harmonyos/             # HarmonyOS版本 (ArkTS) - 40%
├── 🛡️ client-agent/          # 客户端代理
│   ├── windows/               # Windows版本 (C++ + MiniFilter) - 85%
│   ├── linux/                 # Linux版本 (C + FUSE) - 80%
│   ├── macos/                 # macOS版本 (Swift + SystemExt) - 75%
│   ├── harmonyos/             # HarmonyOS版本 (ArkTS + NDK) - 30%
│   ├── common/                # 跨平台通用组件
│   └── samples/               # 示例代码
├── 📤 declassification-client/ # 脱密客户端
│   ├── windows/               # Windows版本 (C# + WPF) - 70%
│   ├── linux/                 # Linux版本 (C++ + Qt) - 65%
│   ├── macos/                 # macOS版本 (Swift + SwiftUI) - 95% ✨
│   └── harmonyos/             # HarmonyOS版本 (ArkTS) - 35%
└── 📋 README.md               # 本文档
```

## 🔧 关于Node.js依赖的说明

### 为什么存在package.json文件？

在HarmonyOS版本中，您会发现`package.json`文件，这是因为：

1. **HarmonyOS开发要求**: HarmonyOS使用npm管理开发依赖
2. **构建工具**: `@ohos/hvigor`构建工具和`@ohos/hypium`测试框架
3. **开发环境**: Node.js是HarmonyOS开发的基础环境

### 清理建议

如果您不需要HarmonyOS支持：
```bash
# 可以安全删除以下目录
rm -rf customer/*/harmonyos/
```

如果保留HarmonyOS支持：
```bash
# 这些文件是必需的，不建议删除
customer/*/harmonyos/package.json
customer/*/harmonyos/node_modules/
```

## 📈 开发进展

### 最新里程碑 (2024年)
- ✅ **macOS脱密客户端完成**: 完整的Swift + SwiftUI实现
- ✅ **Windows客户端代理核心功能**: MiniFilter驱动和加密核心基本完成
- ✅ **Linux客户端代理基础实现**: FUSE文件系统和加密功能基本完成
- 🟡 **HarmonyOS架构设计**: 所有组件的ArkTS架构设计完成，核心功能开发中

### 下一阶段目标
1. **HarmonyOS平台核心功能开发** - 完成客户端代理和系统管理器的核心实现
2. **Windows系统管理器功能完善** - 补充高级管理功能和UI优化
3. **Linux/Windows脱密客户端功能完善** - 完成剩余功能模块
4. **跨平台集成测试** - 确保各组件间的协调工作

### 完成度统计
```
总体完成度: 61%
├── 系统管理器: 50% (Windows 60%, HarmonyOS 40%)
├── 客户端代理: 68% (平均各平台)
└── 脱密客户端: 66% (Windows 70%, Linux 65%, macOS 95%, HarmonyOS 35%)
```

## 📚 相关文档

### 组件详细文档
- 🎛️ [系统管理器文档](system-manager/README.md)
- 🛡️ [客户端代理文档](client-agent/README.md)
- 📤 [脱密客户端文档](declassification-client/README.md)

### 平台特定文档
- 🪟 [Windows开发指南](client-agent/windows/README.md)
- 🐧 [Linux开发指南](client-agent/linux/README.md)
- 🍎 [macOS开发指南](client-agent/macos/README.md)
- 📱 [HarmonyOS开发指南](client-agent/harmonyos/README.md)

### 技术文档
- 🔒 [安全架构设计](../docs/SECURITY_ARCHITECTURE.md)
- 🚀 [部署最佳实践](../docs/DEPLOYMENT_GUIDE.md)
- 🔧 [开发环境配置](../docs/DEVELOPMENT_SETUP.md)

## 🎯 质量保证

### 代码质量
- **架构设计**: 模块化、可扩展的现代化架构
- **代码规范**: 严格的编码标准和代码审查
- **测试覆盖**: 单元测试、集成测试、端到端测试
- **文档完整**: 详细的API文档和用户指南

### 安全标准
- **加密算法**: 国密SM4/SM3 + AES-256/SHA-256
- **通信安全**: TLS 1.3双向认证
- **存储安全**: 敏感数据加密存储
- **访问控制**: 基于角色的权限管理

### 性能指标
- **资源占用**: 客户端后台 <1% CPU, <100MB 内存
- **响应时间**: 文件操作延迟 <100ms
- **并发能力**: 支持1000+并发用户
- **稳定性**: 7×24小时稳定运行

---

**Copyright © 2024 Document Encryption System. All rights reserved.** 