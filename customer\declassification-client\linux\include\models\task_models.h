#pragma once

#include <string>
#include <vector>
#include <map>
#include <ctime>
#include <memory>

namespace DeclassificationClient {
namespace Models {

/**
 * 脱密任务状态枚举
 */
enum class TaskStatus {
    Pending,        // 待处理
    Processing,     // 处理中
    Completed,      // 已完成
    Failed,         // 失败
    Cancelled       // 已取消
};

/**
 * 文件处理状态枚举
 */
enum class FileProcessStatus {
    Waiting,        // 等待处理
    Processing,     // 处理中
    Completed,      // 已完成
    Failed,         // 失败
    Skipped         // 跳过
};

/**
 * 外发方式枚举
 */
enum class SendMethod {
    Email,          // 邮件
    FileTransfer,   // 文件传输
    MobileDevice,   // 移动设备
    CloudStorage    // 云存储
};

/**
 * 文件信息结构
 */
struct FileInfo {
    std::string id;                     // 文件ID
    std::string originalPath;           // 原始路径
    std::string fileName;               // 文件名
    std::string fileType;               // 文件类型
    size_t fileSize;                    // 文件大小
    std::time_t createdTime;            // 创建时间
    std::time_t modifiedTime;           // 修改时间
    bool isEncrypted;                   // 是否加密
    std::string encryptionAlgorithm;    // 加密算法
    uint32_t keyVersion;                // 密钥版本
    FileProcessStatus status;           // 处理状态
    std::string errorMessage;           // 错误信息
    int progressPercentage;             // 处理进度
    
    FileInfo() : fileSize(0), createdTime(0), modifiedTime(0), 
                 isEncrypted(false), keyVersion(0), 
                 status(FileProcessStatus::Waiting), 
                 progressPercentage(0) {}
};

/**
 * 外发配置结构
 */
struct SendConfiguration {
    SendMethod method;                  // 发送方式
    std::string recipient;              // 收件人
    std::string subject;                // 主题
    std::string message;                // 消息内容
    std::vector<std::string> ccList;    // 抄送列表
    bool requirePassword;               // 是否需要密码
    std::string password;               // 访问密码
    std::time_t expirationTime;         // 过期时间
    int maxAccessCount;                 // 最大访问次数
    bool enableWatermark;               // 是否启用水印
    std::string watermarkText;          // 水印文本
    std::map<std::string, std::string> customHeaders;  // 自定义头部
    
    SendConfiguration() : method(SendMethod::Email), requirePassword(false),
                          expirationTime(0), maxAccessCount(0), 
                          enableWatermark(true) {}
};

/**
 * 审批信息结构
 */
struct ApprovalInfo {
    std::string approvalId;             // 审批ID
    std::string approver;               // 审批人
    std::string approvalComment;        // 审批意见
    std::time_t approvalTime;           // 审批时间
    bool isApproved;                    // 是否通过
    std::string rejectionReason;        // 拒绝原因
    
    ApprovalInfo() : approvalTime(0), isApproved(false) {}
};

/**
 * 脱密任务结构
 */
struct DeclassificationTask {
    std::string taskId;                 // 任务ID
    std::string taskName;               // 任务名称
    std::string description;            // 任务描述
    std::string applicant;              // 申请人
    std::string department;             // 部门
    TaskStatus status;                  // 任务状态
    std::vector<FileInfo> files;        // 文件列表
    SendConfiguration sendConfig;       // 发送配置
    ApprovalInfo approvalInfo;          // 审批信息
    std::time_t createdTime;            // 创建时间
    std::time_t startTime;              // 开始时间
    std::time_t completedTime;          // 完成时间
    std::time_t updatedTime;            // 更新时间
    int progressPercentage;             // 任务进度
    std::string errorMessage;           // 错误信息
    std::string resultPackagePath;      // 结果包路径
    std::map<std::string, std::string> metadata;  // 元数据
    
    DeclassificationTask() : status(TaskStatus::Pending), 
                            createdTime(0), startTime(0), 
                            completedTime(0), updatedTime(0), 
                            progressPercentage(0) {}
};

/**
 * 安全包结构
 */
struct SecurePackage {
    std::string packageId;              // 包ID
    std::string packageName;            // 包名称
    std::string packagePath;            // 包路径
    std::string taskId;                 // 任务ID
    std::vector<std::string> fileIds;   // 文件ID列表
    size_t packageSize;                 // 包大小
    std::string checksum;               // 校验和
    std::time_t createdTime;            // 创建时间
    std::time_t expirationTime;         // 过期时间
    bool isPasswordProtected;           // 是否有密码保护
    std::string accessPassword;         // 访问密码
    int accessCount;                    // 访问次数
    int maxAccessCount;                 // 最大访问次数
    bool hasWatermark;                  // 是否有水印
    std::string watermarkText;          // 水印文本
    std::map<std::string, std::string> properties;  // 属性
    
    SecurePackage() : packageSize(0), createdTime(0), expirationTime(0),
                      isPasswordProtected(false), accessCount(0), 
                      maxAccessCount(0), hasWatermark(false) {}
};

/**
 * 操作统计结构
 */
struct OperationStatistics {
    int totalTasks;                     // 总任务数
    int completedTasks;                 // 完成任务数
    int failedTasks;                    // 失败任务数
    int processingTasks;                // 处理中任务数
    int totalFiles;                     // 总文件数
    int processedFiles;                 // 已处理文件数
    size_t totalDataSize;               // 总数据大小
    size_t processedDataSize;           // 已处理数据大小
    std::time_t lastUpdateTime;         // 最后更新时间
    
    OperationStatistics() : totalTasks(0), completedTasks(0), failedTasks(0),
                           processingTasks(0), totalFiles(0), processedFiles(0),
                           totalDataSize(0), processedDataSize(0), 
                           lastUpdateTime(0) {}
};

/**
 * 任务状态转换为字符串
 */
std::string taskStatusToString(TaskStatus status);

/**
 * 文件处理状态转换为字符串
 */
std::string fileProcessStatusToString(FileProcessStatus status);

/**
 * 发送方式转换为字符串
 */
std::string sendMethodToString(SendMethod method);

/**
 * 字符串转换为任务状态
 */
TaskStatus stringToTaskStatus(const std::string& statusStr);

/**
 * 字符串转换为文件处理状态
 */
FileProcessStatus stringToFileProcessStatus(const std::string& statusStr);

/**
 * 字符串转换为发送方式
 */
SendMethod stringToSendMethod(const std::string& methodStr);

} // namespace Models
} // namespace DeclassificationClient 