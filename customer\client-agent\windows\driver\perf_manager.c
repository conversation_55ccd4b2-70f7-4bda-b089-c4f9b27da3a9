/*
 * perf_manager.c
 * 
 * Windows MiniFilter驱动性能管理器主实现
 * 统一管理所有性能优化组件
 */

#include <fltKernel.h>
#include <ntstrsafe.h>
#include "perf_optimization.h"

// 全局性能管理器实例
PERF_MANAGER g_PerfManager = {0};

// 清理定时器DPC例程
static VOID
PerfCleanupDpcRoutine(
    _In_ PKDPC Dpc,
    _In_opt_ PVOID DeferredContext,
    _In_opt_ PVOID SystemArgument1,
    _In_opt_ PVOID SystemArgument2
    );

// 清理工作项例程
static VOID
PerfCleanupWorkItemRoutine(
    _In_ PVOID Context
    );

// 前向声明
NTSTATUS
PerfInitializeAllBufferPools(
    VOID
    );

VOID
PerfCleanupAllBufferPools(
    VOID
    );

NTSTATUS
PerfInitializeCryptoCache(
    VOID
    );

VOID
PerfCleanupCryptoCache(
    VOID
    );

NTSTATUS
PerfInitializeKeyHashTable(
    VOID
    );

VOID
PerfCleanupKeyHashTable(
    VOID
    );

NTSTATUS
PerfInitializeMdlCache(
    VOID
    );

VOID
PerfCleanupMdlCache(
    VOID
    );

//
// 初始化性能管理器
//
NTSTATUS
PerfInitialize(
    VOID
    )
{
    NTSTATUS status = STATUS_SUCCESS;
    LARGE_INTEGER dueTime;

    PERF_DEBUG_PRINT("Initializing performance manager...");

    // 检查是否已经初始化
    if (g_PerfManager.Initialized) {
        PERF_DEBUG_PRINT("Performance manager already initialized");
        return STATUS_SUCCESS;
    }

    // 清零管理器结构
    RtlZeroMemory(&g_PerfManager, sizeof(PERF_MANAGER));

    // 初始化同步对象
    status = ExInitializeResourceLite(&g_PerfManager.Resource);
    if (!NT_SUCCESS(status)) {
        PERF_DEBUG_PRINT("Failed to initialize resource, status: 0x%08x", status);
        goto cleanup;
    }

    // 初始化各个组件

    // 1. 初始化缓冲区池
    status = PerfInitializeAllBufferPools();
    if (!NT_SUCCESS(status)) {
        PERF_DEBUG_PRINT("Failed to initialize buffer pools, status: 0x%08x", status);
        goto cleanup;
    }

    // 2. 初始化加密上下文缓存
    status = PerfInitializeCryptoCache();
    if (!NT_SUCCESS(status)) {
        PERF_DEBUG_PRINT("Failed to initialize crypto cache, status: 0x%08x", status);
        goto cleanup;
    }

    // 3. 初始化密钥哈希表
    status = PerfInitializeKeyHashTable();
    if (!NT_SUCCESS(status)) {
        PERF_DEBUG_PRINT("Failed to initialize key hash table, status: 0x%08x", status);
        goto cleanup;
    }

    // 4. 初始化MDL缓存
    status = PerfInitializeMdlCache();
    if (!NT_SUCCESS(status)) {
        PERF_DEBUG_PRINT("Failed to initialize MDL cache, status: 0x%08x", status);
        goto cleanup;
    }

    // 5. 初始化全局统计
    RtlZeroMemory(&g_PerfManager.GlobalStats, sizeof(PERF_GLOBAL_STATS));

    // 6. 初始化清理定时器和DPC
    KeInitializeTimer(&g_PerfManager.CleanupTimer);
    KeInitializeDpc(&g_PerfManager.CleanupDpc, PerfCleanupDpcRoutine, NULL);
    
    // 7. 初始化清理工作项
    ExInitializeWorkItem(&g_PerfManager.CleanupWorkItem, PerfCleanupWorkItemRoutine, NULL);

    // 8. 启动定期清理定时器（每5分钟）
    dueTime.QuadPart = -5 * 60 * 10000000LL;  // 5分钟（负值表示相对时间）
    KeSetTimerEx(&g_PerfManager.CleanupTimer, dueTime, 5 * 60 * 1000, &g_PerfManager.CleanupDpc);

    // 标记为已初始化
    g_PerfManager.Initialized = TRUE;

    PERF_DEBUG_PRINT("Performance manager initialized successfully");
    return STATUS_SUCCESS;

cleanup:
    // 清理已初始化的组件
    PerfCleanup();
    return status;
}

//
// 清理性能管理器
//
VOID
PerfCleanup(
    VOID
    )
{
    PERF_DEBUG_PRINT("Cleaning up performance manager...");

    if (!g_PerfManager.Initialized) {
        PERF_DEBUG_PRINT("Performance manager not initialized");
        return;
    }

    // 停止清理定时器
    KeCancelTimer(&g_PerfManager.CleanupTimer);

    // 等待所有DPC和工作项完成
    KeFlushQueuedDpcs();

    // 清理各个组件
    PerfCleanupMdlCache();
    PerfCleanupKeyHashTable();
    PerfCleanupCryptoCache();
    PerfCleanupAllBufferPools();

    // 清理同步对象
    ExDeleteResourceLite(&g_PerfManager.Resource);

    // 清零管理器结构
    RtlZeroMemory(&g_PerfManager, sizeof(PERF_MANAGER));

    PERF_DEBUG_PRINT("Performance manager cleaned up");
}

//
// 更新I/O统计信息
//
VOID
PerfUpdateIOStats(
    _In_ BOOLEAN IsRead,
    _In_ ULONG BytesTransferred
    )
{
    PPERF_GLOBAL_STATS stats = &g_PerfManager.GlobalStats;

    if (!g_PerfManager.Initialized) {
        return;
    }

    if (IsRead) {
        InterlockedIncrement64(&stats->TotalReadOperations.QuadPart);
        InterlockedAdd64(&stats->TotalBytesRead.QuadPart, BytesTransferred);
    } else {
        InterlockedIncrement64(&stats->TotalWriteOperations.QuadPart);
        InterlockedAdd64(&stats->TotalBytesWritten.QuadPart, BytesTransferred);
    }
}

//
// 更新加密统计信息
//
VOID
PerfUpdateCryptoStats(
    _In_ BOOLEAN IsEncrypt,
    _In_ ULONG BytesProcessed,
    _In_ LARGE_INTEGER ProcessingTime
    )
{
    PPERF_GLOBAL_STATS stats = &g_PerfManager.GlobalStats;

    if (!g_PerfManager.Initialized) {
        return;
    }

    if (IsEncrypt) {
        InterlockedIncrement64(&stats->TotalEncryptOperations.QuadPart);
        InterlockedAdd64(&stats->TotalEncryptBytes.QuadPart, BytesProcessed);
        InterlockedAdd64(&stats->TotalEncryptTime.QuadPart, ProcessingTime.QuadPart);
        
        // 计算平均加密时间
        if (stats->TotalEncryptOperations.QuadPart > 0) {
            stats->AverageEncryptTime.QuadPart = 
                stats->TotalEncryptTime.QuadPart / stats->TotalEncryptOperations.QuadPart;
        }
    } else {
        InterlockedIncrement64(&stats->TotalDecryptOperations.QuadPart);
        InterlockedAdd64(&stats->TotalDecryptBytes.QuadPart, BytesProcessed);
        InterlockedAdd64(&stats->TotalDecryptTime.QuadPart, ProcessingTime.QuadPart);
        
        // 计算平均解密时间
        if (stats->TotalDecryptOperations.QuadPart > 0) {
            stats->AverageDecryptTime.QuadPart = 
                stats->TotalDecryptTime.QuadPart / stats->TotalDecryptOperations.QuadPart;
        }
    }
}

//
// 获取全局统计信息
//
VOID
PerfGetGlobalStats(
    _Out_ PPERF_GLOBAL_STATS Stats
    )
{
    ULONG bufferPoolHits, bufferPoolMisses;
    ULONG cryptoCacheHits, cryptoCacheMisses;
    ULONG keyLookupHits, keyLookupMisses;
    ULONG mdlCacheHits, mdlCacheMisses;
    ULONG i;

    if (Stats == NULL || !g_PerfManager.Initialized) {
        return;
    }

    // 复制基本统计信息
    RtlCopyMemory(Stats, &g_PerfManager.GlobalStats, sizeof(PERF_GLOBAL_STATS));

    // 计算缓存命中率

    // 1. 缓冲区池命中率
    bufferPoolHits = bufferPoolMisses = 0;
    for (i = 0; i < BufferSizeMax; i++) {
        bufferPoolHits += g_PerfManager.BufferPools[i].AllocHits;
        bufferPoolMisses += g_PerfManager.BufferPools[i].AllocMisses;
    }
    if ((bufferPoolHits + bufferPoolMisses) > 0) {
        Stats->BufferPoolHitRate = (bufferPoolHits * 100) / (bufferPoolHits + bufferPoolMisses);
    }

    // 2. 加密上下文缓存命中率
    cryptoCacheHits = g_PerfManager.CryptoCache.LookupHits;
    cryptoCacheMisses = g_PerfManager.CryptoCache.LookupMisses;
    if ((cryptoCacheHits + cryptoCacheMisses) > 0) {
        Stats->CryptoContextHitRate = (cryptoCacheHits * 100) / (cryptoCacheHits + cryptoCacheMisses);
    }

    // 3. 密钥查找命中率
    keyLookupHits = g_PerfManager.KeyHashTable.LookupHits;
    keyLookupMisses = g_PerfManager.KeyHashTable.LookupMisses;
    if ((keyLookupHits + keyLookupMisses) > 0) {
        Stats->KeyLookupHitRate = (keyLookupHits * 100) / (keyLookupHits + keyLookupMisses);
    }

    // 4. MDL缓存命中率
    mdlCacheHits = g_PerfManager.MdlCache.AllocHits;
    mdlCacheMisses = g_PerfManager.MdlCache.AllocMisses;
    if ((mdlCacheHits + mdlCacheMisses) > 0) {
        Stats->MdlCacheHitRate = (mdlCacheHits * 100) / (mdlCacheHits + mdlCacheMisses);
    }
}

//
// 根据大小确定缓冲区类型
//
PERF_BUFFER_SIZE_TYPE
PerfGetBufferSizeType(
    _In_ ULONG Size
    )
{
    if (Size <= PERF_BUFFER_POOL_SIZE_4K) {
        return BufferSize4K;
    } else if (Size <= PERF_BUFFER_POOL_SIZE_16K) {
        return BufferSize16K;
    } else if (Size <= PERF_BUFFER_POOL_SIZE_64K) {
        return BufferSize64K;
    } else if (Size <= PERF_BUFFER_POOL_SIZE_256K) {
        return BufferSize256K;
    } else {
        return BufferSizeMax;
    }
}

//
// 清理过期条目（定期清理）
//
VOID
PerfCleanupExpiredEntries(
    VOID
    )
{
    if (!g_PerfManager.Initialized) {
        return;
    }

    PERF_DEBUG_PRINT("Starting periodic cleanup of expired entries");

    // 注意：各个组件的过期清理由其自身实现
    // 这里只是触发清理，实际清理在访问时进行

    PERF_DEBUG_PRINT("Periodic cleanup completed");
}

//
// 清理定时器DPC例程
//
static VOID
PerfCleanupDpcRoutine(
    _In_ PKDPC Dpc,
    _In_opt_ PVOID DeferredContext,
    _In_opt_ PVOID SystemArgument1,
    _In_opt_ PVOID SystemArgument2
    )
{
    UNREFERENCED_PARAMETER(Dpc);
    UNREFERENCED_PARAMETER(DeferredContext);
    UNREFERENCED_PARAMETER(SystemArgument1);
    UNREFERENCED_PARAMETER(SystemArgument2);

    // 排队清理工作项
    ExQueueWorkItem(&g_PerfManager.CleanupWorkItem, DelayedWorkQueue);
}

//
// 清理工作项例程
//
static VOID
PerfCleanupWorkItemRoutine(
    _In_ PVOID Context
    )
{
    UNREFERENCED_PARAMETER(Context);

    // 执行定期清理
    PerfCleanupExpiredEntries();
} 