/*
 * context.c
 * 
 * 上下文管理实现文件
 * 实现流上下文相关的函数
 */

#include <fltKernel.h>
#include "enc_filter.h"
#include "utils.h"

// 前向声明
VOID
ContextCleanupCallback(
    _In_ PVOID Context,
    _In_ FLT_CONTEXT_TYPE ContextType
    );

//
// 创建流上下文
//
NTSTATUS
EncCreateStreamContext(
    _In_ PFLT_RELATED_OBJECTS FltObjects,
    _In_ PCUNICODE_STRING FileName,
    _Out_ PENC_STREAM_CONTEXT *StreamContext
    )
{
    NTSTATUS status;
    PENC_STREAM_CONTEXT streamContext = NULL;
    
    // 参数验证
    if (FltObjects == NULL || FileName == NULL || StreamContext == NULL) {
        return STATUS_INVALID_PARAMETER;
    }
    
    // 分配上下文
    status = FltAllocateContext(
        FltObjects->Filter,
        FLT_STREAM_CONTEXT,
        sizeof(ENC_STREAM_CONTEXT),
        NonPagedPool,
        (PFLT_CONTEXT*)&streamContext
    );
    
    if (!NT_SUCCESS(status)) {
        return status;
    }
    
    // 初始化上下文
    RtlZeroMemory(streamContext, sizeof(ENC_STREAM_CONTEXT));
    
    // 初始化引用计数
    streamContext->ReferenceCount = 1;
    
    // 初始化同步资源
    streamContext->Resource = ExAllocatePoolWithTag(
        NonPagedPool,
        sizeof(ERESOURCE),
        'cneS'
    );
    
    if (streamContext->Resource == NULL) {
        FltReleaseContext(streamContext);
        return STATUS_INSUFFICIENT_RESOURCES;
    }
    
    ExInitializeResourceLite(streamContext->Resource);
    
    // 设置清理回调
    FltSetContextCleanupCallback(
        FltObjects->Filter,
        streamContext,
        ContextCleanupCallback
    );
    
    // 复制文件名
    status = UtilDuplicateUnicodeString(FileName, &streamContext->FileName);
    if (!NT_SUCCESS(status)) {
        ExDeleteResourceLite(streamContext->Resource);
        ExFreePool(streamContext->Resource);
        FltReleaseContext(streamContext);
        return status;
    }
    
    // 返回上下文
    *StreamContext = streamContext;
    
    return STATUS_SUCCESS;
}

//
// 获取或创建流上下文
//
NTSTATUS
EncGetOrCreateStreamContext(
    _In_ PFLT_CALLBACK_DATA Data,
    _In_ PFLT_RELATED_OBJECTS FltObjects,
    _In_ BOOLEAN CreateIfNotExists,
    _Out_ PENC_STREAM_CONTEXT *StreamContext,
    _Out_opt_ BOOLEAN *IsContextCreated
    )
{
    NTSTATUS status;
    PENC_STREAM_CONTEXT streamContext = NULL;
    BOOLEAN contextCreated = FALSE;
    UNICODE_STRING fileName;
    
    // 参数验证
    if (Data == NULL || FltObjects == NULL || StreamContext == NULL) {
        return STATUS_INVALID_PARAMETER;
    }
    
    // 初始化输出参数
    *StreamContext = NULL;
    if (IsContextCreated != NULL) {
        *IsContextCreated = FALSE;
    }
    
    // 尝试获取现有上下文
    status = FltGetStreamContext(
        FltObjects->Instance,
        FltObjects->FileObject,
        (PFLT_CONTEXT*)&streamContext
    );
    
    if (NT_SUCCESS(status)) {
        // 找到上下文，增加引用计数
        InterlockedIncrement(&streamContext->ReferenceCount);
        *StreamContext = streamContext;
        return STATUS_SUCCESS;
    }
    
    // 如果不创建，直接返回
    if (!CreateIfNotExists) {
        return status;
    }
    
    // 获取文件名
    RtlZeroMemory(&fileName, sizeof(UNICODE_STRING));
    status = UtilGetFileName(Data, &fileName);
    if (!NT_SUCCESS(status)) {
        return status;
    }
    
    // 创建新上下文
    status = EncCreateStreamContext(
        FltObjects,
        &fileName,
        &streamContext
    );
    
    // 释放文件名
    UtilFreeUnicodeString(&fileName);
    
    if (!NT_SUCCESS(status)) {
        return status;
    }
    
    // 设置上下文到文件对象
    status = FltSetStreamContext(
        FltObjects->Instance,
        FltObjects->FileObject,
        FLT_SET_CONTEXT_KEEP_IF_EXISTS,
        streamContext,
        (PFLT_CONTEXT*)&streamContext
    );
    
    if (status == STATUS_FLT_CONTEXT_ALREADY_DEFINED) {
        // 其他线程已创建上下文，释放我们的上下文，使用已有的
        EncReleaseStreamContext(streamContext);
        
        // 重新获取上下文
        status = FltGetStreamContext(
            FltObjects->Instance,
            FltObjects->FileObject,
            (PFLT_CONTEXT*)&streamContext
        );
        
        if (NT_SUCCESS(status)) {
            // 增加引用计数
            InterlockedIncrement(&streamContext->ReferenceCount);
        }
    } else if (NT_SUCCESS(status)) {
        // 成功设置我们的上下文
        contextCreated = TRUE;
    }
    
    if (NT_SUCCESS(status)) {
        *StreamContext = streamContext;
        if (IsContextCreated != NULL) {
            *IsContextCreated = contextCreated;
        }
    }
    
    return status;
}

//
// 释放流上下文
//
VOID
EncReleaseStreamContext(
    _In_ PENC_STREAM_CONTEXT StreamContext
    )
{
    if (StreamContext == NULL) {
        return;
    }
    
    // 减少引用计数
    if (InterlockedDecrement(&StreamContext->ReferenceCount) == 0) {
        // 引用计数降为0，清理资源
        if (StreamContext->Resource != NULL) {
            ExDeleteResourceLite(StreamContext->Resource);
            ExFreePool(StreamContext->Resource);
        }
        
        // 释放文件名
        if (StreamContext->FileName.Buffer != NULL) {
            ExFreePool(StreamContext->FileName.Buffer);
        }
        
        // 释放上下文
        FltReleaseContext(StreamContext);
    }
}

//
// 上下文清理回调
//
VOID
ContextCleanupCallback(
    _In_ PVOID Context,
    _In_ FLT_CONTEXT_TYPE ContextType
    )
{
    PENC_STREAM_CONTEXT streamContext = (PENC_STREAM_CONTEXT)Context;
    
    // 参数验证
    if (Context == NULL || ContextType != FLT_STREAM_CONTEXT) {
        return;
    }
    
    // 清理资源
    if (streamContext->Resource != NULL) {
        ExDeleteResourceLite(streamContext->Resource);
        ExFreePool(streamContext->Resource);
        streamContext->Resource = NULL;
    }
    
    // 释放文件名
    if (streamContext->FileName.Buffer != NULL) {
        ExFreePool(streamContext->FileName.Buffer);
        streamContext->FileName.Buffer = NULL;
        streamContext->FileName.Length = 0;
        streamContext->FileName.MaximumLength = 0;
    }
} 