#include "WatermarkManager.h"
#include "Logger.h"
#include <chrono>
#include <ctime>
#include <sstream>
#include <iomanip>
#include <iphlpapi.h>
#include <lmcons.h>

#pragma comment(lib, "iphlpapi.lib")

// 水印窗口类名
const wchar_t* WATERMARK_CLASS_NAME = L"CryptoSystemWatermarkWindow";

// 定时器ID
const UINT REFRESH_TIMER_ID = 1001;

// 单例访问
WatermarkManager& WatermarkManager::getInstance() {
    static WatermarkManager instance;
    return instance;
}

// 构造函数
WatermarkManager::WatermarkManager()
    : m_initialized(false)
    , m_enabled(false)
    , m_watermarkHwnd(NULL)
    , m_hInstance(GetModuleHandle(NULL))
    , m_pD2DFactory(nullptr)
    , m_pRenderTarget(nullptr)
    , m_pTextBrush(nullptr)
    , m_pDWriteFactory(nullptr)
    , m_pTextFormat(nullptr)
    , m_threadRunning(false)
{
    // 获取当前用户名
    wchar_t username[256];
    DWORD usernameLen = 256;
    if (GetUserNameW(username, &usernameLen)) {
        m_username = username;
    } else {
        m_username = L"未知用户";
        Logger::error("Failed to get username");
    }

    // 获取计算机名
    wchar_t computerName[256];
    DWORD computerNameLen = 256;
    if (GetComputerNameW(computerName, &computerNameLen)) {
        m_computerName = computerName;
    } else {
        m_computerName = L"未知主机";
        Logger::error("Failed to get computer name");
    }

    // 获取IP地址
    m_ipAddress = L"127.0.0.1"; // 默认
    
    PIP_ADAPTER_INFO pAdapterInfo = (IP_ADAPTER_INFO*)malloc(sizeof(IP_ADAPTER_INFO));
    ULONG bufLen = sizeof(IP_ADAPTER_INFO);
    
    if (GetAdaptersInfo(pAdapterInfo, &bufLen) == ERROR_BUFFER_OVERFLOW) {
        free(pAdapterInfo);
        pAdapterInfo = (IP_ADAPTER_INFO*)malloc(bufLen);
    }
    
    if (GetAdaptersInfo(pAdapterInfo, &bufLen) == NO_ERROR) {
        PIP_ADAPTER_INFO pAdapter = pAdapterInfo;
        while (pAdapter) {
            // 跳过回环适配器和未连接的适配器
            if (pAdapter->Type != MIB_IF_TYPE_LOOPBACK && pAdapter->IpAddressList.IpAddress.String[0] != '0') {
                std::string ipAddr = pAdapter->IpAddressList.IpAddress.String;
                if (ipAddr != "0.0.0.0") {
                    std::wstring wIpAddr(ipAddr.begin(), ipAddr.end());
                    m_ipAddress = wIpAddr;
                    break;
                }
            }
            pAdapter = pAdapter->Next;
        }
    }
    
    if (pAdapterInfo) {
        free(pAdapterInfo);
    }
}

// 析构函数
WatermarkManager::~WatermarkManager() {
    shutdown();
}

// 初始化水印管理器
bool WatermarkManager::initialize() {
    if (m_initialized) {
        return true;
    }
    
    // 初始化Direct2D资源
    if (!initializeDirect2D()) {
        Logger::error("Failed to initialize Direct2D resources");
        return false;
    }
    
    // 创建水印窗口
    if (!createWatermarkWindow()) {
        Logger::error("Failed to create watermark window");
        return false;
    }
    
    m_initialized = true;
    Logger::info("WatermarkManager initialized successfully");
    return true;
}

// 关闭水印管理器
void WatermarkManager::shutdown() {
    if (!m_initialized) {
        return;
    }
    
    // 停止刷新线程
    if (m_threadRunning && m_refreshThread) {
        m_threadRunning = false;
        if (m_refreshThread->joinable()) {
            m_refreshThread->join();
        }
        m_refreshThread.reset();
    }
    
    // 销毁窗口
    if (m_watermarkHwnd) {
        DestroyWindow(m_watermarkHwnd);
        m_watermarkHwnd = NULL;
    }
    
    // 注销窗口类
    UnregisterClassW(WATERMARK_CLASS_NAME, m_hInstance);
    
    // 释放Direct2D资源
    if (m_pTextBrush) {
        m_pTextBrush->Release();
        m_pTextBrush = nullptr;
    }
    
    if (m_pTextFormat) {
        m_pTextFormat->Release();
        m_pTextFormat = nullptr;
    }
    
    if (m_pRenderTarget) {
        m_pRenderTarget->Release();
        m_pRenderTarget = nullptr;
    }
    
    if (m_pDWriteFactory) {
        m_pDWriteFactory->Release();
        m_pDWriteFactory = nullptr;
    }
    
    if (m_pD2DFactory) {
        m_pD2DFactory->Release();
        m_pD2DFactory = nullptr;
    }
    
    m_initialized = false;
    m_enabled = false;
    Logger::info("WatermarkManager shut down");
}

// 启用水印
bool WatermarkManager::enable() {
    if (!m_initialized) {
        Logger::error("Cannot enable watermark: manager not initialized");
        return false;
    }
    
    if (m_enabled) {
        return true; // 已经启用
    }
    
    // 显示水印窗口
    ShowWindow(m_watermarkHwnd, SW_SHOW);
    
    // 启动刷新线程
    m_threadRunning = true;
    m_refreshThread = std::make_unique<std::thread>(&WatermarkManager::refreshThreadFunc, this);
    
    m_enabled = true;
    Logger::info("Watermark enabled");
    return true;
}

// 禁用水印
void WatermarkManager::disable() {
    if (!m_enabled) {
        return;
    }
    
    // 停止刷新线程
    if (m_threadRunning && m_refreshThread) {
        m_threadRunning = false;
        if (m_refreshThread->joinable()) {
            m_refreshThread->join();
        }
        m_refreshThread.reset();
    }
    
    // 隐藏水印窗口
    if (m_watermarkHwnd) {
        ShowWindow(m_watermarkHwnd, SW_HIDE);
    }
    
    m_enabled = false;
    Logger::info("Watermark disabled");
}

// 设置水印配置
void WatermarkManager::setConfig(const WatermarkConfig& config) {
    std::lock_guard<std::mutex> lock(m_configMutex);
    m_config = config;
    
    // 更新字体格式
    if (m_pTextFormat) {
        m_pTextFormat->Release();
        m_pTextFormat = nullptr;
    }
    
    if (m_pDWriteFactory) {
        HRESULT hr = m_pDWriteFactory->CreateTextFormat(
            L"Arial", 
            NULL,
            DWRITE_FONT_WEIGHT_NORMAL,
            DWRITE_FONT_STYLE_NORMAL,
            DWRITE_FONT_STRETCH_NORMAL,
            config.fontSize,
            L"zh-CN",
            &m_pTextFormat
        );
        
        if (SUCCEEDED(hr) && m_pTextFormat) {
            m_pTextFormat->SetTextAlignment(DWRITE_TEXT_ALIGNMENT_CENTER);
            m_pTextFormat->SetParagraphAlignment(DWRITE_PARAGRAPH_ALIGNMENT_CENTER);
        }
    }
    
    // 更新文本画刷颜色
    if (m_pTextBrush && m_pRenderTarget) {
        D2D1_COLOR_F color = D2D1::ColorF(
            config.color.r,
            config.color.g,
            config.color.b,
            config.color.a * config.opacity
        );
        m_pTextBrush->SetColor(color);
    }
    
    // 刷新水印显示
    if (m_enabled && m_watermarkHwnd) {
        InvalidateRect(m_watermarkHwnd, NULL, TRUE);
    }
    
    Logger::info("Watermark configuration updated");
}

// 获取当前水印配置
WatermarkConfig WatermarkManager::getConfig() const {
    std::lock_guard<std::mutex> lock(m_configMutex);
    return m_config;
}

// 初始化Direct2D资源
bool WatermarkManager::initializeDirect2D() {
    // 创建D2D工厂
    HRESULT hr = D2D1CreateFactory(
        D2D1_FACTORY_TYPE_SINGLE_THREADED,
        &m_pD2DFactory
    );
    
    if (FAILED(hr)) {
        Logger::error("Failed to create D2D factory");
        return false;
    }
    
    // 创建DirectWrite工厂
    hr = DWriteCreateFactory(
        DWRITE_FACTORY_TYPE_SHARED,
        __uuidof(IDWriteFactory),
        reinterpret_cast<IUnknown**>(&m_pDWriteFactory)
    );
    
    if (FAILED(hr)) {
        Logger::error("Failed to create DirectWrite factory");
        return false;
    }
    
    // 创建文本格式
    hr = m_pDWriteFactory->CreateTextFormat(
        L"Arial",
        NULL,
        DWRITE_FONT_WEIGHT_NORMAL,
        DWRITE_FONT_STYLE_NORMAL,
        DWRITE_FONT_STRETCH_NORMAL,
        m_config.fontSize,
        L"zh-CN",
        &m_pTextFormat
    );
    
    if (FAILED(hr)) {
        Logger::error("Failed to create text format");
        return false;
    }
    
    m_pTextFormat->SetTextAlignment(DWRITE_TEXT_ALIGNMENT_CENTER);
    m_pTextFormat->SetParagraphAlignment(DWRITE_PARAGRAPH_ALIGNMENT_CENTER);
    
    return true;
}

// 创建水印窗口
bool WatermarkManager::createWatermarkWindow() {
    // 注册窗口类
    WNDCLASSEXW wcex = {};
    wcex.cbSize = sizeof(WNDCLASSEXW);
    wcex.style = CS_HREDRAW | CS_VREDRAW;
    wcex.lpfnWndProc = WatermarkWndProc;
    wcex.hInstance = m_hInstance;
    wcex.hCursor = LoadCursor(NULL, IDC_ARROW);
    wcex.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
    wcex.lpszClassName = WATERMARK_CLASS_NAME;
    
    if (!RegisterClassExW(&wcex)) {
        Logger::error("Failed to register watermark window class");
        return false;
    }
    
    // 获取屏幕尺寸
    int screenWidth = GetSystemMetrics(SM_CXSCREEN);
    int screenHeight = GetSystemMetrics(SM_CYSCREEN);
    
    // 创建透明叠加窗口
    m_watermarkHwnd = CreateWindowExW(
        WS_EX_LAYERED | WS_EX_TRANSPARENT | WS_EX_TOPMOST,
        WATERMARK_CLASS_NAME,
        L"Watermark",
        WS_POPUP,
        0, 0, screenWidth, screenHeight,
        NULL, NULL, m_hInstance, this
    );
    
    if (!m_watermarkHwnd) {
        Logger::error("Failed to create watermark window");
        return false;
    }
    
    // 设置窗口透明度
    SetLayeredWindowAttributes(m_watermarkHwnd, 0, 255, LWA_ALPHA);
    
    // 创建渲染目标
    if (m_pD2DFactory) {
        RECT rc;
        GetClientRect(m_watermarkHwnd, &rc);
        
        D2D1_SIZE_U size = D2D1::SizeU(rc.right - rc.left, rc.bottom - rc.top);
        
        HRESULT hr = m_pD2DFactory->CreateHwndRenderTarget(
            D2D1::RenderTargetProperties(),
            D2D1::HwndRenderTargetProperties(m_watermarkHwnd, size),
            &m_pRenderTarget
        );
        
        if (FAILED(hr)) {
            Logger::error("Failed to create render target");
            return false;
        }
        
        // 创建文本画刷
        D2D1_COLOR_F color = D2D1::ColorF(
            m_config.color.r,
            m_config.color.g,
            m_config.color.b,
            m_config.color.a * m_config.opacity
        );
        
        hr = m_pRenderTarget->CreateSolidColorBrush(color, &m_pTextBrush);
        
        if (FAILED(hr)) {
            Logger::error("Failed to create text brush");
            return false;
        }
    }
    
    return true;
}

// 水印窗口过程
LRESULT CALLBACK WatermarkManager::WatermarkWndProc(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam) {
    WatermarkManager* pThis = nullptr;
    
    if (msg == WM_CREATE) {
        CREATESTRUCT* pCreate = reinterpret_cast<CREATESTRUCT*>(lParam);
        pThis = reinterpret_cast<WatermarkManager*>(pCreate->lpCreateParams);
        SetWindowLongPtr(hwnd, GWLP_USERDATA, reinterpret_cast<LONG_PTR>(pThis));
    } else {
        pThis = reinterpret_cast<WatermarkManager*>(GetWindowLongPtr(hwnd, GWLP_USERDATA));
    }
    
    switch (msg) {
        case WM_PAINT:
            if (pThis) {
                pThis->drawWatermark();
            }
            return 0;
        
        case WM_DISPLAYCHANGE:
            // 屏幕分辨率或颜色深度变化
            if (pThis && pThis->m_pRenderTarget) {
                // 重新调整渲染目标大小
                RECT rc;
                GetClientRect(hwnd, &rc);
                D2D1_SIZE_U size = D2D1::SizeU(rc.right - rc.left, rc.bottom - rc.top);
                pThis->m_pRenderTarget->Resize(size);
                
                // 重绘水印
                InvalidateRect(hwnd, NULL, TRUE);
            }
            return 0;
        
        case WM_DESTROY:
            return 0;
    }
    
    return DefWindowProc(hwnd, msg, wParam, lParam);
}

// 更新水印内容（替换变量）
std::wstring WatermarkManager::updateWatermarkContent() {
    std::wstring content = m_config.contentTemplate;
    std::wstring result = content;
    
    // 替换用户名
    size_t pos = result.find(L"{username}");
    if (pos != std::wstring::npos) {
        result.replace(pos, 10, m_username);
    }
    
    // 替换主机名
    pos = result.find(L"{hostname}");
    if (pos != std::wstring::npos) {
        result.replace(pos, 10, m_computerName);
    }
    
    // 替换IP地址
    pos = result.find(L"{ip}");
    if (pos != std::wstring::npos) {
        result.replace(pos, 4, m_ipAddress);
    }
    
    // 替换日期时间
    pos = result.find(L"{datetime}");
    if (pos != std::wstring::npos) {
        auto now = std::chrono::system_clock::now();
        auto time = std::chrono::system_clock::to_time_t(now);
        
        std::wstringstream wss;
        struct tm timeinfo;
        localtime_s(&timeinfo, &time);
        wss << std::put_time(&timeinfo, L"%Y-%m-%d %H:%M:%S");
        
        result.replace(pos, 10, wss.str());
    }
    
    return result;
}

// 绘制水印
void WatermarkManager::drawWatermark() {
    if (!m_pRenderTarget || !m_pTextBrush || !m_pTextFormat) {
        return;
    }
    
    PAINTSTRUCT ps;
    BeginPaint(m_watermarkHwnd, &ps);
    
    m_pRenderTarget->BeginDraw();
    m_pRenderTarget->Clear(D2D1::ColorF(D2D1::ColorF::White, 0.0f)); // 透明背景
    
    // 获取渲染区域大小
    D2D1_SIZE_F size = m_pRenderTarget->GetSize();
    
    // 计算水印内容
    std::wstring content = updateWatermarkContent();
    
    // 计算文本布局需要的格式和矩形
    D2D1_RECT_F rect = D2D1::RectF(0, 0, size.width, size.height);
    
    // 保存当前渲染状态
    m_pRenderTarget->SaveDrawingState(NULL);
    
    // 在平铺的位置上绘制水印
    float spacingX = 300.0f;
    float spacingY = 150.0f;
    
    for (float y = -spacingY; y < size.height + spacingY; y += spacingY) {
        for (float x = -spacingX; x < size.width + spacingX; x += spacingX) {
            // 平移到文本位置
            D2D1_MATRIX_3X2_F translateMatrix = D2D1::Matrix3x2F::Translation(x, y);
            
            // 应用旋转
            D2D1_MATRIX_3X2_F rotateMatrix = D2D1::Matrix3x2F::Rotation(
                m_config.rotation,
                D2D1::Point2F(x + spacingX / 2, y + spacingY / 2)
            );
            
            // 应用变换
            m_pRenderTarget->SetTransform(rotateMatrix * translateMatrix);
            
            // 绘制文本
            m_pRenderTarget->DrawText(
                content.c_str(),
                static_cast<UINT32>(content.length()),
                m_pTextFormat,
                D2D1::RectF(0, 0, spacingX, spacingY),
                m_pTextBrush
            );
        }
    }
    
    // 恢复渲染状态
    m_pRenderTarget->RestoreDrawingState(NULL);
    
    // 结束绘制
    HRESULT hr = m_pRenderTarget->EndDraw();
    if (FAILED(hr)) {
        Logger::error("Direct2D EndDraw failed");
    }
    
    EndPaint(m_watermarkHwnd, &ps);
}

// 刷新线程函数
void WatermarkManager::refreshThreadFunc() {
    while (m_threadRunning) {
        // 刷新水印窗口
        if (m_watermarkHwnd && IsWindow(m_watermarkHwnd)) {
            InvalidateRect(m_watermarkHwnd, NULL, TRUE);
        }
        
        // 等待下一次刷新
        int interval;
        {
            std::lock_guard<std::mutex> lock(m_configMutex);
            interval = m_config.refreshInterval;
        }
        
        // 避免过于频繁的刷新，确保至少1秒间隔
        if (interval < 1000) {
            interval = 1000;
        }
        
        // 分段睡眠，以便能及时响应线程终止请求
        const int sleepStep = 100; // 100毫秒检查一次
        for (int i = 0; i < interval && m_threadRunning; i += sleepStep) {
            std::this_thread::sleep_for(std::chrono::milliseconds(sleepStep));
        }
    }
} 