#include "api_client.h"

namespace crypto {
namespace api {

// KeyLifecycleClient的工厂函数声明（在key_lifecycle_client.cpp中定义）
std::unique_ptr<KeyLifecycleClient> CreateKeyLifecycleClient(
    const std::string& baseUrl, const std::string& apiKey, bool useTls);

// 私有实现类
class ApiClient::Impl {
public:
    explicit Impl(const ApiClientConfig& config) {
        // 创建密钥服务客户端
        KeyServiceConfig keyServiceConfig;
        keyServiceConfig.serviceUrl = config.serviceUrl;
        keyServiceConfig.apiKey = config.apiKey;
        keyServiceConfig.timeoutMs = config.timeoutMs;
        keyServiceConfig.useTls = config.useTls;
        
        keyServiceClient_ = std::make_unique<KeyServiceClient>(keyServiceConfig);
        
        // 创建密钥生命周期管理客户端
        keyLifecycleClient_ = CreateKeyLifecycleClient(
            config.serviceUrl, config.apiKey, config.useTls);
    }
    
    ~Impl() = default;
    
    // 获取密钥服务客户端
    KeyServiceClient& GetKeyServiceClient() {
        return *keyServiceClient_;
    }
    
    // 获取密钥生命周期管理客户端
    KeyLifecycleClient& GetKeyLifecycleClient() {
        return *keyLifecycleClient_;
    }
    
private:
    std::unique_ptr<KeyServiceClient> keyServiceClient_;
    std::unique_ptr<KeyLifecycleClient> keyLifecycleClient_;
};

// 构造函数
ApiClient::ApiClient(const ApiClientConfig& config)
    : pImpl_(std::make_unique<Impl>(config)) {
}

// 析构函数
ApiClient::~ApiClient() = default;

// 获取密钥服务客户端
KeyServiceClient& ApiClient::GetKeyServiceClient() {
    return pImpl_->GetKeyServiceClient();
}

// 获取密钥生命周期管理客户端
KeyLifecycleClient& ApiClient::GetKeyLifecycleClient() {
    return pImpl_->GetKeyLifecycleClient();
}

} // namespace api
} // namespace crypto 