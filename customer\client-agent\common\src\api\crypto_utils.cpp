#include "crypto_utils.h"
#include "api/encoding_utils.h"
#include <openssl/evp.h>
#include <openssl/sha.h>
#include <openssl/md5.h>
#include <openssl/rand.h>
#include <openssl/err.h>
#include <fstream>
#include <iomanip>
#include <sstream>
#include <stdexcept>
#include <algorithm>
#include <thread>
#ifdef __linux__
#include <fcntl.h>
#include <unistd.h>
#endif
#ifdef __APPLE__
#include <Security/Security.h>
#endif

namespace crypto {
namespace api {

// 辅助函数，用于记录OpenSSL的错误信息
static void logOpenSSLError(const std::string& context_message) {
    unsigned long err_code;
    char err_buf[256];
    std::string full_error_message = context_message;
    while ((err_code = ERR_get_error()) != 0) {
        ERR_error_string_n(err_code, err_buf, sizeof(err_buf));
        full_error_message += " | OpenSSL Error: " + std::string(err_buf);
    }
    // TODO: Replace fprintf with a proper logging mechanism consistent with logError, logWarn etc.
    fprintf(stderr, "[CRYPTO_UTILS] [OPENSSL_ERROR] %s\n", full_error_message.c_str());
}

// 散列相关函数实现
std::string CryptoUtils::ComputeHash(const std::string& data, HashAlgorithm algorithm) {
    EVP_MD_CTX* mdctx = EVP_MD_CTX_new();
    const EVP_MD* md = nullptr;
    
    switch(algorithm) {
        case HashAlgorithm::SHA256:
            md = EVP_sha256();
            break;
        case HashAlgorithm::SHA512:
            md = EVP_sha512();
            break;
        case HashAlgorithm::MD5:
            md = EVP_md5();
            break;
        default:
            md = EVP_sha256();
    }
    
    unsigned char hash[EVP_MAX_MD_SIZE];
    unsigned int lengthOfHash = 0;
    
    EVP_DigestInit_ex(mdctx, md, nullptr);
    EVP_DigestUpdate(mdctx, data.c_str(), data.size());
    EVP_DigestFinal_ex(mdctx, hash, &lengthOfHash);
    EVP_MD_CTX_free(mdctx);
    
    // 使用统一的十六进制编码实现
    return crypto::encoding::HexEncode(std::vector<uint8_t>(hash, hash + lengthOfHash));
}

std::optional<std::string> CryptoUtils::ComputeFileHash(const std::string& filePath, HashAlgorithm algorithm) {
    std::ifstream file(filePath, std::ios::binary);
    if (!file) {
        return std::nullopt;
    }
    
    EVP_MD_CTX* mdctx = EVP_MD_CTX_new();
    const EVP_MD* md = nullptr;
    
    switch(algorithm) {
        case HashAlgorithm::SHA256:
            md = EVP_sha256();
            break;
        case HashAlgorithm::SHA512:
            md = EVP_sha512();
            break;
        case HashAlgorithm::MD5:
            md = EVP_md5();
            break;
        default:
            md = EVP_sha256();
    }
    
    unsigned char hash[EVP_MAX_MD_SIZE];
    unsigned int lengthOfHash = 0;
    
    EVP_DigestInit_ex(mdctx, md, nullptr);
    
    const size_t bufferSize = 8192;
    char buffer[bufferSize];
    
    while (file) {
        file.read(buffer, bufferSize);
        if (file.gcount() > 0) {
            EVP_DigestUpdate(mdctx, buffer, file.gcount());
        }
    }
    
    EVP_DigestFinal_ex(mdctx, hash, &lengthOfHash);
    EVP_MD_CTX_free(mdctx);
    
    // 使用统一的十六进制编码实现
    return crypto::encoding::HexEncode(std::vector<uint8_t>(hash, hash + lengthOfHash));
}

// 简单随机数质量检测：检查是否全为0
bool CheckRandomQualitySimple(const std::vector<uint8_t>& data) {
    return std::any_of(data.begin(), data.end(), [](uint8_t b){ return b != 0; });
}

// 尝试从 /dev/urandom 读取随机数 (Linux/macOS 备选方案)
bool ReadFromDevUrandom(std::vector<uint8_t>& buffer) {
#if defined(__linux__) || defined(__APPLE__)
    int fd = open("/dev/urandom", O_RDONLY);
    if (fd < 0) {
        perror("Failed to open /dev/urandom");
        return false;
    }
    
    size_t bytes_read = 0;
    while (bytes_read < buffer.size()) {
        ssize_t result = read(fd, buffer.data() + bytes_read, buffer.size() - bytes_read);
        if (result < 0) { // 处理 EINTR 等错误
            if (errno == EINTR) continue;
            perror("Failed to read from /dev/urandom");
            close(fd);
            return false;
        }
        if (result == 0) { // EOF? 不应该发生
             fprintf(stderr, "Read 0 bytes from /dev/urandom\n");
             close(fd);
             return false;
        }
        bytes_read += result;
    }
    
    close(fd);
    return true;
#else
    // Windows 或其他平台不支持此方法
    return false;
#endif
}

std::vector<uint8_t> CryptoUtils::GenerateRandomBytes(size_t length) {
    std::vector<uint8_t> result(length);
    int retries = 0;
    const int MAX_RETRIES = 3;
    const int RETRY_DELAY_MS = 20; // 稍微增加延迟

    auto logError = [](const std::string& msg) {
        // TODO: Replace with actual logging framework
        fprintf(stderr, "[CRYPTO_UTILS] [ERROR] %s\n", msg.c_str());
    };
    auto logWarn = [](const std::string& msg) {
        fprintf(stderr, "[CRYPTO_UTILS] [WARN] %s\n", msg.c_str());
    };
     auto logInfo = [](const std::string& msg) {
        fprintf(stdout, "[CRYPTO_UTILS] [INFO] %s\n", msg.c_str());
    };
    auto logCritical = [](const std::string& msg) {
        fprintf(stderr, "[CRYPTO_UTILS] [CRITICAL] %s\n", msg.c_str());
    };

    while (retries < MAX_RETRIES) {
        logInfo("Attempting to generate random bytes using OpenSSL RAND_bytes (try " +
                std::to_string(retries + 1) + "/" + std::to_string(MAX_RETRIES) + ")");
        if (RAND_bytes(result.data(), static_cast<int>(length)) == 1) {
            if (CheckRandomQualitySimple(result)) {
                logInfo("RAND_bytes successful and passed quality check.");
                return result;
            } else {
                logWarn("RAND_bytes returned success but data failed simple quality check (all zeros).");
                // 继续尝试，可能只是暂时问题
            }
        }
        
        // 记录 OpenSSL 错误
        unsigned long err = ERR_get_error();
        char errBuffer[256];
        ERR_error_string_n(err, errBuffer, sizeof(errBuffer));
        logError("OpenSSL RAND_bytes failed: " + std::string(errBuffer) + 
                 " (try " + std::to_string(retries + 1) + "/" + std::to_string(MAX_RETRIES) + ")");
        
        // 尝试备选方案: /dev/urandom (Linux/macOS)
#if defined(__linux__) || defined(__APPLE__)
        logInfo("Attempting fallback: reading from /dev/urandom");
        if (ReadFromDevUrandom(result)) {
            if (CheckRandomQualitySimple(result)) {
                 logInfo("/dev/urandom read successful and passed quality check.");
                 return result;
            } else {
                 logWarn("/dev/urandom read successful but data failed simple quality check (all zeros).");
                 // 继续重试主方法
            }
        } else {
             logError("Fallback /dev/urandom failed.");
        }
#endif
        // TODO: 可以添加 Windows 平台的备选方案，例如直接调用 BCryptGenRandom，尽管 RAND_bytes 可能已经用了它
        
        retries++;
        if (retries < MAX_RETRIES) {
            std::this_thread::sleep_for(std::chrono::milliseconds(RETRY_DELAY_MS * retries));
        }
    }
    
    // 达到最大重试次数仍然失败
    logCritical("Random number generation failed after multiple retries and fallbacks.");
    throw std::runtime_error("Failed to generate secure random numbers after multiple attempts.");
}

std::vector<uint8_t> CryptoUtils::GenerateKey(SymmetricAlgorithm algorithm) {
    size_t keySize = 0;
    
    switch(algorithm) {
        case SymmetricAlgorithm::AES_256_GCM:
        case SymmetricAlgorithm::AES_256_CBC:
            keySize = 32; // 256 bits
            break;
        case SymmetricAlgorithm::ChaCha20Poly1305:
            keySize = 32; // 256 bits
            break;
        case SymmetricAlgorithm::SM4_128_GCM:
            keySize = 16; // 128 bits for SM4
            break;
        default:
            logWarn("GenerateKey called with unknown or unsupported algorithm, defaulting to 32 bytes key size.");
            keySize = 32;
    }
    
    if (keySize == 0) {
        logError("Key size cannot be zero. Algorithm: " + std::to_string(static_cast<int>(algorithm)));
        throw std::invalid_argument("Key size cannot be zero for the specified algorithm.");
    }
    return GenerateRandomBytes(keySize);
}

// 对称加密实现
std::vector<uint8_t> CryptoUtils::EncryptSymmetric(
    const std::vector<uint8_t>& plaintext,
    const std::vector<uint8_t>& key,
    SymmetricAlgorithm algorithm,
    const std::vector<uint8_t>& iv) {
    
    EVP_CIPHER_CTX* ctx = EVP_CIPHER_CTX_new();
    if (!ctx) {
        logCritical("Failed to create EVP_CIPHER_CTX");
        throw std::runtime_error("EVP_CIPHER_CTX_new failed");
    }

    const EVP_CIPHER* cipher = nullptr;
    size_t expectedKeySize = 0;
    size_t expectedIvSize = 0;
    size_t tagSize = 0; // 仅用于AEAD算法如GCM
    
    // 设置加密算法、预期密钥大小、IV大小和Tag大小
    switch(algorithm) {
        case SymmetricAlgorithm::AES_256_GCM:
            cipher = EVP_aes_256_gcm();
            expectedKeySize = 32;
            expectedIvSize = 12; // 96 bits for GCM is recommended
            tagSize = 16;        // 128 bits authentication tag
            break;
        case SymmetricAlgorithm::AES_256_CBC:
            cipher = EVP_aes_256_cbc();
            expectedKeySize = 32;
            expectedIvSize = 16; // AES block size
            tagSize = 0;         // No authentication tag for CBC
            break;
        case SymmetricAlgorithm::ChaCha20Poly1305:
            cipher = EVP_chacha20_poly1305();
            expectedKeySize = 32;
            expectedIvSize = 12; // ChaCha20-Poly1305 uses a 12-byte nonce
            tagSize = 16;        // 128 bits authentication tag
            break;
        case SymmetricAlgorithm::SM4_128_GCM:
            cipher = EVP_sm4_gcm();
            expectedKeySize = 16; // 128 bits for SM4
            expectedIvSize = 12;  // 96 bits for GCM is recommended
            tagSize = 16;         // 128 bits authentication tag
            break;
        default:
            EVP_CIPHER_CTX_free(ctx);
            logError("EncryptSymmetric called with unknown or unsupported algorithm.");
            throw std::invalid_argument("Unsupported symmetric algorithm specified for encryption.");
    }

    if (!cipher) {
        EVP_CIPHER_CTX_free(ctx);
        logError("Cipher could not be initialized for algorithm: " + std::to_string(static_cast<int>(algorithm)) + ". Check OpenSSL version and support.");
        throw std::runtime_error("Failed to initialize cipher. The algorithm might not be supported by the linked OpenSSL library.");
    }

    // 校验密钥和IV的大小
    if (key.size() != expectedKeySize) {
        EVP_CIPHER_CTX_free(ctx);
        logError("Invalid key size for the specified algorithm. Expected " + std::to_string(expectedKeySize) + " bytes, got " + std::to_string(key.size()) + " bytes.");
        throw std::invalid_argument("Invalid key size.");
    }
    if (iv.size() != expectedIvSize) {
        EVP_CIPHER_CTX_free(ctx);
        logError("Invalid IV size for the specified algorithm. Expected " + std::to_string(expectedIvSize) + " bytes, got " + std::to_string(iv.size()) + " bytes.");
        throw std::invalid_argument("Invalid IV size.");
    }

    // 初始化加密操作
    if (1 != EVP_EncryptInit_ex(ctx, cipher, nullptr, nullptr, nullptr)) {
        EVP_CIPHER_CTX_free(ctx);
        logOpenSSLError("EVP_EncryptInit_ex (initial) failed");
        throw std::runtime_error("Encryption initialization failed.");
    }

    // 对于GCM等需要设置IV长度的模式 (ChaCha20Poly1305也需要)
    if (algorithm == SymmetricAlgorithm::AES_256_GCM || algorithm == SymmetricAlgorithm::SM4_128_GCM || algorithm == SymmetricAlgorithm::ChaCha20Poly1305) {
        if (1 != EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_SET_IVLEN, static_cast<int>(iv.size()), nullptr)) {
            EVP_CIPHER_CTX_free(ctx);
            logOpenSSLError("EVP_CTRL_AEAD_SET_IVLEN failed");
            throw std::runtime_error("Setting IV length failed for AEAD mode.");
        }
    }

    // 设置密钥和IV
    if (1 != EVP_EncryptInit_ex(ctx, nullptr, nullptr, key.data(), iv.data())) {
        EVP_CIPHER_CTX_free(ctx);
        logOpenSSLError("EVP_EncryptInit_ex (setting key and IV) failed");
        throw std::runtime_error("Encryption key/IV setup failed.");
    }

    // 如果有AAD (附加认证数据)，在这里设置
    // const unsigned char* aad = ...;
    // int aad_len = ...;
    // if (1 != EVP_EncryptUpdate(ctx, nullptr, &outlen, aad, aad_len)) { /* error */ }
    // 当前设计不显式使用AAD，GCM自身处理认证

    int outlen = 0;
    int finallen = 0;
    std::vector<uint8_t> ciphertext_buffer(plaintext.size() + EVP_CIPHER_block_size(cipher)); // 预分配足够空间

    // 执行加密
    if (1 != EVP_EncryptUpdate(ctx, ciphertext_buffer.data(), &outlen, plaintext.data(), static_cast<int>(plaintext.size()))) {
        EVP_CIPHER_CTX_free(ctx);
        logOpenSSLError("EVP_EncryptUpdate failed");
        throw std::runtime_error("Encryption update failed.");
    }

    // 完成加密操作
    if (1 != EVP_EncryptFinal_ex(ctx, ciphertext_buffer.data() + outlen, &finallen)) {
        EVP_CIPHER_CTX_free(ctx);
        logOpenSSLError("EVP_EncryptFinal_ex failed");
        throw std::runtime_error("Encryption finalization failed.");
    }
    
    ciphertext_buffer.resize(outlen + finallen);

    std::vector<uint8_t> tag_buffer;
    if (tagSize > 0) {
        tag_buffer.resize(tagSize);
        if (1 != EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_GET_TAG, static_cast<int>(tagSize), tag_buffer.data())) {
            EVP_CIPHER_CTX_free(ctx);
            logOpenSSLError("EVP_CTRL_AEAD_GET_TAG failed");
            throw std::runtime_error("Failed to get GCM/Poly1305 authentication tag.");
        }
    }

    EVP_CIPHER_CTX_free(ctx);

    // 组装最终结果: IV + Ciphertext + Tag (for GCM/Poly1305)
    // 或 IV + Ciphertext (for CBC - though CBC here doesn't auto-prepend IV)
    // 根据头文件约定和之前的讨论，EncryptSymmetric 返回 IV + Ciphertext + Tag
    std::vector<uint8_t> combined_output;
    combined_output.reserve(iv.size() + ciphertext_buffer.size() + tag_buffer.size());
    combined_output.insert(combined_output.end(), iv.begin(), iv.end());
    combined_output.insert(combined_output.end(), ciphertext_buffer.begin(), ciphertext_buffer.end());
    if (!tag_buffer.empty()) {
        combined_output.insert(combined_output.end(), tag_buffer.begin(), tag_buffer.end());
    }

    return combined_output;
}

// 对称解密实现
std::vector<uint8_t> CryptoUtils::DecryptSymmetric(
    const std::vector<uint8_t>& combined_data,
    const std::vector<uint8_t>& key,
    SymmetricAlgorithm algorithm)
{
    EVP_CIPHER_CTX* ctx = EVP_CIPHER_CTX_new();
    if (!ctx) {
        logCritical("Failed to create EVP_CIPHER_CTX for decryption");
        throw std::runtime_error("EVP_CIPHER_CTX_new failed for decryption");
    }

    const EVP_CIPHER* cipher = nullptr;
    size_t expectedKeySize = 0;
    size_t ivSize = 0;          // Actual IV size to be extracted from combined_data
    size_t tagSize = 0;         // For AEAD modes

    switch(algorithm) {
        case SymmetricAlgorithm::AES_256_GCM:
            cipher = EVP_aes_256_gcm();
            expectedKeySize = 32;
            ivSize = 12; 
            tagSize = 16;
            break;
        case SymmetricAlgorithm::AES_256_CBC:
            cipher = EVP_aes_256_cbc();
            expectedKeySize = 32;
            ivSize = 16; 
            tagSize = 0;
            break;
        case SymmetricAlgorithm::ChaCha20Poly1305:
            cipher = EVP_chacha20_poly1305();
            expectedKeySize = 32;
            ivSize = 12;
            tagSize = 16;
            break;
        case SymmetricAlgorithm::SM4_128_GCM: // 新增
            cipher = EVP_sm4_gcm();
            expectedKeySize = 16; // 128 bits for SM4
            ivSize = 12;          // 96 bits for GCM
            tagSize = 16;         // 128 bits authentication tag
            break;
        default:
            EVP_CIPHER_CTX_free(ctx);
            logError("DecryptSymmetric called with unknown or unsupported algorithm.");
            throw std::invalid_argument("Unsupported symmetric algorithm specified for decryption.");
    }

    if (!cipher) {
        EVP_CIPHER_CTX_free(ctx);
        logError("Cipher could not be initialized for decryption algorithm: " + std::to_string(static_cast<int>(algorithm)) + ". Check OpenSSL version and support.");
        throw std::runtime_error("Failed to initialize cipher for decryption. The algorithm might not be supported by the linked OpenSSL library.");
    }

    if (key.size() != expectedKeySize) {
        EVP_CIPHER_CTX_free(ctx);
        logError("Invalid key size for decryption. Expected " + std::to_string(expectedKeySize) + " bytes, got " + std::to_string(key.size()) + " bytes.");
        throw std::invalid_argument("Invalid key size for decryption.");
    }

    if (combined_data.size() < ivSize + (tagSize > 0 ? tagSize : 0)) { // 基本长度检查: IV + Tag (如果是AEAD)
        EVP_CIPHER_CTX_free(ctx);
        logError("Combined data is too short to contain IV and Tag (if applicable) for decryption.");
        throw std::invalid_argument("Combined data too short.");
    }

    std::vector<uint8_t> iv_extracted(ivSize);
    std::copy(combined_data.begin(), combined_data.begin() + ivSize, iv_extracted.begin());

    std::vector<uint8_t> actual_ciphertext;
    std::vector<uint8_t> tag_extracted;

    if (tagSize > 0) { // AEAD模式 (GCM, Poly1305)
        if (combined_data.size() < ivSize + tagSize) { // 确保至少有空间给IV和Tag
             EVP_CIPHER_CTX_free(ctx);
             logError("Combined data is too short for IV and Tag in AEAD mode.");
             throw std::invalid_argument("Combined data too short for AEAD IV and Tag.");
        }
        actual_ciphertext.assign(combined_data.begin() + ivSize, combined_data.end() - tagSize);
        tag_extracted.resize(tagSize);
        std::copy(combined_data.end() - tagSize, combined_data.end(), tag_extracted.begin());
    } else { // 非AEAD模式 (CBC)
        actual_ciphertext.assign(combined_data.begin() + ivSize, combined_data.end());
    }
    
    if (actual_ciphertext.empty() && tagSize > 0) {
        // 对于GCM等AEAD模式，即使明文为空，也应该有IV和Tag。
        // 如果actual_ciphertext为空，但有tag，这可能是有效的空明文加密，解密时需要处理。
        // OpenSSL的EVP_DecryptUpdate可以处理输入长度为0的情况。
        logInfo("Actual ciphertext part is empty for AEAD decryption, proceeding (might be valid empty plaintext encryption).");
    } else if (actual_ciphertext.empty() && tagSize == 0) {
        // 对于CBC等模式，如果IV之后的部分为空，则明文也为空。
        logInfo("Actual ciphertext part is empty for non-AEAD decryption, likely means empty plaintext.");
         EVP_CIPHER_CTX_free(ctx);
        return {}; // 返回空vector代表空明文
    }

    // 初始化解密操作
    if (1 != EVP_DecryptInit_ex(ctx, cipher, nullptr, nullptr, nullptr)) {
        EVP_CIPHER_CTX_free(ctx);
        logOpenSSLError("EVP_DecryptInit_ex (initial) failed for decryption");
        throw std::runtime_error("Decryption initialization failed.");
    }

    // 对于GCM等需要设置IV长度的模式
    if (algorithm == SymmetricAlgorithm::AES_256_GCM || algorithm == SymmetricAlgorithm::SM4_128_GCM || algorithm == SymmetricAlgorithm::ChaCha20Poly1305) {
        if (1 != EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_SET_IVLEN, static_cast<int>(iv_extracted.size()), nullptr)) {
            EVP_CIPHER_CTX_free(ctx);
            logOpenSSLError("EVP_CTRL_AEAD_SET_IVLEN failed for decryption");
            throw std::runtime_error("Setting IV length failed for AEAD decryption.");
        }
    }

    // 设置密钥和IV
    if (1 != EVP_DecryptInit_ex(ctx, nullptr, nullptr, key.data(), iv_extracted.data())) {
        EVP_CIPHER_CTX_free(ctx);
        logOpenSSLError("EVP_DecryptInit_ex (setting key and IV) failed for decryption");
        throw std::runtime_error("Decryption key/IV setup failed.");
    }

    // 如果有AAD，在这里设置 (当前设计不使用外部AAD，依赖GCM自身)
    // if (1 != EVP_DecryptUpdate(ctx, nullptr, &outlen, aad, aad_len)) { /* error */ }

    int outlen = 0;
    int finallen = 0;
    std::vector<uint8_t> plaintext_buffer(actual_ciphertext.size() + EVP_CIPHER_block_size(cipher)); // 预留足够空间，CBC可能需要多一个块用于padding处理

    // 执行解密
    if (1 != EVP_DecryptUpdate(ctx, plaintext_buffer.data(), &outlen, actual_ciphertext.data(), static_cast<int>(actual_ciphertext.size()))) {
        EVP_CIPHER_CTX_free(ctx);
        logOpenSSLError("EVP_DecryptUpdate failed for decryption");
        throw std::runtime_error("Decryption update failed.");
    }

    // 对于AEAD模式，在Final之前设置期望的认证标签
    if (tagSize > 0) {
        if (1 != EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_AEAD_SET_TAG, static_cast<int>(tag_extracted.size()), tag_extracted.data())) {
            EVP_CIPHER_CTX_free(ctx);
            logOpenSSLError("EVP_CTRL_AEAD_SET_TAG failed for decryption");
            throw std::runtime_error("Failed to set GCM/Poly1305 authentication tag for decryption.");
        }
    }

    // 完成解密操作
    // 对于AEAD (GCM, Poly1305)，EVP_DecryptFinal_ex 会校验Tag。如果校验失败，返回0或负值。
    if (1 != EVP_DecryptFinal_ex(ctx, plaintext_buffer.data() + outlen, &finallen)) {
        EVP_CIPHER_CTX_free(ctx);
        // 对于AEAD模式，这通常意味着认证失败（Tag不匹配）
        if (algorithm == SymmetricAlgorithm::AES_256_GCM || algorithm == SymmetricAlgorithm::SM4_128_GCM || algorithm == SymmetricAlgorithm::ChaCha20Poly1305) {
            logError("Decryption finalization failed. For AEAD modes, this usually means AUTHENTICATION FAILURE (TAG MISMATCH).");
            throw std::runtime_error("Authentication failed during decryption (tag mismatch).");
        } else {
            logOpenSSLError("EVP_DecryptFinal_ex failed for decryption (non-AEAD)");
            throw std::runtime_error("Decryption finalization failed (non-AEAD).");
        }
    }

    plaintext_buffer.resize(outlen + finallen);
    EVP_CIPHER_CTX_free(ctx);

    return plaintext_buffer;
}

// Base64和十六进制编解码函数 - 使用统一编码接口实现
std::string CryptoUtils::Base64Encode(const std::vector<uint8_t>& data) {
    return crypto::encoding::Base64Encode(data);
}

std::vector<uint8_t> CryptoUtils::Base64Decode(const std::string& base64String) {
    return crypto::encoding::Base64Decode(base64String);
}

std::string CryptoUtils::HexEncode(const std::vector<uint8_t>& data) {
    return crypto::encoding::HexEncode(data, false); // 使用小写字母
}

std::vector<uint8_t> CryptoUtils::HexDecode(const std::string& hexString) {
    return crypto::encoding::HexDecode(hexString);
}

// 字符串与二进制转换
std::vector<uint8_t> CryptoUtils::StringToBytes(const std::string& str) {
    return std::vector<uint8_t>(str.begin(), str.end());
}

std::string CryptoUtils::BytesToString(const std::vector<uint8_t>& bytes) {
    return std::string(bytes.begin(), bytes.end());
}

} // namespace api
} // namespace crypto
} // namespace crypto