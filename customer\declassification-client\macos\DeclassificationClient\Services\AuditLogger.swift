import Foundation
import SwiftUI
import Combine

/// 审计日志记录器
/// 负责记录所有系统操作、用户行为和安全事件
@MainActor
class AuditLogger: ObservableObject {
    
    // MARK: - Singleton
    static let shared = AuditLogger()
    
    // MARK: - Published Properties
    @Published var recentLogs: [AuditLog] = []
    @Published var isLogging = true
    
    // MARK: - Private Properties
    private var allLogs: [AuditLog] = []
    private let maxRecentLogs = 100
    private let maxTotalLogs = 10000
    private let logQueue = DispatchQueue(label: "audit.logger", qos: .utility)
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - File Paths
    private let logDirectory: URL
    private let currentLogFile: URL
    
    // MARK: - Initialization
    private init() {
        // 设置日志目录
        let appSupport = FileManager.default.urls(for: .applicationSupportDirectory, in: .userDomainMask).first!
        logDirectory = appSupport.appendingPathComponent("DeclassificationClient/Logs")
        
        // 创建日志目录
        try? FileManager.default.createDirectory(at: logDirectory, withIntermediateDirectories: true)
        
        // 设置当前日志文件
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let todayString = dateFormatter.string(from: Date())
        currentLogFile = logDirectory.appendingPathComponent("audit_\(todayString).json")
        
        setupPeriodicCleanup()
        loadRecentLogs()
    }
    
    // MARK: - Public Methods
    
    /// 记录审计日志事件
    func logEvent(
        type: AuditLog.AuditLogType,
        message: String,
        details: [String: String]? = nil
    ) async {
        guard isLogging else { return }
        
        let log = AuditLog(
            type: type,
            userId: getCurrentUserId(),
            userName: getCurrentUserName(),
            action: extractAction(from: message),
            message: message,
            details: details
        )
        
        await addLog(log)
    }
    
    /// 记录用户操作
    func logUserAction(_ action: String, details: [String: String]? = nil) async {
        await logEvent(
            type: .userAction,
            message: action,
            details: details
        )
    }
    
    /// 记录系统事件
    func logSystemEvent(_ event: String, details: [String: String]? = nil) async {
        await logEvent(
            type: .systemEvent,
            message: event,
            details: details
        )
    }
    
    /// 记录安全事件
    func logSecurityEvent(_ event: String, details: [String: String]? = nil) async {
        await logEvent(
            type: .securityEvent,
            message: event,
            details: details
        )
    }
    
    /// 记录错误事件
    func logError(_ error: String, details: [String: String]? = nil) async {
        await logEvent(
            type: .errorEvent,
            message: error,
            details: details
        )
    }
    
    /// 记录任务事件
    func logTaskEvent(_ event: String, details: [String: String]? = nil) async {
        await logEvent(
            type: .taskEvent,
            message: event,
            details: details
        )
    }
    
    /// 导出日志到指定位置
    func exportLogs(to url: URL) async {
        do {
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            encoder.outputFormatting = [.prettyPrinted, .sortedKeys]
            
            let exportData = ExportData(
                exportDate: Date(),
                totalLogs: allLogs.count,
                dateRange: getDateRange(),
                logs: allLogs
            )
            
            let data = try encoder.encode(exportData)
            try data.write(to: url)
            
            await logEvent(
                type: .systemEvent,
                message: "导出审计日志",
                details: [
                    "exportPath": url.path,
                    "logCount": String(allLogs.count)
                ]
            )
            
        } catch {
            await logEvent(
                type: .errorEvent,
                message: "导出日志失败",
                details: ["error": error.localizedDescription]
            )
        }
    }
    
    /// 搜索日志
    func searchLogs(query: String, type: AuditLog.AuditLogType? = nil) -> [AuditLog] {
        var filteredLogs = allLogs
        
        // 按类型过滤
        if let type = type {
            filteredLogs = filteredLogs.filter { $0.type == type }
        }
        
        // 按查询字符串过滤
        if !query.isEmpty {
            filteredLogs = filteredLogs.filter { log in
                log.message.localizedCaseInsensitiveContains(query) ||
                log.action.localizedCaseInsensitiveContains(query) ||
                log.userName.localizedCaseInsensitiveContains(query) ||
                (log.details?.values.contains { $0.localizedCaseInsensitiveContains(query) } ?? false)
            }
        }
        
        return filteredLogs.sorted { $0.timestamp > $1.timestamp }
    }
    
    /// 获取日志统计信息
    func getLogStatistics() -> LogStatistics {
        let calendar = Calendar.current
        let now = Date()
        let today = calendar.startOfDay(for: now)
        let weekAgo = calendar.date(byAdding: .day, value: -7, to: today)!
        let monthAgo = calendar.date(byAdding: .month, value: -1, to: today)!
        
        let todayLogs = allLogs.filter { calendar.isDate($0.timestamp, inSameDayAs: today) }
        let weekLogs = allLogs.filter { $0.timestamp >= weekAgo }
        let monthLogs = allLogs.filter { $0.timestamp >= monthAgo }
        
        let typeDistribution = Dictionary(grouping: allLogs) { $0.type }
            .mapValues { $0.count }
        
        return LogStatistics(
            totalLogs: allLogs.count,
            todayLogs: todayLogs.count,
            weekLogs: weekLogs.count,
            monthLogs: monthLogs.count,
            typeDistribution: typeDistribution,
            oldestLog: allLogs.min { $0.timestamp < $1.timestamp }?.timestamp,
            newestLog: allLogs.max { $0.timestamp < $1.timestamp }?.timestamp
        )
    }
    
    /// 清理过期日志
    func cleanupOldLogs() async {
        let calendar = Calendar.current
        let cutoffDate = calendar.date(byAdding: .day, value: -90, to: Date())!
        
        let beforeCount = allLogs.count
        allLogs.removeAll { $0.timestamp < cutoffDate }
        let afterCount = allLogs.count
        
        // 更新最近日志列表
        updateRecentLogs()
        
        // 保存到文件
        await saveLogsToFile()
        
        await logEvent(
            type: .systemEvent,
            message: "清理过期日志",
            details: [
                "deletedCount": String(beforeCount - afterCount),
                "remainingCount": String(afterCount),
                "cutoffDate": ISO8601DateFormatter().string(from: cutoffDate)
            ]
        )
    }
    
    /// 设置日志级别
    func setLoggingEnabled(_ enabled: Bool) async {
        isLogging = enabled
        
        await logEvent(
            type: .systemEvent,
            message: enabled ? "启用日志记录" : "禁用日志记录"
        )
    }
    
    // MARK: - Private Methods
    
    private func addLog(_ log: AuditLog) async {
        // 添加到内存中的日志列表
        allLogs.append(log)
        
        // 限制内存中的日志数量
        if allLogs.count > maxTotalLogs {
            allLogs.removeFirst(allLogs.count - maxTotalLogs)
        }
        
        // 更新最近日志列表
        updateRecentLogs()
        
        // 异步保存到文件
        Task.detached { [weak self] in
            await self?.saveLogToFile(log)
        }
    }
    
    private func updateRecentLogs() {
        recentLogs = Array(allLogs.suffix(maxRecentLogs).reversed())
    }
    
    private func saveLogToFile(_ log: AuditLog) async {
        await logQueue.asyncAndWait { [weak self] in
            guard let self = self else { return }
            
            do {
                let encoder = JSONEncoder()
                encoder.dateEncodingStrategy = .iso8601
                
                let logData = try encoder.encode(log)
                let logString = String(data: logData, encoding: .utf8)! + "\n"
                
                if FileManager.default.fileExists(atPath: self.currentLogFile.path) {
                    let fileHandle = try FileHandle(forWritingTo: self.currentLogFile)
                    fileHandle.seekToEndOfFile()
                    fileHandle.write(logString.data(using: .utf8)!)
                    fileHandle.closeFile()
                } else {
                    try logString.write(to: self.currentLogFile, atomically: true, encoding: .utf8)
                }
                
            } catch {
                print("保存日志失败: \(error)")
            }
        }
    }
    
    private func saveLogsToFile() async {
        await logQueue.asyncAndWait { [weak self] in
            guard let self = self else { return }
            
            do {
                let encoder = JSONEncoder()
                encoder.dateEncodingStrategy = .iso8601
                encoder.outputFormatting = .prettyPrinted
                
                let data = try encoder.encode(self.allLogs)
                try data.write(to: self.currentLogFile)
                
            } catch {
                print("保存所有日志失败: \(error)")
            }
        }
    }
    
    private func loadRecentLogs() {
        logQueue.async { [weak self] in
            guard let self = self else { return }
            
            do {
                guard FileManager.default.fileExists(atPath: self.currentLogFile.path) else { return }
                
                let data = try Data(contentsOf: self.currentLogFile)
                let decoder = JSONDecoder()
                decoder.dateDecodingStrategy = .iso8601
                
                // 尝试解码为日志数组
                if let logs = try? decoder.decode([AuditLog].self, from: data) {
                    Task { @MainActor in
                        self.allLogs = logs
                        self.updateRecentLogs()
                    }
                } else {
                    // 如果失败，尝试逐行解码
                    let content = String(data: data, encoding: .utf8) ?? ""
                    let lines = content.components(separatedBy: .newlines)
                    var logs: [AuditLog] = []
                    
                    for line in lines {
                        if !line.isEmpty, let lineData = line.data(using: .utf8) {
                            if let log = try? decoder.decode(AuditLog.self, from: lineData) {
                                logs.append(log)
                            }
                        }
                    }
                    
                    Task { @MainActor in
                        self.allLogs = logs
                        self.updateRecentLogs()
                    }
                }
                
            } catch {
                print("加载日志失败: \(error)")
            }
        }
    }
    
    private func setupPeriodicCleanup() {
        // 每天午夜清理一次过期日志
        Timer.publish(every: 86400, on: .main, in: .common) // 24小时
            .autoconnect()
            .sink { [weak self] _ in
                Task {
                    await self?.cleanupOldLogs()
                }
            }
            .store(in: &cancellables)
    }
    
    private func getCurrentUserId() -> String {
        return NSUserName()
    }
    
    private func getCurrentUserName() -> String {
        return NSFullUserName()
    }
    
    private func extractAction(from message: String) -> String {
        // 简单的动作提取逻辑
        let words = message.components(separatedBy: .whitespaces)
        return words.first ?? "unknown"
    }
    
    private func getDateRange() -> DateRange? {
        guard let oldest = allLogs.min(by: { $0.timestamp < $1.timestamp }),
              let newest = allLogs.max(by: { $0.timestamp < $1.timestamp }) else {
            return nil
        }
        
        return DateRange(start: oldest.timestamp, end: newest.timestamp)
    }
}

// MARK: - Supporting Types

struct ExportData: Codable {
    let exportDate: Date
    let totalLogs: Int
    let dateRange: DateRange?
    let logs: [AuditLog]
}

struct DateRange: Codable {
    let start: Date
    let end: Date
}

struct LogStatistics {
    let totalLogs: Int
    let todayLogs: Int
    let weekLogs: Int
    let monthLogs: Int
    let typeDistribution: [AuditLog.AuditLogType: Int]
    let oldestLog: Date?
    let newestLog: Date?
    
    var averageLogsPerDay: Double {
        guard let oldest = oldestLog else { return 0 }
        let daysDiff = Calendar.current.dateComponents([.day], from: oldest, to: Date()).day ?? 1
        return Double(totalLogs) / Double(max(daysDiff, 1))
    }
}

// MARK: - DispatchQueue Extension
extension DispatchQueue {
    func asyncAndWait<T>(execute work: @escaping () throws -> T) async rethrows -> T {
        return try await withCheckedThrowingContinuation { continuation in
            self.async {
                do {
                    let result = try work()
                    continuation.resume(returning: result)
                } catch {
                    continuation.resume(throwing: error)
                }
            }
        }
    }
} 