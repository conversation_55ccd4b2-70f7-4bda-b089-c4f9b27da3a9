/*
 * crypto.h
 * 
 * 加密模块头文件
 * 定义加密相关的数据结构和函数
 */

#pragma once

#include <fltKernel.h>

// 加密算法类型
typedef enum _ENC_ALGORITHM_TYPE {
    AlgorithmSM4 = 0,     // 国密 SM4 算法
    AlgorithmAES256,      // AES-256 算法
    AlgorithmAES512,      // AES-512 算法
    AlgorithmMaximum      // 边界值
} ENC_ALGORITHM_TYPE;

// 加密模式类型
typedef enum _ENC_MODE_TYPE {
    ModeGCM = 0,          // GCM 模式 (推荐)
    ModeCBC,              // CBC 模式
    ModeCTR,              // CTR 模式
    ModeMaximum           // 边界值
} ENC_MODE_TYPE;

// 文件元数据头
#pragma pack(push, 1)
typedef struct _ENC_FILE_HEADER {
    // 标识 "ENCF"
    UCHAR Signature[4];
    
    // 版本
    USHORT Version;
    
    // 加密算法
    UCHAR Algorithm;
    
    // 加密模式
    UCHAR Mode;
    
    // 密钥版本
    ULONG KeyVersion;
    
    // IV/Nonce (16字节)
    UCHAR IV[16];
    
    // 认证标签 (16字节) - 仅用于GCM模式
    UCHAR AuthTag[16];
    
    // 元数据长度
    ULONG MetadataLength;
    
    // 原始文件大小
    ULONGLONG OriginalFileSize;
    
    // 保留字段
    UCHAR Reserved[16];
    
    // 元数据校验和
    ULONG Checksum;
    
    // 可变长元数据
    // UCHAR Metadata[MetadataLength];
    
} ENC_FILE_HEADER, *PENC_FILE_HEADER;
#pragma pack(pop)

// 加密上下文
typedef struct _ENC_CRYPTO_CONTEXT {
    // 算法类型
    ENC_ALGORITHM_TYPE Algorithm;
    
    // 模式类型
    ENC_MODE_TYPE Mode;
    
    // 密钥版本
    ULONG KeyVersion;
    
    // 密钥数据
    UCHAR Key[64];
    ULONG KeyLength;
    
    // IV/Nonce
    UCHAR IV[16];
    
    // 认证数据 (AAD)
    PVOID Aad;
    ULONG AadLength;
    
    // 算法特定上下文
    PVOID AlgorithmContext;
    
} ENC_CRYPTO_CONTEXT, *PENC_CRYPTO_CONTEXT;

// 初始化和清理函数
NTSTATUS
CryptoInitialize(
    VOID
    );

VOID
CryptoCleanup(
    VOID
    );

// 密钥管理函数
NTSTATUS
CryptoLoadKey(
    _In_ ULONG KeyVersion,
    _In_ PUCHAR KeyData,
    _In_ ULONG KeyLength
    );

NTSTATUS
CryptoRemoveKey(
    _In_ ULONG KeyVersion
    );

// 加密上下文函数
NTSTATUS
CryptoCreateContext(
    _In_ ENC_ALGORITHM_TYPE Algorithm,
    _In_ ENC_MODE_TYPE Mode,
    _In_ ULONG KeyVersion,
    _Out_ PENC_CRYPTO_CONTEXT *CryptoContext
    );

VOID
CryptoDestroyContext(
    _In_ PENC_CRYPTO_CONTEXT CryptoContext
    );

// 加解密函数
NTSTATUS
CryptoEncrypt(
    _In_ PENC_CRYPTO_CONTEXT CryptoContext,
    _In_ PVOID PlainText,
    _In_ ULONG PlainTextLength,
    _Out_ PVOID CipherText,
    _Inout_ PULONG CipherTextLength
    );

NTSTATUS
CryptoDecrypt(
    _In_ PENC_CRYPTO_CONTEXT CryptoContext,
    _In_ PVOID CipherText,
    _In_ ULONG CipherTextLength,
    _Out_ PVOID PlainText,
    _Inout_ PULONG PlainTextLength
    );

// 文件元数据函数
NTSTATUS
CryptoCreateFileHeader(
    _In_ PENC_CRYPTO_CONTEXT CryptoContext,
    _In_ ULONGLONG OriginalFileSize,
    _In_opt_ PVOID Metadata,
    _In_opt_ ULONG MetadataLength,
    _Out_ PENC_FILE_HEADER FileHeader,
    _Inout_ PULONG FileHeaderSize
    );

NTSTATUS
CryptoVerifyFileHeader(
    _In_ PENC_FILE_HEADER FileHeader,
    _In_ ULONG FileHeaderSize,
    _Out_opt_ PENC_CRYPTO_CONTEXT *CryptoContext
    );

// 哈希和校验函数
NTSTATUS
CryptoComputeChecksum(
    _In_ PVOID Data,
    _In_ ULONG DataLength,
    _Out_ PULONG Checksum
    ); 