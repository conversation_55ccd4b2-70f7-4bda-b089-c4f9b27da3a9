/*
 * key_sync_service.c
 *
 * Cryptosystem Linux 密钥同步服务实现
 * 实现与服务器的安全通信，获取密钥和策略
 */

#include "key_sync_service.h"
#include "crypto_manager.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <curl/curl.h>
#include <openssl/ssl.h>
#include <openssl/evp.h>
#include <openssl/hmac.h>
#include <json-c/json.h>
#include <sys/time.h>
#include <pthread.h>

// 配置
#define DEFAULT_SYNC_INTERVAL 3600  // 默认同步间隔(秒)
#define MAX_RESPONSE_SIZE 8192      // 最大响应大小
#define API_PATH_KEY "/api/key/get" // 密钥API路径
#define API_PATH_POLICY "/api/policy/get" // 策略API路径
#define API_PATH_REPORT "/api/log/report" // 日志上报API路径

// 同步服务配置
static struct {
    char server_url[256];      // 服务器URL
    char device_id[64];        // 设备ID
    char device_key[64];       // 设备密钥
    int initialized;           // 是否已初始化
    int sync_interval;         // 同步间隔(秒)
    pthread_t sync_thread;     // 同步线程
    int thread_running;        // 线程是否运行
    pthread_mutex_t mutex;     // 同步锁
} sync_config = {
    .initialized = 0,
    .sync_interval = DEFAULT_SYNC_INTERVAL,
    .thread_running = 0
};

// 响应数据结构
typedef struct {
    char *data;
    size_t size;
} response_data;

// CURL写回调函数
static size_t write_callback(void *contents, size_t size, size_t nmemb, void *userp) {
    size_t realsize = size * nmemb;
    response_data *resp = (response_data *)userp;
    
    // 确保不超过最大响应大小
    if (resp->size + realsize > MAX_RESPONSE_SIZE) {
        return 0;  // 中止传输
    }
    
    // 扩展缓冲区
    char *ptr = realloc(resp->data, resp->size + realsize + 1);
    if (!ptr) {
        return 0;  // 内存分配失败
    }
    
    resp->data = ptr;
    memcpy(&(resp->data[resp->size]), contents, realsize);
    resp->size += realsize;
    resp->data[resp->size] = 0;
    
    return realsize;
}

// 生成API请求签名
static char *generate_signature(const char *device_id, const char *device_key, 
                              const char *timestamp, const char *path) {
    char message[512];
    snprintf(message, sizeof(message), "%s|%s|%s", device_id, timestamp, path);
    
    unsigned char digest[EVP_MAX_MD_SIZE];
    unsigned int digest_len;
    
    HMAC(EVP_sha256(), device_key, strlen(device_key), 
         (unsigned char*)message, strlen(message), 
         digest, &digest_len);
    
    char *signature = calloc(digest_len * 2 + 1, 1);
    if (!signature) {
        return NULL;
    }
    
    for (unsigned int i = 0; i < digest_len; i++) {
        sprintf(&signature[i * 2], "%02x", digest[i]);
    }
    
    return signature;
}

// 发送API请求到服务器
static sync_status send_api_request(const char *path, json_object *request_json, 
                                  json_object **response_json) {
    CURL *curl;
    CURLcode res;
    char url[512];
    struct curl_slist *headers = NULL;
    response_data resp = { .data = malloc(1), .size = 0 };
    sync_status status = SYNC_STATUS_ERROR;
    
    if (!resp.data) {
        return SYNC_STATUS_ERROR;
    }
    resp.data[0] = '\0';
    
    // 构建完整URL
    snprintf(url, sizeof(url), "%s%s", sync_config.server_url, path);
    
    // 生成时间戳
    struct timeval tv;
    gettimeofday(&tv, NULL);
    char timestamp[32];
    snprintf(timestamp, sizeof(timestamp), "%ld", tv.tv_sec);
    
    // 生成签名
    char *signature = generate_signature(sync_config.device_id, sync_config.device_key, 
                                        timestamp, path);
    if (!signature) {
        free(resp.data);
        return SYNC_STATUS_ERROR;
    }
    
    // 初始化CURL
    curl = curl_easy_init();
    if (!curl) {
        free(signature);
        free(resp.data);
        return SYNC_STATUS_ERROR;
    }
    
    // 构建请求头
    char auth_header[256];
    snprintf(auth_header, sizeof(auth_header), "X-Device-ID: %s", sync_config.device_id);
    headers = curl_slist_append(headers, auth_header);
    
    char sig_header[256];
    snprintf(sig_header, sizeof(sig_header), "X-Signature: %s", signature);
    headers = curl_slist_append(headers, sig_header);
    
    char ts_header[256];
    snprintf(ts_header, sizeof(ts_header), "X-Timestamp: %s", timestamp);
    headers = curl_slist_append(headers, ts_header);
    
    headers = curl_slist_append(headers, "Content-Type: application/json");
    
    // 设置CURL选项
    curl_easy_setopt(curl, CURLOPT_URL, url);
    curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
    curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, write_callback);
    curl_easy_setopt(curl, CURLOPT_WRITEDATA, (void *)&resp);
    curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 1L);
    curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 2L);
    curl_easy_setopt(curl, CURLOPT_TIMEOUT, 30L);
    
    // 发送POST请求
    if (request_json) {
        const char *json_str = json_object_to_json_string(request_json);
        curl_easy_setopt(curl, CURLOPT_POSTFIELDS, json_str);
    }
    
    // 执行请求
    res = curl_easy_perform(curl);
    
    // 处理响应
    if (res == CURLE_OK) {
        long http_code = 0;
        curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &http_code);
        
        if (http_code == 200) {
            // 解析JSON响应
            *response_json = json_tokener_parse(resp.data);
            if (*response_json) {
                json_object *status_obj;
                if (json_object_object_get_ex(*response_json, "status", &status_obj)) {
                    const char *status_str = json_object_get_string(status_obj);
                    if (strcmp(status_str, "success") == 0) {
                        status = SYNC_STATUS_OK;
                    }
                }
            }
        } else if (http_code == 401 || http_code == 403) {
            status = SYNC_STATUS_UNAUTHORIZED;
        } else if (http_code >= 500) {
            status = SYNC_STATUS_SERVER;
        }
    } else {
        status = SYNC_STATUS_NETWORK;
    }
    
    // 清理
    curl_easy_cleanup(curl);
    curl_slist_free_all(headers);
    free(signature);
    free(resp.data);
    
    return status;
}

// 处理密钥同步响应
static sync_status process_key_response(json_object *response_json, 
                                      crypto_algorithm algorithm,
                                      uint32_t key_version) {
    json_object *data_obj;
    if (!json_object_object_get_ex(response_json, "data", &data_obj)) {
        return SYNC_STATUS_ERROR;
    }
    
    json_object *key_obj;
    if (!json_object_object_get_ex(data_obj, "key", &key_obj)) {
        return SYNC_STATUS_ERROR;
    }
    
    // 获取密钥数据
    const char *key_base64 = json_object_get_string(key_obj);
    
    // 解码Base64
    EVP_ENCODE_CTX *ctx = EVP_ENCODE_CTX_new();
    if (!ctx) {
        return SYNC_STATUS_ERROR;
    }
    
    size_t key_len = strlen(key_base64);
    size_t output_len = key_len;
    unsigned char *key_data = malloc(output_len);
    if (!key_data) {
        EVP_ENCODE_CTX_free(ctx);
        return SYNC_STATUS_ERROR;
    }
    
    int len;
    EVP_DecodeInit(ctx);
    EVP_DecodeUpdate(ctx, key_data, &len, (unsigned char*)key_base64, key_len);
    int final_len;
    EVP_DecodeFinal(ctx, key_data + len, &final_len);
    EVP_ENCODE_CTX_free(ctx);
    
    output_len = len + final_len;
    
    // 设置密钥
    sync_status status = SYNC_STATUS_ERROR;
    if (crypto_set_key(algorithm, key_version, key_data, output_len) == CRYPTO_SUCCESS) {
        status = SYNC_STATUS_OK;
    }
    
    // 安全清除并释放内存
    memset(key_data, 0, output_len);
    free(key_data);
    
    return status;
}

// 处理策略同步响应
static sync_status process_policy_response(json_object *response_json, 
                                         policy_rule *rules,
                                         size_t max_rules,
                                         size_t *rule_count) {
    json_object *data_obj;
    if (!json_object_object_get_ex(response_json, "data", &data_obj)) {
        return SYNC_STATUS_ERROR;
    }
    
    json_object *policies_array;
    if (!json_object_object_get_ex(data_obj, "policies", &policies_array) || 
        !json_object_is_type(policies_array, json_type_array)) {
        return SYNC_STATUS_ERROR;
    }
    
    size_t policy_count = json_object_array_length(policies_array);
    size_t processed = 0;
    
    for (size_t i = 0; i < policy_count && i < max_rules; i++) {
        json_object *policy_obj = json_object_array_get_idx(policies_array, i);
        
        json_object *type_obj, *action_obj, *target_obj, *algorithm_obj;
        json_object *mode_obj, *key_version_obj, *priority_obj, *timestamp_obj;
        
        if (!json_object_object_get_ex(policy_obj, "type", &type_obj) ||
            !json_object_object_get_ex(policy_obj, "action", &action_obj) ||
            !json_object_object_get_ex(policy_obj, "target", &target_obj) ||
            !json_object_object_get_ex(policy_obj, "algorithm", &algorithm_obj) ||
            !json_object_object_get_ex(policy_obj, "mode", &mode_obj) ||
            !json_object_object_get_ex(policy_obj, "keyVersion", &key_version_obj) ||
            !json_object_object_get_ex(policy_obj, "priority", &priority_obj) ||
            !json_object_object_get_ex(policy_obj, "timestamp", &timestamp_obj)) {
            continue;
        }
        
        // 解析策略类型
        const char *type_str = json_object_get_string(type_obj);
        if (strcmp(type_str, "path") == 0) {
            rules[processed].type = POLICY_TYPE_PATH;
        } else if (strcmp(type_str, "app") == 0) {
            rules[processed].type = POLICY_TYPE_APP;
        } else if (strcmp(type_str, "user") == 0) {
            rules[processed].type = POLICY_TYPE_USER;
        } else if (strcmp(type_str, "group") == 0) {
            rules[processed].type = POLICY_TYPE_GROUP;
        } else if (strcmp(type_str, "device") == 0) {
            rules[processed].type = POLICY_TYPE_DEVICE;
        } else {
            continue;
        }
        
        // 解析动作
        const char *action_str = json_object_get_string(action_obj);
        if (strcmp(action_str, "encrypt") == 0) {
            rules[processed].action = POLICY_ACTION_ENCRYPT;
        } else if (strcmp(action_str, "decrypt") == 0) {
            rules[processed].action = POLICY_ACTION_DECRYPT;
        } else if (strcmp(action_str, "deny") == 0) {
            rules[processed].action = POLICY_ACTION_DENY;
        } else if (strcmp(action_str, "allow") == 0) {
            rules[processed].action = POLICY_ACTION_ALLOW;
        } else {
            continue;
        }
        
        // 设置目标
        const char *target_str = json_object_get_string(target_obj);
        strncpy(rules[processed].target, target_str, sizeof(rules[processed].target) - 1);
        
        // 设置加密算法
        const char *algorithm_str = json_object_get_string(algorithm_obj);
        if (strcmp(algorithm_str, "AES") == 0) {
            rules[processed].algorithm = ALGORITHM_AES;
        } else if (strcmp(algorithm_str, "SM4") == 0) {
            rules[processed].algorithm = ALGORITHM_SM4;
        } else {
            rules[processed].algorithm = ALGORITHM_NONE;
        }
        
        // 设置加密模式
        const char *mode_str = json_object_get_string(mode_obj);
        if (strcmp(mode_str, "CBC") == 0) {
            rules[processed].mode = MODE_CBC;
        } else if (strcmp(mode_str, "GCM") == 0) {
            rules[processed].mode = MODE_GCM;
        } else if (strcmp(mode_str, "CTR") == 0) {
            rules[processed].mode = MODE_CTR;
        } else {
            rules[processed].mode = MODE_NONE;
        }
        
        // 设置其他属性
        rules[processed].key_version = json_object_get_int(key_version_obj);
        rules[processed].priority = json_object_get_int(priority_obj);
        rules[processed].timestamp = json_object_get_int64(timestamp_obj);
        
        processed++;
    }
    
    *rule_count = processed;
    return SYNC_STATUS_OK;
}

// 同步线程函数
static void *sync_thread_func(void *arg) {
    (void)arg;  // 未使用
    
    while (sync_config.thread_running) {
        // 同步策略
        policy_rule rules[100];
        size_t rule_count = 0;
        key_sync_get_policies(rules, 100, &rule_count);
        
        // 待机
        for (int i = 0; i < sync_config.sync_interval && sync_config.thread_running; i++) {
            sleep(1);
        }
    }
    
    return NULL;
}

/**
 * 初始化密钥同步服务
 */
sync_status key_sync_init(const char *server_url, const char *device_id, const char *device_key) {
    if (sync_config.initialized) {
        return SYNC_STATUS_OK;
    }
    
    if (!server_url || !device_id || !device_key) {
        return SYNC_STATUS_ERROR;
    }
    
    // 初始化配置
    strncpy(sync_config.server_url, server_url, sizeof(sync_config.server_url) - 1);
    strncpy(sync_config.device_id, device_id, sizeof(sync_config.device_id) - 1);
    strncpy(sync_config.device_key, device_key, sizeof(sync_config.device_key) - 1);
    
    // 初始化互斥锁
    if (pthread_mutex_init(&sync_config.mutex, NULL) != 0) {
        return SYNC_STATUS_ERROR;
    }
    
    // 初始化CURL
    curl_global_init(CURL_GLOBAL_DEFAULT);
    
    // 启动同步线程
    sync_config.thread_running = 1;
    if (pthread_create(&sync_config.sync_thread, NULL, sync_thread_func, NULL) != 0) {
        pthread_mutex_destroy(&sync_config.mutex);
        curl_global_cleanup();
        return SYNC_STATUS_ERROR;
    }
    
    sync_config.initialized = 1;
    return SYNC_STATUS_OK;
}

/**
 * 关闭密钥同步服务
 */
void key_sync_cleanup(void) {
    if (!sync_config.initialized) {
        return;
    }
    
    // 停止同步线程
    sync_config.thread_running = 0;
    pthread_join(sync_config.sync_thread, NULL);
    
    // 清理资源
    pthread_mutex_destroy(&sync_config.mutex);
    curl_global_cleanup();
    
    // 重置配置
    memset(sync_config.server_url, 0, sizeof(sync_config.server_url));
    memset(sync_config.device_id, 0, sizeof(sync_config.device_id));
    memset(sync_config.device_key, 0, sizeof(sync_config.device_key));
    sync_config.initialized = 0;
}

/**
 * 同步密钥
 */
sync_status key_sync_get_key(crypto_algorithm algorithm, uint32_t key_version) {
    if (!sync_config.initialized) {
        return SYNC_STATUS_ERROR;
    }
    
    pthread_mutex_lock(&sync_config.mutex);
    
    // 构建请求
    json_object *request_json = json_object_new_object();
    json_object_object_add(request_json, "algorithm", 
                         json_object_new_string(algorithm == ALGORITHM_AES ? "AES" : "SM4"));
    json_object_object_add(request_json, "keyVersion", json_object_new_int(key_version));
    
    // 发送请求
    json_object *response_json = NULL;
    sync_status status = send_api_request(API_PATH_KEY, request_json, &response_json);
    
    // 处理响应
    if (status == SYNC_STATUS_OK && response_json) {
        status = process_key_response(response_json, algorithm, key_version);
        json_object_put(response_json);
    }
    
    json_object_put(request_json);
    pthread_mutex_unlock(&sync_config.mutex);
    
    return status;
}

/**
 * 同步策略
 */
sync_status key_sync_get_policies(policy_rule *rules, size_t max_rules, size_t *rule_count) {
    if (!sync_config.initialized || !rules || !rule_count || max_rules == 0) {
        return SYNC_STATUS_ERROR;
    }
    
    *rule_count = 0;
    pthread_mutex_lock(&sync_config.mutex);
    
    // 构建请求
    json_object *request_json = json_object_new_object();
    
    // 发送请求
    json_object *response_json = NULL;
    sync_status status = send_api_request(API_PATH_POLICY, request_json, &response_json);
    
    // 处理响应
    if (status == SYNC_STATUS_OK && response_json) {
        status = process_policy_response(response_json, rules, max_rules, rule_count);
        json_object_put(response_json);
    }
    
    json_object_put(request_json);
    pthread_mutex_unlock(&sync_config.mutex);
    
    return status;
}

/**
 * 上报加密操作日志
 */
sync_status key_sync_report_activity(const char *operation, const char *path, 
                                  const char *user_id, const char *app_id, int result) {
    if (!sync_config.initialized || !operation || !path) {
        return SYNC_STATUS_ERROR;
    }
    
    pthread_mutex_lock(&sync_config.mutex);
    
    // 构建请求
    json_object *request_json = json_object_new_object();
    json_object_object_add(request_json, "operation", json_object_new_string(operation));
    json_object_object_add(request_json, "path", json_object_new_string(path));
    
    if (user_id) {
        json_object_object_add(request_json, "userId", json_object_new_string(user_id));
    }
    
    if (app_id) {
        json_object_object_add(request_json, "appId", json_object_new_string(app_id));
    }
    
    json_object_object_add(request_json, "result", json_object_new_int(result));
    json_object_object_add(request_json, "timestamp", 
                         json_object_new_int64(time(NULL)));
    
    // 发送请求
    json_object *response_json = NULL;
    sync_status status = send_api_request(API_PATH_REPORT, request_json, &response_json);
    
    if (response_json) {
        json_object_put(response_json);
    }
    
    json_object_put(request_json);
    pthread_mutex_unlock(&sync_config.mutex);
    
    return status;
} 