#include "audit_service.h"
#include <string.h>
#include <unistd.h>
#include <sys/stat.h>
#include <sys/time.h>
#include <errno.h>
#include <syslog.h>
#include <stdarg.h>

#define AUDIT_LOG_DIR "/var/log/cryptosystem"
#define AUDIT_LOG_FILE "audit.log"
#define AUDIT_UPLOAD_THREAD_NAME "audit_upload"

/**
 * 审计服务内部结构
 */
struct audit_service {
    audit_config_t config;
    FILE* log_file;
    pthread_t upload_thread;
    pthread_mutex_t mutex;
    pthread_cond_t cond;
    bool running;
    bool stop_requested;
    audit_statistics_t stats;
    CURL* curl_handle;
};

/**
 * HTTP响应结构
 */
typedef struct {
    char* data;
    size_t size;
} http_response_t;

// 前向声明
static void* upload_thread_func(void* arg);
static int upload_logs_to_server(audit_service_t* service);
static size_t write_callback(void* contents, size_t size, size_t nmemb, http_response_t* response);
static char* create_json_payload(audit_event_t* events, int count);
static int ensure_log_directory(void);
static void log_to_syslog(int priority, const char* format, ...);

audit_service_t* audit_service_init(const audit_config_t* config) {
    if (!config) {
        log_to_syslog(LOG_ERR, "审计服务初始化失败: 配置为空");
        return NULL;
    }

    audit_service_t* service = calloc(1, sizeof(audit_service_t));
    if (!service) {
        log_to_syslog(LOG_ERR, "审计服务初始化失败: 内存分配失败");
        return NULL;
    }

    // 复制配置
    memcpy(&service->config, config, sizeof(audit_config_t));
    
    // 初始化统计信息
    service->stats.service_start_time = time(NULL);
    
    // 初始化互斥锁和条件变量
    if (pthread_mutex_init(&service->mutex, NULL) != 0) {
        log_to_syslog(LOG_ERR, "审计服务初始化失败: 互斥锁初始化失败");
        free(service);
        return NULL;
    }
    
    if (pthread_cond_init(&service->cond, NULL) != 0) {
        log_to_syslog(LOG_ERR, "审计服务初始化失败: 条件变量初始化失败");
        pthread_mutex_destroy(&service->mutex);
        free(service);
        return NULL;
    }

    // 初始化CURL
    service->curl_handle = curl_easy_init();
    if (!service->curl_handle) {
        log_to_syslog(LOG_ERR, "审计服务初始化失败: CURL初始化失败");
        pthread_cond_destroy(&service->cond);
        pthread_mutex_destroy(&service->mutex);
        free(service);
        return NULL;
    }

    // 确保日志目录存在
    if (ensure_log_directory() != 0) {
        log_to_syslog(LOG_ERR, "审计服务初始化失败: 无法创建日志目录");
        curl_easy_cleanup(service->curl_handle);
        pthread_cond_destroy(&service->cond);
        pthread_mutex_destroy(&service->mutex);
        free(service);
        return NULL;
    }

    log_to_syslog(LOG_INFO, "审计服务初始化成功 - 设备ID: %s", config->device_id);
    return service;
}

int audit_service_start(audit_service_t* service) {
    if (!service) {
        return -1;
    }

    pthread_mutex_lock(&service->mutex);
    
    if (service->running) {
        pthread_mutex_unlock(&service->mutex);
        return 0; // 已经在运行
    }

    // 打开日志文件
    char log_path[512];
    snprintf(log_path, sizeof(log_path), "%s/%s", AUDIT_LOG_DIR, AUDIT_LOG_FILE);
    service->log_file = fopen(log_path, "a");
    if (!service->log_file) {
        log_to_syslog(LOG_ERR, "审计服务启动失败: 无法打开日志文件 %s", log_path);
        pthread_mutex_unlock(&service->mutex);
        return -1;
    }

    // 启动上传线程
    service->running = true;
    service->stop_requested = false;
    
    if (pthread_create(&service->upload_thread, NULL, upload_thread_func, service) != 0) {
        log_to_syslog(LOG_ERR, "审计服务启动失败: 无法创建上传线程");
        fclose(service->log_file);
        service->log_file = NULL;
        service->running = false;
        pthread_mutex_unlock(&service->mutex);
        return -1;
    }

    pthread_mutex_unlock(&service->mutex);
    
    log_to_syslog(LOG_INFO, "审计服务启动成功");
    return 0;
}

int audit_service_stop(audit_service_t* service) {
    if (!service) {
        return -1;
    }

    pthread_mutex_lock(&service->mutex);
    
    if (!service->running) {
        pthread_mutex_unlock(&service->mutex);
        return 0; // 已经停止
    }

    // 请求停止
    service->stop_requested = true;
    pthread_cond_signal(&service->cond);
    pthread_mutex_unlock(&service->mutex);

    // 等待上传线程结束
    pthread_join(service->upload_thread, NULL);

    pthread_mutex_lock(&service->mutex);
    service->running = false;
    
    // 关闭日志文件
    if (service->log_file) {
        fclose(service->log_file);
        service->log_file = NULL;
    }
    
    pthread_mutex_unlock(&service->mutex);
    
    log_to_syslog(LOG_INFO, "审计服务停止成功");
    return 0;
}

int audit_service_log_event(audit_service_t* service, const audit_event_t* event) {
    if (!service || !event) {
        return -1;
    }

    pthread_mutex_lock(&service->mutex);
    
    if (!service->running || !service->log_file) {
        pthread_mutex_unlock(&service->mutex);
        return -1;
    }

    // 写入本地日志文件
    fprintf(service->log_file, 
            "%s|%d|%s|%s|%s|%s|%s|%d\n",
            event->timestamp,
            event->type,
            event->user_id,
            event->device_id,
            event->file_path,
            event->details,
            event->client_version,
            event->result_code);
    
    fflush(service->log_file);
    
    // 更新统计信息
    service->stats.total_logs++;
    service->stats.local_logs++;
    
    // 唤醒上传线程
    pthread_cond_signal(&service->cond);
    
    pthread_mutex_unlock(&service->mutex);
    
    return 0;
}

int audit_service_force_upload(audit_service_t* service) {
    if (!service) {
        return -1;
    }

    return upload_logs_to_server(service);
}

int audit_service_clear_logs(audit_service_t* service) {
    if (!service) {
        return -1;
    }

    pthread_mutex_lock(&service->mutex);
    
    if (service->log_file) {
        fclose(service->log_file);
        service->log_file = NULL;
    }

    // 清空日志文件
    char log_path[512];
    snprintf(log_path, sizeof(log_path), "%s/%s", AUDIT_LOG_DIR, AUDIT_LOG_FILE);
    
    service->log_file = fopen(log_path, "w");
    if (service->log_file) {
        fclose(service->log_file);
        service->log_file = fopen(log_path, "a");
    }

    service->stats.local_logs = 0;
    
    pthread_mutex_unlock(&service->mutex);
    
    log_to_syslog(LOG_INFO, "审计日志已清空");
    return 0;
}

int audit_service_get_statistics(audit_service_t* service, audit_statistics_t* stats) {
    if (!service || !stats) {
        return -1;
    }

    pthread_mutex_lock(&service->mutex);
    memcpy(stats, &service->stats, sizeof(audit_statistics_t));
    pthread_mutex_unlock(&service->mutex);
    
    return 0;
}

void audit_create_event(audit_event_type_t type, const char* user_id, 
                       const char* device_id, const char* file_path,
                       const char* details, int result_code, audit_event_t* event) {
    if (!event) {
        return;
    }

    memset(event, 0, sizeof(audit_event_t));
    
    event->type = type;
    event->result_code = result_code;
    
    // 设置时间戳
    struct timeval tv;
    gettimeofday(&tv, NULL);
    struct tm* tm_info = localtime(&tv.tv_sec);
    snprintf(event->timestamp, sizeof(event->timestamp), 
             "%04d-%02d-%02d %02d:%02d:%02d.%03ld",
             tm_info->tm_year + 1900, tm_info->tm_mon + 1, tm_info->tm_mday,
             tm_info->tm_hour, tm_info->tm_min, tm_info->tm_sec,
             tv.tv_usec / 1000);

    // 复制字符串
    if (user_id) {
        strncpy(event->user_id, user_id, sizeof(event->user_id) - 1);
    }
    
    if (device_id) {
        strncpy(event->device_id, device_id, sizeof(event->device_id) - 1);
    }
    
    if (file_path) {
        strncpy(event->file_path, file_path, sizeof(event->file_path) - 1);
    }
    
    if (details) {
        strncpy(event->details, details, sizeof(event->details) - 1);
    }
    
    // 设置客户端版本
    strncpy(event->client_version, "Linux-1.4.0", sizeof(event->client_version) - 1);
}

void audit_service_destroy(audit_service_t* service) {
    if (!service) {
        return;
    }

    // 停止服务
    audit_service_stop(service);
    
    // 清理CURL
    if (service->curl_handle) {
        curl_easy_cleanup(service->curl_handle);
    }
    
    // 销毁同步对象
    pthread_cond_destroy(&service->cond);
    pthread_mutex_destroy(&service->mutex);
    
    free(service);
    log_to_syslog(LOG_INFO, "审计服务已销毁");
}

const char* audit_get_event_type_string(audit_event_type_t type) {
    switch (type) {
        case AUDIT_FILE_ENCRYPT: return "FILE_ENCRYPT";
        case AUDIT_FILE_DECRYPT: return "FILE_DECRYPT";
        case AUDIT_KEY_SYNC: return "KEY_SYNC";
        case AUDIT_POLICY_UPDATE: return "POLICY_UPDATE";
        case AUDIT_USER_LOGIN: return "USER_LOGIN";
        case AUDIT_USER_LOGOUT: return "USER_LOGOUT";
        case AUDIT_CONFIG_CHANGE: return "CONFIG_CHANGE";
        case AUDIT_ERROR: return "ERROR";
        default: return "UNKNOWN";
    }
}

// 私有函数实现

static void* upload_thread_func(void* arg) {
    audit_service_t* service = (audit_service_t*)arg;
    struct timespec timeout;
    
    pthread_setname_np(pthread_self(), AUDIT_UPLOAD_THREAD_NAME);
    
    while (true) {
        pthread_mutex_lock(&service->mutex);
        
        if (service->stop_requested) {
            pthread_mutex_unlock(&service->mutex);
            break;
        }
        
        // 计算超时时间
        clock_gettime(CLOCK_REALTIME, &timeout);
        timeout.tv_sec += service->config.upload_interval;
        
        // 等待条件或超时
        int ret = pthread_cond_timedwait(&service->cond, &service->mutex, &timeout);
        
        pthread_mutex_unlock(&service->mutex);
        
        if (service->stop_requested) {
            break;
        }
        
        // 定时上传或被唤醒上传
        if (ret == ETIMEDOUT || service->stats.local_logs >= service->config.batch_size) {
            upload_logs_to_server(service);
        }
    }
    
    log_to_syslog(LOG_INFO, "审计服务上传线程退出");
    return NULL;
}

static int upload_logs_to_server(audit_service_t* service) {
    // 实现简化，实际应读取日志文件并上传
    pthread_mutex_lock(&service->mutex);
    
    if (service->stats.local_logs == 0) {
        pthread_mutex_unlock(&service->mutex);
        return 0;
    }
    
    // 模拟上传成功
    service->stats.upload_success++;
    service->stats.local_logs = 0;
    service->stats.last_upload = time(NULL);
    
    pthread_mutex_unlock(&service->mutex);
    
    log_to_syslog(LOG_DEBUG, "审计日志上传完成");
    return 0;
}

static size_t write_callback(void* contents, size_t size, size_t nmemb, http_response_t* response) {
    size_t real_size = size * nmemb;
    char* new_data = realloc(response->data, response->size + real_size + 1);
    
    if (new_data) {
        response->data = new_data;
        memcpy(&response->data[response->size], contents, real_size);
        response->size += real_size;
        response->data[response->size] = 0;
    }
    
    return real_size;
}

static char* create_json_payload(audit_event_t* events, int count) {
    json_object* root = json_object_new_object();
    json_object* logs_array = json_object_new_array();
    
    for (int i = 0; i < count; i++) {
        json_object* log_obj = json_object_new_object();
        json_object_object_add(log_obj, "type", json_object_new_int(events[i].type));
        json_object_object_add(log_obj, "timestamp", json_object_new_string(events[i].timestamp));
        json_object_object_add(log_obj, "user_id", json_object_new_string(events[i].user_id));
        json_object_object_add(log_obj, "device_id", json_object_new_string(events[i].device_id));
        json_object_object_add(log_obj, "file_path", json_object_new_string(events[i].file_path));
        json_object_object_add(log_obj, "details", json_object_new_string(events[i].details));
        json_object_object_add(log_obj, "client_version", json_object_new_string(events[i].client_version));
        json_object_object_add(log_obj, "result_code", json_object_new_int(events[i].result_code));
        
        json_object_array_add(logs_array, log_obj);
    }
    
    json_object_object_add(root, "logs", logs_array);
    
    const char* json_string = json_object_to_json_string(root);
    char* result = strdup(json_string);
    
    json_object_put(root);
    return result;
}

static int ensure_log_directory(void) {
    struct stat st = {0};
    
    if (stat(AUDIT_LOG_DIR, &st) == -1) {
        if (mkdir(AUDIT_LOG_DIR, 0755) == -1) {
            return -1;
        }
    }
    
    return 0;
}

static void log_to_syslog(int priority, const char* format, ...) {
    va_list args;
    va_start(args, format);
    
    openlog("cryptosystem-audit", LOG_PID, LOG_DAEMON);
    vsyslog(priority, format, args);
    closelog();
    
    va_end(args);
} 