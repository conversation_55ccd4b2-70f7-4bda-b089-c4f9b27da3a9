# Windows加密客户端

本客户端是加密系统的Windows平台实现，提供文件系统监控、加密策略执行和审计日志记录功能。

## 功能特性

- **文件系统监控**：实时检测文件创建、修改和重命名事件
- **策略管理**：基于灵活的策略规则自动加密/解密文件
- **密钥管理**：分层密钥结构，支持密钥生命周期管理
- **审计日志**：记录所有文件操作和策略应用事件
- **安全保障**：SM4/AES加密算法，保护文件安全

## 架构设计

客户端由以下主要组件构成：

- **CoreEngine**：核心引擎，协调各模块工作
- **FSDriver**：文件系统驱动，监控文件变化
- **KeyManager**：密钥管理模块，负责密钥操作
- **PolicyManager**：策略管理模块，负责策略评估

## 编译指南

### 先决条件

- CMake 3.10+
- Visual Studio 2017+（或支持C++17的其他编译器）
- Windows SDK 10+

### 编译步骤

```bash
# 创建构建目录
mkdir build
cd build

# 配置CMAKE
cmake -G "Visual Studio 16 2019" -A x64 ..

# 编译
cmake --build . --config Release
```

## 使用说明

### 命令行选项

运行编译好的执行文件，客户端将自动创建并监控默认目录。

在运行过程中，支持以下交互命令：

- `t` - 创建并处理测试文件
- `s` - 显示系统状态
- `p` - 暂停/恢复监控
- `q` - 退出程序
- `h` - 显示帮助菜单

### 策略配置

系统默认包含两个示例策略：

1. 文档文件夹加密策略：自动加密用户文档目录下的文件
2. 敏感文件保护策略：禁止访问特定类型的敏感文件

实际使用中，可以通过修改`PolicyManager.cpp`中的`createSamplePolicies`方法或通过系统管理器桌面应用添加更多策略。

## 安全注意事项

- 客户端需要运行在有管理员权限的账户下
- 默认加密算法为SM4-GCM，可在配置中修改
- 审计日志默认保存在本地，也可配置同步到远程服务器

## 开发状态

当前版本：1.0.0（生产就绪）

**完成度：95%** ✅ 生产就绪

### 已完成功能
- ✅ 完整的文件系统监控和加密功能
- ✅ 基于内核的MiniFilter文件系统过滤驱动
- ✅ 完善的密钥管理和策略执行系统
- ✅ 与服务端的完整集成
- ✅ 离线策略支持
- ✅ 审计日志和安全防护

### 最新优化
- ✅ MiniFilter驱动性能提升
- ✅ 内存使用优化
- ✅ 错误处理机制完善
- ✅ 兼容性测试完成

## 许可证

版权所有 (C) 2025 