# HarmonyOS 项目构建指南

![HarmonyOS](https://img.shields.io/badge/HarmonyOS-5.1.1-blue)
![构建工具](https://img.shields.io/badge/构建工具-hvigor-green)

## 📋 概述

本文档为文档加密系统中所有 HarmonyOS 组件提供统一的构建指南。项目包含以下 HarmonyOS 组件：

- **客户端代理** (`customer/client-agent/harmonyos/`)
- **脱密客户端** (`customer/declassification-client/harmonyos/`)
- **系统管理器** (`customer/system-manager/harmonyos/`)
- **密钥生成器** (`operator/key-generator/harmonyos/`)

## ⚠️ 重要说明

**HarmonyOS 项目使用 `hvigor` 构建工具，不是 `npm`！**

如果您看到以下错误：
```
npm ERR! Could not read package.json: Error: ENOENT: no such file or directory, open 'package.json'
```

这是因为 HarmonyOS 项目使用 `oh-package.json5` 而不是 `package.json`，请使用正确的 hvigor 命令。

## 🛠️ 环境要求

### 必需软件
- **DevEco Studio**: 4.0 或更高版本
- **HarmonyOS SDK**: 5.1.1 (API Level 19) 或更高
- **Node.js**: 16.x 或更高版本

### 系统要求
- **操作系统**: Windows 10/11, macOS 10.15+, Ubuntu 18.04+
- **内存**: 最低 8GB RAM，推荐 16GB+
- **存储**: 最低 10GB 可用空间

## 🚀 通用构建命令

### 基本命令

```bash
# 清理项目
hvigor clean

# 安装依赖
hvigor install

# 构建调试版本
hvigor assembleHap

# 构建发布版本
hvigor assembleHap --mode release

# 安装到设备
hvigor installHap

# 卸载应用
hvigor uninstallHap

# 运行测试
hvigor test
```

### 完整构建流程

```bash
# 1. 进入项目目录
cd customer/client-agent/harmonyos/

# 2. 清理并重新构建
hvigor clean
hvigor install
hvigor assembleHap

# 3. 安装到设备
hvigor installHap
```

## 📁 项目结构

每个 HarmonyOS 组件都遵循标准的 HarmonyOS 项目结构：

```text
[component]/harmonyos/
├── AppScope/                   # 应用全局配置
│   ├── app.json5              # 应用基本信息配置
│   └── resources/             # 全局资源文件
├── entry/                     # 主模块
│   ├── src/                   # 源代码目录
│   ├── build-profile.json5    # 模块构建配置
│   ├── hvigorfile.ts         # 模块构建脚本
│   └── oh-package.json5       # 模块依赖配置
├── hvigor/                    # 构建工具配置
│   └── hvigor-config.json5    # hvigor 全局配置
├── build-profile.json5        # 项目构建配置
├── hvigorfile.ts             # 项目构建脚本
├── oh-package.json5          # 项目依赖配置
├── local.properties          # 本地环境配置
└── README.md                 # 组件说明文档
```

## 🔧 常见问题解决

### 1. npm 命令错误

**问题**: 运行 `npm install` 等命令报错找不到 package.json

**解决**: HarmonyOS 项目使用 `hvigor` 而不是 `npm`：
```bash
# ❌ 错误的命令
npm install
npm run build
npm run clean

# ✅ 正确的命令
hvigor install
hvigor assembleHap
hvigor clean
```

### 2. 构建失败

**问题**: 构建时出现依赖或配置错误

**解决**: 
```bash
# 清理并重新构建
hvigor clean
hvigor install
hvigor assembleHap
```

### 3. 设备连接问题

**问题**: 无法连接到设备或模拟器

**解决**: 
- 确保设备已开启开发者模式和 USB 调试
- 检查 DevEco Studio 中的设备管理器
- 重启 ADB 服务：`hdc kill-server && hdc start-server`

### 4. 权限问题

**问题**: 应用安装后无法正常运行

**解决**: 
- 检查 `module.json5` 中的权限配置
- 确保设备已授予必要权限
- 查看应用日志：`hdc hilog`

## 📋 命令参考

| 命令 | 描述 | 示例 |
|------|------|------|
| `hvigor clean` | 清理构建产物 | `hvigor clean` |
| `hvigor install` | 安装依赖 | `hvigor install` |
| `hvigor assembleHap` | 构建 HAP 包 | `hvigor assembleHap` |
| `hvigor assembleHap --mode release` | 构建发布版本 | `hvigor assembleHap --mode release` |
| `hvigor installHap` | 安装到设备 | `hvigor installHap` |
| `hvigor uninstallHap` | 从设备卸载 | `hvigor uninstallHap` |
| `hvigor test` | 运行单元测试 | `hvigor test` |

## 🔗 相关文档

- [HarmonyOS 开发指南](https://developer.harmonyos.com/cn/docs)
- [ArkTS 语言参考](https://developer.harmonyos.com/cn/docs/documentation/doc-guides-V3/arkts-get-started-0000001504769321-V3)
- [ArkUI 框架](https://developer.harmonyos.com/cn/docs/documentation/doc-guides-V3/arkui-overview-0000001524569345-V3)
- [hvigor 构建工具](https://developer.harmonyos.com/cn/docs/documentation/doc-guides-V3/hvigor-0000001505433201-V3)

## 📞 技术支持

如果遇到构建问题，请：

1. 检查本文档的常见问题部分
2. 查看具体组件的 README 文档
3. 查看 DevEco Studio 的构建日志
4. 通过项目 Issues 反馈问题

---

**记住**: HarmonyOS 项目使用 `hvigor`，不是 `npm`！
