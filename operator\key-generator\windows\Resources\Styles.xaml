<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- 默认按钮样式 -->
    <Style x:Key="DefaultButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="12,6"/>
        <Setter Property="Margin" Value="5"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="4">
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" Margin="{TemplateBinding Padding}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="{StaticResource PrimaryHoverBrush}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="{StaticResource PrimaryPressedBrush}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Background" Value="{StaticResource ButtonDisabledBrush}"/>
                            <Setter Property="Foreground" Value="{StaticResource MutedTextBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 次要按钮样式 -->
    <Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource DefaultButtonStyle}">
        <Setter Property="Background" Value="{StaticResource SecondaryBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource SecondaryBrush}"/>
    </Style>

    <!-- 危险按钮样式 -->
    <Style x:Key="DangerButtonStyle" TargetType="Button" BasedOn="{StaticResource DefaultButtonStyle}">
        <Setter Property="Background" Value="{StaticResource ErrorBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource ErrorBrush}"/>
    </Style>

    <!-- 成功按钮样式 -->
    <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource DefaultButtonStyle}">
        <Setter Property="Background" Value="{StaticResource SuccessBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource SuccessBrush}"/>
    </Style>

    <!-- 文本框样式 -->
    <Style x:Key="DefaultTextBoxStyle" TargetType="TextBox">
        <Setter Property="Background" Value="White"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="8,6"/>
        <Setter Property="Margin" Value="5"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="TextBox">
                    <Border Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="4">
                        <ScrollViewer x:Name="PART_ContentHost" Margin="{TemplateBinding Padding}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Background" Value="{StaticResource InputDisabledBackgroundBrush}"/>
                            <Setter Property="Foreground" Value="{StaticResource MutedTextBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 下拉框样式 -->
    <Style x:Key="DefaultComboBoxStyle" TargetType="ComboBox">
        <Setter Property="Background" Value="White"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="8,6"/>
        <Setter Property="Margin" Value="5"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Height" Value="35"/>
    </Style>

    <!-- 标签样式 -->
    <Style x:Key="DefaultLabelStyle" TargetType="Label">
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Margin" Value="5"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>

    <!-- 标题标签样式 -->
    <Style x:Key="TitleLabelStyle" TargetType="Label" BasedOn="{StaticResource DefaultLabelStyle}">
        <Setter Property="FontSize" Value="18"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
    </Style>

    <!-- 列表视图样式 -->
    <Style x:Key="DefaultListViewStyle" TargetType="ListView">
        <Setter Property="Background" Value="White"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Margin" Value="5"/>
        <Setter Property="GridViewColumnHeader.Background" Value="{StaticResource DataGridHeaderBrush}"/>
        <Setter Property="GridViewColumnHeader.Foreground" Value="{StaticResource PrimaryTextBrush}"/>
        <Setter Property="GridViewColumnHeader.FontWeight" Value="Bold"/>
    </Style>

    <!-- 数据网格样式 -->
    <Style x:Key="DefaultDataGridStyle" TargetType="DataGrid">
        <Setter Property="Background" Value="White"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Margin" Value="5"/>
        <Setter Property="GridLinesVisibility" Value="Horizontal"/>
        <Setter Property="HorizontalGridLinesBrush" Value="{StaticResource BorderLightBrush}"/>
        <Setter Property="AlternatingRowBackground" Value="{StaticResource DataGridAlternatingRowBrush}"/>
        <Setter Property="RowBackground" Value="White"/>
        <Setter Property="HeadersVisibility" Value="Column"/>
        <Setter Property="AutoGenerateColumns" Value="False"/>
        <Setter Property="CanUserAddRows" Value="False"/>
        <Setter Property="CanUserDeleteRows" Value="False"/>
        <Setter Property="IsReadOnly" Value="True"/>
        <Setter Property="SelectionMode" Value="Single"/>
        <Setter Property="SelectionUnit" Value="FullRow"/>
    </Style>

    <!-- 分组框样式 -->
    <Style x:Key="DefaultGroupBoxStyle" TargetType="GroupBox">
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Margin" Value="5"/>
        <Setter Property="Padding" Value="10"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
    </Style>

    <!-- 复选框样式 -->
    <Style x:Key="DefaultCheckBoxStyle" TargetType="CheckBox">
        <Setter Property="Margin" Value="5"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="FontSize" Value="14"/>
    </Style>

    <!-- 单选按钮样式 -->
    <Style x:Key="DefaultRadioButtonStyle" TargetType="RadioButton">
        <Setter Property="Margin" Value="5"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="FontSize" Value="14"/>
    </Style>

    <!-- 进度条样式 -->
    <Style x:Key="DefaultProgressBarStyle" TargetType="ProgressBar">
        <Setter Property="Height" Value="20"/>
        <Setter Property="Margin" Value="5"/>
        <Setter Property="Background" Value="{StaticResource BorderLightBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
    </Style>

    <!-- 状态栏样式 -->
    <Style x:Key="DefaultStatusBarStyle" TargetType="StatusBar">
        <Setter Property="Background" Value="{StaticResource StatusBarBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource StatusTextBrush}"/>
        <Setter Property="Height" Value="25"/>
    </Style>

    <!-- 工具栏样式 -->
    <Style x:Key="DefaultToolBarStyle" TargetType="ToolBar">
        <Setter Property="Background" Value="{StaticResource HeaderBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource HeaderTextBrush}"/>
    </Style>

    <!-- 菜单样式 -->
    <Style x:Key="DefaultMenuStyle" TargetType="Menu">
        <Setter Property="Background" Value="{StaticResource HeaderBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource HeaderTextBrush}"/>
    </Style>

    <!-- 选项卡样式 -->
    <Style x:Key="DefaultTabControlStyle" TargetType="TabControl">
        <Setter Property="Background" Value="White"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Margin" Value="5"/>
    </Style>

    <!-- 滚动查看器样式 -->
    <Style x:Key="DefaultScrollViewerStyle" TargetType="ScrollViewer">
        <Setter Property="VerticalScrollBarVisibility" Value="Auto"/>
        <Setter Property="HorizontalScrollBarVisibility" Value="Auto"/>
    </Style>

</ResourceDictionary> 