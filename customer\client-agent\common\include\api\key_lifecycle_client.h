#pragma once

#include <string>
#include <vector>
#include <memory>
#include <chrono>
#include "../key_management.h"

namespace crypto {
namespace api {

/**
 * 密钥轮换请求
 */
struct KeyRotationRequest {
    std::string keyId;                // 待轮换的密钥ID
    bool reEncryptChildren = true;    // 是否重新加密子密钥
    std::string description;          // 轮换原因描述
};

/**
 * 密钥轮换结果
 */
struct KeyRotationResult {
    std::string oldKeyId;             // 旧密钥ID
    std::string newKeyId;             // 新密钥ID
    std::string keyType;              // 密钥类型
    int childKeysReEncrypted = 0;     // 重新加密的子密钥数量
    bool completed = false;           // 轮换是否完成
};

/**
 * 密钥生命周期管理客户端接口
 */
class KeyLifecycleClient {
public:
    virtual ~KeyLifecycleClient() = default;
    
    /**
     * 轮换密钥
     * 
     * @param request 轮换请求
     * @return 轮换结果
     */
    virtual KeyRotationResult RotateKey(const KeyRotationRequest& request) = 0;
    
    /**
     * 获取密钥轮换历史
     * 
     * @param keyId 密钥ID
     * @return 密钥轮换历史列表
     */
    virtual std::vector<key::Key> GetKeyRotationHistory(const std::string& keyId) = 0;
    
    /**
     * 设置密钥过期时间
     * 
     * @param keyId 密钥ID
     * @param expiryTime 过期时间
     * @return 是否设置成功
     */
    virtual bool SetKeyExpiryDate(const std::string& keyId, 
                                 const std::chrono::system_clock::time_point& expiryTime) = 0;
    
    /**
     * 手动触发过期密钥处理
     * 
     * @return 处理的过期密钥数量
     */
    virtual int ProcessExpiredKeys() = 0;
};

} // namespace api
} // namespace crypto 