import Foundation
import Network
import CryptoKit
import Security

/// macOS 密钥同步服务
/// 负责与密钥服务器通信，同步和缓存加密密钥
@available(macOS 10.15, *)
public class KeySyncService: ObservableObject {
    
    // MARK: - 属性
    private let serverEndpoint: NWEndpoint
    private let syncInterval: TimeInterval
    private let maxRetryCount: Int
    private let keychainService = "com.cryptosystem.keysync"
    
    @Published public private(set) var syncStatus: SyncStatus = .idle
    @Published public private(set) var lastSyncTime: Date?
    @Published public private(set) var cachedKeyCount: Int = 0
    
    private var syncTimer: Timer?
    private var networkConnection: NWConnection?
    private let syncQueue = DispatchQueue(label: "KeySyncQueue", qos: .utility)
    
    // MARK: - 枚举定义
    public enum SyncStatus {
        case idle
        case syncing
        case success
        case failed(Error)
    }
    
    public enum KeySyncError: Error, LocalizedError {
        case networkUnavailable
        case authenticationFailed
        case serverError(Int)
        case keyValidationFailed
        case keychainError(OSStatus)
        case encryptionFailed
        
        public var errorDescription: String? {
            switch self {
            case .networkUnavailable:
                return "网络连接不可用"
            case .authenticationFailed:
                return "服务器认证失败"
            case .serverError(let code):
                return "服务器错误: \(code)"
            case .keyValidationFailed:
                return "密钥验证失败"
            case .keychainError(let status):
                return "钥匙串错误: \(status)"
            case .encryptionFailed:
                return "加密操作失败"
            }
        }
    }
    
    // MARK: - 数据结构
    private struct KeySyncRequest: Codable {
        let clientId: String
        let timestamp: TimeInterval
        let signature: String
        let lastSyncTime: TimeInterval?
    }
    
    private struct KeySyncResponse: Codable {
        let status: String
        let keys: [SyncKey]
        let timestamp: TimeInterval
        let signature: String
    }
    
    private struct SyncKey: Codable {
        let keyId: String
        let keyData: Data
        let algorithm: String
        let createdAt: TimeInterval
        let expiresAt: TimeInterval?
        let checksum: String
    }
    
    // MARK: - 初始化
    public init(serverHost: String, serverPort: UInt16, syncInterval: TimeInterval = 300) {
        self.serverEndpoint = NWEndpoint.hostPort(host: NWEndpoint.Host(serverHost), port: NWEndpoint.Port(rawValue: serverPort)!)
        self.syncInterval = syncInterval
        self.maxRetryCount = 3
        
        // 启动时加载缓存统计
        loadCachedKeyCount()
    }
    
    deinit {
        stopPeriodicSync()
        networkConnection?.cancel()
    }
    
    // MARK: - 公共方法
    
    /// 启动定期同步
    public func startPeriodicSync() {
        syncTimer = Timer.scheduledTimer(withTimeInterval: syncInterval, repeats: true) { [weak self] _ in
            self?.performSync()
        }
        
        // 立即执行一次同步
        performSync()
    }
    
    /// 停止定期同步
    public func stopPeriodicSync() {
        syncTimer?.invalidate()
        syncTimer = nil
    }
    
    /// 手动触发同步
    public func performSync() {
        syncQueue.async { [weak self] in
            self?.executeSyncOperation()
        }
    }
    
    /// 获取密钥
    public func getKey(for keyId: String) -> Data? {
        return loadKeyFromKeychain(keyId: keyId)
    }
    
    /// 验证密钥是否有效
    public func validateKey(keyId: String) -> Bool {
        guard let keyData = getKey(for: keyId) else { return false }
        
        // 验证密钥格式和完整性
        return keyData.count >= 16 // 最小密钥长度检查
    }
    
    /// 清除所有缓存密钥
    public func clearCache() {
        syncQueue.async { [weak self] in
            self?.clearKeychainCache()
        }
    }
    
    // MARK: - 私有方法
    
    private func executeSyncOperation() {
        DispatchQueue.main.async {
            self.syncStatus = .syncing
        }
        
        var retryCount = 0
        
        func attemptSync() {
            do {
                try performNetworkSync { [weak self] result in
                    switch result {
                    case .success:
                        DispatchQueue.main.async {
                            self?.syncStatus = .success
                            self?.lastSyncTime = Date()
                            self?.loadCachedKeyCount()
                        }
                    case .failure(let error):
                        retryCount += 1
                        if retryCount < self?.maxRetryCount ?? 0 {
                            DispatchQueue.global().asyncAfter(deadline: .now() + 2.0) {
                                attemptSync()
                            }
                        } else {
                            DispatchQueue.main.async {
                                self?.syncStatus = .failed(error)
                            }
                        }
                    }
                }
            } catch {
                DispatchQueue.main.async {
                    self.syncStatus = .failed(error)
                }
            }
        }
        
        attemptSync()
    }
    
    private func performNetworkSync(completion: @escaping (Result<Void, Error>) -> Void) throws {
        let connection = NWConnection(to: serverEndpoint, using: .tcp)
        self.networkConnection = connection
        
        connection.start(queue: syncQueue)
        
        connection.stateUpdateHandler = { [weak self] state in
            switch state {
            case .ready:
                self?.sendSyncRequest(connection: connection, completion: completion)
            case .failed(let error):
                completion(.failure(error))
            case .cancelled:
                completion(.failure(KeySyncError.networkUnavailable))
            default:
                break
            }
        }
    }
    
    private func sendSyncRequest(connection: NWConnection, completion: @escaping (Result<Void, Error>) -> Void) {
        do {
            let request = try createSyncRequest()
            let requestData = try JSONEncoder().encode(request)
            
            // 发送HTTP请求头
            let httpRequest = "POST /api/keys/sync HTTP/1.1\r\nHost: \(serverEndpoint)\r\nContent-Type: application/json\r\nContent-Length: \(requestData.count)\r\n\r\n"
            let headerData = httpRequest.data(using: .utf8)!
            
            connection.send(content: headerData + requestData, completion: .contentProcessed { error in
                if let error = error {
                    completion(.failure(error))
                    return
                }
                
                // 接收响应
                self.receiveResponse(connection: connection, completion: completion)
            })
        } catch {
            completion(.failure(error))
        }
    }
    
    private func receiveResponse(connection: NWConnection, completion: @escaping (Result<Void, Error>) -> Void) {
        connection.receive(minimumIncompleteLength: 1, maximumLength: 65536) { [weak self] data, _, isComplete, error in
            defer {
                connection.cancel()
            }
            
            if let error = error {
                completion(.failure(error))
                return
            }
            
            guard let data = data, !data.isEmpty else {
                completion(.failure(KeySyncError.serverError(0)))
                return
            }
            
            do {
                try self?.processSyncResponse(data: data)
                completion(.success(()))
            } catch {
                completion(.failure(error))
            }
        }
    }
    
    private func createSyncRequest() throws -> KeySyncRequest {
        let clientId = try getOrCreateClientId()
        let timestamp = Date().timeIntervalSince1970
        let signature = try createRequestSignature(clientId: clientId, timestamp: timestamp)
        
        return KeySyncRequest(
            clientId: clientId,
            timestamp: timestamp,
            signature: signature,
            lastSyncTime: lastSyncTime?.timeIntervalSince1970
        )
    }
    
    private func processSyncResponse(data: Data) throws {
        // 解析HTTP响应
        guard let responseString = String(data: data, encoding: .utf8) else {
            throw KeySyncError.serverError(0)
        }
        
        let components = responseString.components(separatedBy: "\r\n\r\n")
        guard components.count >= 2 else {
            throw KeySyncError.serverError(0)
        }
        
        let jsonData = components[1].data(using: .utf8)!
        let response = try JSONDecoder().decode(KeySyncResponse.self, from: jsonData)
        
        // 验证响应签名
        try validateResponseSignature(response: response)
        
        // 保存密钥到钥匙串
        for key in response.keys {
            try saveKeyToKeychain(key: key)
        }
    }
    
    private func getOrCreateClientId() throws -> String {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: keychainService,
            kSecAttrAccount as String: "clientId",
            kSecReturnData as String: true
        ]
        
        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)
        
        if status == errSecSuccess {
            guard let data = result as? Data,
                  let clientId = String(data: data, encoding: .utf8) else {
                throw KeySyncError.keychainError(status)
            }
            return clientId
        } else if status == errSecItemNotFound {
            // 创建新的客户端ID
            let clientId = UUID().uuidString
            let data = clientId.data(using: .utf8)!
            
            let addQuery: [String: Any] = [
                kSecClass as String: kSecClassGenericPassword,
                kSecAttrService as String: keychainService,
                kSecAttrAccount as String: "clientId",
                kSecValueData as String: data
            ]
            
            let addStatus = SecItemAdd(addQuery as CFDictionary, nil)
            if addStatus != errSecSuccess {
                throw KeySyncError.keychainError(addStatus)
            }
            
            return clientId
        } else {
            throw KeySyncError.keychainError(status)
        }
    }
    
    private func createRequestSignature(clientId: String, timestamp: TimeInterval) throws -> String {
        let message = "\(clientId):\(timestamp)"
        let messageData = message.data(using: .utf8)!
        
        // 使用HMAC-SHA256创建签名
        let key = SymmetricKey(size: .bits256)
        let signature = HMAC<SHA256>.authenticationCode(for: messageData, using: key)
        return Data(signature).base64EncodedString()
    }
    
    private func validateResponseSignature(response: KeySyncResponse) throws {
        // 验证服务器响应签名的逻辑
        // 这里简化处理，实际应该验证服务器签名
        if response.signature.isEmpty {
            throw KeySyncError.authenticationFailed
        }
    }
    
    private func saveKeyToKeychain(key: SyncKey) throws {
        // 验证密钥校验和
        let calculatedChecksum = SHA256.hash(data: key.keyData).compactMap { String(format: "%02x", $0) }.joined()
        if calculatedChecksum != key.checksum {
            throw KeySyncError.keyValidationFailed
        }
        
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: keychainService,
            kSecAttrAccount as String: key.keyId,
            kSecValueData as String: key.keyData,
            kSecAttrComment as String: "Algorithm: \(key.algorithm), Created: \(Date(timeIntervalSince1970: key.createdAt))"
        ]
        
        // 先尝试更新
        let updateStatus = SecItemUpdate([kSecClass as String: kSecClassGenericPassword,
                                         kSecAttrService as String: keychainService,
                                         kSecAttrAccount as String: key.keyId] as CFDictionary,
                                        query as CFDictionary)
        
        if updateStatus == errSecItemNotFound {
            // 如果不存在则添加
            let addStatus = SecItemAdd(query as CFDictionary, nil)
            if addStatus != errSecSuccess {
                throw KeySyncError.keychainError(addStatus)
            }
        } else if updateStatus != errSecSuccess {
            throw KeySyncError.keychainError(updateStatus)
        }
    }
    
    private func loadKeyFromKeychain(keyId: String) -> Data? {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: keychainService,
            kSecAttrAccount as String: keyId,
            kSecReturnData as String: true
        ]
        
        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)
        
        if status == errSecSuccess {
            return result as? Data
        }
        
        return nil
    }
    
    private func loadCachedKeyCount() {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: keychainService,
            kSecMatchLimit as String: kSecMatchLimitAll,
            kSecReturnAttributes as String: true
        ]
        
        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)
        
        if status == errSecSuccess, let items = result as? [[String: Any]] {
            DispatchQueue.main.async {
                self.cachedKeyCount = items.count
            }
        } else {
            DispatchQueue.main.async {
                self.cachedKeyCount = 0
            }
        }
    }
    
    private func clearKeychainCache() {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: keychainService
        ]
        
        let status = SecItemDelete(query as CFDictionary)
        if status == errSecSuccess || status == errSecItemNotFound {
            DispatchQueue.main.async {
                self.cachedKeyCount = 0
            }
        }
    }
}

// MARK: - 扩展：调试支持
@available(macOS 10.15, *)
extension KeySyncService {
    
    /// 获取同步状态描述
    public var statusDescription: String {
        switch syncStatus {
        case .idle:
            return "空闲"
        case .syncing:
            return "同步中..."
        case .success:
            return "同步成功"
        case .failed(let error):
            return "同步失败: \(error.localizedDescription)"
        }
    }
    
    /// 获取详细统计信息
    public func getStatistics() -> [String: Any] {
        return [
            "status": statusDescription,
            "lastSyncTime": lastSyncTime?.description ?? "从未同步",
            "cachedKeyCount": cachedKeyCount,
            "syncInterval": syncInterval,
            "serverEndpoint": serverEndpoint.debugDescription
        ]
    }
} 