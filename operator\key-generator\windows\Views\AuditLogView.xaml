<UserControl x:Class="KeyGenerator.Views.AuditLogView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:KeyGenerator.Views"
    xmlns:viewmodels="clr-namespace:KeyGenerator.ViewModels" mc:Ignorable="d" d:DataContext="{d:DesignInstance Type=viewmodels:AuditLogViewModel, IsDesignTimeCreatable=False}" d:DesignHeight="600" d:DesignWidth="800" Background="#F5F5F5">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <TextBlock Grid.Row="0" Text="审计日志查询" FontSize="24" FontWeight="Bold" Margin="0,0,0,20"/>

        <!-- Filter Area -->
        <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,10">
            <Label Content="从:" VerticalAlignment="Center"/>
            <DatePicker SelectedDate="{Binding FilterFrom}" Margin="5,0"/>
            <Label Content="到:" VerticalAlignment="Center"/>
            <DatePicker SelectedDate="{Binding FilterTo}" Margin="5,0"/>
            <Label Content="用户:" VerticalAlignment="Center"/>
            <TextBox Text="{Binding FilterUser, UpdateSourceTrigger=PropertyChanged}" Width="150" Margin="5,0"/>
            <Button Content="查询" Command="{Binding LoadLogsCommand}" Style="{StaticResource AccentButtonStyle}"/>
        </StackPanel>

        <!-- Log List -->
        <DataGrid Grid.Row="2" ItemsSource="{Binding AuditLogs}" AutoGenerateColumns="False" IsReadOnly="True" CanUserAddRows="False">
            <DataGrid.Columns>
                <DataGridTextColumn Header="时间戳" Binding="{Binding Timestamp, StringFormat='yyyy-MM-dd HH:mm:ss'}" Width="*"/>
                <DataGridTextColumn Header="操作" Binding="{Binding Action}" Width="*"/>
                <DataGridTextColumn Header="资源ID" Binding="{Binding ResourceId}" Width="*"/>
                <DataGridTextColumn Header="用户ID" Binding="{Binding UserId}" Width="*"/>
                <DataGridCheckBoxColumn Header="成功" Binding="{Binding Success}" Width="Auto"/>
                <DataGridTextColumn Header="IP地址" Binding="{Binding SourceIpAddress}" Width="*"/>
                <DataGridTextColumn Header="描述" Binding="{Binding Description}" Width="2*"/>
            </DataGrid.Columns>
        </DataGrid>

        <TextBlock Grid.Row="3" Text="{Binding StatusText}" Margin="0,10,0,0" FontStyle="Italic"/>

        <ProgressBar Grid.Row="2" IsIndeterminate="{Binding IsLoading}" Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}" VerticalAlignment="Center" Height="10"/>
    </Grid>
</UserControl> 