import BackupExtensionAbility, { BundleVersion } from '@ohos.app.ability.BackupExtensionAbility';
import hilog from '@ohos.hilog';

export default class EntryBackupAbility extends BackupExtensionAbility {
  async onBackup() {
    hilog.info(0x0000, 'KeyGenerator', 'onBackup ok');
  }

  async onRestore(bundleVersion: BundleVersion) {
    hilog.info(0x0000, 'KeyGenerator', 'onRestore ok %{public}s', JSON.stringify(bundleVersion));
  }
}
