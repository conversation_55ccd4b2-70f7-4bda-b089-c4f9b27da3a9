// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		1A1B2C3D4E5F6A7B8C9D0E1F /* DeclassificationClientApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1B2C3D4E5F6A7B8C9D0E1E /* DeclassificationClientApp.swift */; };
		1A1B2C3D4E5F6A7B8C9D0E21 /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1B2C3D4E5F6A7B8C9D0E20 /* ContentView.swift */; };
		1A1B2C3D4E5F6A7B8C9D0E23 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 1A1B2C3D4E5F6A7B8C9D0E22 /* Assets.xcassets */; };
		1A1B2C3D4E5F6A7B8C9D0E26 /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 1A1B2C3D4E5F6A7B8C9D0E25 /* Preview Assets.xcassets */; };
		1A1B2C3D4E5F6A7B8C9D0E2A /* DeclassificationService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1B2C3D4E5F6A7B8C9D0E29 /* DeclassificationService.swift */; };
		1A1B2C3D4E5F6A7B8C9D0E2C /* SecurityManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1B2C3D4E5F6A7B8C9D0E2B /* SecurityManager.swift */; };
		1A1B2C3D4E5F6A7B8C9D0E2E /* NetworkManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1B2C3D4E5F6A7B8C9D0E2D /* NetworkManager.swift */; };
		1A1B2C3D4E5F6A7B8C9D0E30 /* AuditLogger.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1B2C3D4E5F6A7B8C9D0E2F /* AuditLogger.swift */; };
		1A1B2C3D4E5F6A7B8C9D0E32 /* TaskModels.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1B2C3D4E5F6A7B8C9D0E31 /* TaskModels.swift */; };
		1A1B2C3D4E5F6A7B8C9D0E34 /* TaskListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1B2C3D4E5F6A7B8C9D0E33 /* TaskListView.swift */; };
		1A1B2C3D4E5F6A7B8C9D0E36 /* TaskDetailView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1B2C3D4E5F6A7B8C9D0E35 /* TaskDetailView.swift */; };
		1A1B2C3D4E5F6A7B8C9D0E38 /* SettingsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1B2C3D4E5F6A7B8C9D0E37 /* SettingsView.swift */; };
		1A1B2C3D4E5F6A7B8C9D0E3A /* ConfigurationManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1B2C3D4E5F6A7B8C9D0E39 /* ConfigurationManager.swift */; };
		1A1B2C3D4E5F6A7B8C9D0E3C /* KeychainService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1B2C3D4E5F6A7B8C9D0E3B /* KeychainService.swift */; };
		1A1B2C3D4E5F6A7B8C9D0E3E /* CryptoUtils.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1B2C3D4E5F6A7B8C9D0E3D /* CryptoUtils.swift */; };
		1A1B2C3D4E5F6A7B8C9D0E40 /* FileManager+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1B2C3D4E5F6A7B8C9D0E3F /* FileManager+Extensions.swift */; };
		1A1B2C3D4E5F6A7B8C9D0E42 /* Localizable.strings in Resources */ = {isa = PBXBuildFile; fileRef = 1A1B2C3D4E5F6A7B8C9D0E41 /* Localizable.strings */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		1A1B2C3D4E5F6A7B8C9D0E1B /* DeclassificationClient.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = DeclassificationClient.app; sourceTree = BUILT_PRODUCTS_DIR; };
		1A1B2C3D4E5F6A7B8C9D0E1E /* DeclassificationClientApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DeclassificationClientApp.swift; sourceTree = "<group>"; };
		1A1B2C3D4E5F6A7B8C9D0E20 /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		1A1B2C3D4E5F6A7B8C9D0E22 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		1A1B2C3D4E5F6A7B8C9D0E25 /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		1A1B2C3D4E5F6A7B8C9D0E29 /* DeclassificationService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DeclassificationService.swift; sourceTree = "<group>"; };
		1A1B2C3D4E5F6A7B8C9D0E2B /* SecurityManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SecurityManager.swift; sourceTree = "<group>"; };
		1A1B2C3D4E5F6A7B8C9D0E2D /* NetworkManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NetworkManager.swift; sourceTree = "<group>"; };
		1A1B2C3D4E5F6A7B8C9D0E2F /* AuditLogger.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AuditLogger.swift; sourceTree = "<group>"; };
		1A1B2C3D4E5F6A7B8C9D0E31 /* TaskModels.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TaskModels.swift; sourceTree = "<group>"; };
		1A1B2C3D4E5F6A7B8C9D0E33 /* TaskListView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TaskListView.swift; sourceTree = "<group>"; };
		1A1B2C3D4E5F6A7B8C9D0E35 /* TaskDetailView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TaskDetailView.swift; sourceTree = "<group>"; };
		1A1B2C3D4E5F6A7B8C9D0E37 /* SettingsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SettingsView.swift; sourceTree = "<group>"; };
		1A1B2C3D4E5F6A7B8C9D0E39 /* ConfigurationManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ConfigurationManager.swift; sourceTree = "<group>"; };
		1A1B2C3D4E5F6A7B8C9D0E3B /* KeychainService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = KeychainService.swift; sourceTree = "<group>"; };
		1A1B2C3D4E5F6A7B8C9D0E3D /* CryptoUtils.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CryptoUtils.swift; sourceTree = "<group>"; };
		1A1B2C3D4E5F6A7B8C9D0E3F /* FileManager+Extensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "FileManager+Extensions.swift"; sourceTree = "<group>"; };
		1A1B2C3D4E5F6A7B8C9D0E41 /* Localizable.strings */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; path = Localizable.strings; sourceTree = "<group>"; };
		1A1B2C3D4E5F6A7B8C9D0E43 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		1A1B2C3D4E5F6A7B8C9D0E44 /* DeclassificationClient.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = DeclassificationClient.entitlements; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		1A1B2C3D4E5F6A7B8C9D0E18 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		1A1B2C3D4E5F6A7B8C9D0E12 = {
			isa = PBXGroup;
			children = (
				1A1B2C3D4E5F6A7B8C9D0E1D /* DeclassificationClient */,
				1A1B2C3D4E5F6A7B8C9D0E1C /* Products */,
			);
			sourceTree = "<group>";
		};
		1A1B2C3D4E5F6A7B8C9D0E1C /* Products */ = {
			isa = PBXGroup;
			children = (
				1A1B2C3D4E5F6A7B8C9D0E1B /* DeclassificationClient.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		1A1B2C3D4E5F6A7B8C9D0E1D /* DeclassificationClient */ = {
			isa = PBXGroup;
			children = (
				1A1B2C3D4E5F6A7B8C9D0E44 /* DeclassificationClient.entitlements */,
				1A1B2C3D4E5F6A7B8C9D0E43 /* Info.plist */,
				1A1B2C3D4E5F6A7B8C9D0E1E /* DeclassificationClientApp.swift */,
				1A1B2C3D4E5F6A7B8C9D0E20 /* ContentView.swift */,
				1A1B2C3D4E5F6A7B8C9D0E27 /* Views */,
				1A1B2C3D4E5F6A7B8C9D0E28 /* Services */,
				1A1B2C3D4E5F6A7B8C9D0E45 /* Models */,
				1A1B2C3D4E5F6A7B8C9D0E46 /* Utils */,
				1A1B2C3D4E5F6A7B8C9D0E47 /* Resources */,
				1A1B2C3D4E5F6A7B8C9D0E24 /* Preview Content */,
			);
			path = DeclassificationClient;
			sourceTree = "<group>";
		};
		1A1B2C3D4E5F6A7B8C9D0E24 /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				1A1B2C3D4E5F6A7B8C9D0E25 /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
		1A1B2C3D4E5F6A7B8C9D0E27 /* Views */ = {
			isa = PBXGroup;
			children = (
				1A1B2C3D4E5F6A7B8C9D0E33 /* TaskListView.swift */,
				1A1B2C3D4E5F6A7B8C9D0E35 /* TaskDetailView.swift */,
				1A1B2C3D4E5F6A7B8C9D0E37 /* SettingsView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		1A1B2C3D4E5F6A7B8C9D0E28 /* Services */ = {
			isa = PBXGroup;
			children = (
				1A1B2C3D4E5F6A7B8C9D0E29 /* DeclassificationService.swift */,
				1A1B2C3D4E5F6A7B8C9D0E2B /* SecurityManager.swift */,
				1A1B2C3D4E5F6A7B8C9D0E2D /* NetworkManager.swift */,
				1A1B2C3D4E5F6A7B8C9D0E2F /* AuditLogger.swift */,
				1A1B2C3D4E5F6A7B8C9D0E39 /* ConfigurationManager.swift */,
				1A1B2C3D4E5F6A7B8C9D0E3B /* KeychainService.swift */,
			);
			path = Services;
			sourceTree = "<group>";
		};
		1A1B2C3D4E5F6A7B8C9D0E45 /* Models */ = {
			isa = PBXGroup;
			children = (
				1A1B2C3D4E5F6A7B8C9D0E31 /* TaskModels.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		1A1B2C3D4E5F6A7B8C9D0E46 /* Utils */ = {
			isa = PBXGroup;
			children = (
				1A1B2C3D4E5F6A7B8C9D0E3D /* CryptoUtils.swift */,
				1A1B2C3D4E5F6A7B8C9D0E3F /* FileManager+Extensions.swift */,
			);
			path = Utils;
			sourceTree = "<group>";
		};
		1A1B2C3D4E5F6A7B8C9D0E47 /* Resources */ = {
			isa = PBXGroup;
			children = (
				1A1B2C3D4E5F6A7B8C9D0E22 /* Assets.xcassets */,
				1A1B2C3D4E5F6A7B8C9D0E41 /* Localizable.strings */,
			);
			path = Resources;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		1A1B2C3D4E5F6A7B8C9D0E1A /* DeclassificationClient */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1A1B2C3D4E5F6A7B8C9D0E48 /* Build configuration list for PBXNativeTarget "DeclassificationClient" */;
			buildPhases = (
				1A1B2C3D4E5F6A7B8C9D0E17 /* Sources */,
				1A1B2C3D4E5F6A7B8C9D0E18 /* Frameworks */,
				1A1B2C3D4E5F6A7B8C9D0E19 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = DeclassificationClient;
			productName = DeclassificationClient;
			productReference = 1A1B2C3D4E5F6A7B8C9D0E1B /* DeclassificationClient.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		1A1B2C3D4E5F6A7B8C9D0E13 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1500;
				LastUpgradeCheck = 1500;
				TargetAttributes = {
					1A1B2C3D4E5F6A7B8C9D0E1A = {
						CreatedOnToolsVersion = 15.0;
					};
				};
			};
			buildConfigurationList = 1A1B2C3D4E5F6A7B8C9D0E16 /* Build configuration list for PBXProject "DeclassificationClient" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-Hans",
			);
			mainGroup = 1A1B2C3D4E5F6A7B8C9D0E12;
			productRefGroup = 1A1B2C3D4E5F6A7B8C9D0E1C /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				1A1B2C3D4E5F6A7B8C9D0E1A /* DeclassificationClient */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		1A1B2C3D4E5F6A7B8C9D0E19 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1A1B2C3D4E5F6A7B8C9D0E26 /* Preview Assets.xcassets in Resources */,
				1A1B2C3D4E5F6A7B8C9D0E23 /* Assets.xcassets in Resources */,
				1A1B2C3D4E5F6A7B8C9D0E42 /* Localizable.strings in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		1A1B2C3D4E5F6A7B8C9D0E17 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1A1B2C3D4E5F6A7B8C9D0E21 /* ContentView.swift in Sources */,
				1A1B2C3D4E5F6A7B8C9D0E2A /* DeclassificationService.swift in Sources */,
				1A1B2C3D4E5F6A7B8C9D0E2C /* SecurityManager.swift in Sources */,
				1A1B2C3D4E5F6A7B8C9D0E2E /* NetworkManager.swift in Sources */,
				1A1B2C3D4E5F6A7B8C9D0E30 /* AuditLogger.swift in Sources */,
				1A1B2C3D4E5F6A7B8C9D0E32 /* TaskModels.swift in Sources */,
				1A1B2C3D4E5F6A7B8C9D0E34 /* TaskListView.swift in Sources */,
				1A1B2C3D4E5F6A7B8C9D0E36 /* TaskDetailView.swift in Sources */,
				1A1B2C3D4E5F6A7B8C9D0E38 /* SettingsView.swift in Sources */,
				1A1B2C3D4E5F6A7B8C9D0E3A /* ConfigurationManager.swift in Sources */,
				1A1B2C3D4E5F6A7B8C9D0E3C /* KeychainService.swift in Sources */,
				1A1B2C3D4E5F6A7B8C9D0E3E /* CryptoUtils.swift in Sources */,
				1A1B2C3D4E5F6A7B8C9D0E40 /* FileManager+Extensions.swift in Sources */,
				1A1B2C3D4E5F6A7B8C9D0E1F /* DeclassificationClientApp.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		1A1B2C3D4E5F6A7B8C9D0E49 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		1A1B2C3D4E5F6A7B8C9D0E4A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		1A1B2C3D4E5F6A7B8C9D0E4B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = DeclassificationClient/DeclassificationClient.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"DeclassificationClient/Preview Content\"";
				DEVELOPMENT_TEAM = "";
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = DeclassificationClient/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "脱密客户端";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.security";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INFOPLIST_KEY_NSMainStoryboardFile = Main;
				INFOPLIST_KEY_NSPrincipalClass = NSApplication;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 13.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.cryptosystem.declassification.client;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		1A1B2C3D4E5F6A7B8C9D0E4C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = DeclassificationClient/DeclassificationClient.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"DeclassificationClient/Preview Content\"";
				DEVELOPMENT_TEAM = "";
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = DeclassificationClient/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "脱密客户端";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.security";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INFOPLIST_KEY_NSMainStoryboardFile = Main;
				INFOPLIST_KEY_NSPrincipalClass = NSApplication;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 13.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.cryptosystem.declassification.client;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		1A1B2C3D4E5F6A7B8C9D0E16 /* Build configuration list for PBXProject "DeclassificationClient" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1A1B2C3D4E5F6A7B8C9D0E49 /* Debug */,
				1A1B2C3D4E5F6A7B8C9D0E4A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1A1B2C3D4E5F6A7B8C9D0E48 /* Build configuration list for PBXNativeTarget "DeclassificationClient" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1A1B2C3D4E5F6A7B8C9D0E4B /* Debug */,
				1A1B2C3D4E5F6A7B8C9D0E4C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 1A1B2C3D4E5F6A7B8C9D0E13 /* Project object */;
} 