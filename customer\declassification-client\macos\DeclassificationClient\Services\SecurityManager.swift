import Foundation
import Security
import CryptoKit

/// 安全管理器
/// 负责处理所有安全相关的功能，包括文件权限、加密解密、密钥管理等
class SecurityManager: ObservableObject {
    
    // MARK: - Singleton
    static let shared = SecurityManager()
    
    // MARK: - Private Properties
    private let keychainService = KeychainService.shared
    private let auditLogger = AuditLogger.shared
    
    // MARK: - Security Configuration
    private struct SecurityConfig {
        static let encryptionAlgorithm = "SM4-GCM"
        static let keyLength = 256
        static let nonceLength = 12
        static let tagLength = 16
    }
    
    // MARK: - Initialization
    private init() {
        setupSecurityEnvironment()
    }
    
    // MARK: - File Permission Methods
    
    /// 检查是否有权限访问指定文件
    func hasPermission(for url: URL) async -> Bool {
        do {
            // 检查文件是否存在
            guard FileManager.default.fileExists(atPath: url.path) else {
                await auditLogger.logEvent(
                    type: .securityEvent,
                    message: "文件不存在",
                    details: ["path": url.path]
                )
                return false
            }
            
            // 检查读取权限
            guard FileManager.default.isReadableFile(atPath: url.path) else {
                await auditLogger.logEvent(
                    type: .securityEvent,
                    message: "文件无读取权限",
                    details: ["path": url.path]
                )
                return false
            }
            
            // 检查文件属性
            let attributes = try FileManager.default.attributesOfItem(atPath: url.path)
            let fileSize = attributes[.size] as? Int64 ?? 0
            
            // 检查文件大小限制（最大100MB）
            if fileSize > 100 * 1024 * 1024 {
                await auditLogger.logEvent(
                    type: .securityEvent,
                    message: "文件大小超出限制",
                    details: [
                        "path": url.path,
                        "size": String(fileSize),
                        "limit": "104857600"
                    ]
                )
                return false
            }
            
            // 检查文件类型
            if !isAllowedFileType(url: url) {
                await auditLogger.logEvent(
                    type: .securityEvent,
                    message: "文件类型不被允许",
                    details: [
                        "path": url.path,
                        "extension": url.pathExtension
                    ]
                )
                return false
            }
            
            await auditLogger.logEvent(
                type: .securityEvent,
                message: "文件权限验证通过",
                details: ["path": url.path]
            )
            
            return true
            
        } catch {
            await auditLogger.logEvent(
                type: .errorEvent,
                message: "权限检查失败",
                details: [
                    "path": url.path,
                    "error": error.localizedDescription
                ]
            )
            return false
        }
    }
    
    /// 验证用户权限
    func validateUserPermission(userId: String, action: String) async -> Bool {
        // 在实际实现中，这里会连接到权限管理系统
        // 目前返回true允许所有操作
        
        await auditLogger.logEvent(
            type: .securityEvent,
            message: "用户权限验证",
            details: [
                "userId": userId,
                "action": action,
                "result": "允许"
            ]
        )
        
        return true
    }
    
    // MARK: - Encryption Methods
    
    /// 加密文件
    func encryptFile(at sourceURL: URL, to destinationURL: URL) async throws {
        guard await hasPermission(for: sourceURL) else {
            throw SecurityError.permissionDenied("无权限访问源文件")
        }
        
        let data = try Data(contentsOf: sourceURL)
        let encryptedData = try await encryptData(data)
        
        try encryptedData.write(to: destinationURL)
        
        await auditLogger.logEvent(
            type: .securityEvent,
            message: "文件加密完成",
            details: [
                "sourcePath": sourceURL.path,
                "destinationPath": destinationURL.path,
                "algorithm": SecurityConfig.encryptionAlgorithm
            ]
        )
    }
    
    /// 解密文件
    func decryptFile(at sourceURL: URL, to destinationURL: URL) async throws {
        guard await hasPermission(for: sourceURL) else {
            throw SecurityError.permissionDenied("无权限访问源文件")
        }
        
        let encryptedData = try Data(contentsOf: sourceURL)
        let decryptedData = try await decryptData(encryptedData)
        
        try decryptedData.write(to: destinationURL)
        
        await auditLogger.logEvent(
            type: .securityEvent,
            message: "文件解密完成",
            details: [
                "sourcePath": sourceURL.path,
                "destinationPath": destinationURL.path,
                "algorithm": SecurityConfig.encryptionAlgorithm
            ]
        )
    }
    
    /// 加密数据
    func encryptData(_ data: Data) async throws -> Data {
        // 获取加密密钥
        guard let key = try await getOrCreateEncryptionKey() else {
            throw SecurityError.keyGenerationFailed("无法获取加密密钥")
        }
        
        // 生成随机nonce
        let nonce = CryptoUtils.generateNonce()
        
        // 执行加密
        let encryptedData = try CryptoUtils.encrypt(data: data, key: key, nonce: nonce)
        
        // 组合nonce和加密数据
        var result = Data()
        result.append(nonce)
        result.append(encryptedData)
        
        await auditLogger.logEvent(
            type: .securityEvent,
            message: "数据加密完成",
            details: [
                "dataSize": String(data.count),
                "encryptedSize": String(result.count),
                "algorithm": SecurityConfig.encryptionAlgorithm
            ]
        )
        
        return result
    }
    
    /// 解密数据
    func decryptData(_ encryptedData: Data) async throws -> Data {
        guard encryptedData.count > SecurityConfig.nonceLength else {
            throw SecurityError.decryptionFailed("加密数据格式无效")
        }
        
        // 分离nonce和加密数据
        let nonce = encryptedData.prefix(SecurityConfig.nonceLength)
        let ciphertext = encryptedData.dropFirst(SecurityConfig.nonceLength)
        
        // 获取解密密钥
        guard let key = try await getOrCreateEncryptionKey() else {
            throw SecurityError.keyRetrievalFailed("无法获取解密密钥")
        }
        
        // 执行解密
        let decryptedData = try CryptoUtils.decrypt(data: Data(ciphertext), key: key, nonce: Data(nonce))
        
        await auditLogger.logEvent(
            type: .securityEvent,
            message: "数据解密完成",
            details: [
                "encryptedSize": String(encryptedData.count),
                "decryptedSize": String(decryptedData.count),
                "algorithm": SecurityConfig.encryptionAlgorithm
            ]
        )
        
        return decryptedData
    }
    
    // MARK: - Key Management
    
    /// 获取或创建加密密钥
    private func getOrCreateEncryptionKey() async throws -> Data? {
        let keyIdentifier = "com.cryptosystem.declassification.encryption.key"
        
        // 首先尝试从钥匙串获取现有密钥
        if let existingKey = try keychainService.getItem(identifier: keyIdentifier) {
            return existingKey
        }
        
        // 如果没有现有密钥，创建新密钥
        let newKey = CryptoUtils.generateKey(length: SecurityConfig.keyLength / 8)
        
        // 保存到钥匙串
        try keychainService.setItem(data: newKey, identifier: keyIdentifier)
        
        await auditLogger.logEvent(
            type: .securityEvent,
            message: "生成新的加密密钥",
            details: [
                "keyIdentifier": keyIdentifier,
                "keyLength": String(SecurityConfig.keyLength)
            ]
        )
        
        return newKey
    }
    
    /// 轮换加密密钥
    func rotateEncryptionKey() async throws {
        let keyIdentifier = "com.cryptosystem.declassification.encryption.key"
        let backupIdentifier = "com.cryptosystem.declassification.encryption.key.backup"
        
        // 备份当前密钥
        if let currentKey = try keychainService.getItem(identifier: keyIdentifier) {
            try keychainService.setItem(data: currentKey, identifier: backupIdentifier)
        }
        
        // 生成新密钥
        let newKey = CryptoUtils.generateKey(length: SecurityConfig.keyLength / 8)
        try keychainService.setItem(data: newKey, identifier: keyIdentifier)
        
        await auditLogger.logEvent(
            type: .securityEvent,
            message: "密钥轮换完成",
            details: [
                "keyIdentifier": keyIdentifier,
                "backupIdentifier": backupIdentifier
            ]
        )
    }
    
    // MARK: - Digital Signature
    
    /// 为数据生成数字签名
    func signData(_ data: Data) async throws -> Data {
        let privateKey = try await getOrCreateSigningKey()
        let signature = try CryptoUtils.sign(data: data, privateKey: privateKey)
        
        await auditLogger.logEvent(
            type: .securityEvent,
            message: "数据签名完成",
            details: ["dataSize": String(data.count)]
        )
        
        return signature
    }
    
    /// 验证数字签名
    func verifySignature(_ signature: Data, for data: Data) async throws -> Bool {
        let publicKey = try await getSigningPublicKey()
        let isValid = try CryptoUtils.verify(signature: signature, data: data, publicKey: publicKey)
        
        await auditLogger.logEvent(
            type: .securityEvent,
            message: "签名验证\(isValid ? "成功" : "失败")",
            details: [
                "dataSize": String(data.count),
                "signatureSize": String(signature.count),
                "result": String(isValid)
            ]
        )
        
        return isValid
    }
    
    // MARK: - Watermark
    
    /// 为文件添加水印
    func addWatermark(to fileURL: URL, watermarkText: String) async throws {
        // 这里实现水印添加逻辑
        // 根据文件类型选择不同的水印策略
        
        await auditLogger.logEvent(
            type: .securityEvent,
            message: "添加水印",
            details: [
                "filePath": fileURL.path,
                "watermarkText": watermarkText
            ]
        )
    }
    
    // MARK: - Private Methods
    
    private func setupSecurityEnvironment() {
        // 设置安全环境
        // 这里可以初始化安全策略、配置等
    }
    
    private func isAllowedFileType(url: URL) -> Bool {
        let allowedExtensions = [
            "pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx",
            "txt", "rtf", "odt", "ods", "odp",
            "jpg", "jpeg", "png", "gif", "bmp", "tiff",
            "mp4", "avi", "mov", "wmv", "flv",
            "zip", "rar", "7z", "tar", "gz"
        ]
        
        let fileExtension = url.pathExtension.lowercased()
        return allowedExtensions.contains(fileExtension)
    }
    
    private func getOrCreateSigningKey() async throws -> Data {
        let keyIdentifier = "com.cryptosystem.declassification.signing.private"
        
        if let existingKey = try keychainService.getItem(identifier: keyIdentifier) {
            return existingKey
        }
        
        let keyPair = try CryptoUtils.generateKeyPair()
        try keychainService.setItem(data: keyPair.privateKey, identifier: keyIdentifier)
        try keychainService.setItem(data: keyPair.publicKey, identifier: keyIdentifier + ".public")
        
        return keyPair.privateKey
    }
    
    private func getSigningPublicKey() async throws -> Data {
        let keyIdentifier = "com.cryptosystem.declassification.signing.private.public"
        
        guard let publicKey = try keychainService.getItem(identifier: keyIdentifier) else {
            throw SecurityError.keyRetrievalFailed("无法获取公钥")
        }
        
        return publicKey
    }
    
    // MARK: - Security Validation
    
    /// 验证文件完整性
    func validateFileIntegrity(at url: URL, expectedChecksum: String) async throws -> Bool {
        let data = try Data(contentsOf: url)
        let actualChecksum = data.sha256
        
        let isValid = actualChecksum == expectedChecksum
        
        await auditLogger.logEvent(
            type: .securityEvent,
            message: "文件完整性验证\(isValid ? "通过" : "失败")",
            details: [
                "filePath": url.path,
                "expectedChecksum": expectedChecksum,
                "actualChecksum": actualChecksum,
                "result": String(isValid)
            ]
        )
        
        return isValid
    }
    
    /// 检查文件是否被篡改
    func checkFileIntegrity(at url: URL) async throws -> FileIntegrityResult {
        let data = try Data(contentsOf: url)
        let checksum = data.sha256
        
        // 这里可以与已知的文件签名数据库进行比较
        // 目前返回基本的完整性检查结果
        
        let result = FileIntegrityResult(
            isValid: true,
            checksum: checksum,
            timestamp: Date(),
            details: "文件完整性检查通过"
        )
        
        await auditLogger.logEvent(
            type: .securityEvent,
            message: "文件完整性检查完成",
            details: [
                "filePath": url.path,
                "checksum": checksum,
                "isValid": String(result.isValid)
            ]
        )
        
        return result
    }
}

// MARK: - Supporting Types

struct FileIntegrityResult {
    let isValid: Bool
    let checksum: String
    let timestamp: Date
    let details: String
}

enum SecurityError: LocalizedError {
    case permissionDenied(String)
    case encryptionFailed(String)
    case decryptionFailed(String)
    case keyGenerationFailed(String)
    case keyRetrievalFailed(String)
    case signatureFailed(String)
    case verificationFailed(String)
    case fileTypeNotAllowed(String)
    case fileSizeExceeded(String)
    
    var errorDescription: String? {
        switch self {
        case .permissionDenied(let message):
            return "权限被拒绝: \(message)"
        case .encryptionFailed(let message):
            return "加密失败: \(message)"
        case .decryptionFailed(let message):
            return "解密失败: \(message)"
        case .keyGenerationFailed(let message):
            return "密钥生成失败: \(message)"
        case .keyRetrievalFailed(let message):
            return "密钥获取失败: \(message)"
        case .signatureFailed(let message):
            return "签名失败: \(message)"
        case .verificationFailed(let message):
            return "验证失败: \(message)"
        case .fileTypeNotAllowed(let message):
            return "文件类型不被允许: \(message)"
        case .fileSizeExceeded(let message):
            return "文件大小超出限制: \(message)"
        }
    }
} 