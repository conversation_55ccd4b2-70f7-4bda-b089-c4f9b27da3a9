using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using CryptoSystem.SystemManager.Models;
using CryptoSystem.SystemManager.Services;

namespace CryptoSystem.SystemManager.ViewModels
{
    public partial class MainWindowViewModel : ObservableObject
    {
        private readonly IUserService _userService;
        private readonly IDeviceService _deviceService;
        private readonly IPolicyService _policyService;

        [ObservableProperty]
        private string statusMessage = "就绪";

        [ObservableProperty]
        private User? currentUser;

        [ObservableProperty]
        private DateTime currentTime = DateTime.Now;

        [ObservableProperty]
        private object? currentViewModel;

        [ObservableProperty]
        private bool hasUnsavedChanges = false;

        public MainWindowViewModel(
            IUserService userService,
            IDeviceService deviceService,
            IPolicyService policyService)
        {
            _userService = userService;
            _deviceService = deviceService;
            _policyService = policyService;

            // 启动时间更新定时器
            var timer = new System.Windows.Threading.DispatcherTimer();
            timer.Interval = TimeSpan.FromSeconds(1);
            timer.Tick += (s, e) => CurrentTime = DateTime.Now;
            timer.Start();
        }

        public async Task InitializeAsync()
        {
            try
            {
                StatusMessage = "正在初始化系统...";
                
                // 模拟加载当前用户
                CurrentUser = new User 
                { 
                    UserId = "admin", 
                    Username = "admin", 
                    DisplayName = "系统管理员",
                    Role = UserRole.SystemAdmin 
                };
                
                StatusMessage = "系统初始化完成";
            }
            catch (Exception ex)
            {
                StatusMessage = $"初始化失败: {ex.Message}";
            }
        }

        [RelayCommand]
        private async Task NavigateAsync(string? page)
        {
            if (string.IsNullOrEmpty(page)) return;
            
            StatusMessage = $"正在加载 {page}...";
            await Task.Delay(500); // 模拟加载
            StatusMessage = $"当前页面: {page}";
        }

        [RelayCommand]
        private async Task LogoutAsync()
        {
            StatusMessage = "正在注销...";
            await Task.Delay(1000);
            // 这里应该清理用户状态并返回登录界面
            StatusMessage = "已注销";
        }

        public async Task CleanupAsync()
        {
            // 清理资源
            await Task.CompletedTask;
        }

        // 其他命令（暂时为空实现）
        public ICommand? RefreshCommand => null;
        public ICommand? ShowHelpCommand => null;
        public ICommand? SaveCommand => null;
        public ICommand? ShowSearchCommand => null;

        // 通知相关方法
        public void ShowTrayNotification(string message)
        {
            StatusMessage = message;
        }

        public void ShowUpdateNotification()
        {
            StatusMessage = "发现新版本，请更新";
        }
    }
}