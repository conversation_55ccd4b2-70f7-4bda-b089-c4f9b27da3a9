using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace CryptoSystem.DeclassificationClient.Models
{
    /// <summary>
    /// 脱密任务状态
    /// </summary>
    public enum DeclassificationStatus
    {
        Pending = 0,      // 待处理
        Processing = 1,   // 处理中
        Completed = 2,    // 已完成
        Failed = 3,       // 失败
        Cancelled = 4     // 已取消
    }

    /// <summary>
    /// 文件类型
    /// </summary>
    public enum FileType
    {
        Document = 0,     // 文档
        Image = 1,        // 图片
        Video = 2,        // 视频
        Audio = 3,        // 音频
        Archive = 4,      // 压缩包
        Other = 5         // 其他
    }

    /// <summary>
    /// 安全等级
    /// </summary>
    public enum SecurityLevel
    {
        Public = 0,       // 公开
        Internal = 1,     // 内部
        Confidential = 2, // 机密
        Secret = 3        // 绝密
    }

    /// <summary>
    /// 脱密文件信息
    /// </summary>
    public class DeclassificationFile
    {
        public string FileId { get; set; } = Guid.NewGuid().ToString();
        
        [Required]
        [MaxLength(255)]
        public string OriginalFileName { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(500)]
        public string OriginalFilePath { get; set; } = string.Empty;
        
        [MaxLength(500)]
        public string DeclassifiedFilePath { get; set; } = string.Empty;
        
        public long FileSize { get; set; }
        public FileType FileType { get; set; }
        
        [MaxLength(10)]
        public string FileExtension { get; set; } = string.Empty;
        
        [MaxLength(32)]
        public string FileMD5 { get; set; } = string.Empty;
        
        public SecurityLevel SecurityLevel { get; set; }
        public bool IsEncrypted { get; set; }
        public DateTime CreatedTime { get; set; } = DateTime.UtcNow;
        public DateTime LastModifiedTime { get; set; } = DateTime.UtcNow;
        
        [MaxLength(1000)]
        public string Remarks { get; set; } = string.Empty;
    }

    /// <summary>
    /// 脱密任务
    /// </summary>
    public class DeclassificationTask
    {
        public string TaskId { get; set; } = Guid.NewGuid().ToString();
        
        [Required]
        [MaxLength(200)]
        public string TaskName { get; set; } = string.Empty;
        
        [MaxLength(1000)]
        public string Description { get; set; } = string.Empty;
        
        public DeclassificationStatus Status { get; set; } = DeclassificationStatus.Pending;
        
        [Required]
        [MaxLength(100)]
        public string Applicant { get; set; } = string.Empty;
        
        [MaxLength(200)]
        public string ApplicantDepartment { get; set; } = string.Empty;
        
        [MaxLength(100)]
        public string Approver { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(200)]
        public string Recipient { get; set; } = string.Empty;
        
        [MaxLength(200)]
        public string RecipientEmail { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(1000)]
        public string Reason { get; set; } = string.Empty;
        
        public DateTime? ExpiryDate { get; set; }
        public DateTime CreatedTime { get; set; } = DateTime.UtcNow;
        public DateTime? StartTime { get; set; }
        public DateTime? CompletedTime { get; set; }
        
        [MaxLength(1000)]
        public string ErrorMessage { get; set; } = string.Empty;
        
        public int ProgressPercentage { get; set; } = 0;
        public List<DeclassificationFile> Files { get; set; } = new List<DeclassificationFile>();
    }

    /// <summary>
    /// 安全外发包
    /// </summary>
    public class SecurePackage
    {
        public string PackageId { get; set; } = Guid.NewGuid().ToString();
        
        [Required]
        [MaxLength(200)]
        public string PackageName { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(500)]
        public string PackageFilePath { get; set; } = string.Empty;
        
        public long PackageSize { get; set; }
        
        [MaxLength(100)]
        public string PackagePassword { get; set; } = string.Empty;
        
        [MaxLength(500)]
        public string DigitalSignature { get; set; } = string.Empty;
        
        [Required]
        public string TaskId { get; set; } = string.Empty;
        
        public DateTime CreatedTime { get; set; } = DateTime.UtcNow;
        public DateTime? ExpiryDate { get; set; }
        public int DownloadLimit { get; set; } = 1;
        public int DownloadCount { get; set; } = 0;
        
        public bool IsExpired => ExpiryDate.HasValue && DateTime.UtcNow > ExpiryDate.Value;
        public bool CanDownload => !IsExpired && DownloadCount < DownloadLimit;
    }

    /// <summary>
    /// 审计日志
    /// </summary>
    public class AuditLog
    {
        public string LogId { get; set; } = Guid.NewGuid().ToString();
        
        [Required]
        [MaxLength(50)]
        public string OperationType { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(500)]
        public string OperationDescription { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(100)]
        public string UserId { get; set; } = string.Empty;
        
        [MaxLength(100)]
        public string UserName { get; set; } = string.Empty;
        
        [MaxLength(45)]
        public string ClientIP { get; set; } = string.Empty;
        
        [MaxLength(50)]
        public string RelatedTaskId { get; set; } = string.Empty;
        
        [MaxLength(50)]
        public string RelatedFileId { get; set; } = string.Empty;
        
        [MaxLength(20)]
        public string Result { get; set; } = "Success";
        
        public DateTime OperationTime { get; set; } = DateTime.UtcNow;
        
        [MaxLength(2000)]
        public string AdditionalInfo { get; set; } = string.Empty;
    }
} 