<Window x:Class="KeyGenerator.Views.KeyEditDialog"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" Title="编辑密钥" Height="450" Width="500" WindowStartupLocation="CenterOwner" ResizeMode="NoResize" ShowInTaskbar="False">
    <Window.Resources>
        <Style TargetType="Label">
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="HorizontalAlignment" Value="Right"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
        </Style>
        <Style TargetType="TextBox">
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Padding" Value="5"/>
            <Setter Property="Height" Value="30"/>
        </Style>
        <Style TargetType="DatePicker">
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Height" Value="30"/>
        </Style>
        <Style TargetType="Button">
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="MinWidth" Value="80"/>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="120"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" Grid.ColumnSpan="2" Text="编辑密钥信息" FontSize="18" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,0,0,20"/>

        <!-- 密钥ID（只读） -->
        <Label Grid.Row="1" Grid.Column="0" Content="密钥ID:"/>
        <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding KeyId}" IsReadOnly="True" Background="#F0F0F0"/>

        <!-- 密钥名称（只读） -->
        <Label Grid.Row="2" Grid.Column="0" Content="密钥名称:"/>
        <TextBox Grid.Row="2" Grid.Column="1" Text="{Binding KeyName}" IsReadOnly="True" Background="#F0F0F0"/>

        <!-- 客户名称（可编辑） -->
        <Label Grid.Row="3" Grid.Column="0" Content="客户名称:"/>
        <TextBox Grid.Row="3" Grid.Column="1" Name="ClientNameTextBox" Text="{Binding ClientName}" MaxLength="100"/>

        <!-- 联系方式（可编辑） -->
        <Label Grid.Row="4" Grid.Column="0" Content="联系方式:"/>
        <TextBox Grid.Row="4" Grid.Column="1" Name="ContactInfoTextBox" Text="{Binding ContactInfo}" MaxLength="200"/>

        <!-- 生效日期（只读） -->
        <Label Grid.Row="5" Grid.Column="0" Content="生效日期:"/>
        <DatePicker Grid.Row="5" Grid.Column="1" SelectedDate="{Binding EffectiveDate}" IsEnabled="False"/>

        <!-- 过期日期（可编辑） -->
        <Label Grid.Row="6" Grid.Column="0" Content="过期日期:"/>
        <DatePicker Grid.Row="6" Grid.Column="1" Name="ExpirationDatePicker" SelectedDate="{Binding ExpirationDate}"/>

        <!-- 备注信息（可编辑） -->
        <Label Grid.Row="7" Grid.Column="0" Content="备注信息:" VerticalAlignment="Top" Margin="5,10,5,5"/>
        <TextBox Grid.Row="7" Grid.Column="1" Name="RemarksTextBox" Text="{Binding Remarks}" TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto" Height="80" MaxLength="500" Margin="5,10,5,5"/>

        <!-- 按钮区域 -->
        <StackPanel Grid.Row="9" Grid.ColumnSpan="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button Name="SaveButton" Content="保存" IsDefault="True" Click="SaveButton_Click"/>
            <Button Name="CancelButton" Content="取消" IsCancel="True" Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window> 