using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using KeyGenerator.Views;
using KeyGenerator.ViewModels;
using KeyGenerator.Services;
using System.Windows.Threading;
using CommunityToolkit.Mvvm.Input;
using CommunityToolkit.Mvvm.Messaging;
using KeyGenerator.Messages;

namespace KeyGenerator.ViewModels
{
    /// <summary>
    /// 主窗口ViewModel
    /// 管理密钥生成器的主界面导航和状态
    /// </summary>
    public class MainWindowViewModel : ViewModelBase
    {
        private readonly ILogger<MainWindowViewModel> _logger;
        private readonly IServiceProvider _serviceProvider;
        private readonly IDatabaseService _databaseService;

        private object? _currentView;
        private string _currentPageTitle = "密钥生成";
        private string _statusText = "就绪";
        private string _currentTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        private bool _isLoading = false;
        private string _title = "密钥生成系统 - 运营管理端";
        private readonly bool _isInitialized = false;
        private int _selectedTab = 0;
        
        // 数据库连接状态相关属性
        private bool _isDatabaseConnected = false;
        private string _databaseStatus = "检测中...";
        private System.Windows.Media.Brush _databaseStatusColor = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Orange);
        private DateTime _lastConnectionCheck = DateTime.Now;
        private DispatcherTimer _connectionCheckTimer;

        // 子ViewModel实例 - 只保留核心功能
        private readonly KeyGenerationViewModel _keyGenerationViewModel;
        private readonly KeyManagementViewModel _keyManagementViewModel;
        private readonly KeyDistributionViewModel _keyDistributionViewModel;
        private readonly AuditLogViewModel _auditLogViewModel;
        private readonly SettingsViewModel _settingsViewModel;

        public MainWindowViewModel(
            ILogger<MainWindowViewModel> logger,
            IServiceProvider serviceProvider,
            IDatabaseService databaseService)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _databaseService = databaseService ?? throw new ArgumentNullException(nameof(databaseService));

            // 初始化核心ViewModels
            _keyGenerationViewModel = _serviceProvider.GetRequiredService<KeyGenerationViewModel>();
            _keyManagementViewModel = _serviceProvider.GetRequiredService<KeyManagementViewModel>();
            _keyDistributionViewModel = _serviceProvider.GetRequiredService<KeyDistributionViewModel>();
            _auditLogViewModel = _serviceProvider.GetRequiredService<AuditLogViewModel>();
            _settingsViewModel = _serviceProvider.GetRequiredService<SettingsViewModel>();

            InitializeCommands();
            _ = InitializeAsync(); // Fire-and-forget

            // 默认显示密钥生成页面
            NavigateToKeyGeneration();

            // 启动时间更新定时器
            StartTimeUpdateTimer();
            
            // 启动数据库连接检测定时器
            StartDatabaseConnectionCheckTimer();

            // 注册导航消息
            WeakReferenceMessenger.Default.Register<NavigationRequestMessage>(this, HandleNavigationRequest);

            _isInitialized = true;
            _logger.LogInformation("密钥生成器主窗口初始化完成");
        }

        #region 属性

        /// <summary>
        /// 当前显示的视图
        /// </summary>
        public object? CurrentView
        {
            get => _currentView;
            set => SetProperty(ref _currentView, value);
        }

        /// <summary>
        /// 当前页面标题
        /// </summary>
        public string CurrentPageTitle
        {
            get => _currentPageTitle;
            set => SetProperty(ref _currentPageTitle, value);
        }

        /// <summary>
        /// 状态栏文本
        /// </summary>
        public string StatusText
        {
            get => _statusText;
            set => SetProperty(ref _statusText, value);
        }

        /// <summary>
        /// 当前时间
        /// </summary>
        public string CurrentTime
        {
            get => _currentTime;
            set => SetProperty(ref _currentTime, value);
        }

        /// <summary>
        /// 是否正在加载
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        public string Title
        {
            get => _title;
            set => SetProperty(ref _title, value);
        }

        public int SelectedTab
        {
            get => _selectedTab;
            set
            {
                if (SetProperty(ref _selectedTab, value) && _isInitialized)
                {
                    NavigateTo(value);
                }
            }
        }

        /// <summary>
        /// 数据库连接状态
        /// </summary>
        public bool IsDatabaseConnected
        {
            get => _isDatabaseConnected;
            set => SetProperty(ref _isDatabaseConnected, value);
        }

        /// <summary>
        /// 数据库状态文本
        /// </summary>
        public string DatabaseStatus
        {
            get => _databaseStatus;
            set => SetProperty(ref _databaseStatus, value);
        }

        /// <summary>
        /// 数据库状态指示器颜色
        /// </summary>
        public System.Windows.Media.Brush DatabaseStatusColor
        {
            get => _databaseStatusColor;
            set => SetProperty(ref _databaseStatusColor, value);
        }

        /// <summary>
        /// 最后一次连接检查时间
        /// </summary>
        public DateTime LastConnectionCheck
        {
            get => _lastConnectionCheck;
            set => SetProperty(ref _lastConnectionCheck, value);
        }

        /// <summary>
        /// 最后连接检查时间的显示文本
        /// </summary>
        public string LastConnectionCheckText => $"最后检查: {LastConnectionCheck:HH:mm:ss}";

        public KeyGenerationViewModel KeyGenerationVM => _keyGenerationViewModel;
        public KeyManagementViewModel KeyManagementVM => _keyManagementViewModel;

        #endregion

        #region 命令

        public ICommand NavigateToKeyGenerationCommand { get; private set; } = null!;
        public ICommand NavigateToKeyManagementCommand { get; private set; } = null!;
        public ICommand NavigateToAuditLogCommand { get; private set; } = null!;
        public ICommand NavigateToSettingsCommand { get; private set; } = null!;
        public ICommand ShowHelpCommand { get; private set; } = null!;
        public ICommand ShowAboutCommand { get; private set; } = null!;
        public ICommand ExitApplicationCommand { get; private set; } = null!;
        public ICommand RefreshDatabaseConnectionCommand { get; private set; } = null!;

        #endregion

        #region 私有方法

        private void InitializeCommands()
        {
            NavigateToKeyGenerationCommand = new RelayCommand(NavigateToKeyGeneration);
            NavigateToKeyManagementCommand = new RelayCommand(NavigateToKeyManagement);
            NavigateToAuditLogCommand = new RelayCommand(NavigateToAuditLog);
            NavigateToSettingsCommand = new RelayCommand(NavigateToSettings);
            ShowHelpCommand = new RelayCommand(ShowHelp);
            ShowAboutCommand = new RelayCommand(ShowAbout);
            ExitApplicationCommand = new RelayCommand(ExitApplication);
            RefreshDatabaseConnectionCommand = new RelayCommand(async () => await CheckDatabaseConnectionAsync());
        }

        private async Task InitializeAsync()
        {
            try
            {
                IsLoading = true;
                StatusText = "正在初始化...";

                // 初始化数据库连接检查
                await CheckDatabaseConnectionAsync();

                StatusText = "初始化完成";
                _logger.LogInformation("系统初始化完成");
            }
            catch (Exception ex)
            {
                StatusText = "初始化失败";
                _logger.LogError(ex, "系统初始化失败: {Message}", ex.Message);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void StartTimeUpdateTimer()
        {
            var timer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(1)
            };
            timer.Tick += (s, e) => CurrentTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            timer.Start();
        }

        private void StartDatabaseConnectionCheckTimer()
        {
            _connectionCheckTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMinutes(1) // 每分钟检查一次
            };
            _connectionCheckTimer.Tick += async (s, e) => await CheckDatabaseConnectionAsync();
            _connectionCheckTimer.Start();
        }

        private async Task CheckDatabaseConnectionAsync()
        {
            try
            {
                var isConnected = await _databaseService.CheckConnectionAsync();
                UpdateDatabaseConnectionStatus(isConnected);
                LastConnectionCheck = DateTime.Now;
                OnPropertyChanged(nameof(LastConnectionCheckText));

                if (isConnected)
                {
                    _logger.LogDebug("数据库连接检查: 连接正常");
                }
                else
                {
                    _logger.LogWarning("数据库连接检查: 连接断开");
                }
            }
            catch (Exception ex)
            {
                UpdateDatabaseConnectionStatus(false);
                LastConnectionCheck = DateTime.Now;
                OnPropertyChanged(nameof(LastConnectionCheckText));
                _logger.LogError(ex, "数据库连接检查失败: {Message}", ex.Message);
            }
        }

        private void UpdateDatabaseConnectionStatus(bool isConnected)
        {
            IsDatabaseConnected = isConnected;
            
            if (isConnected)
            {
                DatabaseStatus = "已连接";
                DatabaseStatusColor = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Green);
            }
            else
            {
                DatabaseStatus = "连接断开";
                DatabaseStatusColor = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Red);
            }
        }

        private async void HandleNavigationRequest(object recipient, NavigationRequestMessage message)
        {
            _logger.LogInformation("接收到导航请求: {Target}, 参数: {Parameter}", message.TargetPage, message.Parameter);
            StatusText = $"正在导航到 {message.TargetPage}...";
            
            await Dispatcher.CurrentDispatcher.InvokeAsync(async () =>
            {
                CurrentView = message.TargetPage switch
                {
                    "KeyManagement" => _keyManagementViewModel,
                    "KeyGeneration" => _keyGenerationViewModel,
                    "KeyDistribution" => _keyDistributionViewModel,
                    "AuditLog" => _auditLogViewModel,
                    "ClientManagement" => _clientManagementViewModel,
                    _ => CurrentView
                };

                // Optionally, pass parameter to the view model if supported
                if (message.Parameter != null)
                {
                    switch (message.TargetPage)
                    {
                        case "KeyDistribution":
                            if (_keyDistributionViewModel != null) _keyDistributionViewModel.LoadKeyForDistribution(message.Parameter.ToString());
                            break;
                        // Add other cases as needed
                    }
                }
            });
        }

        private void NavigateToKeyGeneration()
        {
            try
            {
                CurrentView = _keyGenerationViewModel;
                CurrentPageTitle = "密钥生成";
                SelectedTab = 0;
                StatusText = "密钥生成页面";
                _logger.LogDebug("导航到密钥生成页面");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导航到密钥生成页面失败");
                StatusText = "页面加载失败";
            }
        }

        private void NavigateToKeyManagement()
        {
            try
            {
                CurrentView = _keyManagementViewModel;
                CurrentPageTitle = "密钥管理";
                SelectedTab = 1;
                StatusText = "密钥管理页面";
                _logger.LogDebug("导航到密钥管理页面");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导航到密钥管理页面失败");
                StatusText = "页面加载失败";
            }
        }

        private void NavigateToAuditLog()
        {
            CurrentView = _auditLogViewModel;
            CurrentPageTitle = "审计日志";
            _auditLogViewModel.LoadLogsCommand.Execute(null);
        }

        private void NavigateToSettings()
        {
            CurrentView = _settingsViewModel;
            CurrentPageTitle = "系统设置";
            _settingsViewModel.LoadSettingsCommand.Execute(null);
        }

        private void ShowHelp()
        {
            try
            {
                System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                {
                    FileName = "https://docs.cryptosystem.local/key-generator",
                    UseShellExecute = true
                });
                _logger.LogInformation("打开帮助文档");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "打开帮助文档失败");
                StatusText = "无法打开帮助文档";
            }
        }

        private void ShowAbout()
        {
            try
            {
                var aboutInfo = $"密钥生成系统 - 运营管理端\n版本: 1.0.0\n构建时间: {System.IO.File.GetCreationTime(System.Reflection.Assembly.GetExecutingAssembly().Location):yyyy-MM-dd}\n\n专为密钥生成和管理设计的专业工具";
                System.Windows.MessageBox.Show(aboutInfo, "关于", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
                _logger.LogInformation("显示关于信息");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "显示关于信息失败");
            }
        }

        private void ExitApplication()
        {
            try
            {
                var result = System.Windows.MessageBox.Show("确定要退出密钥生成系统吗？", "确认退出", 
                    System.Windows.MessageBoxButton.YesNo, System.Windows.MessageBoxImage.Question);
                
                if (result == System.Windows.MessageBoxResult.Yes)
                {
                    _connectionCheckTimer?.Stop();
                    _logger.LogInformation("用户退出应用程序");
                    System.Windows.Application.Current.Shutdown();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "退出应用程序时发生错误");
            }
        }

        private void NavigateTo(int index)
        {
            switch (index)
            {
                case 0:
                    NavigateToKeyGeneration();
                    break;
                case 1:
                    NavigateToKeyManagement();
                    break;
                default:
                    _logger.LogWarning("无效的导航索引: {Index}", index);
                    break;
            }
        }

        #endregion
    }
}