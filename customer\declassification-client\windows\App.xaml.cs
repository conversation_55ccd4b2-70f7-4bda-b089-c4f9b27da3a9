using CryptoSystem.DeclassificationClient.Services;
using CryptoSystem.DeclassificationClient.ViewModels;
using CryptoSystem.DeclassificationClient.Views;

namespace CryptoSystem.DeclassificationClient
{
    /// <summary>
    /// 脱密客户端应用程序
    /// </summary>
    public partial class App : Application
    {
        private ServiceProvider? _serviceProvider;

        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);

            // 配置服务
            var services = new ServiceCollection();
            ConfigureServices(services);
            _serviceProvider = services.BuildServiceProvider();

            // 创建并显示主窗口
            var mainWindow = _serviceProvider.GetRequiredService<MainWindow>();
            mainWindow.Show();
        }

        protected override void OnExit(ExitEventArgs e)
        {
            _serviceProvider?.Dispose();
            base.OnExit(e);
        }

        private void ConfigureServices(IServiceCollection services)
        {
            // 配置
            var configuration = new ConfigurationBuilder()
                .SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .Build();

            services.AddSingleton<IConfiguration>(configuration);

            // 日志
            services.AddLogging(builder =>
            {
                builder.AddConsole();
                builder.AddDebug();
                builder.SetMinimumLevel(LogLevel.Information);
            });

            // 服务
            services.AddScoped<IDeclassificationService, DeclassificationService>();
            services.AddScoped<IFileService, FileService>();
            services.AddScoped<ICryptoService, CryptoService>();
            services.AddScoped<IAuditService, AuditService>();
            services.AddScoped<ISecurityService, SecurityService>();

            // ViewModels
            services.AddTransient<MainWindowViewModel>();
            services.AddTransient<DeclassificationViewModel>();
            services.AddTransient<FileListViewModel>();
            services.AddTransient<SettingsViewModel>();

            // Views
            services.AddTransient<MainWindow>();
        }
    }
}