# Xcode
#
# gitignore contributors: remember to keep this file platform-agnostic
# gitignore test: ./tools/test-gitignore.sh

## User-specific
*.xcuserdatad
*.xcworkspace/contents.xcworkspacedata
*.xctestrun
*.mood
*.xcscheme
xcuserdata/

## Build generated
build/
DerivedData/
*.app
*.ipa
*.log
*.dSYM/
*.swiftmodule
*.swiftdoc
*.swiftinterface

## Swift Package Manager
.build/
Packages/
*.xcworkspace

# Cocoapods
Pods/
Podfile.lock

# Carthage
Carthage/Build
Carthage/Checkouts

# fastlane
fastlane/report.xml
fastlane/Preview.html
fastlane/screenshots
fastlane/test_output

# Danger
Danger/

# Realm
*.realm.lock
*.realm.note
*.realm.management
*.realm.sync

# Secrets
.env
config.json

# Other
.DS_Store
*.orig
*.rej
*.swo
*.swp
*~
.#*
.idea/
*.bak
*.tmp
*.temp
*#
%*
//*
*.*.swp