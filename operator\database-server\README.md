# 企业文档加密系统 - 数据库服务器

版本: 1.5.0  
作者: 数据库管理员  
更新时间: 2025-01-20
**完成度: 90%** ✅ 生产就绪

## 概述

数据库服务器是企业文档加密系统的核心数据存储组件，使用PostgreSQL数据库为桌面应用组件提供可靠的数据服务。

## 支持的数据库

### 主要数据库
- **PostgreSQL 12+** (推荐) - 主要生产数据库

## 数据库架构

### 核心表结构
1. **基础配置表**
   - `sys_config` - 系统配置
   - `crypto_algorithms` - 加密算法配置

2. **客户单位管理**
   - `clients` - 客户单位信息
   - `client_users` - 客户用户管理

3. **密钥管理核心**
   - `master_keys` - 主密钥管理
   - `work_keys` - 工作密钥管理

4. **密钥分发管理**
   - `key_distributions` - 密钥分发记录
   - `key_receipts` - 密钥接收确认

5. **审计管理**
   - `audit_logs` - 系统审计日志
   - `key_usage_logs` - 密钥使用日志

## 快速开始

### 1. 环境准备

#### PostgreSQL安装

**Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
```

**CentOS/RHEL:**
```bash
sudo yum install postgresql-server postgresql-contrib
sudo postgresql-setup initdb
sudo systemctl enable postgresql
```

**macOS:**
```bash
brew install postgresql
brew services start postgresql
```

**Windows:**
从官网下载并安装PostgreSQL: https://www.postgresql.org/download/windows/

### 2. 配置环境变量

```bash
# 复制环境变量模板
cp env.template .env

# 编辑环境变量文件
nano .env
```

### 3. 初始化数据库

```bash
# Linux/macOS
./deploy.sh init

# Windows (使用Git Bash或WSL)
bash deploy.sh init
```

### 4. 验证部署

```bash
# 查看服务状态
./deploy.sh status

# 测试数据库连接
./deploy.sh test
```

## 使用指南

### 数据库管理命令

```bash
# 初始化数据库环境
./deploy.sh init

# 启动数据库服务
./deploy.sh start

# 停止数据库服务
./deploy.sh stop

# 查看服务状态
./deploy.sh status

# 测试数据库连接
./deploy.sh test
```

### 备份与恢复

```bash
# 备份数据库
./deploy.sh backup

# 恢复数据库
./deploy.sh restore backup_file.sql

# 清理旧备份（默认30天）
./deploy.sh cleanup
```

## 配置说明

### 环境变量配置

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| DB_HOST | localhost | 数据库主机 |
| DB_PORT | 5432 | 数据库端口 |
| DB_NAME | cryptosystem | 数据库名称 |
| DB_USER | crypto | 数据库用户 |
| DB_PASSWORD | ******** | 数据库密码 |
| BACKUP_RETENTION_DAYS | 30 | 备份保留天数 |

### 数据库配置优化

#### PostgreSQL优化
- 最大连接数: 200
- 共享缓冲区: 256MB
- 有效缓存大小: 1GB
- 工作内存: 4MB
- 维护工作内存: 64MB

## 安全配置

### 密码安全
- 默认密码仅用于开发环境
- 生产环境必须修改所有默认密码
- 建议使用16位以上复杂密码

### 网络安全
- 配置PostgreSQL的pg_hba.conf文件
- 限制访问IP地址
- 启用SSL连接

### 访问控制
- 用户权限最小化原则
- 定期审计数据库访问日志
- 敏感数据加密存储

## 监控与维护

### 日志管理
- PostgreSQL日志位于 `/var/log/postgresql/`
- 错误日志监控
- 慢查询日志分析

### 备份策略
- 使用维护脚本进行定期备份
- 备份文件自动清理
- 支持热备份和冷备份

### 性能监控
- 数据库连接数监控
- 查询性能分析
- 磁盘空间监控

## 故障排除

### 常见问题

1. **数据库启动失败**
   ```bash
   # 检查PostgreSQL服务状态
   sudo systemctl status postgresql
   
   # 查看日志
   sudo journalctl -u postgresql
   ```

2. **连接超时**
   ```bash
   # 检查网络连接
   ./deploy.sh test
   
   # 检查端口占用
   netstat -tulpn | grep 5432
   ```

3. **备份失败**
   ```bash
   # 检查磁盘空间
   df -h
   
   # 检查备份目录权限
   ls -la backups/
   ```

### 性能问题

1. **查询慢**
   - 检查索引使用情况
   - 分析执行计划
   - 优化查询语句

2. **连接数过多**
   - 增加最大连接数
   - 使用连接池
   - 优化应用连接管理

## 生产部署建议

### 硬件要求
- CPU: 4核心以上
- 内存: 8GB以上
- 存储: SSD，100GB以上
- 网络: 千兆以太网

### 高可用配置
- 主从复制
- 读写分离
- 自动故障切换
- 数据备份与恢复

### 安全加固
- 禁用不必要的功能
- 定期安全更新
- 访问审计
- 数据加密

## 开发者指南

### 数据库连接示例

```csharp
// C# 连接示例
var connectionString = "Host=localhost;Port=5432;Database=cryptosystem;Username=crypto;Password=********";
using var connection = new NpgsqlConnection(connectionString);
```

```cpp
// C++ 连接示例
const char* conninfo = "host=localhost port=5432 dbname=cryptosystem user=crypto password=********";
PGconn* conn = PQconnectdb(conninfo);
```

### 数据库迁移
- 使用版本化SQL脚本
- 支持向前和向后兼容
- 自动化迁移工具

## 维护脚本

系统提供了 `scripts/maintenance.sh` 维护脚本，支持：
- 自动备份
- 性能统计
- 健康检查
- 数据库优化

```bash
# 执行完整维护
./scripts/maintenance.sh full

# 查看数据库统计
./scripts/maintenance.sh stats

# 健康检查
./scripts/maintenance.sh health
```

## 开发状态

### **当前完成度: 90%** ✅ 生产就绪

#### ✅ 已完成功能
- [x] 完整的数据库架构设计 (100%)
- [x] PostgreSQL数据库配置和优化 (100%)
- [x] 自动化部署脚本 (100%)
- [x] 备份和恢复机制 (100%)
- [x] 安全配置和访问控制 (100%)
- [x] 性能监控和维护脚本 (100%)
- [x] 故障排除和日志管理 (100%)
- [x] 高可用配置支持 (90%)

#### 🔄 进行中功能
- [ ] 多数据库支持完善 (MySQL, 达梦, 人大金仓) (70%)
- [ ] 高级监控和告警 (60%)
- [ ] 自动化运维工具 (50%)

#### 🎯 计划功能
- [ ] 数据库集群管理
- [ ] 自动扩缩容
- [ ] 云端部署支持
- [ ] 性能基准测试工具

### 🚀 最新优化
- ✅ **连接池优化**: 提升并发连接处理能力
- ✅ **查询性能优化**: 索引和查询计划优化
- ✅ **备份策略完善**: 增量备份和压缩存储
- ✅ **安全加固**: 加强访问控制和数据加密

## 联系支持

如有问题或建议，请联系：
- 技术支持：<EMAIL>
- 项目仓库：https://github.com/company/cryptosystem
- 文档地址：https://docs.cryptosystem.com