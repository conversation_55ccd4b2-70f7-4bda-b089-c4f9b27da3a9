<Window x:Class="CryptoSystem.SystemManager.Views.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:viewmodels="clr-namespace:CryptoSystem.SystemManager.ViewModels" mc:Ignorable="d" Title="CryptoSystem 系统管理器" Height="800" Width="1200" Loaded="Window_Loaded">

    <Window.DataContext>
        <viewmodels:MainWindowViewModel />
    </Window.DataContext>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="30"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="DarkBlue">
            <TextBlock Text="CryptoSystem 系统管理器" Foreground="White" FontSize="20" HorizontalAlignment="Center" VerticalAlignment="Center"/>
        </Border>

        <!-- 主内容 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="200"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 导航菜单 -->
            <StackPanel Grid.Column="0" Background="LightGray" Margin="5">
                <Button Content="仪表板" Margin="5"/>
                <Button Content="用户管理" Margin="5"/>
                <Button Content="设备管理" Margin="5"/>
                <Button Content="策略管理" Margin="5"/>
                <Button Content="审计日志" Margin="5"/>
            </StackPanel>

            <!-- 内容区域 -->
            <Border Grid.Column="1" Background="White" Margin="5">
                <TextBlock Text="欢迎使用 CryptoSystem 系统管理器" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="16"/>
            </Border>
        </Grid>

        <!-- 状态栏 -->
        <Border Grid.Row="2" Background="Gray">
            <TextBlock Text="{Binding StatusMessage}" Foreground="White" Margin="10,5"/>
        </Border>
    </Grid>
</Window> 