# 构建目录
build/
out/
bin/
lib/
Debug/
Release/
x64/
x86/

# CMake生成文件
CMakeFiles/
CMakeCache.txt
cmake_install.cmake
compile_commands.json
CTestTestfile.cmake
Makefile
*.vcxproj
*.vcxproj.filters
*.sln

# Maven构建目录
target/

# Node.js依赖和构建目录
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dist/
coverage/
.nyc_output/

# 编译生成的文件
*.exe
*.dll
*.so
*.dylib
*.lib
*.a
*.pdb
*.ilk
*.obj
*.o
*.out

# Visual Studio文件
.vs/
ipch/
*.suo
*.user
*.VC.db
*.VC.opendb
*.aps
*.ncb
*.opensdf
*.sdf
