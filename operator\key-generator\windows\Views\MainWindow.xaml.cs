using System;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Extensions.Logging;
using KeyGenerator.ViewModels;

namespace KeyGenerator.Views
{
    /// <summary>
    /// 主窗口后端代码
    /// </summary>
    public partial class MainWindow : Window
    {
        private readonly ILogger<MainWindow> _logger;
        private readonly MainWindowViewModel _viewModel;

        public MainWindow()
        {
            InitializeComponent();
            
            _logger = App.GetService<ILogger<MainWindow>>();
            _viewModel = App.GetService<MainWindowViewModel>();
            
            DataContext = _viewModel;
            
            // 默认显示密钥生成页面
            NavigateToKeyGeneration(this, new RoutedEventArgs());
        }

        private void NavigateToKeyGeneration(object? sender, RoutedEventArgs? e)
        {
            try
            {
                _logger.LogInformation("开始导航到密钥生成页面");
                
                // 尝试获取ViewModel
                KeyGenerationViewModel? keyGenerationViewModel;
                try
                {
                    keyGenerationViewModel = App.GetService<KeyGenerationViewModel>();
                    _logger.LogInformation("成功获取KeyGenerationViewModel");
                }
                catch (Exception vmEx)
                {
                    _logger.LogError(vmEx, "获取KeyGenerationViewModel失败");
                    ShowError("ViewModel创建失败", $"无法创建KeyGenerationViewModel: {vmEx.Message}");
                    return;
                }
                
                // 尝试创建View
                KeyGenerationView keyGenerationView;
                try
                {
                    keyGenerationView = new KeyGenerationView();
                    _logger.LogInformation("成功创建KeyGenerationView");
                }
                catch (Exception viewEx)
                {
                    _logger.LogError(viewEx, "创建KeyGenerationView失败");
                    ShowError("View创建失败", $"无法创建KeyGenerationView: {viewEx.Message}");
                    return;
                }
                
                // 设置DataContext
                try
                {
                    keyGenerationView.DataContext = keyGenerationViewModel;
                    _logger.LogInformation("成功设置DataContext");
                }
                catch (Exception dcEx)
                {
                    _logger.LogError(dcEx, "设置DataContext失败");
                    ShowError("DataContext设置失败", $"无法设置DataContext: {dcEx.Message}");
                    return;
                }
                
                // 设置内容
                try
                {
                    MainContent.Content = keyGenerationView;
                    PageTitle.Text = "密钥生成";
                    UpdateNavigationButtons("KeyGeneration");
                    _logger.LogInformation("成功导航到密钥生成页面");
                }
                catch (Exception contentEx)
                {
                    _logger.LogError(contentEx, "设置页面内容失败");
                    ShowError("页面显示失败", $"无法显示页面内容: {contentEx.Message}");
                    return;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导航到密钥生成页面失败");
                ShowError("导航失败", ex.Message);
            }
        }

        private void NavigateToKeyManagement(object? sender, RoutedEventArgs? e)
        {
            try
            {
                var keyManagementView = new KeyManagementView();
                var keyManagementViewModel = App.GetService<KeyManagementViewModel>();
                keyManagementView.DataContext = keyManagementViewModel;
                
                MainContent.Content = keyManagementView;
                PageTitle.Text = "密钥管理";
                UpdateNavigationButtons("KeyManagement");
                _logger.LogInformation("导航到密钥管理页面");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导航到密钥管理页面失败");
                ShowError("导航失败", ex.Message);
            }
        }











        private void ShowAbout(object? sender, RoutedEventArgs? e)
        {
            try
            {
                MessageBox.Show(this, 
                    "运营商密钥管理系统\n" +
                    "版本：1.5.0\n" +
                    "Copyright © 2025 Enterprise Security Solutions\n\n" +
                    "专为运营商设计的密钥管理工具\n" +
                    "核心功能：生成密钥、修改密钥、删除密钥\n" +
                    "支持数据库连接状态实时监控", 
                    "关于", 
                    MessageBoxButton.OK, 
                    MessageBoxImage.Information);
                _logger.LogInformation("显示关于信息");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "显示关于信息失败");
                ShowError("关于", ex.Message);
            }
        }

        private void UpdateNavigationButtons(string currentPage)
        {
            // 重置所有按钮状态
            KeyGenerationButton.Background = System.Windows.Media.Brushes.Transparent;
            KeyManagementButton.Background = System.Windows.Media.Brushes.Transparent;

            // 高亮当前页面按钮
            var activeBrush = (System.Windows.Media.Brush)FindResource("ActiveButtonBrush");
            switch (currentPage)
            {
                case "KeyGeneration":
                    KeyGenerationButton.Background = activeBrush;
                    break;
                case "KeyManagement":
                    KeyManagementButton.Background = activeBrush;
                    break;
            }
        }

        private static void ShowError(string title, string message)
        {
            MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Error);
        }

        private void UpdateStatus(string status)
        {
            StatusText.Text = status;
        }

        protected override void OnClosed(EventArgs e)
        {
            _logger.LogInformation("主窗口关闭");
            base.OnClosed(e);
        }
    }
} 