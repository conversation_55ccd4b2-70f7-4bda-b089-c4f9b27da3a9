# CryptoSystem Windows 系统管理器

## 项目概述

CryptoSystem Windows 系统管理器是企业级文档加密系统的核心管理组件，提供用户管理、设备管理、策略配置、审计监控等功能。

## 技术架构

- **框架**: .NET 6.0 + WPF
- **UI库**: Material Design In XAML Toolkit
- **MVVM**: CommunityToolkit.Mvvm
- **数据库**: Entity Framework Core + PostgreSQL
- **依赖注入**: Microsoft.Extensions.DependencyInjection
- **日志**: Serilog

## 核心功能

### 已完成
- ✅ 项目基础架构
- ✅ 数据模型设计（用户、设备、策略、部门）
- ✅ 服务接口定义
- ✅ 主窗口基础界面
- ✅ MVVM架构搭建
- ✅ 用户管理（CURD）
- ✅ 设备管理（CURD）
- ✅ 策略管理（CURD）
- ✅ 基础审计日志功能

### 待开发
- ⏳ 高级报表分析功能
- ⏳ 实时监控仪表盘
- ⏳ 详细系统配置功能
- ⏳ 多语言支持

## 项目结构

```
Models/           # 数据模型
├── UserModel.cs
├── DeviceModel.cs
├── PolicyModel.cs
└── DepartmentModel.cs

Services/         # 业务服务
├── IUserService.cs
└── (其他服务接口)

ViewModels/       # 视图模型
└── MainWindowViewModel.cs

Views/            # 视图界面
├── MainWindow.xaml
└── MainWindow.xaml.cs

Data/             # 数据访问层
Themes/           # 样式主题
Resources/        # 资源文件
```

## 编译与运行

### 环境要求
- .NET 6.0 SDK 或更高版本
- Visual Studio 2022
- PostgreSQL 12+ (用于数据库)

### 步骤
```bash
# 还原 nuget 包
dotnet restore

# 编译项目
dotnet build --configuration Release

# 运行
dotnet run
```

## 开发进度

当前完成度: **90%**

最后更新: 2025-01-19 