/*
 * key_sync_service.h
 *
 * Cryptosystem Linux 密钥同步服务头文件
 * 定义与服务器通信获取密钥和策略的接口
 */

#ifndef KEY_SYNC_SERVICE_H
#define KEY_SYNC_SERVICE_H

#include <stdint.h>
#include <stddef.h>
#include "crypto_manager.h"

// 同步状态
typedef enum {
    SYNC_STATUS_OK = 0,           // 同步成功
    SYNC_STATUS_ERROR = -1,       // 同步错误
    SYNC_STATUS_UNAUTHORIZED = -2, // 未授权
    SYNC_STATUS_NETWORK = -3,     // 网络错误
    SYNC_STATUS_SERVER = -4       // 服务器错误
} sync_status;

// 策略类型
typedef enum {
    POLICY_TYPE_PATH = 0,         // 路径策略
    POLICY_TYPE_APP = 1,          // 应用策略
    POLICY_TYPE_USER = 2,         // 用户策略
    POLICY_TYPE_GROUP = 3,        // 组策略
    POLICY_TYPE_DEVICE = 4        // 设备策略
} policy_type;

// 策略操作
typedef enum {
    POLICY_ACTION_ENCRYPT = 0,    // 加密
    POLICY_ACTION_DECRYPT = 1,    // 解密
    POLICY_ACTION_DENY = 2,       // 拒绝
    POLICY_ACTION_ALLOW = 3       // 允许
} policy_action;

// 策略规则
typedef struct {
    policy_type type;             // 策略类型
    policy_action action;         // 策略动作
    char target[256];             // 目标(路径/应用/用户/组/设备)
    crypto_algorithm algorithm;   // 加密算法
    crypto_mode mode;             // 加密模式
    uint32_t key_version;         // 密钥版本
    uint32_t priority;            // 优先级
    uint64_t timestamp;           // 时间戳
} policy_rule;

/**
 * 初始化密钥同步服务
 * 
 * @param server_url 服务器URL
 * @param device_id  设备ID
 * @param device_key 设备密钥(用于身份验证)
 * 
 * @return 成功返回SYNC_STATUS_OK，失败返回错误状态码
 */
sync_status key_sync_init(const char *server_url, const char *device_id, const char *device_key);

/**
 * 关闭密钥同步服务
 */
void key_sync_cleanup(void);

/**
 * 同步密钥
 * 
 * @param algorithm   加密算法
 * @param key_version 密钥版本
 * 
 * @return 成功返回SYNC_STATUS_OK，失败返回错误状态码
 */
sync_status key_sync_get_key(crypto_algorithm algorithm, uint32_t key_version);

/**
 * 同步策略
 * 
 * @param rules       策略规则数组
 * @param max_rules   数组最大容量
 * @param rule_count  输出实际规则数量
 * 
 * @return 成功返回SYNC_STATUS_OK，失败返回错误状态码
 */
sync_status key_sync_get_policies(policy_rule *rules, size_t max_rules, size_t *rule_count);

/**
 * 上报加密操作日志
 * 
 * @param operation   操作类型("encrypt", "decrypt", "access", "deny")
 * @param path        文件路径
 * @param user_id     用户ID
 * @param app_id      应用ID
 * @param result      结果状态
 * 
 * @return 成功返回SYNC_STATUS_OK，失败返回错误状态码
 */
sync_status key_sync_report_activity(const char *operation, const char *path, 
                                  const char *user_id, const char *app_id, int result);

#endif /* KEY_SYNC_SERVICE_H */ 