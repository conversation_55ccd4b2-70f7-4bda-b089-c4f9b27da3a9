#pragma warning disable CS8632 // 只能在 "#nullable" 注释上下文内的代码中使用可为 null 的引用类型的注释

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using System.Text.Json;
using System.IO;
using System.Threading;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using KeyGenerator.Models;
using KeyGenerator.Services;

namespace KeyGenerator.Services
{
    /// <summary>
    /// 审计事件类型
    /// </summary>
    public enum AuditEventType
    {
        // 密钥管理事件
        KeyCreated,
        KeyRotated,
        KeyRevoked,
        KeyAccessed,
        KeyDistributed,
        KeyExported,
        KeyImported,
        
        // 用户管理事件
        UserLogin,
        UserLogout,
        UserCreated,
        UserDeleted,
        UserPasswordChanged,
        UserRoleChanged,
        
        // 系统管理事件
        SystemStarted,
        SystemStopped,
        ConfigurationChanged,
        PolicyUpdated,
        BackupCreated,
        BackupRestored,
        
        // 文档加密事件
        DocumentEncrypted,
        DocumentDecrypted,
        DocumentAccessed,
        DocumentShared,
        DocumentDeleted,
        
        // 安全事件
        SecurityViolation,
        UnauthorizedAccess,
        LoginFailed,
        PasswordAttack,
        DataBreach,
        
        // 系统错误事件
        SystemError,
        DatabaseError,
        NetworkError,
        ServiceError
    }

    /// <summary>
    /// 审计事件严重级别
    /// </summary>
    public enum AuditSeverity
    {
        Low,      // 低
        Medium,   // 中
        High,     // 高
        Critical  // 严重
    }

    /// <summary>
    /// 审计事件
    /// </summary>
    public class AuditEvent
    {
        public required string EventId { get; set; } = Guid.NewGuid().ToString();
        public required AuditEventType EventType { get; set; }
        public AuditSeverity Severity { get; set; } = AuditSeverity.Medium;
        public DateTime Timestamp { get; set; } = DateTime.Now;
        public required string UserId { get; set; } = "";
        public required string UserName { get; set; } = "";
        public required string SessionId { get; set; } = "";
        public required string IPAddress { get; set; } = "";
        public required string UserAgent { get; set; } = "";
        public required string ResourceId { get; set; } = "";
        public required string ResourceType { get; set; } = "";
        public required string Action { get; set; } = "";
        public required string Description { get; set; } = "";
        public required string Result { get; set; } = "Success";
        public required string ErrorMessage { get; set; } = "";
        public required Dictionary<string, object> Details { get; set; } = [];
        public required string Source { get; set; } = "KeyGenerator";
        public required string Category { get; set; } = "";
        public long Duration { get; set; } = 0; // 毫秒
    }

    /// <summary>
    /// 审计查询请求
    /// </summary>
    public class AuditQueryRequest
    {
        public DateTime? StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public List<AuditEventType>? EventTypes { get; set; }
        public List<AuditSeverity>? Severities { get; set; }
        public string? UserId { get; set; }
        public string? ResourceId { get; set; }
        public string? ResourceType { get; set; }
        public string? Result { get; set; }
        public string? SearchText { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 50;
        public string SortBy { get; set; } = "Timestamp";
        public bool SortDescending { get; set; } = true;
    }

    /// <summary>
    /// 审计统计信息
    /// </summary>
    public class AuditStatistics
    {
        public int TotalEvents { get; set; }
        public int TodayEvents { get; set; }
        public int WeekEvents { get; set; }
        public int MonthEvents { get; set; }
        public Dictionary<AuditEventType, int> EventTypeStats { get; set; } = new();
        public Dictionary<AuditSeverity, int> SeverityStats { get; set; } = new();
        public Dictionary<string, int> UserStats { get; set; } = new();
        public Dictionary<string, int> HourlyStats { get; set; } = new();
        public List<AuditEvent> RecentCriticalEvents { get; set; } = new();
        public List<AuditEvent> RecentFailedEvents { get; set; } = new();
    }

    /// <summary>
    /// 审计配置
    /// </summary>
    public class AuditConfiguration
    {
        public bool Enabled { get; set; } = true;
        public string LogLevel { get; set; } = "INFO";
        public int MaxLogSize { get; set; } = 1000000; // 1MB
        public int MaxLogFiles { get; set; } = 10;
        public int RetentionDays { get; set; } = 90;
        public bool EnableRealTimeAlerts { get; set; } = true;
        public bool EnableFileLogging { get; set; } = true;
        public bool EnableDatabaseLogging { get; set; } = true;
        public bool EnableRemoteLogging { get; set; } = false;
        public string RemoteLogEndpoint { get; set; } = "";
        public string LogDirectory { get; set; } = "logs";
        public List<AuditEventType> CriticalEvents { get; set; } = new();
        public List<string> AlertRecipients { get; set; } = new();
    }

    /// <summary>
    /// 审计服务实现
    /// </summary>
    public class AuditService : IAuditService, IHostedService
    {
        private readonly ILogger<AuditService> _logger;
        private readonly IConfiguration _configuration;
        private readonly AuditConfiguration _auditConfig;
        private readonly List<AuditEvent> _eventCache;
        private readonly Timer _flushTimer;
        private readonly Timer _cleanupTimer;
        private readonly SemaphoreSlim _semaphore;
        private readonly string _logDirectory;
        private readonly string _currentLogFile;
        private readonly IDatabaseService _databaseService;

        public AuditService(ILogger<AuditService> logger, IConfiguration configuration, IDatabaseService databaseService)
        {
            _logger = logger;
            _configuration = configuration;
            _auditConfig = LoadConfiguration();
            _eventCache = new List<AuditEvent>();
            _semaphore = new SemaphoreSlim(1, 1);
            
            // 设置日志目录
            _logDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, _auditConfig.LogDirectory);
            Directory.CreateDirectory(_logDirectory);
            
            _currentLogFile = Path.Combine(_logDirectory, $"audit-{DateTime.Now:yyyyMMdd}.log");
            _databaseService = databaseService;
            
            // 设置定时器
            _flushTimer = new Timer(FlushEvents, null, TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));
            _cleanupTimer = new Timer(CleanupOldLogs, null, TimeSpan.FromHours(1), TimeSpan.FromHours(24));
            
            // 初始化示例数据
            InitializeSampleData();
        }

        private AuditConfiguration LoadConfiguration()
        {
            var config = new AuditConfiguration();
            _configuration.GetSection("Audit").Bind(config);
            
            // 设置关键事件类型
            config.CriticalEvents = new List<AuditEventType>
            {
                AuditEventType.SecurityViolation,
                AuditEventType.UnauthorizedAccess,
                AuditEventType.DataBreach,
                AuditEventType.KeyRevoked,
                AuditEventType.SystemError
            };
            
            return config;
        }

        private void InitializeSampleData()
        {
            // 创建示例审计事件
            var sampleEvents = new List<AuditEvent>
            {
                new AuditEvent
                {
                    EventId = Guid.NewGuid().ToString(),
                    EventType = AuditEventType.UserLogin,
                    Severity = AuditSeverity.Low,
                    Timestamp = DateTime.Now.AddMinutes(-30),
                    UserId = "admin",
                    UserName = "系统管理员",
                    SessionId = "session_001",
                    IPAddress = "*************",
                    UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64)",
                    ResourceId = "",
                    ResourceType = "",
                    Action = "登录",
                    Description = "用户成功登录系统",
                    Result = "Success",
                    ErrorMessage = "",
                    Details = new()
                    {
                        ["loginMethod"] = "password",
                        ["userAgent"] = "Mozilla/5.0 (Windows NT 10.0; Win64; x64)"
                    },
                    Source = "KeyGenerator",
                    Category = "Authentication"
                },
                new AuditEvent
                {
                    EventId = Guid.NewGuid().ToString(),
                    EventType = AuditEventType.KeyCreated,
                    Severity = AuditSeverity.Medium,
                    Timestamp = DateTime.Now.AddMinutes(-25),
                    UserId = "admin",
                    UserName = "系统管理员",
                    SessionId = "session_001",
                    IPAddress = "*************",
                    UserAgent = "KeyGenerator/1.0",
                    ResourceId = "MK-001",
                    ResourceType = "MasterKey",
                    Action = "创建密钥",
                    Description = "创建企业主密钥",
                    Result = "Success",
                    ErrorMessage = "",
                    Details = new()
                    {
                        ["keyType"] = "Master",
                        ["algorithm"] = "SM4",
                        ["keySize"] = 256
                    },
                    Source = "KeyGenerator",
                    Category = "KeyManagement"
                },
                new AuditEvent
                {
                    EventId = Guid.NewGuid().ToString(),
                    EventType = AuditEventType.KeyDistributed,
                    Severity = AuditSeverity.Medium,
                    Timestamp = DateTime.Now.AddMinutes(-20),
                    UserId = "admin",
                    UserName = "系统管理员",
                    SessionId = "session_001",
                    IPAddress = "*************",
                    UserAgent = "KeyGenerator/1.0",
                    ResourceId = "DIST-001",
                    ResourceType = "DistributionTask",
                    Action = "分发密钥",
                    Description = "向客户端分发主密钥",
                    Result = "Success",
                    ErrorMessage = "",
                    Details = new()
                    {
                        ["targetCount"] = 3,
                        ["successCount"] = 3,
                        ["failureCount"] = 0
                    },
                    Source = "KeyGenerator",
                    Category = "KeyDistribution"
                },
                new AuditEvent
                {
                    EventId = Guid.NewGuid().ToString(),
                    EventType = AuditEventType.UnauthorizedAccess,
                    Severity = AuditSeverity.High,
                    Timestamp = DateTime.Now.AddMinutes(-15),
                    UserId = "unknown",
                    UserName = "未知用户",
                    SessionId = "",
                    IPAddress = "*************",
                    UserAgent = "curl/7.68.0",
                    ResourceId = "MK-001",
                    ResourceType = "MasterKey",
                    Action = "访问密钥",
                    Description = "尝试访问未授权的密钥资源",
                    Result = "Failed",
                    ErrorMessage = "访问被拒绝：权限不足",
                    Details = new()
                    {
                        ["attemptedResource"] = "MK-001",
                        ["userAgent"] = "curl/7.68.0",
                        ["blockedReason"] = "insufficient_privileges"
                    },
                    Source = "KeyGenerator",
                    Category = "Security"
                },
                new AuditEvent
                {
                    EventId = Guid.NewGuid().ToString(),
                    EventType = AuditEventType.DocumentEncrypted,
                    Severity = AuditSeverity.Low,
                    Timestamp = DateTime.Now.AddMinutes(-10),
                    UserId = "user001",
                    UserName = "张三",
                    SessionId = "session_002",
                    IPAddress = "*************",
                    UserAgent = "Windows Client/1.0",
                    ResourceId = "DOC-001",
                    ResourceType = "Document",
                    Action = "加密文档",
                    Description = "加密财务报表文档",
                    Result = "Success",
                    ErrorMessage = "",
                    Duration = 1250,
                    Details = new()
                    {
                        ["fileName"] = "财务报表-2024Q1.xlsx",
                        ["fileSize"] = 2048576,
                        ["keyId"] = "UK-001"
                    },
                    Source = "KeyGenerator",
                    Category = "DocumentOperation"
                }
            };

            foreach (var evt in sampleEvents)
            {
                _eventCache.Add(evt);
            }
        }

        public async Task LogAsync(AuditLogEntity log)
        {
            try
            {
                await _semaphore.WaitAsync();

                var auditEvent = new AuditEvent
                {
                    EventId = log.LogId,
                    EventType = (AuditEventType)Enum.Parse(typeof(AuditEventType), log.LogType),
                    Severity = (AuditSeverity)Enum.Parse(typeof(AuditSeverity), log.SeverityLevel),
                    Timestamp = log.EventTime,
                    UserId = log.UserId ?? "",
                    UserName = log.OperatorName ?? "",
                    SessionId = log.SessionId ?? "",
                    IPAddress = log.OperatorIp ?? "",
                    UserAgent = "", // AuditLogEntity doesn't have UserAgent
                    ResourceId = log.TargetResourceId ?? "",
                    ResourceType = log.TargetResourceType ?? "",
                    Action = log.EventAction,
                    Description = log.EventDescription ?? "",
                    Result = log.EventResult,
                    ErrorMessage = log.ErrorMessage ?? "",
                    Details = new Dictionary<string, object>(), // Need to parse RequestParameters/ResponseData into Details if needed
                    Source = log.ApplicationName ?? "KeyGenerator",
                    Category = log.EventCategory
                };

                // 如果 EventId 为空，生成一个新的 GUID
                if (string.IsNullOrEmpty(auditEvent.EventId))
                    auditEvent.EventId = Guid.NewGuid().ToString();

                // 如果 Timestamp 为默认值，设置为当前时间
                if (auditEvent.Timestamp == default)
                    auditEvent.Timestamp = DateTime.Now;

                // 如果 Source 为空，设置为 "KeyGenerator"
                if (string.IsNullOrEmpty(auditEvent.Source))
                    auditEvent.Source = "KeyGenerator";

                // 添加到缓存
                _eventCache.Add(auditEvent);

                // 如果是关键事件，立即处理
                if (_auditConfig.CriticalEvents.Contains(auditEvent.EventType) || auditEvent.Severity == AuditSeverity.Critical)
                {
                    await ProcessCriticalEventAsync(auditEvent);
                }

                _logger.LogInformation("审计事件已记录: {EventType} - {Description}", auditEvent.EventType, auditEvent.Description);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "记录审计事件失败: {Message}", ex.Message);
            }
            finally
            {
                _semaphore.Release();
            }
        }

        public async Task<List<AuditEvent>> QueryEventsAsync(AuditQueryRequest request)
        {
            await Task.CompletedTask;
            
            var events = _eventCache.AsQueryable();

            // 应用过滤条件
            if (request.StartTime.HasValue)
                events = events.Where(e => e.Timestamp >= request.StartTime.Value);

            if (request.EndTime.HasValue)
                events = events.Where(e => e.Timestamp <= request.EndTime.Value);

            if (request.EventTypes != null && request.EventTypes.Any())
                events = events.Where(e => request.EventTypes.Contains(e.EventType));

            if (request.Severities != null && request.Severities.Any())
                events = events.Where(e => request.Severities.Contains(e.Severity));

            if (!string.IsNullOrEmpty(request.UserId))
                events = events.Where(e => e.UserId.Contains(request.UserId, StringComparison.OrdinalIgnoreCase));

            if (!string.IsNullOrEmpty(request.ResourceId))
                events = events.Where(e => e.ResourceId.Contains(request.ResourceId, StringComparison.OrdinalIgnoreCase));

            if (!string.IsNullOrEmpty(request.ResourceType))
                events = events.Where(e => e.ResourceType.Contains(request.ResourceType, StringComparison.OrdinalIgnoreCase));

            if (!string.IsNullOrEmpty(request.Result))
                events = events.Where(e => e.Result.Equals(request.Result, StringComparison.OrdinalIgnoreCase));

            if (!string.IsNullOrEmpty(request.SearchText))
            {
                events = events.Where(e => 
                    e.Description.Contains(request.SearchText, StringComparison.OrdinalIgnoreCase) ||
                    e.UserName.Contains(request.SearchText, StringComparison.OrdinalIgnoreCase) ||
                    e.Action.Contains(request.SearchText, StringComparison.OrdinalIgnoreCase));
            }

            // 排序
            events = request.SortDescending 
                ? events.OrderByDescending(e => e.Timestamp)
                : events.OrderBy(e => e.Timestamp);

            // 分页
            return events
                .Skip((request.Page - 1) * request.PageSize)
                .Take(request.PageSize)
                .ToList();
        }

        public async Task<AuditStatistics> GetStatisticsAsync(DateTime? startTime = null, DateTime? endTime = null)
        {
            await Task.CompletedTask;
            
            var now = DateTime.Now;
            var events = _eventCache.AsQueryable();

            if (startTime.HasValue)
                events = events.Where(e => e.Timestamp >= startTime.Value);
            if (endTime.HasValue)
                events = events.Where(e => e.Timestamp <= endTime.Value);

            var eventList = events.ToList();

            return new AuditStatistics
            {
                TotalEvents = eventList.Count,
                TodayEvents = eventList.Count(e => e.Timestamp.Date == now.Date),
                WeekEvents = eventList.Count(e => e.Timestamp >= now.AddDays(-7)),
                MonthEvents = eventList.Count(e => e.Timestamp >= now.AddDays(-30)),
                EventTypeStats = eventList.GroupBy(e => e.EventType).ToDictionary(g => g.Key, g => g.Count()),
                SeverityStats = eventList.GroupBy(e => e.Severity).ToDictionary(g => g.Key, g => g.Count()),
                UserStats = eventList.Where(e => !string.IsNullOrEmpty(e.UserId))
                    .GroupBy(e => e.UserId).ToDictionary(g => g.Key, g => g.Count()),
                HourlyStats = eventList.Where(e => e.Timestamp.Date == now.Date)
                    .GroupBy(e => e.Timestamp.Hour.ToString("D2"))
                    .ToDictionary(g => g.Key, g => g.Count()),
                RecentCriticalEvents = eventList.Where(e => e.Severity == AuditSeverity.Critical)
                    .OrderByDescending(e => e.Timestamp).Take(10).ToList(),
                RecentFailedEvents = eventList.Where(e => e.Result == "Failed")
                    .OrderByDescending(e => e.Timestamp).Take(10).ToList()
            };
        }

        public async Task<bool> DeleteEventsAsync(DateTime beforeDate)
        {
            try
            {
                await _semaphore.WaitAsync();
                
                var eventsToRemove = _eventCache.Where(e => e.Timestamp < beforeDate).ToList();
                foreach (var evt in eventsToRemove)
                {
                    _eventCache.Remove(evt);
                }

                _logger.LogInformation("删除了 {Count} 个审计事件 (早于 {Date})", eventsToRemove.Count, beforeDate);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除审计事件失败: {Message}", ex.Message);
                return false;
            }
            finally
            {
                _semaphore.Release();
            }
        }

        public async Task<bool> ExportEventsAsync(AuditQueryRequest request, string filePath, string format = "JSON")
        {
            try
            {
                var events = await QueryEventsAsync(request);
                
                switch (format.ToUpper())
                {
                    case "JSON":
                        var json = JsonSerializer.Serialize(events, new JsonSerializerOptions { WriteIndented = true });
                        await File.WriteAllTextAsync(filePath, json);
                        break;
                        
                    case "CSV":
                        await ExportToCsvAsync(events, filePath);
                        break;
                        
                    default:
                        throw new ArgumentException($"不支持的导出格式: {format}");
                }

                _logger.LogInformation("导出了 {Count} 个审计事件到 {FilePath}", events.Count, filePath);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导出审计事件失败: {Message}", ex.Message);
                return false;
            }
        }

        public async Task<List<AuditEvent>> GetRecentEventsAsync(int count = 50)
        {
            await Task.CompletedTask;
            return _eventCache.OrderByDescending(e => e.Timestamp).Take(count).ToList();
        }

        public async Task<List<AuditEvent>> GetCriticalEventsAsync(int count = 20)
        {
            await Task.CompletedTask;
            return _eventCache
                .Where(e => e.Severity == AuditSeverity.Critical || _auditConfig.CriticalEvents.Contains(e.EventType))
                .OrderByDescending(e => e.Timestamp)
                .Take(count)
                .ToList();
        }

        public async Task<bool> ArchiveEventsAsync(DateTime beforeDate, string archivePath)
        {
            try
            {
                var eventsToArchive = _eventCache.Where(e => e.Timestamp < beforeDate).ToList();
                
                if (eventsToArchive.Any())
                {
                    var json = JsonSerializer.Serialize(eventsToArchive, new JsonSerializerOptions { WriteIndented = true });
                    await File.WriteAllTextAsync(archivePath, json);
                    
                    // 从缓存中移除已归档的事件
                    foreach (var evt in eventsToArchive)
                    {
                        _eventCache.Remove(evt);
                    }
                }

                _logger.LogInformation("归档了 {Count} 个审计事件到 {ArchivePath}", eventsToArchive.Count, archivePath);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "归档审计事件失败: {Message}", ex.Message);
                return false;
            }
        }

        public async Task<List<AuditLogEntity>> GetLogsAsync(DateTime from, DateTime to, string? userFilter)
        {
            _logger.LogInformation("正在获取从 {From} 到 {To} 的审计日志, 用户筛选: {UserFilter}", from, to, userFilter);
            try
            {
                // This method will be added to IDatabaseService in the next step
                return await _databaseService.GetAuditLogsAsync(from, to, userFilter);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取审计日志失败");
                return new List<AuditLogEntity>();
            }
        }

        // IHostedService 实现
        public Task StartAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("审计服务已启动");
            return Task.CompletedTask;
        }

        public async Task StopAsync(CancellationToken cancellationToken)
        {
            _flushTimer?.Dispose();
            _cleanupTimer?.Dispose();
            
            // 刷新剩余事件
            await FlushEventsAsync();
            
            _logger.LogInformation("审计服务已停止");
        }

        // 私有方法
        private async Task ProcessCriticalEventAsync(AuditEvent auditEvent)
        {
            try
            {
                // 写入日志文件
                await WriteToLogFileAsync(auditEvent);
                
                // 如果启用了实时告警，发送告警
                if (_auditConfig.EnableRealTimeAlerts)
                {
                    await SendAlertAsync(auditEvent);
                }
                
                _logger.LogWarning("处理关键审计事件: {EventType} - {Description}", auditEvent.EventType, auditEvent.Description);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理关键审计事件失败: {Message}", ex.Message);
            }
        }

        private async Task SendAlertAsync(AuditEvent auditEvent)
        {
            // 实现警报发送逻辑（邮件、短信、Webhook等）
            await Task.CompletedTask;
            _logger.LogInformation("发送警报: {Description}", auditEvent.Description);
        }

        private async Task WriteToLogFileAsync(AuditEvent auditEvent)
        {
            if (!_auditConfig.EnableFileLogging)
                return;

            try
            {
                var logEntry = JsonSerializer.Serialize(auditEvent);
                await File.AppendAllTextAsync(_currentLogFile, logEntry + Environment.NewLine);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "写入日志文件失败: {Message}", ex.Message);
            }
        }

        private async void FlushEvents(object? state)
        {
            await FlushEventsAsync();
        }

        private async Task FlushEventsAsync()
        {
            if (!_auditConfig.EnableFileLogging || !_eventCache.Any())
                return;

            try
            {
                await _semaphore.WaitAsync();
                
                var eventsToFlush = _eventCache.ToList();
                foreach (var evt in eventsToFlush)
                {
                    await WriteToLogFileAsync(evt);
                }
                
                _logger.LogDebug("刷新了 {Count} 个审计事件到日志文件", eventsToFlush.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新审计事件失败: {Message}", ex.Message);
            }
            finally
            {
                _semaphore.Release();
            }
        }

        private async void CleanupOldLogs(object? state)
        {
            await CleanupOldLogsAsync();
        }

        private async Task CleanupOldLogsAsync()
        {
            try
            {
                var cutoffDate = DateTime.Now.AddDays(-_auditConfig.RetentionDays);
                
                // 清理内存中的旧事件
                await DeleteEventsAsync(cutoffDate);
                
                // 清理旧的日志文件
                var logFiles = Directory.GetFiles(_logDirectory, "audit-*.log");
                foreach (var file in logFiles)
                {
                    var fileInfo = new FileInfo(file);
                    if (fileInfo.CreationTime < cutoffDate)
                    {
                        File.Delete(file);
                        _logger.LogInformation("删除旧日志文件: {File}", file);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理旧日志失败: {Message}", ex.Message);
            }
        }

        private static async Task ExportToCsvAsync(List<AuditEvent> events, string filePath)
        {
            var lines = new List<string>
            {
                "EventId,EventType,Severity,Timestamp,UserId,UserName,ResourceId,ResourceType,Action,Description,Result,ErrorMessage,IPAddress,Duration"
            };

            foreach (var evt in events)
            {
                // 不是日志调用，所以可以使用字符串插值
                var line = $"{evt.EventId},{evt.EventType},{evt.Severity},{evt.Timestamp:yyyy-MM-dd HH:mm:ss}," +
                          $"{evt.UserId},{evt.UserName},{evt.ResourceId},{evt.ResourceType},{evt.Action}," +
                          $"\"{evt.Description}\",{evt.Result},\"{evt.ErrorMessage}\",{evt.IPAddress},{evt.Duration}";
                lines.Add(line);
            }

            await File.WriteAllLinesAsync(filePath, lines);
        }

        private static AuditSeverity GetDefaultSeverity(AuditEventType eventType)
        {
            return eventType switch
            {
                AuditEventType.SecurityViolation => AuditSeverity.Critical,
                AuditEventType.UnauthorizedAccess => AuditSeverity.High,
                AuditEventType.DataBreach => AuditSeverity.Critical,
                AuditEventType.LoginFailed => AuditSeverity.Medium,
                AuditEventType.KeyRevoked => AuditSeverity.High,
                AuditEventType.SystemError => AuditSeverity.High,
                AuditEventType.UserLogin => AuditSeverity.Low,
                AuditEventType.UserLogout => AuditSeverity.Low,
                AuditEventType.DocumentEncrypted => AuditSeverity.Low,
                AuditEventType.DocumentDecrypted => AuditSeverity.Low,
                _ => AuditSeverity.Medium
            };
        }

        public void Dispose()
        {
            _flushTimer?.Dispose();
            _cleanupTimer?.Dispose();
            _semaphore?.Dispose();
        }
    }
} 