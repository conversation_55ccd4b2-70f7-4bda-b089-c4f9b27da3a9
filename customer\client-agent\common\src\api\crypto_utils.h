#ifndef CRYPTO_API_CRYPTO_UTILS_H
#define CRYPTO_API_CRYPTO_UTILS_H

#include <string>
#include <vector>
#include <optional> // For std::optional in ComputeFileHash

namespace crypto {
namespace api {

// 哈希算法枚举
enum class HashAlgorithm {
    SHA256,
    SHA512,
    MD5
};

// 对称加密算法枚举
enum class SymmetricAlgorithm {
    AES_256_GCM,        // AES 256位 GCM模式
    AES_256_CBC,        // AES 256位 CBC模式
    ChaCha20Poly1305, // ChaCha20 Poly1305模式
    SM4_128_GCM         // SM4 128位 GCM模式 (新增)
};

class CryptoUtils {
public:
    //此类主要包含静态工具方法，禁止实例化
    CryptoUtils() = delete;
    ~CryptoUtils() = delete;
    CryptoUtils(const CryptoUtils&) = delete;
    CryptoUtils& operator=(const CryptoUtils&) = delete;
    CryptoUtils(CryptoUtils&&) = delete;
    CryptoUtils& operator=(CryptoUtils&&) = delete;

    // 计算数据的哈希值
    static std::string ComputeHash(const std::string& data, HashAlgorithm algorithm);

    // 计算文件的哈希值
    static std::optional<std::string> ComputeFileHash(const std::string& filePath, HashAlgorithm algorithm);

    // 生成指定长度的安全随机字节序列
    static std::vector<uint8_t> GenerateRandomBytes(size_t length);

    // 根据指定的对称加密算法生成密钥
    static std::vector<uint8_t> GenerateKey(SymmetricAlgorithm algorithm);

    // 对称加密操作
    // plaintext: 待加密的明文
    // key: 加密密钥
    // algorithm: 对称加密算法
    // iv: 初始化向量 (对于GCM模式，必须提供唯一的IV；对于CBC，IV长度需与块大小一致)
    // 返回值: 对于GCM模式，通常是 IV + Ciphertext + Tag；对于CBC模式，通常是 IV + Ciphertext.
    //         具体结构由实现确定，但调用者需了解。
    //         根据 crypto_utils.cpp 的实现，EncryptSymmetric的输出不包含IV和Tag，它们在外部处理或作为参数。
    //         因此，此函数仅返回纯密文。IV由调用者提供，Tag由GCM模式在内部处理并需额外获取。
    //         鉴于.cpp中EncryptSymmetric的实现细节（它内部处理了IV的预置和tag的获取），
    //         我们保持接口与cpp中一致：输入IV，输出纯密文，tag需通过其他方式或上下文获取。
    //         然而，标准的GCM做法是加密函数返回密文，认证标签作为输出参数或单独函数获取。
    //         为了简化，并基于cpp的模式，我们暂定返回仅密文。
    //         更正：cpp中的EncryptSymmetric接收IV，将密文写入输出缓冲区，GCM模式下会额外获取tag。
    //         它的返回值是包含 IV + Ciphertext + Tag (GCM) 或 IV + Ciphertext (CBC) 的组合数据。
    //         在 cpp 中: `std::vector<uint8_t> ciphertext(ivSize + outlen + finalLen + (algorithm == SymmetricAlgorithm::AES_256_GCM ? tagSize : 0));`
    //         `std::copy(actual_iv.begin(), actual_iv.end(), ciphertext.begin());`
    //         `std::copy(buffer, buffer + outlen + finalLen, ciphertext.begin() + ivSize);`
    //         `std::copy(tag_buffer.begin(), tag_buffer.end(), ciphertext.begin() + ivSize + outlen + finalLen);`
    //         所以EncryptSymmetric应该返回一个包含IV、密文和可能的Tag的vector。
    static std::vector<uint8_t> EncryptSymmetric(
        const std::vector<uint8_t>& plaintext,
        const std::vector<uint8_t>& key,
        SymmetricAlgorithm algorithm,
        const std::vector<uint8_t>& iv // 对于GCM，IV应由调用者确保唯一性
    );

    // 对称解密操作
    // combined_data: 待解密的组合数据。
    //                对于GCM模式, 期望结构是 IV + Ciphertext + Tag.
    //                对于CBC模式, 期望结构是 IV + Ciphertext.
    // key: 解密密钥
    // algorithm: 对称加密算法
    // 返回值: 解密后的明文。如果解密失败（如GCM认证失败），则抛出异常或返回空vector。
    static std::vector<uint8_t> DecryptSymmetric(
        const std::vector<uint8_t>& combined_data,
        const std::vector<uint8_t>& key,
        SymmetricAlgorithm algorithm
    );

    // 未来可能添加其他加密相关工具函数，如签名、验证等。
};

} // namespace api
} // namespace crypto

#endif // CRYPTO_API_CRYPTO_UTILS_H 