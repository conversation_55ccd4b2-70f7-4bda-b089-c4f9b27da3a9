#include <QApplication>
#include <QStyleFactory>
#include <QDir>
#include <QStandardPaths>
#include <QTranslator>
#include <QLibraryInfo>
#include <QCommandLineParser>
#include <QCommandLineOption>
#include <QMessageBox>
#include <QSplashScreen>
#include <QPixmap>
#include <QTimer>
#include <QLoggingCategory>
#include <QDebug>

#include <iostream>
#include <memory>
#include <signal.h>

#include "declassification_service.h"
#include "ui/main_window.h"

Q_LOGGING_CATEGORY(main, "main")

namespace DeclassificationClient {

/**
 * 全局变量
 */
std::unique_ptr<DeclassificationService> g_service;
std::unique_ptr<UI::MainWindow> g_mainWindow;

/**
 * 信号处理器
 */
void signalHandler(int signal) {
    qCDebug(main) << "Received signal:" << signal;
    
    switch (signal) {
        case SIGINT:
        case SIGTERM:
            qCDebug(main) << "Shutting down gracefully...";
            if (g_service) {
                g_service->shutdown();
            }
            if (g_mainWindow) {
                g_mainWindow->close();
            }
            QCoreApplication::quit();
            break;
        default:
            break;
    }
}

/**
 * 注册信号处理器
 */
void registerSignalHandlers() {
    signal(SIGINT, signalHandler);
    signal(SIGTERM, signalHandler);
}

/**
 * 初始化应用程序配置
 */
void initializeApplicationConfig() {
    // 设置应用程序信息
    QCoreApplication::setOrganizationName("CryptoSystem");
    QCoreApplication::setOrganizationDomain("cryptosystem.com");
    QCoreApplication::setApplicationName("Declassification Client");
    QCoreApplication::setApplicationVersion("1.0.0");
    
    // 设置应用程序属性
    QCoreApplication::setAttribute(Qt::AA_EnableHighDpiScaling);
    QCoreApplication::setAttribute(Qt::AA_UseHighDpiPixmaps);
    
    // 创建配置目录
    QString configDir = QStandardPaths::writableLocation(QStandardPaths::ConfigLocation);
    QDir().mkpath(configDir + "/CryptoSystem");
    
    // 创建数据目录
    QString dataDir = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QDir().mkpath(dataDir);
    
    // 创建日志目录
    QString logDir = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/logs";
    QDir().mkpath(logDir);
    
    qCDebug(main) << "Config directory:" << configDir;
    qCDebug(main) << "Data directory:" << dataDir;
    qCDebug(main) << "Log directory:" << logDir;
}

/**
 * 初始化样式
 */
void initializeStyle(QApplication* app) {
    // 设置应用程序样式
    QApplication::setStyle(QStyleFactory::create("Fusion"));
    
    // 设置调色板
    QPalette darkPalette;
    darkPalette.setColor(QPalette::Window, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::WindowText, Qt::white);
    darkPalette.setColor(QPalette::Base, QColor(25, 25, 25));
    darkPalette.setColor(QPalette::AlternateBase, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::ToolTipBase, Qt::white);
    darkPalette.setColor(QPalette::ToolTipText, Qt::white);
    darkPalette.setColor(QPalette::Text, Qt::white);
    darkPalette.setColor(QPalette::Button, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::ButtonText, Qt::white);
    darkPalette.setColor(QPalette::BrightText, Qt::red);
    darkPalette.setColor(QPalette::Link, QColor(42, 130, 218));
    darkPalette.setColor(QPalette::Highlight, QColor(42, 130, 218));
    darkPalette.setColor(QPalette::HighlightedText, Qt::black);
    
    app->setPalette(darkPalette);
    
    // 设置样式表
    QString styleSheet = R"(
        QMainWindow {
            background-color: #353535;
        }
        
        QMenuBar {
            background-color: #2d2d2d;
            color: white;
            border: 1px solid #404040;
        }
        
        QMenuBar::item {
            background-color: transparent;
            padding: 4px 8px;
        }
        
        QMenuBar::item:selected {
            background-color: #404040;
        }
        
        QMenu {
            background-color: #2d2d2d;
            color: white;
            border: 1px solid #404040;
        }
        
        QMenu::item {
            padding: 4px 20px;
        }
        
        QMenu::item:selected {
            background-color: #404040;
        }
        
        QStatusBar {
            background-color: #2d2d2d;
            color: white;
            border: 1px solid #404040;
        }
        
        QToolBar {
            background-color: #2d2d2d;
            border: 1px solid #404040;
        }
        
        QToolButton {
            background-color: transparent;
            border: none;
            padding: 4px;
        }
        
        QToolButton:hover {
            background-color: #404040;
        }
        
        QToolButton:pressed {
            background-color: #505050;
        }
        
        QProgressBar {
            border: 1px solid #404040;
            text-align: center;
            background-color: #2d2d2d;
        }
        
        QProgressBar::chunk {
            background-color: #2a82da;
        }
    )";
    
    app->setStyleSheet(styleSheet);
}

/**
 * 初始化国际化
 */
void initializeTranslation(QApplication* app) {
    // 加载Qt翻译
    QTranslator* qtTranslator = new QTranslator(app);
    if (qtTranslator->load("qt_" + QLocale::system().name(),
                          QLibraryInfo::location(QLibraryInfo::TranslationsPath))) {
        app->installTranslator(qtTranslator);
    }
    
    // 加载应用程序翻译
    QTranslator* appTranslator = new QTranslator(app);
    if (appTranslator->load("declassification_" + QLocale::system().name(),
                           ":/translations")) {
        app->installTranslator(appTranslator);
    }
}

/**
 * 显示启动画面
 */
QSplashScreen* showSplashScreen() {
    QPixmap pixmap(400, 300);
    pixmap.fill(QColor(53, 53, 53));
    
    QPainter painter(&pixmap);
    painter.setPen(Qt::white);
    painter.setFont(QFont("Arial", 18, QFont::Bold));
    painter.drawText(pixmap.rect(), Qt::AlignCenter, 
                    "CryptoSystem\nDeclassification Client\n\nLoading...");
    
    QSplashScreen* splash = new QSplashScreen(pixmap);
    splash->show();
    
    return splash;
}

/**
 * 命令行模式运行
 */
int runCommandLine(const QCommandLineParser& parser) {
    qCDebug(main) << "Running in command line mode";
    
    // 初始化服务
    g_service = std::make_unique<DeclassificationService>();
    if (!g_service->initialize()) {
        qCritical() << "Failed to initialize declassification service";
        return -1;
    }
    
    // 处理命令行参数
    if (parser.isSet("list-tasks")) {
        auto [tasks, totalCount] = g_service->getTasks();
        std::cout << "Total tasks: " << totalCount << std::endl;
        for (const auto& task : tasks) {
            std::cout << "Task ID: " << task.taskId << ", Status: " 
                      << static_cast<int>(task.status) << std::endl;
        }
    }
    
    if (parser.isSet("process-task")) {
        QString taskId = parser.value("process-task");
        if (g_service->processTask(taskId.toStdString())) {
            std::cout << "Task processing started: " << taskId.toStdString() << std::endl;
        } else {
            std::cout << "Failed to start task processing: " << taskId.toStdString() << std::endl;
        }
    }
    
    if (parser.isSet("statistics")) {
        auto stats = g_service->getTaskStatistics();
        std::cout << "Statistics:" << std::endl;
        std::cout << "  Total tasks: " << stats.totalTasks << std::endl;
        std::cout << "  Completed tasks: " << stats.completedTasks << std::endl;
        std::cout << "  Failed tasks: " << stats.failedTasks << std::endl;
        std::cout << "  Processing tasks: " << stats.processingTasks << std::endl;
    }
    
    // 关闭服务
    g_service->shutdown();
    
    return 0;
}

/**
 * GUI模式运行
 */
int runGUI(QApplication* app) {
    qCDebug(main) << "Running in GUI mode";
    
    // 显示启动画面
    QSplashScreen* splash = showSplashScreen();
    app->processEvents();
    
    // 初始化服务
    splash->showMessage("Initializing service...", Qt::AlignBottom | Qt::AlignCenter, Qt::white);
    app->processEvents();
    
    g_service = std::make_unique<DeclassificationService>();
    if (!g_service->initialize()) {
        QMessageBox::critical(nullptr, "Error", "Failed to initialize declassification service");
        return -1;
    }
    
    // 创建主窗口
    splash->showMessage("Creating main window...", Qt::AlignBottom | Qt::AlignCenter, Qt::white);
    app->processEvents();
    
    g_mainWindow = std::make_unique<UI::MainWindow>(g_service.get());
    
    // 延迟显示主窗口
    QTimer::singleShot(2000, [splash]() {
        splash->finish(g_mainWindow.get());
        g_mainWindow->show();
    });
    
    return app->exec();
}

} // namespace DeclassificationClient

/**
 * 主函数
 */
int main(int argc, char* argv[]) {
    using namespace DeclassificationClient;
    
    // 创建应用程序
    QApplication app(argc, argv);
    
    // 初始化应用程序配置
    initializeApplicationConfig();
    
    // 注册信号处理器
    registerSignalHandlers();
    
    // 解析命令行参数
    QCommandLineParser parser;
    parser.setApplicationDescription("CryptoSystem Declassification Client");
    parser.addHelpOption();
    parser.addVersionOption();
    
    QCommandLineOption noGuiOption("no-gui", "Run in command line mode");
    parser.addOption(noGuiOption);
    
    QCommandLineOption listTasksOption("list-tasks", "List all tasks");
    parser.addOption(listTasksOption);
    
    QCommandLineOption processTaskOption("process-task", "Process specified task", "taskId");
    parser.addOption(processTaskOption);
    
    QCommandLineOption statisticsOption("statistics", "Show statistics");
    parser.addOption(statisticsOption);
    
    parser.process(app);
    
    // 检查是否为命令行模式
    if (parser.isSet(noGuiOption)) {
        return runCommandLine(parser);
    }
    
    // 初始化GUI
    initializeStyle(&app);
    initializeTranslation(&app);
    
    // 运行GUI模式
    return runGUI(&app);
}

#include "main.moc" 