namespace CryptoSystem.SystemManager.Services
{
    public interface IConfigurationService
    {
        string? GetString(string key, string? defaultValue = null);
        int GetInt(string key, int defaultValue = 0);
        bool GetBool(string key, bool defaultValue = false);
        double? GetDouble(string key, double? defaultValue = null);
        T? GetValue<T>(string key, T? defaultValue = default);
        
        void Set(string key, object value);
        void SetString(string key, string value);
        void SetInt(string key, int value);
        void SetBool(string key, bool value);
        void SetDouble(string key, double value);
        
        bool HasKey(string key);
        void Remove(string key);
        void Save();
        void Reload();
    }
    
    public interface IUpdateService
    {
        Task<bool> CheckForUpdatesAsync();
        Task<string?> GetLatestVersionAsync();
        Task<(bool Success, string Message)> DownloadUpdateAsync(IProgress<int>? progress = null);
        Task<(bool Success, string Message)> InstallUpdateAsync();
    }
} 