import Foundation
import SwiftUI

// MARK: - 任务状态枚举
enum TaskStatus: String, CaseIterable, Codable {
    case pending = "pending"
    case processing = "processing"
    case completed = "completed"
    case failed = "failed"
    case cancelled = "cancelled"
    
    var displayName: String {
        switch self {
        case .pending: return "待处理"
        case .processing: return "处理中"
        case .completed: return "已完成"
        case .failed: return "失败"
        case .cancelled: return "已取消"
        }
    }
    
    var color: Color {
        switch self {
        case .pending: return .orange
        case .processing: return .blue
        case .completed: return .green
        case .failed: return .red
        case .cancelled: return .gray
        }
    }
    
    var iconName: String {
        switch self {
        case .pending: return "clock"
        case .processing: return "gearshape.2"
        case .completed: return "checkmark.circle.fill"
        case .failed: return "xmark.circle.fill"
        case .cancelled: return "minus.circle.fill"
        }
    }
}

// MARK: - 文件处理状态枚举
enum FileProcessStatus: String, CaseIterable, Codable {
    case waiting = "waiting"
    case decrypting = "decrypting"
    case decrypted = "decrypted"
    case packaging = "packaging"
    case packaged = "packaged"
    case sending = "sending"
    case sent = "sent"
    case failed = "failed"
    
    var displayName: String {
        switch self {
        case .waiting: return "等待中"
        case .decrypting: return "解密中"
        case .decrypted: return "已解密"
        case .packaging: return "打包中"
        case .packaged: return "已打包"
        case .sending: return "发送中"
        case .sent: return "已发送"
        case .failed: return "失败"
        }
    }
    
    var color: Color {
        switch self {
        case .waiting: return .gray
        case .decrypting, .packaging, .sending: return .blue
        case .decrypted, .packaged: return .orange
        case .sent: return .green
        case .failed: return .red
        }
    }
    
    var progress: Double {
        switch self {
        case .waiting: return 0.0
        case .decrypting: return 0.2
        case .decrypted: return 0.4
        case .packaging: return 0.6
        case .packaged: return 0.8
        case .sending: return 0.9
        case .sent: return 1.0
        case .failed: return 0.0
        }
    }
}

// MARK: - 发送方式枚举
enum SendMethod: String, CaseIterable, Codable {
    case email = "email"
    case ftp = "ftp"
    case usb = "usb"
    case cloud = "cloud"
    case manual = "manual"
    
    var displayName: String {
        switch self {
        case .email: return "邮件发送"
        case .ftp: return "FTP传输"
        case .usb: return "USB设备"
        case .cloud: return "云存储"
        case .manual: return "手动提取"
        }
    }
    
    var iconName: String {
        switch self {
        case .email: return "envelope"
        case .ftp: return "network"
        case .usb: return "externaldrive"
        case .cloud: return "cloud"
        case .manual: return "hand.raised"
        }
    }
    
    var requiresRecipient: Bool {
        switch self {
        case .email, .ftp, .cloud: return true
        case .usb, .manual: return false
        }
    }
}

// MARK: - 文件信息结构
struct FileInfo: Identifiable, Codable, Hashable {
    let id: UUID
    let name: String
    let path: String
    let size: Int64
    let createdAt: Date
    let modifiedAt: Date
    let checksum: String
    var status: FileProcessStatus
    var progress: Double
    var errorMessage: String?
    
    init(url: URL) {
        self.id = UUID()
        self.name = url.lastPathComponent
        self.path = url.path
        
        let attributes = try? FileManager.default.attributesOfItem(atPath: url.path)
        self.size = attributes?[.size] as? Int64 ?? 0
        self.createdAt = attributes?[.creationDate] as? Date ?? Date()
        self.modifiedAt = attributes?[.modificationDate] as? Date ?? Date()
        
        // 计算文件校验和
        self.checksum = Self.calculateChecksum(for: url)
        self.status = .waiting
        self.progress = 0.0
    }
    
    private static func calculateChecksum(for url: URL) -> String {
        guard let data = try? Data(contentsOf: url) else { return "" }
        return data.sha256
    }
    
    var formattedSize: String {
        ByteCountFormatter.string(fromByteCount: size, countStyle: .file)
    }
    
    var isEncrypted: Bool {
        // 检查文件是否为加密文件
        return name.hasSuffix(".enc") || checksum.isEmpty == false
    }
}

// MARK: - 发送配置结构
struct SendConfiguration: Codable {
    let method: SendMethod
    let recipients: [String]
    let subject: String?
    let message: String?
    let serverHost: String?
    let serverPort: Int?
    let username: String?
    let password: String?
    let cloudPath: String?
    let usbPath: String?
    
    init(method: SendMethod, recipients: [String] = [], subject: String? = nil, message: String? = nil) {
        self.method = method
        self.recipients = recipients
        self.subject = subject
        self.message = message
        self.serverHost = nil
        self.serverPort = nil
        self.username = nil
        self.password = nil
        self.cloudPath = nil
        self.usbPath = nil
    }
}

// MARK: - 审批信息结构
struct ApprovalInfo: Codable {
    let id: UUID
    let requesterName: String
    let requesterId: String
    let approverName: String?
    let approverId: String?
    let requestTime: Date
    let approvalTime: Date?
    let status: ApprovalStatus
    let reason: String?
    let comments: String?
    
    enum ApprovalStatus: String, Codable {
        case pending = "pending"
        case approved = "approved"
        case rejected = "rejected"
        case cancelled = "cancelled"
        
        var displayName: String {
            switch self {
            case .pending: return "待审批"
            case .approved: return "已批准"
            case .rejected: return "已拒绝"
            case .cancelled: return "已取消"
            }
        }
        
        var color: Color {
            switch self {
            case .pending: return .orange
            case .approved: return .green
            case .rejected: return .red
            case .cancelled: return .gray
            }
        }
    }
}

// MARK: - 脱密任务主模型
struct DeclassificationTask: Identifiable, Codable, Hashable {
    let id: UUID
    let name: String
    let createdAt: Date
    let createdBy: String
    var status: TaskStatus
    var files: [FileInfo]
    var sendConfiguration: SendConfiguration
    var approvalInfo: ApprovalInfo?
    var completedAt: Date?
    var errorMessage: String?
    var notes: String?
    
    // 统计信息
    var totalFiles: Int { files.count }
    var completedFiles: Int { files.filter { $0.status == .sent }.count }
    var failedFiles: Int { files.filter { $0.status == .failed }.count }
    var processingFiles: Int { files.filter { $0.status.progress > 0 && $0.status.progress < 1 }.count }
    
    var overallProgress: Double {
        guard !files.isEmpty else { return 0.0 }
        let totalProgress = files.reduce(0.0) { $0 + $1.progress }
        return totalProgress / Double(files.count)
    }
    
    var estimatedCompletionTime: Date? {
        guard status == .processing else { return nil }
        let averageProcessingTime: TimeInterval = 30 // 假设平均处理时间为30秒
        let remainingFiles = files.filter { $0.status != .sent && $0.status != .failed }
        return Date().addingTimeInterval(Double(remainingFiles.count) * averageProcessingTime)
    }
    
    init(name: String, files: [URL], sendConfiguration: SendConfiguration, createdBy: String, notes: String? = nil) {
        self.id = UUID()
        self.name = name
        self.createdAt = Date()
        self.createdBy = createdBy
        self.status = .pending
        self.files = files.map { FileInfo(url: $0) }
        self.sendConfiguration = sendConfiguration
        self.notes = notes
    }
    
    mutating func updateStatus(_ newStatus: TaskStatus) {
        self.status = newStatus
        if newStatus == .completed {
            self.completedAt = Date()
        }
    }
    
    mutating func updateFileStatus(_ fileId: UUID, status: FileProcessStatus, progress: Double? = nil, error: String? = nil) {
        if let index = files.firstIndex(where: { $0.id == fileId }) {
            files[index].status = status
            if let progress = progress {
                files[index].progress = progress
            } else {
                files[index].progress = status.progress
            }
            files[index].errorMessage = error
        }
    }
    
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
    
    static func == (lhs: DeclassificationTask, rhs: DeclassificationTask) -> Bool {
        lhs.id == rhs.id
    }
}

// MARK: - 安全包结构
struct SecurePackage: Codable {
    let id: UUID
    let taskId: UUID
    let name: String
    let createdAt: Date
    let expiresAt: Date?
    let accessCount: Int
    let maxAccessCount: Int?
    let password: String?
    let watermark: String?
    let permissions: PackagePermissions
    let files: [String] // 文件路径列表
    
    struct PackagePermissions: Codable {
        let canPrint: Bool
        let canCopy: Bool
        let canEdit: Bool
        let canForward: Bool
        let canScreenshot: Bool
        
        static let `default` = PackagePermissions(
            canPrint: false,
            canCopy: false,
            canEdit: false,
            canForward: false,
            canScreenshot: false
        )
    }
    
    var isExpired: Bool {
        guard let expiresAt = expiresAt else { return false }
        return Date() > expiresAt
    }
    
    var isAccessLimitReached: Bool {
        guard let maxAccessCount = maxAccessCount else { return false }
        return accessCount >= maxAccessCount
    }
    
    var isAccessible: Bool {
        !isExpired && !isAccessLimitReached
    }
}

// MARK: - 操作统计结构
struct OperationStatistics: Codable {
    let totalTasks: Int
    let completedTasks: Int
    let failedTasks: Int
    let processingTasks: Int
    let todayTasks: Int
    let totalFilesProcessed: Int
    let totalDataTransferred: Int64
    let averageProcessingTime: TimeInterval
    let successRate: Double
    
    var formattedDataTransferred: String {
        ByteCountFormatter.string(fromByteCount: totalDataTransferred, countStyle: .file)
    }
    
    var formattedAverageTime: String {
        let formatter = DateComponentsFormatter()
        formatter.allowedUnits = [.minute, .second]
        formatter.unitsStyle = .abbreviated
        return formatter.string(from: averageProcessingTime) ?? "0秒"
    }
    
    static let empty = OperationStatistics(
        totalTasks: 0,
        completedTasks: 0,
        failedTasks: 0,
        processingTasks: 0,
        todayTasks: 0,
        totalFilesProcessed: 0,
        totalDataTransferred: 0,
        averageProcessingTime: 0,
        successRate: 0.0
    )
}

// MARK: - 审计日志结构
struct AuditLog: Identifiable, Codable {
    let id: UUID
    let timestamp: Date
    let type: AuditLogType
    let userId: String
    let userName: String
    let action: String
    let message: String
    let details: [String: String]?
    let ipAddress: String?
    let deviceInfo: String?
    
    enum AuditLogType: String, Codable, CaseIterable {
        case systemEvent = "system"
        case userAction = "user"
        case securityEvent = "security"
        case errorEvent = "error"
        case taskEvent = "task"
        
        var displayName: String {
            switch self {
            case .systemEvent: return "系统事件"
            case .userAction: return "用户操作"
            case .securityEvent: return "安全事件"
            case .errorEvent: return "错误事件"
            case .taskEvent: return "任务事件"
            }
        }
        
        var color: Color {
            switch self {
            case .systemEvent: return .blue
            case .userAction: return .green
            case .securityEvent: return .orange
            case .errorEvent: return .red
            case .taskEvent: return .purple
            }
        }
        
        var iconName: String {
            switch self {
            case .systemEvent: return "gear"
            case .userAction: return "person"
            case .securityEvent: return "shield"
            case .errorEvent: return "exclamationmark.triangle"
            case .taskEvent: return "doc.text"
            }
        }
    }
    
    init(type: AuditLogType, userId: String, userName: String, action: String, message: String, details: [String: String]? = nil) {
        self.id = UUID()
        self.timestamp = Date()
        self.type = type
        self.userId = userId
        self.userName = userName
        self.action = action
        self.message = message
        self.details = details
        self.ipAddress = NetworkUtils.getLocalIPAddress()
        self.deviceInfo = SystemUtils.getDeviceInfo()
    }
}

// MARK: - 扩展工具
extension Data {
    var sha256: String {
        return self.withUnsafeBytes { bytes in
            let buffer = bytes.bindMemory(to: UInt8.self)
            var hash = [UInt8](repeating: 0, count: Int(CC_SHA256_DIGEST_LENGTH))
            CC_SHA256(buffer.baseAddress, CC_LONG(count), &hash)
            return hash.map { String(format: "%02x", $0) }.joined()
        }
    }
}

// MARK: - 工具类
class NetworkUtils {
    static func getLocalIPAddress() -> String {
        var address: String = "Unknown"
        var ifaddr: UnsafeMutablePointer<ifaddrs>?
        
        if getifaddrs(&ifaddr) == 0 {
            var ptr = ifaddr
            while ptr != nil {
                defer { ptr = ptr?.pointee.ifa_next }
                
                guard let interface = ptr?.pointee else { continue }
                let addrFamily = interface.ifa_addr.pointee.sa_family
                
                if addrFamily == UInt8(AF_INET) || addrFamily == UInt8(AF_INET6) {
                    let name = String(cString: interface.ifa_name)
                    if name == "en0" || name == "en1" {
                        var hostname = [CChar](repeating: 0, count: Int(NI_MAXHOST))
                        getnameinfo(interface.ifa_addr, socklen_t(interface.ifa_addr.pointee.sa_len),
                                   &hostname, socklen_t(hostname.count),
                                   nil, socklen_t(0), NI_NUMERICHOST)
                        address = String(cString: hostname)
                    }
                }
            }
            freeifaddrs(ifaddr)
        }
        
        return address
    }
}

class SystemUtils {
    static func getDeviceInfo() -> String {
        let info = ProcessInfo.processInfo
        return "\(info.operatingSystemVersionString) - \(info.hostName)"
    }
}

// 导入必要的C库
import CommonCrypto
import Darwin 