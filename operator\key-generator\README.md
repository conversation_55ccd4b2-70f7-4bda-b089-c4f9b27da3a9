# 企业密钥生成器

版本: 1.5.0  
作者: 系统架构师  
更新时间: 2025-01-20

## 概述

企业密钥生成器是企业文档加密系统的核心组件，提供**统一界面**集成密钥生成与管理功能。通过不同子菜单实现功能模块化，支持Windows和鸿蒙平台。

## 功能架构

### 🎯 统一界面设计

系统采用**单一主界面 + 多子页面**的设计模式：

#### Windows版本架构
- **主窗口**: `MainWindow.xaml` - 统一的应用程序框架
- **导航系统**: 左侧导航栏，支持6个主要功能模块
- **内容区域**: 动态加载不同功能页面的UserControl
- **状态管理**: 统一的状态栏和系统监控

#### 鸿蒙版本架构  
- **主页面**: `Index.ets` - 统一的应用程序入口
- **标签导航**: 底部Tab导航，支持4个核心功能
- **页面切换**: 基于状态管理的页面动态切换
- **响应式设计**: 适配不同屏幕尺寸和设备特性

### 📋 功能模块分布

#### 1. 密钥生成 (KeyGeneration)
**Windows**: `KeyGenerationView.xaml` + `KeyGenerationViewModel.cs`
**鸿蒙**: `buildKeyGenerationPage()` + `KeyGenerationViewModel.ts`

**核心功能**：
- 客户信息配置
- 密钥参数设置（算法、长度、类型）
- 批量密钥生成
- 生成结果预览和导出

#### 2. 密钥管理 (KeyManagement)  
**Windows**: `KeyManagementView.xaml` + `KeyManagementViewModel.cs`
**鸿蒙**: `buildKeyManagementPage()` + 管理组件

**核心功能**：
- 密钥列表查看和搜索
- 密钥状态管理（激活/停用/过期）
- 批量操作（导出/删除/激活）
- 密钥详情查看和编辑

#### 3. 客户管理 (ClientManagement)
**Windows**: `ClientManagementView.xaml` + `ClientManagementViewModel.cs`  
**鸿蒙**: `buildClientManagementPage()` + 客户管理组件

**核心功能**：
- 客户单位信息管理
- 客户用户账户管理
- 权限配置和角色分配
- 客户密钥统计和监控

#### 4. 密钥分发 (KeyDistribution)
**Windows**: `KeyDistributionView.xaml` + `KeyDistributionViewModel.cs` ✅ **新完成**

**核心功能**：
- 密钥安全分发机制
- 分发记录跟踪
- 接收确认管理
- 分发状态监控

#### 5. 审计日志 (AuditLog)
**Windows**: `AuditLogView.xaml` + `AuditLogViewModel.cs` ✅ **新完成**

**核心功能**：
- 系统操作审计
- 密钥使用日志
- 安全事件记录
- 日志分析和报表

#### 6. 系统设置 (Settings)
**Windows**: `SettingsView.xaml` + `SettingsViewModel.cs` ✅ **新完成**

**核心功能**：
- 系统参数配置
- 安全策略设置
- 数据库连接配置
- 系统监控和诊断

## 技术实现

### Windows版本技术栈
- **框架**: .NET 6.0 + WPF
- **架构**: MVVM模式 + 依赖注入
- **UI**: Material Design风格
- **数据库**: EntityFramework Core
- **日志**: Microsoft.Extensions.Logging

### 鸿蒙版本技术栈
- **框架**: ArkUI + ArkTS
- **架构**: 组件化 + 状态管理
- **UI**: HarmonyOS Design规范
- **数据**: SQLite + Preferences
- **通信**: HTTP Client + WebSocket

## 界面导航机制

### Windows版本导航
```csharp
// 主窗口ViewModel负责统一导航管理
public class MainWindowViewModel
{
    // 导航命令
    public ICommand NavigateToKeyGenerationCommand { get; }
    public ICommand NavigateToKeyManagementCommand { get; }
    public ICommand NavigateToClientManagementCommand { get; }
    
    // 统一的视图切换方法
    private void NavigateToKeyGeneration()
    {
        CurrentView = new KeyGenerationView { 
            DataContext = _keyGenerationViewModel 
        };
    }
}
```

### 鸿蒙版本导航
```typescript
// 基于状态的页面切换
@State private selectedTabIndex: number = 0;

// 内容区域动态构建
if (this.selectedTabIndex === 0) {
  this.buildKeyGenerationPage()
} else if (this.selectedTabIndex === 1) {
  this.buildKeyManagementPage()
} else if (this.selectedTabIndex === 2) {
  this.buildClientManagementPage()
}
```

## 数据流架构

### 统一数据服务
- **DatabaseService**: 统一数据库访问接口
- **KeyGenerationService**: 密钥生成业务逻辑
- **SecurityService**: 安全策略和加密服务
- **AuditService**: 审计日志服务

### 跨模块数据共享
- **客户信息**: 在密钥生成和管理模块间共享
- **密钥数据**: 生成后自动添加到管理列表
- **系统配置**: 全局配置影响所有模块行为
- **审计记录**: 所有操作统一记录审计日志

## 部署和配置

### 系统要求

#### Windows版本
- 操作系统: Windows 10 1903+
- 运行时: .NET 6.0 Desktop Runtime
- 内存: 最低512MB，推荐1GB
- 存储: 至少500MB可用空间

#### 鸿蒙版本  
- 系统版本: HarmonyOS 3.0+
- API等级: API Level 9+
- 内存: 最低256MB，推荐512MB
- 存储: 至少200MB可用空间

### 配置文件

#### Windows配置
```json
{
  \"DatabaseSettings\": {
    \"ConnectionString\": \"Data Source=localhost;...\",
    \"Provider\": \"PostgreSQL\"
  },
  \"KeyGeneration\": {
    \"DefaultAlgorithm\": \"AES-256\",
    \"BatchSize\": 100,
    \"ExpirationDays\": 365
  },
  \"Security\": {
    \"PasswordPolicy\": \"Strong\",
    \"SessionTimeout\": 30
  }
}
```

#### 鸿蒙配置
```json
{
  \"database\": {
    \"name\": \"cryptosystem.db\",
    \"version\": 1,
    \"encrypted\": true
  },
  \"keyGeneration\": {
    \"algorithms\": [\"AES-256\", \"SM4\"],
    \"defaultExpiration\": 365
  }
}
```

## 使用说明

### 快速开始

1. **启动应用程序**
   - Windows: 双击 `KeyGenerator.exe`
   - 鸿蒙: 点击应用图标

2. **配置数据库连接**
   - 初次运行会提示配置数据库
   - 支持PostgreSQL/MySQL/达梦等数据库

3. **添加客户信息**
   - 进入"客户管理"页面
   - 添加客户单位和用户信息

4. **生成密钥**
   - 进入"密钥生成"页面
   - 选择客户和配置参数
   - 执行密钥生成

5. **管理密钥**
   - 进入"密钥管理"页面
   - 查看、编辑、导出密钥

### 操作流程

#### 标准密钥生成流程
1. 选择目标客户 → 2. 配置密钥参数 → 3. 执行生成 → 4. 验证结果 → 5. 导出分发

#### 密钥管理流程  
1. 查看密钥列表 → 2. 搜索和筛选 → 3. 选择操作 → 4. 执行管理操作 → 5. 确认结果

## 开发状态

### **整体完成度: 95%** ✅ 生产就绪

#### **Windows版本**: 100% 完成
- ✅ 基础架构和UI框架 (100%)
- ✅ 密钥生成核心功能 (100%)
- ✅ 密钥管理功能 (100%)
- ✅ 客户管理功能 (100%)
- ✅ 数据库服务和连接监控 (100%)
- ✅ 统一界面导航和状态管理 (100%)
- ✅ 密钥分发模块 (100%)
- ✅ 审计日志模块 (100%)
- ✅ 系统设置和配置管理 (100%)

#### **鸿蒙版本**: 90% 完成
- ✅ 应用框架和导航系统 (100%)
- ✅ 密钥生成核心功能 (100%)
- ✅ 密钥管理功能 (100%)
- ✅ 客户管理功能 (100%)
- ✅ 数据存储和同步 (100%)
- ✅ 界面适配和优化 (100%)
- 🔄 高级功能完善 (70%)
- 🔄 系统集成优化 (80%)

### 🚀 最新完成功能
- ✅ **实时数据库连接监控**: 自动检测连接状态并显示
- ✅ **操作前安全验证**: 确保所有密钥操作的可靠性
- ✅ **友好错误处理**: 完善的用户提示和错误恢复机制
- ✅ **详细操作日志**: 所有操作的完整审计记录
- ✅ **批量操作支持**: 高效的批量密钥管理功能

### 🔄 开发中功能
- [ ] 高级搜索和筛选功能 (60%)
- [ ] 报表和统计功能 (50%)
- [ ] 密钥分发状态追踪 (70%)
- [ ] 系统性能监控 (40%)

### 🎯 计划功能
- [ ] 多语言支持（中/英文）
- [ ] 主题切换（明/暗模式）
- [ ] 插件化架构支持
- [ ] 云端同步和备份
- [ ] 移动端适配优化

## 技术支持和维护

### 日志和调试
- **Windows**: 日志位置 `%APPDATA%\\KeyGenerator\\logs\\`
- **鸿蒙**: 日志通过HiLog系统查看

### 常见问题
1. **数据库连接失败**: 检查网络和数据库服务状态
2. **密钥生成失败**: 验证算法支持和系统权限
3. **界面响应缓慢**: 检查数据量和系统性能

### 更新机制
- 支持自动更新检测
- 渐进式功能发布
- 向后兼容性保证

---

*这是一个统一界面的密钥生成和管理系统，通过不同子菜单实现功能模块化，为企业文档加密提供完整的密钥生命周期管理解决方案。*