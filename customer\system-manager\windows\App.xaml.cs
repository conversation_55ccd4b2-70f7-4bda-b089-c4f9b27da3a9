using System;
using System.Windows;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

namespace CryptoSystem.SystemManager
{
    /// <summary>
    /// 系统管理器应用程序主类
    /// </summary>
    public partial class App : Application
    {
        private IHost? _host;

        public IServiceProvider Services => _host?.Services ?? throw new InvalidOperationException("Services not available");

        protected override async void OnStartup(StartupEventArgs e)
        {
            try
            {
                // 创建主机
                _host = Startup.CreateHost(e.Args);
                await _host.StartAsync();

                // 启动主窗口
                var mainWindow = Services.GetRequiredService<Views.MainWindow>();
                mainWindow.Show();

                base.OnStartup(e);
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"应用程序启动失败: {ex.Message}",
                    "启动错误",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);

                Environment.Exit(1);
            }
        }

        protected override async void OnExit(ExitEventArgs e)
        {
            if (_host != null)
            {
                await _host.StopAsync();
                _host.Dispose();
            }

            base.OnExit(e);
        }

        private static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .UseSerilog()
                .ConfigureAppConfiguration((context, config) =>
                {
                    config.SetBasePath(Directory.GetCurrentDirectory())
                          .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                          .AddJsonFile($"appsettings.{context.HostingEnvironment.EnvironmentName}.json", optional: true)
                          .AddEnvironmentVariables();
                })
                .ConfigureServices((context, services) =>
                {
                    // 配置 Entity Framework
                    var connectionString = context.Configuration.GetConnectionString("DefaultConnection");
                    if (!string.IsNullOrEmpty(connectionString))
                    {
                        services.AddDbContext<SystemManagerDbContext>(options =>
                            options.UseNpgsql(connectionString));
                    }

                    // 注册服务
                    services.AddSingleton<IConfigurationService, ConfigurationService>();
                    services.AddSingleton<IUserService, UserService>();
                    services.AddSingleton<IDeviceService, DeviceService>();
                    services.AddSingleton<IPolicyService, PolicyService>();
                    services.AddSingleton<IKeyService, KeyService>();
                    services.AddSingleton<IAuditService, AuditService>();
                    services.AddSingleton<INotificationService, NotificationService>();
                    services.AddSingleton<ISecurityService, SecurityService>();

                    // 注册 ViewModels
                    services.AddTransient<MainWindowViewModel>();
                    services.AddTransient<DashboardViewModel>();
                    services.AddTransient<UserManagementViewModel>();
                    services.AddTransient<DeviceManagementViewModel>();
                    services.AddTransient<PolicyManagementViewModel>();
                    services.AddTransient<KeyManagementViewModel>();
                    services.AddTransient<AuditLogViewModel>();
                    services.AddTransient<SettingsViewModel>();

                    // 注册窗口
                    services.AddTransient<Views.MainWindow>();
                });

        private void ConfigureLogging()
        {
            var logPath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                "CryptoSystem",
                "SystemManager",
                "Logs",
                "app.log");

            // 确保日志目录存在
            Directory.CreateDirectory(Path.GetDirectoryName(logPath)!);

            Log.Logger = new LoggerConfiguration()
                .MinimumLevel.Information()
                .WriteTo.Console()
                .WriteTo.File(logPath, 
                    rollingInterval: RollingInterval.Day,
                    retainedFileCountLimit: 30,
                    fileSizeLimitBytes: 10_000_000,
                    rollOnFileSizeLimit: true)
                .CreateLogger();
        }

        private async Task EnsureDatabaseAsync()
        {
            using var scope = Services.CreateScope();
            var context = scope.ServiceProvider.GetService<SystemManagerDbContext>();
            
            if (context != null)
            {
                try
                {
                    await context.Database.EnsureCreatedAsync();
                    Log.Information("数据库连接验证成功");
                }
                catch (Exception ex)
                {
                    Log.Warning("数据库连接失败，将使用内存模式: {Error}", ex.Message);
                    
                    // 如果PostgreSQL连接失败，配置内存数据库作为备用
                    var services = scope.ServiceProvider.GetRequiredService<IServiceCollection>();
                    services.AddDbContext<SystemManagerDbContext>(options =>
                        options.UseInMemoryDatabase("SystemManagerInMemory"));
                }
            }
        }

        /// <summary>
        /// 获取服务实例
        /// </summary>
        public T GetRequiredService<T>() where T : notnull
        {
            return Services.GetRequiredService<T>();
        }

        /// <summary>
        /// 获取可选服务实例
        /// </summary>
        public T? GetService<T>()
        {
            return Services.GetService<T>();
        }

        /// <summary>
        /// 处理未捕获的异常
        /// </summary>
        private void Application_DispatcherUnhandledException(object sender, System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
        {
            Log.Error(e.Exception, "未处理的应用程序异常");

            MessageBox.Show(
                $"应用程序发生未处理的错误:\n{e.Exception.Message}\n\n请查看日志文件获取详细信息。",
                "应用程序错误",
                MessageBoxButton.OK,
                MessageBoxImage.Error);

            e.Handled = true;
        }

        /// <summary>
        /// 处理应用程序域未捕获的异常
        /// </summary>
        private void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            if (e.ExceptionObject is Exception ex)
            {
                Log.Fatal(ex, "应用程序域未处理的异常");
                
                MessageBox.Show(
                    $"应用程序发生严重错误:\n{ex.Message}\n\n应用程序将关闭。",
                    "严重错误",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }
    }
} 