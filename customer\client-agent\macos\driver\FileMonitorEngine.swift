import Foundation
import CoreServices
import Combine

/// macOS 文件监控引擎
/// 负责监控文件系统变化，触发加密策略评估和执行
@available(macOS 10.15, *)
public class FileMonitorEngine: ObservableObject {
    
    // MARK: - 属性
    private var eventStream: FSEventStreamRef?
    private let monitorQueue = DispatchQueue(label: "FileMonitorQueue", qos: .utility)
    private let policyManager: PolicyManager
    private let cryptoManager: CryptoManager
    
    @Published public private(set) var isMonitoring: Bool = false
    @Published public private(set) var monitoredPaths: Set<String> = []
    @Published public private(set) var eventCount: Int = 0
    @Published public private(set) var lastEventTime: Date?
    
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 监控统计
    public struct MonitorStatistics {
        let totalEvents: Int
        let encryptionTriggers: Int
        let decryptionTriggers: Int
        let policyViolations: Int
        let averageProcessingTime: TimeInterval
        let lastResetTime: Date
    }
    
    private var statistics = MonitorStatistics(
        totalEvents: 0,
        encryptionTriggers: 0,
        decryptionTriggers: 0,
        policyViolations: 0,
        averageProcessingTime: 0.0,
        lastResetTime: Date()
    )
    
    // MARK: - 事件类型
    public enum FileEvent {
        case created(path: String)
        case modified(path: String)
        case deleted(path: String)
        case moved(from: String, to: String)
        case attributeChanged(path: String)
    }
    
    public enum MonitorError: Error, LocalizedError {
        case streamCreationFailed
        case pathNotFound(String)
        case permissionDenied
        case systemError(Int32)
        
        public var errorDescription: String? {
            switch self {
            case .streamCreationFailed:
                return "无法创建文件监控流"
            case .pathNotFound(let path):
                return "路径不存在: \(path)"
            case .permissionDenied:
                return "权限不足，无法监控文件系统"
            case .systemError(let code):
                return "系统错误: \(code)"
            }
        }
    }
    
    // MARK: - 委托协议
    public protocol FileMonitorDelegate: AnyObject {
        func fileMonitor(_ monitor: FileMonitorEngine, didDetectEvent event: FileEvent)
        func fileMonitor(_ monitor: FileMonitorEngine, didTriggerEncryption path: String)
        func fileMonitor(_ monitor: FileMonitorEngine, didTriggerDecryption path: String)
        func fileMonitor(_ monitor: FileMonitorEngine, didEncounterError error: Error)
    }
    
    public weak var delegate: FileMonitorDelegate?
    
    // MARK: - 初始化
    public init(policyManager: PolicyManager, cryptoManager: CryptoManager) {
        self.policyManager = policyManager
        self.cryptoManager = cryptoManager
        
        setupNotificationObservers()
    }
    
    deinit {
        stopMonitoring()
    }
    
    // MARK: - 公共方法
    
    /// 开始监控指定路径
    public func startMonitoring(paths: [String]) throws {
        guard !isMonitoring else { return }
        
        // 验证路径有效性
        for path in paths {
            var isDirectory: ObjCBool = false
            if !FileManager.default.fileExists(atPath: path, isDirectory: &isDirectory) {
                throw MonitorError.pathNotFound(path)
            }
        }
        
        try createEventStream(for: paths)
        
        DispatchQueue.main.async {
            self.isMonitoring = true
            self.monitoredPaths = Set(paths)
        }
        
        NSLog("文件监控引擎已启动，监控路径: \(paths.joined(separator: ", "))")
    }
    
    /// 停止监控
    public func stopMonitoring() {
        guard isMonitoring else { return }
        
        if let stream = eventStream {
            FSEventStreamStop(stream)
            FSEventStreamInvalidate(stream)
            FSEventStreamRelease(stream)
            eventStream = nil
        }
        
        DispatchQueue.main.async {
            self.isMonitoring = false
            self.monitoredPaths.removeAll()
        }
        
        NSLog("文件监控引擎已停止")
    }
    
    /// 添加监控路径
    public func addMonitorPath(_ path: String) throws {
        guard isMonitoring else {
            throw MonitorError.systemError(-1)
        }
        
        var isDirectory: ObjCBool = false
        if !FileManager.default.fileExists(atPath: path, isDirectory: &isDirectory) {
            throw MonitorError.pathNotFound(path)
        }
        
        let newPaths = Array(monitoredPaths) + [path]
        stopMonitoring()
        try startMonitoring(paths: newPaths)
        
        NSLog("已添加监控路径: \(path)")
    }
    
    /// 移除监控路径
    public func removeMonitorPath(_ path: String) throws {
        guard isMonitoring else { return }
        
        let newPaths = Array(monitoredPaths.filter { $0 != path })
        if !newPaths.isEmpty {
            stopMonitoring()
            try startMonitoring(paths: newPaths)
        } else {
            stopMonitoring()
        }
        
        NSLog("已移除监控路径: \(path)")
    }
    
    /// 获取监控统计信息
    public func getStatistics() -> MonitorStatistics {
        return statistics
    }
    
    /// 重置统计信息
    public func resetStatistics() {
        statistics = MonitorStatistics(
            totalEvents: 0,
            encryptionTriggers: 0,
            decryptionTriggers: 0,
            policyViolations: 0,
            averageProcessingTime: 0.0,
            lastResetTime: Date()
        )
    }
    
    // MARK: - 私有方法
    
    private func setupNotificationObservers() {
        // 监听系统睡眠/唤醒通知
        NotificationCenter.default.publisher(for: NSWorkspace.willSleepNotification)
            .sink { [weak self] _ in
                self?.handleSystemSleep()
            }
            .store(in: &cancellables)
        
        NotificationCenter.default.publisher(for: NSWorkspace.didWakeNotification)
            .sink { [weak self] _ in
                self?.handleSystemWake()
            }
            .store(in: &cancellables)
    }
    
    private func createEventStream(for paths: [String]) throws {
        let pathsToWatch = paths as CFArray
        let context = UnsafeMutablePointer<FileMonitorEngine>.allocate(capacity: 1)
        context.initialize(to: self)
        
        var fsEventContext = FSEventStreamContext(
            version: 0,
            info: UnsafeMutableRawPointer(context),
            retain: nil,
            release: nil,
            copyDescription: nil
        )
        
        let flags: FSEventStreamCreateFlags = UInt32(
            kFSEventStreamCreateFlagUseCFTypes |
            kFSEventStreamCreateFlagFileEvents |
            kFSEventStreamCreateFlagWatchRoot
        )
        
        eventStream = FSEventStreamCreate(
            kCFAllocatorDefault,
            fsEventCallback,
            &fsEventContext,
            pathsToWatch,
            FSEventStreamEventId(kFSEventStreamEventIdSinceNow),
            0.1, // 延迟时间（秒）
            flags
        )
        
        guard let stream = eventStream else {
            context.deallocate()
            throw MonitorError.streamCreationFailed
        }
        
        FSEventStreamSetDispatchQueue(stream, monitorQueue)
        
        if !FSEventStreamStart(stream) {
            FSEventStreamRelease(stream)
            context.deallocate()
            throw MonitorError.systemError(-2)
        }
    }
    
    private func handleFileSystemEvent(path: String, flags: FSEventStreamEventFlags) {
        let startTime = Date()
        
        monitorQueue.async { [weak self] in
            guard let self = self else { return }
            
            // 更新事件计数
            DispatchQueue.main.async {
                self.eventCount += 1
                self.lastEventTime = Date()
            }
            
            // 解析事件类型
            let event = self.parseEventFlags(path: path, flags: flags)
            
            // 通知委托
            DispatchQueue.main.async {
                self.delegate?.fileMonitor(self, didDetectEvent: event)
            }
            
            // 处理加密策略
            self.processEncryptionPolicy(for: path, event: event, startTime: startTime)
        }
    }
    
    private func parseEventFlags(path: String, flags: FSEventStreamEventFlags) -> FileEvent {
        if flags & FSEventStreamEventFlags(kFSEventStreamEventFlagItemCreated) != 0 {
            return .created(path: path)
        } else if flags & FSEventStreamEventFlags(kFSEventStreamEventFlagItemModified) != 0 {
            return .modified(path: path)
        } else if flags & FSEventStreamEventFlags(kFSEventStreamEventFlagItemRemoved) != 0 {
            return .deleted(path: path)
        } else if flags & FSEventStreamEventFlags(kFSEventStreamEventFlagItemRenamed) != 0 {
            return .moved(from: path, to: path) // 简化处理
        } else if flags & FSEventStreamEventFlags(kFSEventStreamEventFlagItemChangeOwner) != 0 ||
                  flags & FSEventStreamEventFlags(kFSEventStreamEventFlagItemXattrMod) != 0 {
            return .attributeChanged(path: path)
        } else {
            return .modified(path: path) // 默认为修改事件
        }
    }
    
    private func processEncryptionPolicy(for path: String, event: FileEvent, startTime: Date) {
        do {
            let shouldEncrypt = policyManager.shouldEncrypt(path: path)
            let isEncrypted = cryptoManager.isFileEncrypted(at: path)
            
            switch event {
            case .created(let filePath), .modified(let filePath):
                if shouldEncrypt && !isEncrypted {
                    // 需要加密但未加密的文件
                    try triggerEncryption(path: filePath)
                    updateStatistics(encryptionTrigger: true, startTime: startTime)
                } else if !shouldEncrypt && isEncrypted {
                    // 不需要加密但已加密的文件（策略变更）
                    try triggerDecryption(path: filePath)
                    updateStatistics(decryptionTrigger: true, startTime: startTime)
                }
                
            case .moved(let fromPath, let toPath):
                // 处理文件移动场景
                let shouldEncryptNew = policyManager.shouldEncrypt(path: toPath)
                if shouldEncryptNew != shouldEncrypt {
                    if shouldEncryptNew {
                        try triggerEncryption(path: toPath)
                        updateStatistics(encryptionTrigger: true, startTime: startTime)
                    } else {
                        try triggerDecryption(path: toPath)
                        updateStatistics(decryptionTrigger: true, startTime: startTime)
                    }
                }
                
            default:
                break
            }
            
        } catch {
            NSLog("处理加密策略时发生错误: \(error)")
            DispatchQueue.main.async {
                self.delegate?.fileMonitor(self, didEncounterError: error)
            }
        }
    }
    
    private func triggerEncryption(path: String) throws {
        NSLog("触发文件加密: \(path)")
        
        // 这里应该调用实际的加密逻辑
        // 为了演示，我们只是记录日志
        DispatchQueue.main.async {
            self.delegate?.fileMonitor(self, didTriggerEncryption: path)
        }
        
        // 实际加密逻辑（需要根据具体实现调整）
        // let keyId = try policyManager.getKeyId(for: path)
        // try cryptoManager.encryptFile(at: path, keyId: keyId)
    }
    
    private func triggerDecryption(path: String) throws {
        NSLog("触发文件解密: \(path)")
        
        // 这里应该调用实际的解密逻辑
        // 为了演示，我们只是记录日志
        DispatchQueue.main.async {
            self.delegate?.fileMonitor(self, didTriggerDecryption: path)
        }
        
        // 实际解密逻辑（需要根据具体实现调整）
        // try cryptoManager.decryptFile(at: path)
    }
    
    private func updateStatistics(encryptionTrigger: Bool = false, decryptionTrigger: Bool = false, startTime: Date) {
        let processingTime = Date().timeIntervalSince(startTime)
        
        statistics = MonitorStatistics(
            totalEvents: statistics.totalEvents + 1,
            encryptionTriggers: statistics.encryptionTriggers + (encryptionTrigger ? 1 : 0),
            decryptionTriggers: statistics.decryptionTriggers + (decryptionTrigger ? 1 : 0),
            policyViolations: statistics.policyViolations,
            averageProcessingTime: (statistics.averageProcessingTime * Double(statistics.totalEvents) + processingTime) / Double(statistics.totalEvents + 1),
            lastResetTime: statistics.lastResetTime
        )
    }
    
    private func handleSystemSleep() {
        NSLog("系统即将休眠，暂停文件监控")
        if isMonitoring {
            stopMonitoring()
        }
    }
    
    private func handleSystemWake() {
        NSLog("系统已唤醒，恢复文件监控")
        // 这里可以添加恢复监控的逻辑
        // 需要保存之前的监控路径状态
    }
}

// MARK: - C 回调函数
private func fsEventCallback(
    streamRef: ConstFSEventStreamRef,
    clientCallBackInfo: UnsafeMutableRawPointer?,
    numEvents: Int,
    eventPaths: UnsafeMutableRawPointer,
    eventFlags: UnsafePointer<FSEventStreamEventFlags>,
    eventIds: UnsafePointer<FSEventStreamEventId>
) {
    guard let info = clientCallBackInfo else { return }
    
    let monitor = info.assumingMemoryBound(to: FileMonitorEngine.self).pointee
    let paths = unsafeBitCast(eventPaths, to: NSArray.self) as! [String]
    
    for i in 0..<numEvents {
        let path = paths[i]
        let flags = eventFlags[i]
        
        monitor.handleFileSystemEvent(path: path, flags: flags)
    }
}

// MARK: - 扩展：调试和工具方法
@available(macOS 10.15, *)
extension FileMonitorEngine {
    
    /// 获取监控状态描述
    public var statusDescription: String {
        if isMonitoring {
            return "监控中 - \(monitoredPaths.count) 个路径"
        } else {
            return "未监控"
        }
    }
    
    /// 获取详细状态信息
    public func getDetailedStatus() -> [String: Any] {
        let stats = getStatistics()
        
        return [
            "isMonitoring": isMonitoring,
            "monitoredPaths": Array(monitoredPaths),
            "eventCount": eventCount,
            "lastEventTime": lastEventTime?.description ?? "无",
            "statistics": [
                "totalEvents": stats.totalEvents,
                "encryptionTriggers": stats.encryptionTriggers,
                "decryptionTriggers": stats.decryptionTriggers,
                "policyViolations": stats.policyViolations,
                "averageProcessingTime": String(format: "%.3f", stats.averageProcessingTime),
                "lastResetTime": stats.lastResetTime.description
            ]
        ]
    }
    
    /// 测试方法：模拟文件事件
    public func simulateFileEvent(path: String, eventType: FileEvent) {
        guard isMonitoring else { return }
        
        monitorQueue.async { [weak self] in
            guard let self = self else { return }
            
            DispatchQueue.main.async {
                self.delegate?.fileMonitor(self, didDetectEvent: eventType)
            }
            
            self.processEncryptionPolicy(for: path, event: eventType, startTime: Date())
        }
    }
} 