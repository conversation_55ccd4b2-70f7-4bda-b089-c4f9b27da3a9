using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows.Input;
using CryptoSystem.DeclassificationClient.Models;
using CryptoSystem.DeclassificationClient.Services;
using Microsoft.Extensions.Logging;

namespace CryptoSystem.DeclassificationClient.ViewModels
{
    /// <summary>
    /// 主窗口ViewModel
    /// </summary>
    public class MainWindowViewModel : INotifyPropertyChanged
    {
        private readonly IDeclassificationService _declassificationService;
        private readonly ISecurityService _securityService;
        private readonly IAuditService _auditService;
        private readonly ILogger<MainWindowViewModel> _logger;

        private string _statusMessage = "就绪";
        private string _connectionStatus = "已连接";
        private DateTime _currentTime = DateTime.Now;
        private UserInfo? _currentUser;
        private int _todayTaskCount;
        private int _processingTaskCount;
        private int _completedTaskCount;

        public MainWindowViewModel(
            IDeclassificationService declassificationService,
            ISecurityService securityService,
            IAuditService auditService,
            ILogger<MainWindowViewModel> logger)
        {
            _declassificationService = declassificationService;
            _securityService = securityService;
            _auditService = auditService;
            _logger = logger;

            Tasks = new ObservableCollection<DeclassificationTask>();
            
            // 初始化命令
            InitializeCommands();
            
            // 启动定时器更新时间
            StartTimeUpdater();
            
            // 加载初始数据
            _ = LoadInitialDataAsync();
        }

        #region 属性

        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        public string ConnectionStatus
        {
            get => _connectionStatus;
            set => SetProperty(ref _connectionStatus, value);
        }

        public DateTime CurrentTime
        {
            get => _currentTime;
            set => SetProperty(ref _currentTime, value);
        }

        public UserInfo? CurrentUser
        {
            get => _currentUser;
            set => SetProperty(ref _currentUser, value);
        }

        public int TodayTaskCount
        {
            get => _todayTaskCount;
            set => SetProperty(ref _todayTaskCount, value);
        }

        public int ProcessingTaskCount
        {
            get => _processingTaskCount;
            set => SetProperty(ref _processingTaskCount, value);
        }

        public int CompletedTaskCount
        {
            get => _completedTaskCount;
            set => SetProperty(ref _completedTaskCount, value);
        }

        public ObservableCollection<DeclassificationTask> Tasks { get; }

        #endregion

        #region 命令

        public ICommand CreateTaskCommand { get; private set; } = null!;
        public ICommand ImportFilesCommand { get; private set; } = null!;
        public ICommand RefreshCommand { get; private set; } = null!;
        public ICommand ExportCommand { get; private set; } = null!;
        public ICommand SettingsCommand { get; private set; } = null!;
        public ICommand HelpCommand { get; private set; } = null!;
        public ICommand LogoutCommand { get; private set; } = null!;
        public ICommand ViewTaskDetailCommand { get; private set; } = null!;
        public ICommand EditTaskCommand { get; private set; } = null!;
        public ICommand DeleteTaskCommand { get; private set; } = null!;

        #endregion

        #region 私有方法

        private void InitializeCommands()
        {
            CreateTaskCommand = new RelayCommand(async () => await CreateTaskAsync());
            ImportFilesCommand = new RelayCommand(async () => await ImportFilesAsync());
            RefreshCommand = new RelayCommand(async () => await RefreshDataAsync());
            ExportCommand = new RelayCommand(async () => await ExportDataAsync());
            SettingsCommand = new RelayCommand(() => ShowSettings());
            HelpCommand = new RelayCommand(() => ShowHelp());
            LogoutCommand = new RelayCommand(async () => await LogoutAsync());
            ViewTaskDetailCommand = new RelayCommand<DeclassificationTask>(task => ViewTaskDetail(task));
            EditTaskCommand = new RelayCommand<DeclassificationTask>(task => EditTask(task));
            DeleteTaskCommand = new RelayCommand<DeclassificationTask>(async task => await DeleteTaskAsync(task));
        }

        private void StartTimeUpdater()
        {
            var timer = new System.Windows.Threading.DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(1)
            };
            timer.Tick += (s, e) => CurrentTime = DateTime.Now;
            timer.Start();
        }

        private async Task LoadInitialDataAsync()
        {
            try
            {
                StatusMessage = "正在加载数据...";
                CurrentUser = _securityService.GetCurrentUser();
                await LoadTasksAsync();
                await LoadStatisticsAsync();
                StatusMessage = "就绪";
                _logger.LogInformation("初始数据加载完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载初始数据时发生错误");
                StatusMessage = "数据加载失败";
            }
        }

        private async Task LoadTasksAsync()
        {
            try
            {
                var (tasks, totalCount) = await _declassificationService.GetTasksAsync(pageSize: 50);
                Tasks.Clear();
                foreach (var task in tasks)
                {
                    Tasks.Add(task);
                }
                _logger.LogInformation($"加载了 {tasks.Count} 个任务");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载任务列表时发生错误");
            }
        }

        private async Task LoadStatisticsAsync()
        {
            try
            {
                var statistics = await _declassificationService.GetTaskStatisticsAsync();
                
                if (statistics.ContainsKey("TodayTaskCount"))
                    TodayTaskCount = Convert.ToInt32(statistics["TodayTaskCount"]);
                
                if (statistics.ContainsKey("ProcessingTaskCount"))
                    ProcessingTaskCount = Convert.ToInt32(statistics["ProcessingTaskCount"]);
                
                if (statistics.ContainsKey("CompletedTaskCount"))
                    CompletedTaskCount = Convert.ToInt32(statistics["CompletedTaskCount"]);

                _logger.LogInformation("统计信息加载完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载统计信息时发生错误");
            }
        }

        private async Task CreateTaskAsync()
        {
            try
            {
                StatusMessage = "创建新任务...";
                _logger.LogInformation("用户请求创建新任务");
                StatusMessage = "就绪";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建任务时发生错误");
                StatusMessage = "创建任务失败";
            }
        }

        private async Task ImportFilesAsync()
        {
            try
            {
                StatusMessage = "导入文件...";
                _logger.LogInformation("用户请求导入文件");
                StatusMessage = "就绪";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导入文件时发生错误");
                StatusMessage = "导入文件失败";
            }
        }

        private async Task RefreshDataAsync()
        {
            try
            {
                StatusMessage = "刷新数据...";
                await LoadTasksAsync();
                await LoadStatisticsAsync();
                StatusMessage = "数据已刷新";
                _logger.LogInformation("用户刷新了数据");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新数据时发生错误");
                StatusMessage = "刷新失败";
            }
        }

        private async Task ExportDataAsync()
        {
            try
            {
                StatusMessage = "导出数据...";
                _logger.LogInformation("用户请求导出数据");
                StatusMessage = "就绪";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导出数据时发生错误");
                StatusMessage = "导出失败";
            }
        }

        private void ShowSettings()
        {
            _logger.LogInformation("用户打开设置");
        }

        private void ShowHelp()
        {
            _logger.LogInformation("用户打开帮助");
        }

        private async Task LogoutAsync()
        {
            try
            {
                StatusMessage = "正在退出...";
                await _securityService.LogoutAsync();
                _logger.LogInformation("用户退出登录");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "退出登录时发生错误");
                StatusMessage = "退出失败";
            }
        }

        private void ViewTaskDetail(DeclassificationTask? task)
        {
            if (task == null) return;
            _logger.LogInformation($"用户查看任务详情: {task.TaskId}");
        }

        private void EditTask(DeclassificationTask? task)
        {
            if (task == null) return;
            _logger.LogInformation($"用户编辑任务: {task.TaskId}");
        }

        private async Task DeleteTaskAsync(DeclassificationTask? task)
        {
            if (task == null) return;
            
            try
            {
                StatusMessage = "正在删除任务...";
                var success = await _declassificationService.DeleteTaskAsync(task.TaskId);
                if (success)
                {
                    Tasks.Remove(task);
                    StatusMessage = "任务已删除";
                    _logger.LogInformation($"删除任务成功: {task.TaskId}");
                }
                else
                {
                    StatusMessage = "删除任务失败";
                    _logger.LogWarning($"删除任务失败: {task.TaskId}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"删除任务时发生错误: {task.TaskId}");
                StatusMessage = "删除任务失败";
            }
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        #endregion
    }

    /// <summary>
    /// 简单的RelayCommand实现
    /// </summary>
    public class RelayCommand : ICommand
    {
        private readonly Action _execute;
        private readonly Func<bool>? _canExecute;

        public RelayCommand(Action execute, Func<bool>? canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        public event EventHandler? CanExecuteChanged
        {
            add => System.Windows.Input.CommandManager.RequerySuggested += value;
            remove => System.Windows.Input.CommandManager.RequerySuggested -= value;
        }

        public bool CanExecute(object? parameter) => _canExecute?.Invoke() ?? true;

        public void Execute(object? parameter) => _execute();
    }

    /// <summary>
    /// 带参数的RelayCommand实现
    /// </summary>
    public class RelayCommand<T> : ICommand
    {
        private readonly Action<T?> _execute;
        private readonly Func<T?, bool>? _canExecute;

        public RelayCommand(Action<T?> execute, Func<T?, bool>? canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        public event EventHandler? CanExecuteChanged
        {
            add => System.Windows.Input.CommandManager.RequerySuggested += value;
            remove => System.Windows.Input.CommandManager.RequerySuggested -= value;
        }

        public bool CanExecute(object? parameter) => _canExecute?.Invoke((T?)parameter) ?? true;

        public void Execute(object? parameter) => _execute((T?)parameter);
    }
} 