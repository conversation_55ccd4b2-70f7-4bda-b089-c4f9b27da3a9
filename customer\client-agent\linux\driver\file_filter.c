/*
 * file_filter.c
 *
 * Linux客户端文件过滤器实现
 * 负责拦截文件操作并实现透明加解密
 */

#include "file_filter.h"
#include "crypto_manager.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <pthread.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <errno.h>
#include <dirent.h>
#include <limits.h>

// 静态全局状态
typedef struct {
    int initialized;                   // 初始化标志
    filter_options options;            // 过滤器选项
    char **protected_paths;            // 受保护路径数组（堆上分配）
    int protected_paths_count;         // 受保护路径数量
    uint32_t default_key_version;      // 默认密钥版本
    filter_callback *callbacks;        // 回调函数数组
    void **callback_contexts;          // 回调上下文数组
    uint32_t *callback_masks;          // 回调掩码数组
    int callbacks_count;               // 回调函数数量
    pthread_mutex_t lock;              // 全局锁
    FILE *log_file;                    // 日志文件句柄
    int log_level;                     // 日志级别
    char version[32];                  // 版本号
} filter_state;

// 全局状态
static filter_state state = {0};

// 日志级别常量
#define LOG_LEVEL_NONE  0
#define LOG_LEVEL_ERROR 1
#define LOG_LEVEL_WARN  2
#define LOG_LEVEL_INFO  3
#define LOG_LEVEL_DEBUG 4

// 内部版本号
#define FILTER_VERSION "1.0.0"

// 最大路径长度
#ifndef PATH_MAX
#define PATH_MAX 4096
#endif

// 内部函数声明
static void filter_log(int level, const char *format, ...);
static int is_path_protected(const char *path);
static int check_file_encrypted(const char *file_path);

// 初始化文件过滤器
filter_status filter_init(const filter_options *options) {
    // 检查是否已初始化
    if (state.initialized) {
        return FILTER_STATUS_ALREADY_INIT;
    }
    
    // 初始化全局锁
    if (pthread_mutex_init(&state.lock, NULL) != 0) {
        return FILTER_STATUS_ERROR;
    }
    
    // 初始化默认选项
    state.log_level = LOG_LEVEL_ERROR;
    state.default_key_version = 1;
    state.protected_paths = NULL;
    state.protected_paths_count = 0;
    state.callbacks = NULL;
    state.callback_contexts = NULL;
    state.callback_masks = NULL;
    state.callbacks_count = 0;
    state.log_file = NULL;
    
    // 设置版本号
    strncpy(state.version, FILTER_VERSION, sizeof(state.version) - 1);
    state.version[sizeof(state.version) - 1] = '\0';
    
    // 如果提供了选项，则使用它们
    if (options) {
        // 复制受保护路径
        if (options->protected_paths_count > 0) {
            state.protected_paths = (char **)malloc(options->protected_paths_count * sizeof(char *));
            if (!state.protected_paths) {
                pthread_mutex_destroy(&state.lock);
                return FILTER_STATUS_MEMORY_ERROR;
            }
            
            for (int i = 0; i < options->protected_paths_count; i++) {
                if (options->protected_paths[i]) {
                    state.protected_paths[i] = strdup(options->protected_paths[i]);
                    if (!state.protected_paths[i]) {
                        // 清理已分配的内存
                        for (int j = 0; j < i; j++) {
                            free(state.protected_paths[j]);
                        }
                        free(state.protected_paths);
                        pthread_mutex_destroy(&state.lock);
                        return FILTER_STATUS_MEMORY_ERROR;
                    }
                    state.protected_paths_count++;
                }
            }
        }
        
        // 设置日志文件
        if (options->log_file[0] != '\0') {
            state.log_file = fopen(options->log_file, "a");
            // 日志文件打开失败不视为致命错误，但会记录到stderr
            if (!state.log_file) {
                fprintf(stderr, "警告：无法打开日志文件 %s: %s\n", 
                        options->log_file, strerror(errno));
            }
        }
        
        // 设置日志级别
        state.log_level = options->log_level;
        
        // 设置默认密钥版本
        if (options->default_key_version > 0) {
            state.default_key_version = options->default_key_version;
        }
    }
    
    // 标记为已初始化
    state.initialized = 1;
    
    filter_log(LOG_LEVEL_INFO, "文件过滤器初始化完成，版本 %s", state.version);
    return FILTER_STATUS_SUCCESS;
}

// 清理文件过滤器
void filter_cleanup(void) {
    // 检查是否已初始化
    if (!state.initialized) {
        return;
    }
    
    pthread_mutex_lock(&state.lock);
    
    // 释放受保护路径
    if (state.protected_paths) {
        for (int i = 0; i < state.protected_paths_count; i++) {
            free(state.protected_paths[i]);
        }
        free(state.protected_paths);
        state.protected_paths = NULL;
        state.protected_paths_count = 0;
    }
    
    // 释放回调数组
    free(state.callbacks);
    free(state.callback_contexts);
    free(state.callback_masks);
    state.callbacks = NULL;
    state.callback_contexts = NULL;
    state.callback_masks = NULL;
    state.callbacks_count = 0;
    
    // 关闭日志文件
    if (state.log_file) {
        fclose(state.log_file);
        state.log_file = NULL;
    }
    
    // 标记为未初始化
    state.initialized = 0;
    
    // 释放锁并销毁
    pthread_mutex_unlock(&state.lock);
    pthread_mutex_destroy(&state.lock);
    
    filter_log(LOG_LEVEL_INFO, "文件过滤器已清理");
}

// 注册过滤器回调函数
filter_status filter_register_callback(
    uint32_t operation_mask,
    filter_callback callback,
    void *context
) {
    // 检查参数
    if (!callback) {
        return FILTER_STATUS_INVALID_PARAM;
    }
    
    // 检查是否已初始化
    if (!state.initialized) {
        return FILTER_STATUS_NOT_INIT;
    }
    
    pthread_mutex_lock(&state.lock);
    
    // 分配回调数组
    int new_size = state.callbacks_count + 1;
    filter_callback *new_callbacks = realloc(state.callbacks, new_size * sizeof(filter_callback));
    void **new_contexts = realloc(state.callback_contexts, new_size * sizeof(void *));
    uint32_t *new_masks = realloc(state.callback_masks, new_size * sizeof(uint32_t));
    
    if (!new_callbacks || !new_contexts || !new_masks) {
        free(new_callbacks);
        free(new_contexts);
        free(new_masks);
        pthread_mutex_unlock(&state.lock);
        return FILTER_STATUS_MEMORY_ERROR;
    }
    
    // 更新回调数组
    state.callbacks = new_callbacks;
    state.callback_contexts = new_contexts;
    state.callback_masks = new_masks;
    
    // 添加新回调
    state.callbacks[state.callbacks_count] = callback;
    state.callback_contexts[state.callbacks_count] = context;
    state.callback_masks[state.callbacks_count] = operation_mask;
    state.callbacks_count++;
    
    pthread_mutex_unlock(&state.lock);
    return FILTER_STATUS_SUCCESS;
}

// 添加受保护的路径
filter_status filter_add_protected_path(const char *path, bool recursive) {
    // 检查参数
    if (!path || path[0] == '\0') {
        return FILTER_STATUS_INVALID_PARAM;
    }
    
    // 检查是否已初始化
    if (!state.initialized) {
        return FILTER_STATUS_NOT_INIT;
    }
    
    pthread_mutex_lock(&state.lock);
    
    // 检查路径是否已存在
    for (int i = 0; i < state.protected_paths_count; i++) {
        if (strcmp(state.protected_paths[i], path) == 0) {
            pthread_mutex_unlock(&state.lock);
            return FILTER_STATUS_SUCCESS; // 路径已存在
        }
    }
    
    // 分配新路径数组
    char **new_paths = realloc(state.protected_paths, 
                               (state.protected_paths_count + 1) * sizeof(char *));
    if (!new_paths) {
        pthread_mutex_unlock(&state.lock);
        return FILTER_STATUS_MEMORY_ERROR;
    }
    
    // 复制路径
    char *path_copy = strdup(path);
    if (!path_copy) {
        pthread_mutex_unlock(&state.lock);
        return FILTER_STATUS_MEMORY_ERROR;
    }
    
    // 更新保护路径数组
    state.protected_paths = new_paths;
    state.protected_paths[state.protected_paths_count] = path_copy;
    state.protected_paths_count++;
    
    pthread_mutex_unlock(&state.lock);
    
    filter_log(LOG_LEVEL_INFO, "添加保护路径: %s", path);
    return FILTER_STATUS_SUCCESS;
}

// 移除受保护的路径
filter_status filter_remove_protected_path(const char *path) {
    // 检查参数
    if (!path || path[0] == '\0') {
        return FILTER_STATUS_INVALID_PARAM;
    }
    
    // 检查是否已初始化
    if (!state.initialized) {
        return FILTER_STATUS_NOT_INIT;
    }
    
    pthread_mutex_lock(&state.lock);
    
    // 查找路径
    int found_index = -1;
    for (int i = 0; i < state.protected_paths_count; i++) {
        if (strcmp(state.protected_paths[i], path) == 0) {
            found_index = i;
            break;
        }
    }
    
    // 路径不存在
    if (found_index == -1) {
        pthread_mutex_unlock(&state.lock);
        return FILTER_STATUS_PATH_NOT_FOUND;
    }
    
    // 释放路径内存
    free(state.protected_paths[found_index]);
    
    // 移动数组元素以填补空缺
    for (int i = found_index; i < state.protected_paths_count - 1; i++) {
        state.protected_paths[i] = state.protected_paths[i + 1];
    }
    
    // 减少计数
    state.protected_paths_count--;
    
    // 如果数组为空，可以释放内存
    if (state.protected_paths_count == 0) {
        free(state.protected_paths);
        state.protected_paths = NULL;
    } else {
        // 调整数组大小
        char **new_paths = realloc(state.protected_paths, 
                                  state.protected_paths_count * sizeof(char *));
        if (new_paths) {
            state.protected_paths = new_paths;
        }
    }
    
    pthread_mutex_unlock(&state.lock);
    
    filter_log(LOG_LEVEL_INFO, "移除保护路径: %s", path);
    return FILTER_STATUS_SUCCESS;
}

// 检查文件是否已加密
filter_status filter_is_file_encrypted(const char *file_path, bool *is_encrypted) {
    // 检查参数
    if (!file_path || !is_encrypted) {
        return FILTER_STATUS_INVALID_PARAM;
    }
    
    // 检查是否已初始化
    if (!state.initialized) {
        return FILTER_STATUS_NOT_INIT;
    }
    
    // 检查文件是否存在
    struct stat st;
    if (stat(file_path, &st) != 0) {
        return FILTER_STATUS_PATH_NOT_FOUND;
    }
    
    // 检查是否是常规文件
    if (!S_ISREG(st.st_mode)) {
        *is_encrypted = false;
        return FILTER_STATUS_SUCCESS;
    }
    
    // 检查文件是否已加密
    *is_encrypted = check_file_encrypted(file_path);
    
    return FILTER_STATUS_SUCCESS;
}

// 手动加密文件
filter_status filter_encrypt_file(const char *file_path, uint32_t key_version) {
    // 检查参数
    if (!file_path) {
        return FILTER_STATUS_INVALID_PARAM;
    }
    
    // 检查是否已初始化
    if (!state.initialized) {
        return FILTER_STATUS_NOT_INIT;
    }
    
    // 检查文件是否存在
    struct stat st;
    if (stat(file_path, &st) != 0) {
        return FILTER_STATUS_PATH_NOT_FOUND;
    }
    
    // 检查是否是常规文件
    if (!S_ISREG(st.st_mode)) {
        return FILTER_STATUS_INVALID_PARAM;
    }
    
    // 检查文件是否已加密
    if (check_file_encrypted(file_path)) {
        return FILTER_STATUS_ALREADY_ENCRYPTED;
    }
    
    // 使用密钥版本
    if (key_version == 0) {
        // 使用默认密钥版本
        if (state.default_key_version == 0) {
            return FILTER_STATUS_INVALID_PARAM;
        }
        key_version = state.default_key_version;
    }
    
    // 打开源文件
    FILE *src_file = fopen(file_path, "rb");
    if (!src_file) {
        return FILTER_STATUS_ERROR;
    }
    
    // 创建临时文件
    char temp_path[PATH_MAX];
    snprintf(temp_path, sizeof(temp_path), "%s.enc_tmp", file_path);
    FILE *dst_file = fopen(temp_path, "wb");
    if (!dst_file) {
        fclose(src_file);
        return FILTER_STATUS_ERROR;
    }
    
    // 写入加密文件头标识
    const char *header = "ENCF";
    if (fwrite(header, 1, 4, dst_file) != 4) {
        fclose(src_file);
        fclose(dst_file);
        unlink(temp_path);
        return FILTER_STATUS_ERROR;
    }
    
    // 生成随机IV
    uint8_t iv[CRYPTO_IV_SIZE];
    if (crypto_generate_random(iv, CRYPTO_IV_SIZE) != CRYPTO_SUCCESS) {
        fclose(src_file);
        fclose(dst_file);
        unlink(temp_path);
        return FILTER_STATUS_ENCRYPTION_ERROR;
    }
    
    // 写入IV
    if (fwrite(iv, 1, CRYPTO_IV_SIZE, dst_file) != CRYPTO_IV_SIZE) {
        fclose(src_file);
        fclose(dst_file);
        unlink(temp_path);
        return FILTER_STATUS_ERROR;
    }
    
    // 写入密钥版本
    uint32_t key_version_network = htonl(key_version);
    if (fwrite(&key_version_network, sizeof(key_version_network), 1, dst_file) != 1) {
        fclose(src_file);
        fclose(dst_file);
        unlink(temp_path);
        return FILTER_STATUS_ERROR;
    }
    
    // 写入算法信息 (默认使用AES-GCM)
    uint8_t algo_info = ALGORITHM_AES;
    uint8_t mode_info = MODE_GCM;
    if (fwrite(&algo_info, 1, 1, dst_file) != 1 || 
        fwrite(&mode_info, 1, 1, dst_file) != 1) {
        fclose(src_file);
        fclose(dst_file);
        unlink(temp_path);
        return FILTER_STATUS_ERROR;
    }
    
    // 读取源文件数据并加密
    uint8_t buffer[8192];
    uint8_t encrypted[8192 + CRYPTO_IV_SIZE + CRYPTO_TAG_SIZE];
    size_t bytes_read;
    
    // 每次读取并加密一块数据
    while ((bytes_read = fread(buffer, 1, sizeof(buffer), src_file)) > 0) {
        size_t encrypted_len = sizeof(encrypted);
        
        // 加密数据块
        int crypto_result = crypto_encrypt(
            ALGORITHM_AES,
            MODE_GCM,
            key_version,
            iv,
            buffer,
            bytes_read,
            encrypted,
            &encrypted_len
        );
        
        if (crypto_result != CRYPTO_SUCCESS) {
            fclose(src_file);
            fclose(dst_file);
            unlink(temp_path);
            return FILTER_STATUS_ENCRYPTION_ERROR;
        }
        
        // 写入加密后的数据
        if (fwrite(encrypted, 1, encrypted_len, dst_file) != encrypted_len) {
            fclose(src_file);
            fclose(dst_file);
            unlink(temp_path);
            return FILTER_STATUS_ERROR;
        }
        
        // 更新IV用于下一块（使用前一块加密输出的最后16字节）
        // 这是一种简单的IV链接方式，提高安全性
        if (encrypted_len >= CRYPTO_IV_SIZE) {
            memcpy(iv, encrypted + (encrypted_len - CRYPTO_IV_SIZE), CRYPTO_IV_SIZE);
        }
    }
    
    // 检查是否发生读取错误
    if (ferror(src_file)) {
        fclose(src_file);
        fclose(dst_file);
        unlink(temp_path);
        return FILTER_STATUS_ERROR;
    }
    
    // 关闭文件
    fclose(src_file);
    fclose(dst_file);
    
    // 替换原文件
    if (rename(temp_path, file_path) != 0) {
        unlink(temp_path);
        return FILTER_STATUS_ERROR;
    }
    
    filter_log(LOG_LEVEL_INFO, "文件已加密: %s", file_path);
    return FILTER_STATUS_SUCCESS;
}

// 手动解密文件
filter_status filter_decrypt_file(const char *file_path) {
    // 检查参数
    if (!file_path) {
        return FILTER_STATUS_INVALID_PARAM;
    }
    
    // 检查是否已初始化
    if (!state.initialized) {
        return FILTER_STATUS_NOT_INIT;
    }
    
    // 检查文件是否存在
    struct stat st;
    if (stat(file_path, &st) != 0) {
        return FILTER_STATUS_PATH_NOT_FOUND;
    }
    
    // 检查是否是常规文件
    if (!S_ISREG(st.st_mode)) {
        return FILTER_STATUS_INVALID_PARAM;
    }
    
    // 检查文件是否已加密
    if (!check_file_encrypted(file_path)) {
        return FILTER_STATUS_NOT_ENCRYPTED;
    }
    
    // 打开源文件
    FILE *src_file = fopen(file_path, "rb");
    if (!src_file) {
        return FILTER_STATUS_ERROR;
    }
    
    // 创建临时文件
    char temp_path[PATH_MAX];
    snprintf(temp_path, sizeof(temp_path), "%s.dec_tmp", file_path);
    FILE *dst_file = fopen(temp_path, "wb");
    if (!dst_file) {
        fclose(src_file);
        return FILTER_STATUS_ERROR;
    }
    
    // 读取并跳过文件头标识
    char header[4];
    if (fread(header, 1, 4, src_file) != 4 || memcmp(header, "ENCF", 4) != 0) {
        fclose(src_file);
        fclose(dst_file);
        unlink(temp_path);
        return FILTER_STATUS_NOT_ENCRYPTED;
    }
    
    // 读取IV
    uint8_t iv[CRYPTO_IV_SIZE];
    if (fread(iv, 1, CRYPTO_IV_SIZE, src_file) != CRYPTO_IV_SIZE) {
        fclose(src_file);
        fclose(dst_file);
        unlink(temp_path);
        return FILTER_STATUS_ERROR;
    }
    
    // 读取密钥版本
    uint32_t key_version_network;
    if (fread(&key_version_network, sizeof(key_version_network), 1, src_file) != 1) {
        fclose(src_file);
        fclose(dst_file);
        unlink(temp_path);
        return FILTER_STATUS_ERROR;
    }
    uint32_t key_version = ntohl(key_version_network);
    
    // 读取算法信息
    uint8_t algo_info;
    uint8_t mode_info;
    if (fread(&algo_info, 1, 1, src_file) != 1 || 
        fread(&mode_info, 1, 1, src_file) != 1) {
        fclose(src_file);
        fclose(dst_file);
        unlink(temp_path);
        return FILTER_STATUS_ERROR;
    }
    
    // 读取源文件数据并解密
    uint8_t encrypted[8192 + CRYPTO_IV_SIZE + CRYPTO_TAG_SIZE];
    uint8_t decrypted[8192];
    size_t bytes_read;
    
    // 计算文件头大小
    const size_t header_size = 4 + CRYPTO_IV_SIZE + sizeof(uint32_t) + 2;
    
    // 获取文件大小
    fseek(src_file, 0, SEEK_END);
    long file_size = ftell(src_file);
    
    // 如果文件只有头部，没有内容
    if (file_size <= header_size) {
        fclose(src_file);
        fclose(dst_file);
        unlink(temp_path);
        return FILTER_STATUS_ERROR;
    }
    
    // 重新定位到头部之后
    fseek(src_file, header_size, SEEK_SET);
    
    // 每次读取并解密一块数据
    while ((bytes_read = fread(encrypted, 1, sizeof(encrypted), src_file)) > 0) {
        size_t decrypted_len = sizeof(decrypted);
        
        // 解密数据块
        int crypto_result = crypto_decrypt(
            algo_info,
            mode_info,
            key_version,
            iv,
            encrypted,
            bytes_read,
            decrypted,
            &decrypted_len
        );
        
        if (crypto_result != CRYPTO_SUCCESS) {
            fclose(src_file);
            fclose(dst_file);
            unlink(temp_path);
            return FILTER_STATUS_DECRYPTION_ERROR;
        }
        
        // 写入解密后的数据
        if (fwrite(decrypted, 1, decrypted_len, dst_file) != decrypted_len) {
            fclose(src_file);
            fclose(dst_file);
            unlink(temp_path);
            return FILTER_STATUS_ERROR;
        }
        
        // 更新IV用于下一块（使用前一块密文的最后16字节）
        if (bytes_read >= CRYPTO_IV_SIZE) {
            memcpy(iv, encrypted + (bytes_read - CRYPTO_IV_SIZE), CRYPTO_IV_SIZE);
        }
    }
    
    // 检查是否发生读取错误
    if (ferror(src_file)) {
        fclose(src_file);
        fclose(dst_file);
        unlink(temp_path);
        return FILTER_STATUS_ERROR;
    }
    
    // 关闭文件
    fclose(src_file);
    fclose(dst_file);
    
    // 替换原文件
    if (rename(temp_path, file_path) != 0) {
        unlink(temp_path);
        return FILTER_STATUS_ERROR;
    }
    
    filter_log(LOG_LEVEL_INFO, "文件已解密: %s", file_path);
    return FILTER_STATUS_SUCCESS;
}

// 设置默认加密密钥版本
filter_status filter_set_default_key_version(uint32_t key_version) {
    // 检查参数
    if (key_version == 0) {
        return FILTER_STATUS_INVALID_PARAM;
    }
    
    // 检查是否已初始化
    if (!state.initialized) {
        return FILTER_STATUS_NOT_INIT;
    }
    
    pthread_mutex_lock(&state.lock);
    state.default_key_version = key_version;
    pthread_mutex_unlock(&state.lock);
    
    filter_log(LOG_LEVEL_INFO, "设置默认密钥版本: %u", key_version);
    return FILTER_STATUS_SUCCESS;
}

// 获取默认加密密钥版本
filter_status filter_get_default_key_version(uint32_t *key_version) {
    // 检查参数
    if (!key_version) {
        return FILTER_STATUS_INVALID_PARAM;
    }
    
    // 检查是否已初始化
    if (!state.initialized) {
        return FILTER_STATUS_NOT_INIT;
    }
    
    pthread_mutex_lock(&state.lock);
    *key_version = state.default_key_version;
    pthread_mutex_unlock(&state.lock);
    
    return FILTER_STATUS_SUCCESS;
}

// 设置日志级别
filter_status filter_set_log_level(int level) {
    // 检查参数
    if (level < LOG_LEVEL_NONE || level > LOG_LEVEL_DEBUG) {
        return FILTER_STATUS_INVALID_PARAM;
    }
    
    // 检查是否已初始化
    if (!state.initialized) {
        return FILTER_STATUS_NOT_INIT;
    }
    
    pthread_mutex_lock(&state.lock);
    state.log_level = level;
    pthread_mutex_unlock(&state.lock);
    
    filter_log(LOG_LEVEL_INFO, "设置日志级别: %d", level);
    return FILTER_STATUS_SUCCESS;
}

// 获取过滤器版本信息
filter_status filter_get_version(char *version, size_t version_size) {
    // 检查参数
    if (!version || version_size == 0) {
        return FILTER_STATUS_INVALID_PARAM;
    }
    
    // 检查是否已初始化
    if (!state.initialized) {
        return FILTER_STATUS_NOT_INIT;
    }
    
    pthread_mutex_lock(&state.lock);
    strncpy(version, state.version, version_size - 1);
    version[version_size - 1] = '\0';
    pthread_mutex_unlock(&state.lock);
    
    return FILTER_STATUS_SUCCESS;
}

// 检查文件路径是否受保护
filter_status filter_is_path_protected(const char *file_path, bool *is_protected) {
    // 检查参数
    if (!file_path || !is_protected) {
        return FILTER_STATUS_INVALID_PARAM;
    }
    
    // 检查是否已初始化
    if (!state.initialized) {
        return FILTER_STATUS_NOT_INIT;
    }
    
    *is_protected = is_path_protected(file_path);
    
    return FILTER_STATUS_SUCCESS;
}

// 内部函数：检查路径是否受保护
static int is_path_protected(const char *path) {
    pthread_mutex_lock(&state.lock);
    
    // 检查路径是否在保护列表中
    for (int i = 0; i < state.protected_paths_count; i++) {
        // 检查路径是否是保护路径的子目录
        size_t len = strlen(state.protected_paths[i]);
        if (strncmp(path, state.protected_paths[i], len) == 0 &&
            (path[len] == '/' || path[len] == '\0')) {
            pthread_mutex_unlock(&state.lock);
            return 1;
        }
    }
    
    pthread_mutex_unlock(&state.lock);
    return 0;
}

// 内部函数：检查文件是否已加密
static int check_file_encrypted(const char *file_path) {
    FILE *file = fopen(file_path, "rb");
    if (!file) {
        return 0;
    }
    
    // 检查文件头部标识
    char signature[4];
    size_t read_size = fread(signature, 1, 4, file);
    fclose(file);
    
    if (read_size != 4) {
        return 0;
    }
    
    // 这里需要根据实际加密方案判断文件是否已加密
    // 例如，检查特定的文件头部标识
    if (memcmp(signature, "ENCF", 4) == 0) {
        return 1;
    }
    
    return 0;
}

// 内部函数：写入日志
static void filter_log(int level, const char *format, ...) {
    // 如果日志级别低于设置，则不记录
    if (level > state.log_level) {
        return;
    }
    
    // 准备日志消息
    va_list args;
    va_start(args, format);
    
    char time_str[32];
    time_t now = time(NULL);
    struct tm *tm_info = localtime(&now);
    strftime(time_str, sizeof(time_str), "%Y-%m-%d %H:%M:%S", tm_info);
    
    const char *level_str = "UNKNOWN";
    switch (level) {
        case LOG_LEVEL_ERROR: level_str = "ERROR"; break;
        case LOG_LEVEL_WARN:  level_str = "WARN"; break;
        case LOG_LEVEL_INFO:  level_str = "INFO"; break;
        case LOG_LEVEL_DEBUG: level_str = "DEBUG"; break;
    }
    
    // 写入日志文件
    if (state.log_file) {
        fprintf(state.log_file, "[%s] [%s] ", time_str, level_str);
        vfprintf(state.log_file, format, args);
        fprintf(state.log_file, "\n");
        fflush(state.log_file);
    } else {
        // 如果没有日志文件，则写入stderr
        fprintf(stderr, "[%s] [%s] ", time_str, level_str);
        vfprintf(stderr, format, args);
        fprintf(stderr, "\n");
    }
    
    va_end(args);
} 