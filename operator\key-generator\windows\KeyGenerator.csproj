<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>WinExe</OutputType>
        <TargetFramework>net9.0-windows</TargetFramework>
        <UseWPF>true</UseWPF>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <AssemblyTitle>企业密钥生成器</AssemblyTitle>
        <AssemblyDescription>运营商密钥生成与管理工具</AssemblyDescription>
        <AssemblyVersion>*******</AssemblyVersion>
        <FileVersion>*******</FileVersion>
        <Company>Enterprise Security Solutions</Company>
        <Product>Document Encryption System</Product>
        <Copyright>© 2025 Enterprise Security Solutions</Copyright>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />
        <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.0" />
        <PackageReference Include="MySql.Data" Version="9.1.0" />
        <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
        <PackageReference Include="Npgsql" Version="9.0.2" />
        <PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
    </ItemGroup>

    <ItemGroup>
        <None Update="appsettings.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
    </ItemGroup>

    <ItemGroup>
        <Folder Include="Models\" />
        <Folder Include="ViewModels\" />
        <Folder Include="Views\" />
        <Folder Include="Services\" />
        <Folder Include="Utils\" />
    </ItemGroup>

</Project> 