// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		1A1B2C3D4E5F6A7B8C9D0E1F /* CryptoManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1B2C3D4E5F6A7B8C9D0E1E /* CryptoManager.swift */; };
		1A1B2C3D4E5F6A7B8C9D0E2F /* PolicyManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1B2C3D4E5F6A7B8C9D0E2E /* PolicyManager.swift */; };
		1A1B2C3D4E5F6A7B8C9D0E3F /* FileProviderExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1B2C3D4E5F6A7B8C9D0E3E /* FileProviderExtension.swift */; };
		1A1B2C3D4E5F6A7B8C9D0E4F /* FileProvider.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1A1B2C3D4E5F6A7B8C9D0E4E /* FileProvider.framework */; };
		1A1B2C3D4E5F6A7B8C9D0E5F /* Security.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1A1B2C3D4E5F6A7B8C9D0E5E /* Security.framework */; };
		1A1B2C3D4E5F6A7B8C9D0E6F /* CryptoKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1A1B2C3D4E5F6A7B8C9D0E6E /* CryptoKit.framework */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		1A1B2C3D4E5F6A7B8C9D0E1E /* CryptoManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CryptoManager.swift; sourceTree = "<group>"; };
		1A1B2C3D4E5F6A7B8C9D0E2E /* PolicyManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PolicyManager.swift; sourceTree = "<group>"; };
		1A1B2C3D4E5F6A7B8C9D0E3E /* FileProviderExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FileProviderExtension.swift; sourceTree = "<group>"; };
		1A1B2C3D4E5F6A7B8C9D0E4E /* FileProvider.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = FileProvider.framework; path = System/Library/Frameworks/FileProvider.framework; sourceTree = SDKROOT; };
		1A1B2C3D4E5F6A7B8C9D0E5E /* Security.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Security.framework; path = System/Library/Frameworks/Security.framework; sourceTree = SDKROOT; };
		1A1B2C3D4E5F6A7B8C9D0E6E /* CryptoKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CryptoKit.framework; path = System/Library/Frameworks/CryptoKit.framework; sourceTree = SDKROOT; };
		1A1B2C3D4E5F6A7B8C9D0E7E /* CryptoSystemMac.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = CryptoSystemMac.app; sourceTree = BUILT_PRODUCTS_DIR; };
		1A1B2C3D4E5F6A7B8C9D0E8E /* CryptoSystemMacExtension.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = CryptoSystemMacExtension.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		1A1B2C3D4E5F6A7B8C9D0E9E /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		1A1B2C3D4E5F6A7B8C9D0EAE /* CryptoSystemMac.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = CryptoSystemMac.entitlements; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		1A1B2C3D4E5F6A7B8C9D0E1A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				1A1B2C3D4E5F6A7B8C9D0E4F /* FileProvider.framework in Frameworks */,
				1A1B2C3D4E5F6A7B8C9D0E5F /* Security.framework in Frameworks */,
				1A1B2C3D4E5F6A7B8C9D0E6F /* CryptoKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		1A1B2C3D4E5F6A7B8C9D0E1B /* CryptoSystemMac */ = {
			isa = PBXGroup;
			children = (
				1A1B2C3D4E5F6A7B8C9D0E1C /* driver */,
				1A1B2C3D4E5F6A7B8C9D0E9E /* Info.plist */,
				1A1B2C3D4E5F6A7B8C9D0EAE /* CryptoSystemMac.entitlements */,
			);
			path = CryptoSystemMac;
			sourceTree = "<group>";
		};
		1A1B2C3D4E5F6A7B8C9D0E1C /* driver */ = {
			isa = PBXGroup;
			children = (
				1A1B2C3D4E5F6A7B8C9D0E1D /* crypto */,
				1A1B2C3D4E5F6A7B8C9D0E2D /* file_filter */,
				1A1B2C3D4E5F6A7B8C9D0E2E /* PolicyManager.swift */,
			);
			path = driver;
			sourceTree = "<group>";
		};
		1A1B2C3D4E5F6A7B8C9D0E1D /* crypto */ = {
			isa = PBXGroup;
			children = (
				1A1B2C3D4E5F6A7B8C9D0E1E /* CryptoManager.swift */,
			);
			path = crypto;
			sourceTree = "<group>";
		};
		1A1B2C3D4E5F6A7B8C9D0E2D /* file_filter */ = {
			isa = PBXGroup;
			children = (
				1A1B2C3D4E5F6A7B8C9D0E3E /* FileProviderExtension.swift */,
			);
			path = file_filter;
			sourceTree = "<group>";
		};
		1A1B2C3D4E5F6A7B8C9D0E3D /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				1A1B2C3D4E5F6A7B8C9D0E4E /* FileProvider.framework */,
				1A1B2C3D4E5F6A7B8C9D0E5E /* Security.framework */,
				1A1B2C3D4E5F6A7B8C9D0E6E /* CryptoKit.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		1A1B2C3D4E5F6A7B8C9D0E4D /* Products */ = {
			isa = PBXGroup;
			children = (
				1A1B2C3D4E5F6A7B8C9D0E7E /* CryptoSystemMac.app */,
				1A1B2C3D4E5F6A7B8C9D0E8E /* CryptoSystemMacExtension.appex */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		1A1B2C3D4E5F6A7B8C9D0E5D = {
			isa = PBXGroup;
			children = (
				1A1B2C3D4E5F6A7B8C9D0E1B /* CryptoSystemMac */,
				1A1B2C3D4E5F6A7B8C9D0E3D /* Frameworks */,
				1A1B2C3D4E5F6A7B8C9D0E4D /* Products */,
			);
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		1A1B2C3D4E5F6A7B8C9D0E1F /* CryptoSystemMac */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1A1B2C3D4E5F6A7B8C9D0E2F /* Build configuration list for PBXNativeTarget "CryptoSystemMac" */;
			buildPhases = (
				1A1B2C3D4E5F6A7B8C9D0E1A /* Frameworks */,
				1A1B2C3D4E5F6A7B8C9D0E2A /* Sources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = CryptoSystemMac;
			productName = CryptoSystemMac;
			productReference = 1A1B2C3D4E5F6A7B8C9D0E7E /* CryptoSystemMac.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		1A1B2C3D4E5F6A7B8C9D0E6D /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1500;
				LastUpgradeCheck = 1500;
				TargetAttributes = {
					1A1B2C3D4E5F6A7B8C9D0E1F = {
						CreatedOnToolsVersion = 15.0;
					};
				};
			};
			buildConfigurationList = 1A1B2C3D4E5F6A7B8C9D0E3F /* Build configuration list for PBXProject "CryptoSystemMac" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 1A1B2C3D4E5F6A7B8C9D0E5D;
			productRefGroup = 1A1B2C3D4E5F6A7B8C9D0E4D /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				1A1B2C3D4E5F6A7B8C9D0E1F /* CryptoSystemMac */,
			);
		};
/* End PBXProject section */

/* Begin PBXSourcesBuildPhase section */
		1A1B2C3D4E5F6A7B8C9D0E2A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				1A1B2C3D4E5F6A7B8C9D0E1F /* CryptoManager.swift in Sources */,
				1A1B2C3D4E5F6A7B8C9D0E2F /* PolicyManager.swift in Sources */,
				1A1B2C3D4E5F6A7B8C9D0E3F /* FileProviderExtension.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		1A1B2C3D4E5F6A7B8C9D0E4F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.15;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		1A1B2C3D4E5F6A7B8C9D0E5F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.15;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		1A1B2C3D4E5F6A7B8C9D0E6F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = CryptoSystemMac/CryptoSystemMac.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				ENABLE_HARDENED_RUNTIME = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = CryptoSystemMac/Info.plist;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INFOPLIST_KEY_NSMainNibFile = MainMenu;
				INFOPLIST_KEY_NSPrincipalClass = NSApplication;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.cryptosystem.mac;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		1A1B2C3D4E5F6A7B8C9D0E7F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = CryptoSystemMac/CryptoSystemMac.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				ENABLE_HARDENED_RUNTIME = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = CryptoSystemMac/Info.plist;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INFOPLIST_KEY_NSMainNibFile = MainMenu;
				INFOPLIST_KEY_NSPrincipalClass = NSApplication;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.cryptosystem.mac;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		1A1B2C3D4E5F6A7B8C9D0E2F /* Build configuration list for PBXNativeTarget "CryptoSystemMac" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1A1B2C3D4E5F6A7B8C9D0E6F /* Debug */,
				1A1B2C3D4E5F6A7B8C9D0E7F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1A1B2C3D4E5F6A7B8C9D0E3F /* Build configuration list for PBXProject "CryptoSystemMac" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1A1B2C3D4E5F6A7B8C9D0E4F /* Debug */,
				1A1B2C3D4E5F6A7B8C9D0E5F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 1A1B2C3D4E5F6A7B8C9D0E6D /* Project object */;
} 