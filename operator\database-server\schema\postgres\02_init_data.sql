-- =====================================================
-- 企业文档加密系统 - 初始化数据脚本 (PostgreSQL版本)
-- 版本: 1.4.0
-- 创建时间: 2025-01-20
-- 描述: 插入系统运行所必需的基础数据
-- =====================================================

-- =====================================================
-- 1. 系统配置数据
-- =====================================================

INSERT INTO sys_config (config_id, config_name, config_value, config_type, description, created_by) VALUES
('SYS_NAME', '系统名称', '企业文档加密系统', 'STRING', '系统显示名称', 'SYSTEM'),
('SYS_VERSION', '系统版本', '1.4.0', 'STRING', '当前系统版本号', 'SYSTEM'),
('DEFAULT_KEY_LENGTH', '默认密钥长度', '256', 'NUMBER', '默认密钥长度(位)', 'SYSTEM'),
('KEY_EXPIRY_DAYS', '密钥默认有效期', '365', 'NUMBER', '密钥默认有效期(天)', 'SYSTEM'),
('MAX_LOGIN_ATTEMPTS', '最大登录尝试次数', '5', 'NUMBER', '用户最大登录失败次数', 'SYSTEM'),
('SESSION_TIMEOUT', '会话超时时间', '30', 'NUMBER', '会话超时时间(分钟)', 'SYSTEM'),
('PASSWORD_MIN_LENGTH', '密码最小长度', '8', 'NUMBER', '用户密码最小长度', 'SYSTEM'),
('PASSWORD_COMPLEXITY', '密码复杂度要求', 'true', 'BOOLEAN', '是否启用密码复杂度检查', 'SYSTEM'),
('AUDIT_LOG_RETENTION_DAYS', '审计日志保留天数', '2555', 'NUMBER', '审计日志保留天数(默认7年)', 'SYSTEM'),
('KEY_USAGE_LOG_RETENTION_DAYS', '密钥使用日志保留天数', '1095', 'NUMBER', '密钥使用日志保留天数(默认3年)', 'SYSTEM'),
('ENABLE_NATIONAL_CRYPTO', '启用国密算法', 'true', 'BOOLEAN', '是否启用国密算法支持', 'SYSTEM'),
('DEFAULT_CRYPTO_ALGORITHM', '默认加密算法', 'AES256', 'STRING', '系统默认加密算法', 'SYSTEM'),
('KEY_BACKUP_ENABLED', '密钥备份启用', 'true', 'BOOLEAN', '是否启用密钥自动备份', 'SYSTEM'),
('KEY_BACKUP_LOCATION', '密钥备份位置', '/backup/keys', 'STRING', '密钥备份存储位置', 'SYSTEM'),
('MAX_KEY_USAGE_COUNT', '最大密钥使用次数', '1000000', 'NUMBER', '单个密钥最大使用次数', 'SYSTEM'),
('KEY_ROTATION_WARN_DAYS', '密钥轮换预警天数', '30', 'NUMBER', '密钥到期前预警天数', 'SYSTEM'),
('DISTRIBUTION_MAX_RETRY', '分发最大重试次数', '3', 'NUMBER', '密钥分发失败时最大重试次数', 'SYSTEM'),
('DISTRIBUTION_TIMEOUT_HOURS', '分发超时时间', '24', 'NUMBER', '密钥分发超时时间(小时)', 'SYSTEM'),
('CLIENT_MAX_DEVICES', '客户最大设备数', '50', 'NUMBER', '单个客户默认最大设备数', 'SYSTEM'),
('CLIENT_MAX_USERS', '客户最大用户数', '100', 'NUMBER', '单个客户默认最大用户数', 'SYSTEM');

-- =====================================================
-- 2. 默认管理员客户数据（用于系统管理）
-- =====================================================

INSERT INTO clients (client_id, client_name, client_code, contact_person, contact_email, industry, client_level, max_users, max_devices, is_active, status, created_by, remarks) VALUES
('SYS_ADMIN', '系统管理员', 'SYSTEM', '系统管理员', '<EMAIL>', 'IT', 'VIP', 999, 999, TRUE, 'ACTIVE', 'SYSTEM', '系统内置管理员客户，用于系统管理和运维');

-- 创建系统管理员用户
INSERT INTO client_users (user_id, client_id, username, user_display_name, email, user_role, password_hash, salt, is_active, created_by) VALUES
('ADMIN_001', 'SYS_ADMIN', 'admin', '系统管理员', '<EMAIL>', 'ADMIN', 
 '9f86d081884c7d659a2feaa0c55ad015a3bf4f1b2b0b822cd15d6c15b0f00a08', -- 'test'的SHA256哈希值，生产环境需要更改
 'system_salt_2025', TRUE, 'SYSTEM');

-- =====================================================
-- 3. 示例客户数据（用于演示和测试）
-- =====================================================

INSERT INTO clients (client_id, client_name, client_code, contact_person, contact_phone, contact_email, address, industry, client_level, max_users, max_devices, contract_start_date, contract_end_date, is_active, status, created_by, remarks) VALUES
('CLIENT_001', '示例科技有限公司', 'DEMO_TECH', '张经理', '13800138000', '<EMAIL>', '北京市海淀区中关村软件园', '软件开发', 'STANDARD', 50, 30, '2025-01-01', '2025-12-31', TRUE, 'ACTIVE', 'ADMIN_001', '演示客户，用于系统功能展示'),
('CLIENT_002', '测试金融集团', 'TEST_FIN', '李总监', '13900139000', '<EMAIL>', '上海市浦东新区陆家嘴金融中心', '金融服务', 'VIP', 200, 100, '2025-01-01', '2026-12-31', TRUE, 'ACTIVE', 'ADMIN_001', '测试客户，金融行业高安全要求');

-- 为示例客户创建用户
INSERT INTO client_users (user_id, client_id, username, user_display_name, email, department, position, user_role, password_hash, salt, is_active, created_by) VALUES
('USER_001', 'CLIENT_001', 'demo_admin', '演示管理员', '<EMAIL>', 'IT部', '系统管理员', 'ADMIN', 
 '9f86d081884c7d659a2feaa0c55ad015a3bf4f1b2b0b822cd15d6c15b0f00a08', 'demo_salt_001', TRUE, 'ADMIN_001'),
('USER_002', 'CLIENT_001', 'demo_user', '演示用户', '<EMAIL>', '研发部', '软件工程师', 'USER', 
 '9f86d081884c7d659a2feaa0c55ad015a3bf4f1b2b0b822cd15d6c15b0f00a08', 'demo_salt_002', TRUE, 'ADMIN_001'),
('USER_003', 'CLIENT_002', 'test_admin', '测试管理员', '<EMAIL>', '信息科技部', '信息安全主管', 'ADMIN', 
 '9f86d081884c7d659a2feaa0c55ad015a3bf4f1b2b0b822cd15d6c15b0f00a08', 'test_salt_001', TRUE, 'ADMIN_001');

-- =====================================================
-- 4. 示例主密钥数据
-- =====================================================

-- 注意：这里的密钥数据是示例数据，实际生产环境中需要通过密钥生成器创建真实的密钥
INSERT INTO master_keys (
    key_id, key_name, client_id, algorithm_id, key_length, 
    key_data_encrypted, key_hash, effective_date, expiration_date, 
    key_status, created_by, description
) VALUES
('MK_DEMO_001', '演示客户主密钥-2025', 'CLIENT_001', 3, 256,
 decode('1234567890ABCDEF1234567890ABCDEF', 'hex'), -- 示例加密密钥数据，实际应该是加密的
 'a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3', -- 示例哈希值
 '2025-01-01 00:00:00', '2025-12-31 23:59:59',
 'ACTIVE', 'ADMIN_001', '演示客户2025年度主密钥'),
 
('MK_TEST_001', '测试金融主密钥-2025', 'CLIENT_002', 3, 256,
 decode('FEDCBA0987654321FEDCBA0987654321', 'hex'), -- 示例加密密钥数据
 'b3a8e0e1f9ab1bfe3a36f231f676f78bb30a519d2b21e6c530c0eee8ebb4a5d0', -- 示例哈希值
 '2025-01-01 00:00:00', '2026-12-31 23:59:59',
 'ACTIVE', 'ADMIN_001', '测试金融集团2025年度主密钥');

-- =====================================================
-- 5. 权限和安全策略配置
-- =====================================================

-- 插入安全策略相关配置
INSERT INTO sys_config (config_id, config_name, config_value, config_type, description, created_by) VALUES
('SECURITY_KEY_MIN_LENGTH', '最小密钥长度', '128', 'NUMBER', '系统允许的最小密钥长度', 'SYSTEM'),
('SECURITY_FORCE_KEY_ROTATION', '强制密钥轮换', 'true', 'BOOLEAN', '是否强制执行密钥轮换策略', 'SYSTEM'),
('SECURITY_AUDIT_ALL_OPERATIONS', '审计所有操作', 'true', 'BOOLEAN', '是否对所有操作进行审计记录', 'SYSTEM'),
('SECURITY_IP_WHITELIST_ENABLED', '启用IP白名单', 'false', 'BOOLEAN', '是否启用IP白名单限制', 'SYSTEM'),
('SECURITY_MULTI_FACTOR_AUTH', '多因子认证', 'false', 'BOOLEAN', '是否启用多因子身份认证', 'SYSTEM'),
('BACKUP_AUTO_BACKUP_ENABLED', '自动备份启用', 'true', 'BOOLEAN', '是否启用密钥自动备份', 'SYSTEM'),
('BACKUP_BACKUP_INTERVAL_HOURS', '备份间隔时间', '24', 'NUMBER', '自动备份间隔时间(小时)', 'SYSTEM'),
('BACKUP_MAX_BACKUP_COUNT', '最大备份数量', '30', 'NUMBER', '保留的最大备份文件数量', 'SYSTEM'),
('NOTIFICATION_EMAIL_ENABLED', '邮件通知启用', 'false', 'BOOLEAN', '是否启用邮件通知功能', 'SYSTEM'),
('NOTIFICATION_SMS_ENABLED', '短信通知启用', 'false', 'BOOLEAN', '是否启用短信通知功能', 'SYSTEM');

-- =====================================================
-- 6. 记录初始化日志
-- =====================================================

INSERT INTO audit_logs (
    log_id, client_id, event_type, event_category, action, result, 
    event_time, event_source, object_type, object_name, details
) VALUES 
(CONCAT('LOG_', EXTRACT(EPOCH FROM NOW())::bigint, '_INIT'), 'SYS_ADMIN', 'SYSTEM', 'SYSTEM_INIT', 'DATABASE_INITIALIZATION', 'SUCCESS',
 NOW(), 'DB_SERVER', 'DATABASE', '企业文档加密系统', '{"description":"数据库初始化完成，基础数据已插入","version":"1.4.0"}');

-- =====================================================
-- 7. 创建视图（便于查询和统计）
-- =====================================================

-- 客户密钥统计视图
CREATE VIEW v_client_key_stats AS
SELECT 
    c.client_id,
    c.client_name,
    COUNT(mk.key_id) as master_key_count,
    COUNT(wk.key_id) as work_key_count,
    COUNT(CASE WHEN mk.key_status = 'ACTIVE' THEN 1 END) as active_master_keys,
    COUNT(CASE WHEN wk.key_status = 'ACTIVE' THEN 1 END) as active_work_keys,
    MIN(mk.expiration_date) as nearest_expiry_date
FROM clients c
LEFT JOIN master_keys mk ON c.client_id = mk.client_id
LEFT JOIN work_keys wk ON c.client_id = wk.client_id
WHERE c.is_active = TRUE
GROUP BY c.client_id, c.client_name;

-- 密钥状态统计视图
CREATE VIEW v_key_status_summary AS
SELECT 
    'MASTER' as key_type,
    key_status,
    COUNT(*) as key_count,
    COUNT(CASE WHEN expiration_date < NOW() + INTERVAL '30 days' THEN 1 END) as expiring_soon
FROM master_keys
GROUP BY key_status
UNION ALL
SELECT 
    'WORK' as key_type,
    key_status,
    COUNT(*) as key_count,
    COUNT(CASE WHEN expiration_date < NOW() + INTERVAL '30 days' THEN 1 END) as expiring_soon
FROM work_keys
GROUP BY key_status;

-- 密钥分发状态统计视图
CREATE VIEW v_distribution_summary AS
SELECT 
    DATE(created_time) as distribution_date,
    distribution_status,
    COUNT(*) as count,
    COUNT(CASE WHEN distribution_method = 'ONLINE' THEN 1 END) as online_count,
    COUNT(CASE WHEN distribution_method = 'OFFLINE' THEN 1 END) as offline_count
FROM key_distributions
WHERE created_time >= NOW() - INTERVAL '30 days'
GROUP BY DATE(created_time), distribution_status
ORDER BY distribution_date DESC;

-- =====================================================
-- 初始化完成提示
-- =====================================================

SELECT '数据库初始化完成!' as message,
       '系统版本: 1.4.0' as version,
       NOW() as init_time,
       '默认管理员用户: admin/test (请及时修改密码)' as admin_info; 