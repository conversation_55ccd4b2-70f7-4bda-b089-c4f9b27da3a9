<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>WinExe</OutputType>
        <TargetFramework>net9.0-windows</TargetFramework>
        <UseWPF>true</UseWPF>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <AssemblyName>CryptoSystem.DeclassificationClient</AssemblyName>
        <RootNamespace>CryptoSystem.DeclassificationClient</RootNamespace>
        <ApplicationIcon>Resources/app_icon.ico</ApplicationIcon>
        <ApplicationManifest>app.manifest</ApplicationManifest>
        <AssemblyTitle>文档脱密客户端</AssemblyTitle>
        <AssemblyDescription>CryptoSystem 文档脱密客户端 - 用于安全外发加密文档</AssemblyDescription>
        <AssemblyCompany>CryptoSystem</AssemblyCompany>
        <AssemblyProduct>文档脱密客户端</AssemblyProduct>
        <AssemblyCopyright>Copyright © 2025</AssemblyCopyright>
        <AssemblyVersion>*******</AssemblyVersion>
        <FileVersion>*******</FileVersion>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />
        <PackageReference Include="MaterialDesignThemes" Version="5.1.0" />
        <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.0" />
        <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.2" />
    </ItemGroup>

    <ItemGroup>
        <None Update="appsettings.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
    </ItemGroup>

    <ItemGroup>
        <Resource Include="Resources/**/*" />
    </ItemGroup>

</Project> 