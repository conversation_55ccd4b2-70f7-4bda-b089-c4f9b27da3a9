<Window x:Class="KeyGenerator.Views.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" mc:Ignorable="d" Title="{Binding Title}" Height="800" Width="1200" WindowStartupLocation="CenterScreen" Icon="/Resources/app-icon.ico">

    <Window.Resources>
        <Style TargetType="TabItem">
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="2,0"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
        </Style>

        <Style TargetType="TextBlock" x:Key="StatusTextStyle">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="#666"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 菜单栏 -->
        <Menu Grid.Row="0" Background="#F5F5F5" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1">
            <MenuItem Header="文件(_F)">
                <MenuItem Header="设置(_S)" Command="{Binding NavigateToSettingsCommand}"/>
                <Separator/>
                <MenuItem Header="退出(_X)" Command="{Binding ExitApplicationCommand}" InputGestureText="Alt+F4"/>
            </MenuItem>
            <MenuItem Header="工具(_T)">
                <MenuItem Header="刷新数据库连接(_R)" Command="{Binding RefreshDatabaseConnectionCommand}"/>
            </MenuItem>
            <MenuItem Header="帮助(_H)">
                <MenuItem Header="帮助文档(_H)" Command="{Binding ShowHelpCommand}" InputGestureText="F1"/>
                <Separator/>
                <MenuItem Header="关于(_A)" Command="{Binding ShowAboutCommand}"/>
            </MenuItem>
        </Menu>

        <!-- 主内容区 -->
        <TabControl Grid.Row="1" SelectedIndex="{Binding SelectedTab}" Background="White" BorderThickness="0">
            <TabItem Header="密钥生成" Background="White">
                <ContentPresenter Content="{Binding KeyGenerationVM}" ContentTemplate="{StaticResource KeyGenerationViewTemplate}"/>
            </TabItem>

            <TabItem Header="密钥管理" Background="White">
                <ContentPresenter Content="{Binding KeyManagementVM}" ContentTemplate="{StaticResource KeyManagementViewTemplate}"/>
            </TabItem>
        </TabControl>

        <!-- 状态栏 -->
        <StatusBar Grid.Row="2" Background="#F8F9FA" BorderBrush="#E9ECEF" BorderThickness="0,1,0,0">
            <StatusBar.ItemsPanel>
                <ItemsPanelTemplate>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                    </Grid>
                </ItemsPanelTemplate>
            </StatusBar.ItemsPanel>

            <!-- 状态文本 -->
            <StatusBarItem Grid.Column="0">
                <TextBlock Text="{Binding StatusText}" Style="{StaticResource StatusTextStyle}"/>
            </StatusBarItem>

            <!-- 数据库连接状态 -->
            <StatusBarItem Grid.Column="1">
                <StackPanel Orientation="Horizontal" Margin="10,0">
                    <Ellipse Width="10" Height="10" Fill="{Binding DatabaseStatusColor}" VerticalAlignment="Center" Margin="0,0,5,0"/>
                    <TextBlock Text="{Binding DatabaseStatus}" Style="{StaticResource StatusTextStyle}" ToolTip="{Binding LastConnectionCheckText}"/>
                </StackPanel>
            </StatusBarItem>

            <!-- 分割线 -->
            <StatusBarItem Grid.Column="2">
                <Separator Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}" Margin="5,0"/>
            </StatusBarItem>

            <!-- 当前时间 -->
            <StatusBarItem Grid.Column="3">
                <TextBlock Text="{Binding CurrentTime}" Style="{StaticResource StatusTextStyle}" Margin="5,0,10,0"/>
            </StatusBarItem>
        </StatusBar>

        <!-- 加载遮罩 -->
        <Grid Grid.Row="0" Grid.RowSpan="3" Background="#80000000" Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar IsIndeterminate="True" Width="200" Height="4" Margin="0,0,0,10"/>
                <TextBlock Text="正在加载..." FontSize="14" Foreground="White" HorizontalAlignment="Center"/>
            </StackPanel>
        </Grid>

        <RadioButton Content="密钥生成" Command="{Binding NavigateToKeyGenerationCommand}" Style="{StaticResource NavRadioButtonStyle}" IsChecked="True"/>
        <RadioButton Content="密钥管理" Command="{Binding NavigateToKeyManagementCommand}" Style="{StaticResource NavRadioButtonStyle}"/>
        <RadioButton Content="审计日志" Command="{Binding NavigateToAuditLogCommand}" Style="{StaticResource NavRadioButtonStyle}"/>

        <ContentControl Grid.Row="1" Grid.Column="1" Margin="10" Content="{Binding CurrentView}"/>
    </Grid>
</Window>