/*
 * policy_manager.h
 *
 * Cryptosystem Linux 策略管理器头文件
 * 负责解析和应用加密策略规则
 */

#ifndef POLICY_MANAGER_H
#define POLICY_MANAGER_H

#include <stddef.h>
#include <stdint.h>
#include "crypto_manager.h"
#include "key_sync_service.h"

// 策略决策结果
typedef enum {
    POLICY_RESULT_ENCRYPT = 0,     // 加密
    POLICY_RESULT_DECRYPT = 1,     // 解密
    POLICY_RESULT_DENY = 2,        // 拒绝
    POLICY_RESULT_ALLOW = 3,       // 允许
    POLICY_RESULT_NO_MATCH = 4     // 无匹配策略
} policy_result;

// 策略决策详情
typedef struct {
    policy_result result;          // 决策结果
    crypto_algorithm algorithm;    // 加密算法
    crypto_mode mode;              // 加密模式
    uint32_t key_version;          // 密钥版本
    uint32_t rule_priority;        // 触发规则优先级
    char rule_target[256];         // 触发规则目标
} policy_decision;

/**
 * 初始化策略管理器
 * 
 * @return 0表示成功，非零表示失败
 */
int policy_init(void);

/**
 * 清理策略管理器
 */
void policy_cleanup(void);

/**
 * 重新加载策略
 * 从服务器同步最新策略
 * 
 * @return 0表示成功，非零表示失败
 */
int policy_reload(void);

/**
 * 为指定路径和用户/应用做策略决策
 * 
 * @param path      文件路径
 * @param app_id    应用ID (可为NULL)
 * @param user_id   用户ID (可为NULL)
 * @param decision  输出的决策结果
 * 
 * @return 0表示成功，非零表示失败
 */
int policy_evaluate(const char *path, const char *app_id, 
                   const char *user_id, policy_decision *decision);

/**
 * 获取策略数量
 * 
 * @return 当前加载的策略数量
 */
size_t policy_get_count(void);

#endif /* POLICY_MANAGER_H */ 