﻿#include "CoreEngine.h"
#include "../keymanager/KeyManager.h"
#include "../keymanager/PolicyManager.h"

#include <iostream>
#include <string>
#include <filesystem>
#include <algorithm>
#include <fstream>
#include <sstream>
#include <iomanip>
#include <ctime>
#include <windows.h>

namespace fs = std::filesystem;

// 临时日志宏，后期需要替换为正式日志系统
#define LOG_ENGINE_INFO(msg) std::wcout << L"[ENGINE] [INFO] " << msg << std::endl
#define LOG_ENGINE_ERROR(msg) std::wcerr << L"[ENGINE] [ERROR] " << msg << std::endl

// 临时配置常量，后期需要从配置文件读取
const int MAX_EVENT_QUEUE_SIZE = 1000;
const std::wstring TEMP_EXTENSION = L".tmp";
const std::wstring ENCRYPTED_EXTENSION = L".enc";
const std::vector<std::wstring> EXCLUDED_DIRECTORIES = {
    L"\\Windows\\", 
    L"\\Program Files\\", 
    L"\\Program Files (x86)\\",
    L"\\ProgramData\\"
};
const std::vector<std::wstring> EXCLUDED_EXTENSIONS = {
    L".exe", L".dll", L".sys", L".tmp", L".enc"
};

CoreEngine::CoreEngine() 
    : m_state(EngineState::INIT),
      m_isPaused(false) {
    LOG_ENGINE_INFO(L"CoreEngine 构造");
}

CoreEngine::~CoreEngine() {
    shutdown();
    LOG_ENGINE_INFO(L"CoreEngine 析构");
}

bool CoreEngine::initialize() {
    if (m_state == EngineState::RUNNING) {
        LOG_ENGINE_INFO(L"引擎已经初始化");
        return true;
    }
    
    try {
        // 初始化密钥管理器
        m_keyManager = std::make_shared<KeyManager>(KeyManager::getInstance());
        if (!m_keyManager->initialize()) {
            LOG_ENGINE_ERROR(L"密钥管理器初始化失败");
            m_state = EngineState::ERROR;
            return false;
        }
        
        // 初始化策略管理器
        m_policyManager = std::make_shared<PolicyManager>(PolicyManager::getInstance());
        if (!m_policyManager->initialize()) {
            LOG_ENGINE_ERROR(L"策略管理器初始化失败");
            m_state = EngineState::ERROR;
            return false;
        }
        
        // 连接两个组件
        m_keyManager->setPolicyManager(m_policyManager);
        m_policyManager->setAuditEventCallback([this](const AuditEvent& event) {
            m_keyManager->logAuditEvent(event);
        });
        
        m_state = EngineState::RUNNING;
        LOG_ENGINE_INFO(L"引擎初始化完成");
        return true;
    } catch (const std::exception& e) {
        LOG_ENGINE_ERROR(L"初始化异常: " + std::wstring(e.what(), e.what() + strlen(e.what())));
        m_state = EngineState::ERROR;
        return false;
    }
}

void CoreEngine::shutdown() {
    if (m_state == EngineState::INIT) {
        return;
    }
    
    // 关闭策略管理器
    if (m_policyManager) {
        m_policyManager->shutdown();
    }
    
    // 关闭密钥管理器
    if (m_keyManager) {
        m_keyManager->shutdown();
    }
    
    m_state = EngineState::INIT;
    LOG_ENGINE_INFO(L"引擎已关闭");
}

void CoreEngine::setPaused(bool paused) {
    m_isPaused = paused;
    m_state = paused ? EngineState::PAUSED : EngineState::RUNNING;
    LOG_ENGINE_INFO(paused ? L"引擎已暂停" : L"引擎已恢复");
}

EngineState CoreEngine::getState() const {
    return m_state;
}

void CoreEngine::onFileEvent(const FileEvent& event) {
    // 检查引擎状态
    if (m_state != EngineState::RUNNING || m_isPaused) {
        return;
    }
    
    // 确保组件初始化
    if (!m_keyManager || !m_policyManager) {
        LOG_ENGINE_ERROR(L"组件未初始化");
        return;
    }
    
    std::wstring filePath = event.filePath1;
    
    // 处理不同类型的事件
    switch (event.type) {
        case FileEventType::CREATED:
            LOG_ENGINE_INFO(L"文件创建: " + filePath);
            if (shouldProcessFile(filePath)) {
                applyFilePolicy(filePath);
            }
            break;
            
        case FileEventType::MODIFIED:
            LOG_ENGINE_INFO(L"文件修改: " + filePath);
            if (shouldProcessFile(filePath)) {
                applyFilePolicy(filePath);
            }
            break;
            
        case FileEventType::DELETED:
            LOG_ENGINE_INFO(L"文件删除: " + filePath);
            // 无需对删除的文件采取操作
            break;
            
        case FileEventType::RENAMED_OLD:
            LOG_ENGINE_INFO(L"文件重命名(旧名称): " + filePath);
            // 原始文件被重命名，不需要特别处理
            break;
            
        case FileEventType::RENAMED_NEW:
            LOG_ENGINE_INFO(L"文件重命名(新名称): " + filePath);
            if (shouldProcessFile(filePath)) {
                applyFilePolicy(filePath);
            }
            break;
            
        case FileEventType::OVERFLOW:
            LOG_ENGINE_ERROR(L"检测到缓冲区溢出事件: " + filePath);
            // 缓冲区溢出由FSDriver处理，这里只记录日志
            break;
            
        case FileEventType::SCANNED:
            LOG_ENGINE_INFO(L"目录扫描发现文件: " + filePath);
            if (shouldProcessFile(filePath)) {
                applyFilePolicy(filePath);
            }
            break;
            
        default:
            LOG_ENGINE_INFO(L"未知事件类型: " + filePath);
            break;
    }
}

bool CoreEngine::processFile(const std::wstring& filePath, bool forceEncrypt) {
    // 检查引擎状态
    if (m_state != EngineState::RUNNING || m_isPaused) {
        LOG_ENGINE_ERROR(L"引擎未运行");
        return false;
    }
    
    // 检查文件是否应该处理
    if (!forceEncrypt && !shouldProcessFile(filePath)) {
        LOG_ENGINE_INFO(L"文件不需要处理: " + filePath);
        return true; // 返回成功，因为不需要处理
    }
    
    // 应用策略
    return applyFilePolicy(filePath);
}

bool CoreEngine::getFileStatus(const std::wstring& filePath, bool& isEncrypted) {
    try {
        if (!fs::exists(filePath)) {
            return false;
        }
        
        isEncrypted = isFileEncrypted(filePath);
        return true;
    } catch (const std::exception& e) {
        LOG_ENGINE_ERROR(L"获取文件状态异常: " + std::wstring(e.what(), e.what() + strlen(e.what())));
        return false;
    }
}

bool CoreEngine::shouldProcessFile(const std::wstring& filePath) {
    try {
        // 检查文件是否存在
        if (!fs::exists(filePath)) {
            return false;
        }
        
        // 检查是否是目录
        if (fs::is_directory(filePath)) {
            return false;
        }
        
        // 检查文件大小（跳过空文件或过大文件）
        uintmax_t fileSize = fs::file_size(filePath);
        if (fileSize == 0 || fileSize > 100 * 1024 * 1024) { // 跳过空文件或大于100MB的文件
            return false;
        }
        
        // 检查文件是否已加密
        if (isFileEncrypted(filePath)) {
            return false;
        }
        
        // 检查路径是否在排除列表中
        for (const auto& excluded : EXCLUDED_DIRECTORIES) {
            if (filePath.find(excluded) != std::wstring::npos) {
                return false;
            }
        }
        
        // 检查扩展名是否在排除列表中
        std::wstring extension = fs::path(filePath).extension().wstring();
        std::transform(extension.begin(), extension.end(), extension.begin(), ::towlower);
        for (const auto& excluded : EXCLUDED_EXTENSIONS) {
            if (extension == excluded) {
                return false;
            }
        }
        
        // 检查文件是否可读写
        std::error_code ec;
        fs::file_status status = fs::status(filePath, ec);
        if (ec || (status.permissions() & fs::perms::owner_write) == fs::perms::none) {
            return false;
        }
        
        return true;
    } catch (const std::exception& e) {
        LOG_ENGINE_ERROR(L"检查文件是否应处理异常: " + std::wstring(e.what(), e.what() + strlen(e.what())));
        return false;
    }
}

bool CoreEngine::applyFilePolicy(const std::wstring& filePath) {
    if (!m_keyManager || !m_policyManager) {
        LOG_ENGINE_ERROR(L"组件未初始化");
        return false;
    }
    
    try {
        // 获取策略评估结果
        PolicyEvaluationResult result = m_policyManager->evaluateFilePolicy(filePath);
        
        // 根据策略结果执行操作
        if (result.shouldEncrypt) {
            // 执行加密
            bool success = m_keyManager->encryptFile(filePath);
            logFileAction(filePath, "ENCRYPT", success);
            return success;
        } else if (result.shouldDecrypt) {
            // 执行解密
            bool success = m_keyManager->decryptFile(filePath);
            logFileAction(filePath, "DECRYPT", success);
            return success;
        } else if (result.shouldDeny) {
            // 策略拒绝访问
            LOG_ENGINE_INFO(L"策略拒绝访问文件: " + filePath);
            logFileAction(filePath, "DENY", true);
            return true;
        } else if (result.shouldAudit) {
            // 仅审计
            logFileAction(filePath, "AUDIT", true);
            return true;
        } else if (result.shouldNotify) {
            // 通知用户
            LOG_ENGINE_INFO(L"策略通知: " + filePath);
            logFileAction(filePath, "NOTIFY", true);
            return true;
        }
        
        // 没有适用的策略操作
        return true;
    } catch (const std::exception& e) {
        LOG_ENGINE_ERROR(L"应用文件策略异常: " + std::wstring(e.what(), e.what() + strlen(e.what())));
        return false;
    }
}

void CoreEngine::logFileAction(const std::wstring& filePath, const std::string& action, bool success) {
    // 简单日志记录
    std::string path(filePath.begin(), filePath.end());
    std::string status = success ? "成功" : "失败";
    
    LOG_ENGINE_INFO(L"文件操作: " << filePath << L" - " << 
                   std::wstring(action.begin(), action.end()) << L" - " << 
                   std::wstring(status.begin(), status.end()));
    
    // 创建审计事件
    if (m_keyManager) {
        AuditEvent event;
        event.type = action == "ENCRYPT" ? AuditEventType::DOCUMENT_ENCRYPTED : 
                    (action == "DECRYPT" ? AuditEventType::DOCUMENT_DECRYPTED : 
                                          AuditEventType::POLICY_APPLIED);
        event.resourceId = path;
        event.resourceType = "file";
        event.timestamp = getCurrentTimestamp();
        event.result = success ? "success" : "failure";
        event.details = "{\"action\":\"" + action + "\"}";
        
        m_keyManager->logAuditEvent(event);
    }
}

std::string CoreEngine::getCurrentTimestamp() {
    auto now = std::chrono::system_clock::now();
    auto time = std::chrono::system_clock::to_time_t(now);
    
    std::stringstream ss;
    std::tm tm_buf;
    localtime_s(&tm_buf, &time);
    
    ss << std::put_time(&tm_buf, "%Y-%m-%d %H:%M:%S");
    return ss.str();
}

std::string CoreEngine::getFileExtension(const std::wstring& filePath) {
    std::wstring extension = fs::path(filePath).extension().wstring();
    std::transform(extension.begin(), extension.end(), extension.begin(), ::towlower);
    return std::string(extension.begin(), extension.end());
}

bool CoreEngine::isFileEncrypted(const std::wstring& filePath) {
    // 实际应用中，这里应该检查文件元数据或魔数来判断文件是否已经加密
    // 此处简单地根据文件扩展名判断
    std::wstring extension = fs::path(filePath).extension().wstring();
    std::transform(extension.begin(), extension.end(), extension.begin(), ::towlower);
    
    return (extension == ENCRYPTED_EXTENSION);
}

// 获取所有连接的外设
std::vector<PeripheralInfo> CoreEngine::getConnectedPeripherals() {
    std::vector<PeripheralInfo> peripherals;
    
    if (m_state != EngineState::RUNNING || m_isPaused) {
        LOG_ENGINE_ERROR(L"引擎未运行");
        return peripherals;
    }
    
    if (!m_policyManager) {
        LOG_ENGINE_ERROR(L"策略管理器未初始化");
        return peripherals;
    }
    
    try {
        // 直接调用策略管理器的方法获取外设列表
        peripherals = m_policyManager->getConnectedPeripherals();
        
        LOG_ENGINE_INFO(L"获取外设列表: " + std::to_wstring(peripherals.size()) + L" 个设备");
        
        return peripherals;
    } catch (const std::exception& e) {
        LOG_ENGINE_ERROR(L"获取外设列表异常: " + std::wstring(e.what(), e.what() + strlen(e.what())));
        return peripherals;
    }
}

// 判断外设是否允许访问
bool CoreEngine::isPeripheralAllowed(const std::string& deviceId, const std::string& userId) {
    if (m_state != EngineState::RUNNING || m_isPaused) {
        LOG_ENGINE_ERROR(L"引擎未运行");
        return false;
    }
    
    if (!m_policyManager) {
        LOG_ENGINE_ERROR(L"策略管理器未初始化");
        return false;
    }
    
    try {
        // 调用策略管理器的方法检查外设是否允许
        bool allowed = m_policyManager->isPeripheralAllowed(deviceId, userId);
        
        std::wstring resultText = allowed ? L"允许" : L"禁止";
        LOG_ENGINE_INFO(L"外设访问检查: " + std::wstring(deviceId.begin(), deviceId.end()) + L" - " + resultText);
        
        return allowed;
    } catch (const std::exception& e) {
        LOG_ENGINE_ERROR(L"检查外设权限异常: " + std::wstring(e.what(), e.what() + strlen(e.what())));
        return false;
    }
}

// 阻止外设访问
bool CoreEngine::blockPeripheral(const std::string& deviceId) {
    if (m_state != EngineState::RUNNING || m_isPaused) {
        LOG_ENGINE_ERROR(L"引擎未运行");
        return false;
    }
    
    if (!m_policyManager) {
        LOG_ENGINE_ERROR(L"策略管理器未初始化");
        return false;
    }
    
    try {
        // 调用策略管理器阻止外设
        bool success = m_policyManager->blockPeripheral(deviceId);
        
        // 记录操作结果
        logPeripheralAction(deviceId, "BLOCK", success);
        
        return success;
    } catch (const std::exception& e) {
        LOG_ENGINE_ERROR(L"阻止外设异常: " + std::wstring(e.what(), e.what() + strlen(e.what())));
        return false;
    }
}

// 允许外设访问
bool CoreEngine::allowPeripheral(const std::string& deviceId, bool readOnly, bool forceEncrypt) {
    if (m_state != EngineState::RUNNING || m_isPaused) {
        LOG_ENGINE_ERROR(L"引擎未运行");
        return false;
    }
    
    if (!m_policyManager) {
        LOG_ENGINE_ERROR(L"策略管理器未初始化");
        return false;
    }
    
    try {
        // 根据参数确定策略动作
        PolicyAction action = PolicyAction::ALLOW;
        if (readOnly) {
            action = PolicyAction::READ_ONLY;
        } else if (forceEncrypt) {
            action = PolicyAction::FORCE_ENCRYPT;
        }
        
        // 调用策略管理器允许外设
        bool success = m_policyManager->allowPeripheral(deviceId, action);
        
        // 记录操作结果
        std::string actionStr = "ALLOW";
        if (readOnly) actionStr = "READ_ONLY";
        if (forceEncrypt) actionStr = "FORCE_ENCRYPT";
        
        logPeripheralAction(deviceId, actionStr, success);
        
        return success;
    } catch (const std::exception& e) {
        LOG_ENGINE_ERROR(L"允许外设异常: " + std::wstring(e.what(), e.what() + strlen(e.what())));
        return false;
    }
}

// 处理外设事件
void CoreEngine::onPeripheralEvent(const std::string& deviceId, const std::string& eventType) {
    if (m_state != EngineState::RUNNING || m_isPaused) {
        return;
    }
    
    if (!m_policyManager) {
        LOG_ENGINE_ERROR(L"策略管理器未初始化");
        return;
    }
    
    try {
        LOG_ENGINE_INFO(L"外设事件: " + std::wstring(deviceId.begin(), deviceId.end()) + 
                      L" - " + std::wstring(eventType.begin(), eventType.end()));
        
        // 处理不同类型的外设事件
        if (eventType == "CONNECTED" || eventType == "INSERTED") {
            // 外设连接事件，评估并应用策略
            evaluateAndApplyPeripheralPolicy(deviceId);
        } else if (eventType == "DISCONNECTED" || eventType == "REMOVED") {
            // 外设断开事件，只需记录日志
            logPeripheralAction(deviceId, "DISCONNECTED", true);
        } else {
            // 其他事件类型
            LOG_ENGINE_INFO(L"未处理的外设事件类型: " + std::wstring(eventType.begin(), eventType.end()));
        }
    } catch (const std::exception& e) {
        LOG_ENGINE_ERROR(L"处理外设事件异常: " + std::wstring(e.what(), e.what() + strlen(e.what())));
    }
}

// 评估并应用外设策略
bool CoreEngine::evaluateAndApplyPeripheralPolicy(const std::string& deviceId, const std::string& userId) {
    if (!m_policyManager || !m_keyManager) {
        LOG_ENGINE_ERROR(L"组件未初始化");
        return false;
    }
    
    try {
        // 获取外设信息
        PeripheralInfo peripheral = m_policyManager->getPeripheralInfo(deviceId);
        if (peripheral.id.empty()) {
            LOG_ENGINE_ERROR(L"未找到外设: " + std::wstring(deviceId.begin(), deviceId.end()));
            return false;
        }
        
        // 获取外设策略评估结果
        PolicyEvaluationResult result = m_policyManager->evaluatePeripheralPolicy(deviceId, userId);
        
        // 根据策略结果执行操作
        if (result.shouldDeny) {
            // 策略拒绝访问
            LOG_ENGINE_INFO(L"策略拒绝外设访问: " + std::wstring(deviceId.begin(), deviceId.end()));
            logPeripheralAction(deviceId, "DENY", true);
            
            // TODO: 实现实际的外设阻断机制
            // 这里需要与驱动层集成，实现物理阻断
            
            return true;
        } else if (result.readOnly) {
            // 外设设置为只读
            LOG_ENGINE_INFO(L"外设设置为只读: " + std::wstring(deviceId.begin(), deviceId.end()));
            logPeripheralAction(deviceId, "READ_ONLY", true);
            
            // TODO: 实现只读权限控制
            // 这里需要与驱动层集成，设置只读权限
            
            return true;
        } else if (result.forceEncrypt) {
            // 强制加密模式
            LOG_ENGINE_INFO(L"外设设置为强制加密: " + std::wstring(deviceId.begin(), deviceId.end()));
            logPeripheralAction(deviceId, "FORCE_ENCRYPT", true);
            
            // TODO: 实现强制加密
            // 这里需要与加密引擎集成，对从该设备读写的文件强制加密
            
            return true;
        } else if (result.shouldAudit) {
            // 仅审计
            logPeripheralAction(deviceId, "AUDIT", true);
            return true;
        } else if (result.shouldNotify) {
            // 通知用户
            LOG_ENGINE_INFO(L"外设策略通知: " + std::wstring(deviceId.begin(), deviceId.end()));
            logPeripheralAction(deviceId, "NOTIFY", true);
            
            // TODO: 实现用户通知机制
            
            return true;
        }
        
        // 默认允许访问
        logPeripheralAction(deviceId, "ALLOW", true);
        return true;
    } catch (const std::exception& e) {
        LOG_ENGINE_ERROR(L"应用外设策略异常: " + std::wstring(e.what(), e.what() + strlen(e.what())));
        return false;
    }
}

// 记录外设操作
void CoreEngine::logPeripheralAction(const std::string& deviceId, const std::string& action, bool success) {
    // 获取设备信息以便日志更详细
    std::string deviceName = "Unknown Device";
    
    try {
        if (m_policyManager) {
            PeripheralInfo info = m_policyManager->getPeripheralInfo(deviceId);
            if (!info.name.empty()) {
                deviceName = info.name;
            }
        }
    } catch (...) {
        // 获取设备名称失败，使用默认值
    }
    
    // 写日志
    std::string status = success ? "成功" : "失败";
    LOG_ENGINE_INFO(L"外设操作: " << 
                   std::wstring(deviceId.begin(), deviceId.end()) << L" (" << 
                   std::wstring(deviceName.begin(), deviceName.end()) << L") - " << 
                   std::wstring(action.begin(), action.end()) << L" - " << 
                   std::wstring(status.begin(), status.end()));
    
    // 创建审计事件
    if (m_keyManager) {
        AuditEvent event;
        event.type = AuditEventType::PERIPHERAL_ACCESS;
        event.resourceId = deviceId;
        event.resourceType = "peripheral";
        event.timestamp = getCurrentTimestamp();
        event.result = success ? "success" : "failure";
        event.details = "{\"action\":\"" + action + "\",\"device_name\":\"" + deviceName + "\"}";
        
        m_keyManager->logAuditEvent(event);
    }
} 