using CryptoSystem.DeclassificationClient.Models;
using Microsoft.Extensions.Logging;

namespace CryptoSystem.DeclassificationClient.Services
{
    public class DeclassificationService : IDeclassificationService
    {
        private readonly ILogger<DeclassificationService> _logger;
        private readonly ICryptoService _cryptoService;
        private readonly IFileService _fileService;
        private readonly IAuditService _auditService;
        
        // 内存中的任务存储（实际项目中应使用数据库）
        private readonly Dictionary<string, DeclassificationTask> _tasks = new();
        private readonly Dictionary<string, SecurePackage> _packages = new();
        private readonly object _lockObject = new();

        public DeclassificationService(
            ILogger<DeclassificationService> logger,
            ICryptoService cryptoService,
            IFileService fileService,
            IAuditService auditService)
        {
            _logger = logger;
            _cryptoService = cryptoService;
            _fileService = fileService;
            _auditService = auditService;
            
            // 初始化示例数据
            InitializeSampleData();
        }

        public async Task<string> CreateTaskAsync(DeclassificationTask task)
        {
            try
            {
                _logger.LogInformation("创建脱密任务: {TaskName}", task.TaskName);
                
                // 验证任务数据
                if (string.IsNullOrEmpty(task.TaskName))
                {
                    throw new ArgumentException("任务名称不能为空");
                }

                // 生成任务ID
                task.TaskId = Guid.NewGuid().ToString();
                task.CreatedTime = DateTime.Now;
                task.Status = DeclassificationStatus.Pending;
                task.ProgressPercentage = 0;

                lock (_lockObject)
                {
                    _tasks[task.TaskId] = task;
                }

                // 记录审计日志
                await _auditService.LogAsync(new AuditLog
                {
                    OperationType = "CreateTask",
                    OperationTarget = task.TaskId,
                    OperationDescription = $"创建脱密任务: {task.TaskName}",
                    UserId = task.Applicant,
                    Timestamp = DateTime.Now,
                    IsSuccess = true
                });

                _logger.LogInformation("脱密任务创建成功: {TaskId}", task.TaskId);
                return task.TaskId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建脱密任务失败: {TaskName}", task.TaskName);
                throw;
            }
        }

        public async Task<(List<DeclassificationTask> Tasks, int TotalCount)> GetTasksAsync(
            DeclassificationStatus? status = null, 
            int pageIndex = 0, 
            int pageSize = 20)
        {
            try
            {
                _logger.LogInformation("获取任务列表，状态过滤: {Status}, 页码: {PageIndex}, 页大小: {PageSize}", 
                    status, pageIndex, pageSize);

                List<DeclassificationTask> allTasks;
                lock (_lockObject)
                {
                    allTasks = _tasks.Values.ToList();
                }

                // 状态过滤
                if (status.HasValue)
                {
                    allTasks = allTasks.Where(t => t.Status == status.Value).ToList();
                }

                // 排序（按创建时间倒序）
                allTasks = allTasks.OrderByDescending(t => t.CreatedTime).ToList();

                var totalCount = allTasks.Count;
                
                // 分页
                var pagedTasks = allTasks
                    .Skip(pageIndex * pageSize)
                    .Take(pageSize)
                    .ToList();

                _logger.LogInformation("获取任务列表成功，返回 {Count}/{Total} 个任务", pagedTasks.Count, totalCount);
                return (pagedTasks, totalCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务列表失败");
                throw;
            }
        }

        public async Task<DeclassificationTask?> GetTaskAsync(string taskId)
        {
            try
            {
                lock (_lockObject)
                {
                    return _tasks.TryGetValue(taskId, out var task) ? task : null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务详情失败: {TaskId}", taskId);
                return null;
            }
        }

        public async Task<bool> UpdateTaskStatusAsync(string taskId, DeclassificationStatus status, string? errorMessage = null)
        {
            try
            {
                lock (_lockObject)
                {
                    if (!_tasks.TryGetValue(taskId, out var task))
                    {
                        _logger.LogWarning("任务不存在: {TaskId}", taskId);
                        return false;
                    }

                    var oldStatus = task.Status;
                    task.Status = status;
                    task.ErrorMessage = errorMessage;
                    task.UpdatedTime = DateTime.Now;

                    // 状态变更时的特殊处理
                    switch (status)
                    {
                        case DeclassificationStatus.Processing:
                            task.StartTime = DateTime.Now;
                            break;
                        case DeclassificationStatus.Completed:
                            task.CompletedTime = DateTime.Now;
                            task.ProgressPercentage = 100;
                            break;
                        case DeclassificationStatus.Failed:
                            task.CompletedTime = DateTime.Now;
                            break;
                    }

                    _logger.LogInformation("任务状态更新: {TaskId}, {OldStatus} -> {NewStatus}", 
                        taskId, oldStatus, status);
                }

                // 记录审计日志
                await _auditService.LogAsync(new AuditLog
                {
                    OperationType = "UpdateTaskStatus",
                    OperationTarget = taskId,
                    OperationDescription = $"任务状态更新: {status}",
                    Timestamp = DateTime.Now,
                    IsSuccess = true
                });

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新任务状态失败: {TaskId}", taskId);
                return false;
            }
        }

        public async Task<bool> UpdateTaskProgressAsync(string taskId, int progressPercentage)
        {
            try
            {
                lock (_lockObject)
                {
                    if (!_tasks.TryGetValue(taskId, out var task))
                    {
                        return false;
                    }

                    task.ProgressPercentage = Math.Max(0, Math.Min(100, progressPercentage));
                    task.UpdatedTime = DateTime.Now;
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新任务进度失败: {TaskId}", taskId);
                return false;
            }
        }

        public async Task<bool> ProcessTaskAsync(string taskId, IProgress<int>? progress = null)
        {
            try
            {
                _logger.LogInformation("开始处理脱密任务: {TaskId}", taskId);

                var task = await GetTaskAsync(taskId);
                if (task == null)
                {
                    _logger.LogError("任务不存在: {TaskId}", taskId);
                    return false;
                }

                if (task.Status != DeclassificationStatus.Pending)
                {
                    _logger.LogWarning("任务状态不允许处理: {TaskId}, 当前状态: {Status}", taskId, task.Status);
                    return false;
                }

                // 更新任务状态为处理中
                await UpdateTaskStatusAsync(taskId, DeclassificationStatus.Processing);

                try
                {
                    var totalFiles = task.Files?.Count ?? 0;
                    var processedFiles = 0;

                    // 处理每个文件
                    if (task.Files != null)
                    {
                        foreach (var file in task.Files)
                        {
                            try
                            {
                                _logger.LogInformation("处理文件: {FileName}", file.FileName);

                                // 检查文件是否存在
                                if (!_fileService.FileExists(file.FilePath))
                                {
                                    _logger.LogError("文件不存在: {FilePath}", file.FilePath);
                                    continue;
                                }

                                // 如果文件已加密，则解密
                                if (file.IsEncrypted)
                                {
                                    var decryptedPath = _fileService.GetTempFilePath(file.Extension);
                                    var key = _cryptoService.GenerateKey(); // 实际应从密钥管理系统获取
                                    
                                    var decryptSuccess = await _cryptoService.DecryptFileAsync(
                                        file.FilePath, decryptedPath, key);

                                    if (decryptSuccess)
                                    {
                                        file.DecryptedPath = decryptedPath;
                                        _logger.LogInformation("文件解密成功: {FileName}", file.FileName);
                                    }
                                    else
                                    {
                                        _logger.LogError("文件解密失败: {FileName}", file.FileName);
                                        continue;
                                    }
                                }

                                // 验证文件完整性
                                var integrityValid = await _fileService.ValidateFileIntegrityAsync(
                                    file.DecryptedPath ?? file.FilePath, file.MD5Hash);

                                if (!integrityValid)
                                {
                                    _logger.LogWarning("文件完整性验证失败: {FileName}", file.FileName);
                                }

                                file.ProcessingStatus = "已处理";
                                processedFiles++;

                                // 更新进度
                                var progressPercentage = (int)((double)processedFiles / totalFiles * 100);
                                await UpdateTaskProgressAsync(taskId, progressPercentage);
                                progress?.Report(progressPercentage);

                                _logger.LogInformation("文件处理完成: {FileName}", file.FileName);
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex, "处理文件失败: {FileName}", file.FileName);
                                file.ProcessingStatus = $"处理失败: {ex.Message}";
                            }
                        }
                    }

                    // 所有文件处理完成，更新任务状态
                    await UpdateTaskStatusAsync(taskId, DeclassificationStatus.Completed);

                    // 记录审计日志
                    await _auditService.LogAsync(new AuditLog
                    {
                        OperationType = "ProcessTask",
                        OperationTarget = taskId,
                        OperationDescription = $"脱密任务处理完成，处理文件数: {processedFiles}",
                        Timestamp = DateTime.Now,
                        IsSuccess = true
                    });

                    _logger.LogInformation("脱密任务处理完成: {TaskId}, 处理文件数: {ProcessedFiles}/{TotalFiles}", 
                        taskId, processedFiles, totalFiles);

                    return true;
                }
                catch (Exception ex)
                {
                    await UpdateTaskStatusAsync(taskId, DeclassificationStatus.Failed, ex.Message);
                    throw;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理脱密任务失败: {TaskId}", taskId);
                return false;
            }
        }

        public async Task<bool> CancelTaskAsync(string taskId)
        {
            try
            {
                _logger.LogInformation("取消脱密任务: {TaskId}", taskId);

                var success = await UpdateTaskStatusAsync(taskId, DeclassificationStatus.Cancelled);
                if (success)
                {
                    // 清理临时文件
                    var task = await GetTaskAsync(taskId);
                    if (task?.Files != null)
                    {
                        foreach (var file in task.Files.Where(f => !string.IsNullOrEmpty(f.DecryptedPath)))
                        {
                            try
                            {
                                await _fileService.DeleteFileAsync(file.DecryptedPath!);
                            }
                            catch (Exception ex)
                            {
                                _logger.LogWarning(ex, "清理临时文件失败: {FilePath}", file.DecryptedPath);
                            }
                        }
                    }

                    _logger.LogInformation("脱密任务取消成功: {TaskId}", taskId);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "取消脱密任务失败: {TaskId}", taskId);
                return false;
            }
        }

        public async Task<bool> DeleteTaskAsync(string taskId)
        {
            try
            {
                _logger.LogInformation("删除脱密任务: {TaskId}", taskId);

                // 先取消任务（如果正在处理）
                var task = await GetTaskAsync(taskId);
                if (task?.Status == DeclassificationStatus.Processing)
                {
                    await CancelTaskAsync(taskId);
                }

                // 清理相关的安全包
                var relatedPackages = _packages.Values.Where(p => p.TaskId == taskId).ToList();
                foreach (var package in relatedPackages)
                {
                    lock (_lockObject)
                    {
                        _packages.Remove(package.PackageId);
                    }

                    // 删除包文件
                    if (_fileService.FileExists(package.FilePath))
                    {
                        await _fileService.DeleteFileAsync(package.FilePath);
                    }
                }

                // 删除任务
                lock (_lockObject)
                {
                    _tasks.Remove(taskId);
                }

                // 记录审计日志
                await _auditService.LogAsync(new AuditLog
                {
                    OperationType = "DeleteTask",
                    OperationTarget = taskId,
                    OperationDescription = "删除脱密任务",
                    Timestamp = DateTime.Now,
                    IsSuccess = true
                });

                _logger.LogInformation("脱密任务删除成功: {TaskId}", taskId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除脱密任务失败: {TaskId}", taskId);
                return false;
            }
        }

        public async Task<int> AddFilesToTaskAsync(string taskId, List<string> filePaths)
        {
            try
            {
                _logger.LogInformation("向任务添加文件: {TaskId}, 文件数: {FileCount}", taskId, filePaths.Count);

                var task = await GetTaskAsync(taskId);
                if (task == null)
                {
                    _logger.LogError("任务不存在: {TaskId}", taskId);
                    return 0;
                }

                if (task.Status != DeclassificationStatus.Pending)
                {
                    _logger.LogError("任务状态不允许添加文件: {TaskId}, 状态: {Status}", taskId, task.Status);
                    return 0;
                }

                task.Files ??= new List<DeclassificationFile>();
                var addedCount = 0;

                foreach (var filePath in filePaths)
                {
                    try
                    {
                        var file = await _fileService.AnalyzeFileAsync(filePath);
                        if (file != null)
                        {
                            task.Files.Add(file);
                            addedCount++;
                            _logger.LogInformation("文件添加成功: {FileName}", file.FileName);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "分析文件失败: {FilePath}", filePath);
                    }
                }

                task.UpdatedTime = DateTime.Now;
                _logger.LogInformation("向任务添加文件完成: {TaskId}, 成功添加 {AddedCount}/{TotalCount} 个文件", 
                    taskId, addedCount, filePaths.Count);

                return addedCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "向任务添加文件失败: {TaskId}", taskId);
                return 0;
            }
        }

        public async Task<bool> RemoveFileFromTaskAsync(string taskId, string fileId)
        {
            try
            {
                var task = await GetTaskAsync(taskId);
                if (task?.Files == null)
                {
                    return false;
                }

                var fileToRemove = task.Files.FirstOrDefault(f => f.FileId == fileId);
                if (fileToRemove != null)
                {
                    task.Files.Remove(fileToRemove);
                    task.UpdatedTime = DateTime.Now;

                    // 清理解密后的临时文件
                    if (!string.IsNullOrEmpty(fileToRemove.DecryptedPath))
                    {
                        await _fileService.DeleteFileAsync(fileToRemove.DecryptedPath);
                    }

                    _logger.LogInformation("从任务移除文件: {TaskId}, {FileName}", taskId, fileToRemove.FileName);
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "从任务移除文件失败: {TaskId}, {FileId}", taskId, fileId);
                return false;
            }
        }

        public async Task<SecurePackage?> GenerateSecurePackageAsync(string taskId, string? packagePassword = null)
        {
            try
            {
                _logger.LogInformation("生成安全外发包: {TaskId}", taskId);

                var task = await GetTaskAsync(taskId);
                if (task == null)
                {
                    _logger.LogError("任务不存在: {TaskId}", taskId);
                    return null;
                }

                if (task.Status != DeclassificationStatus.Completed)
                {
                    _logger.LogError("任务未完成，无法生成安全包: {TaskId}, 状态: {Status}", taskId, task.Status);
                    return null;
                }

                var package = new SecurePackage
                {
                    PackageId = Guid.NewGuid().ToString(),
                    TaskId = taskId,
                    TaskName = task.TaskName,
                    CreatedTime = DateTime.Now,
                    CreatedBy = task.Applicant,
                    Recipient = task.Recipient,
                    ExpiryTime = DateTime.Now.AddDays(30), // 默认30天有效期
                    IsPasswordProtected = !string.IsNullOrEmpty(packagePassword)
                };

                // 生成包文件路径
                var packageFileName = $"SecurePackage_{package.PackageId}_{DateTime.Now:yyyyMMdd_HHmmss}.zip";
                package.FilePath = Path.Combine(_fileService.GetTempDirectoryPath(), packageFileName);

                // 收集要打包的文件
                var filesToPackage = new List<string>();
                if (task.Files != null)
                {
                    foreach (var file in task.Files)
                    {
                        var sourceFile = file.DecryptedPath ?? file.FilePath;
                        if (_fileService.FileExists(sourceFile))
                        {
                            filesToPackage.Add(sourceFile);
                        }
                    }
                }

                // 生成密码（如果未提供）
                if (string.IsNullOrEmpty(packagePassword))
                {
                    packagePassword = _cryptoService.GenerateSecurePassword(16, true);
                }

                // 压缩并加密文件
                var success = await _cryptoService.CompressAndEncryptFilesAsync(
                    filesToPackage.ToArray(), 
                    package.FilePath, 
                    packagePassword);

                if (!success)
                {
                    _logger.LogError("生成安全包失败: {TaskId}", taskId);
                    return null;
                }

                // 计算包文件信息
                package.FileSize = _fileService.GetFileSize(package.FilePath);
                package.MD5Hash = await _fileService.CalculateFileMD5Async(package.FilePath);
                package.Password = packagePassword;

                // 保存包信息
                lock (_lockObject)
                {
                    _packages[package.PackageId] = package;
                }

                // 记录审计日志
                await _auditService.LogAsync(new AuditLog
                {
                    OperationType = "GenerateSecurePackage",
                    OperationTarget = taskId,
                    OperationDescription = $"生成安全外发包: {packageFileName}",
                    Timestamp = DateTime.Now,
                    IsSuccess = true
                });

                _logger.LogInformation("安全外发包生成成功: {PackageId}, 文件大小: {FileSize}", 
                    package.PackageId, _fileService.FormatFileSize(package.FileSize));

                return package;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成安全外发包失败: {TaskId}", taskId);
                return null;
            }
        }

        public async Task<List<SecurePackage>> GetSecurePackagesAsync(string? taskId = null)
        {
            try
            {
                List<SecurePackage> packages;
                lock (_lockObject)
                {
                    packages = _packages.Values.ToList();
                }

                if (!string.IsNullOrEmpty(taskId))
                {
                    packages = packages.Where(p => p.TaskId == taskId).ToList();
                }

                return packages.OrderByDescending(p => p.CreatedTime).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取安全包列表失败");
                return new List<SecurePackage>();
            }
        }

        public async Task<bool> DownloadSecurePackageAsync(string packageId, string targetPath)
        {
            try
            {
                lock (_lockObject)
                {
                    if (!_packages.TryGetValue(packageId, out var package))
                    {
                        _logger.LogError("安全包不存在: {PackageId}", packageId);
                        return false;
                    }

                    if (!_fileService.FileExists(package.FilePath))
                    {
                        _logger.LogError("安全包文件不存在: {FilePath}", package.FilePath);
                        return false;
                    }

                    // 复制文件到目标位置
                    return await _fileService.CopyFileAsync(package.FilePath, targetPath, true);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "下载安全包失败: {PackageId}", packageId);
                return false;
            }
        }

        public async Task<bool> ValidateSecurePackageAsync(string packagePath, string? password = null)
        {
            try
            {
                if (!_fileService.FileExists(packagePath))
                {
                    return false;
                }

                // 简单验证：尝试解压缩
                var tempDir = _fileService.GetTempDirectoryPath();
                var success = await _cryptoService.DecryptAndExtractFilesAsync(packagePath, tempDir, password ?? "");

                // 清理临时目录
                try
                {
                    Directory.Delete(tempDir, true);
                }
                catch
                {
                    // 忽略清理错误
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证安全包失败: {PackagePath}", packagePath);
                return false;
            }
        }

        public async Task<Dictionary<string, object>> GetTaskStatisticsAsync()
        {
            try
            {
                List<DeclassificationTask> allTasks;
                lock (_lockObject)
                {
                    allTasks = _tasks.Values.ToList();
                }

                var today = DateTime.Today;
                var todayTasks = allTasks.Where(t => t.CreatedTime.Date == today).ToList();

                var statistics = new Dictionary<string, object>
                {
                    ["TotalTaskCount"] = allTasks.Count,
                    ["TodayTaskCount"] = todayTasks.Count,
                    ["PendingTaskCount"] = allTasks.Count(t => t.Status == DeclassificationStatus.Pending),
                    ["ProcessingTaskCount"] = allTasks.Count(t => t.Status == DeclassificationStatus.Processing),
                    ["CompletedTaskCount"] = allTasks.Count(t => t.Status == DeclassificationStatus.Completed),
                    ["FailedTaskCount"] = allTasks.Count(t => t.Status == DeclassificationStatus.Failed),
                    ["CancelledTaskCount"] = allTasks.Count(t => t.Status == DeclassificationStatus.Cancelled),
                    ["TotalFileCount"] = allTasks.SelectMany(t => t.Files ?? new List<DeclassificationFile>()).Count(),
                    ["TotalPackageCount"] = _packages.Count,
                    ["SuccessRate"] = allTasks.Count > 0 ? 
                        Math.Round((double)allTasks.Count(t => t.Status == DeclassificationStatus.Completed) / allTasks.Count * 100, 2) : 0
                };

                return statistics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务统计信息失败");
                return new Dictionary<string, object>();
            }
        }

        public async Task<Dictionary<string, bool>> BatchProcessTasksAsync(List<string> taskIds, IProgress<int>? progress = null)
        {
            var results = new Dictionary<string, bool>();
            var totalTasks = taskIds.Count;
            var processedTasks = 0;

            _logger.LogInformation("开始批量处理任务: {TaskCount} 个", totalTasks);

            foreach (var taskId in taskIds)
            {
                try
                {
                    var success = await ProcessTaskAsync(taskId);
                    results[taskId] = success;
                    
                    if (success)
                    {
                        _logger.LogInformation("批量处理任务成功: {TaskId}", taskId);
                    }
                    else
                    {
                        _logger.LogError("批量处理任务失败: {TaskId}", taskId);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "批量处理任务异常: {TaskId}", taskId);
                    results[taskId] = false;
                }

                processedTasks++;
                var progressPercentage = (int)((double)processedTasks / totalTasks * 100);
                progress?.Report(progressPercentage);
            }

            var successCount = results.Values.Count(r => r);
            _logger.LogInformation("批量处理任务完成: 成功 {SuccessCount}/{TotalCount}", successCount, totalTasks);

            return results;
        }

        private void InitializeSampleData()
        {
            // 初始化一些示例任务数据
            var sampleTasks = new[]
            {
                new DeclassificationTask
                {
                    TaskId = Guid.NewGuid().ToString(),
                    TaskName = "技术文档外发申请",
                    Description = "向合作伙伴发送产品技术文档",
                    Applicant = "张三",
                    Recipient = "ABC公司",
                    Status = DeclassificationStatus.Pending,
                    CreatedTime = DateTime.Now.AddHours(-2),
                    ProgressPercentage = 0,
                    Files = new List<DeclassificationFile>
                    {
                        new DeclassificationFile
                        {
                            FileId = Guid.NewGuid().ToString(),
                            FileName = "产品规格书.pdf",
                            FilePath = @"C:\Temp\产品规格书.pdf",
                            FileSize = 2048000,
                            FileType = FileType.Document,
                            SecurityLevel = SecurityLevel.Internal,
                            IsEncrypted = true,
                            MD5Hash = "abc123def456",
                            CreatedTime = DateTime.Now.AddDays(-1)
                        }
                    }
                },
                new DeclassificationTask
                {
                    TaskId = Guid.NewGuid().ToString(),
                    TaskName = "营销资料脱密",
                    Description = "公开发布营销宣传资料",
                    Applicant = "李四",
                    Recipient = "公众",
                    Status = DeclassificationStatus.Processing,
                    CreatedTime = DateTime.Now.AddHours(-1),
                    StartTime = DateTime.Now.AddMinutes(-30),
                    ProgressPercentage = 65,
                    Files = new List<DeclassificationFile>
                    {
                        new DeclassificationFile
                        {
                            FileId = Guid.NewGuid().ToString(),
                            FileName = "宣传册.pptx",
                            FilePath = @"C:\Temp\宣传册.pptx",
                            FileSize = 5120000,
                            FileType = FileType.Presentation,
                            SecurityLevel = SecurityLevel.Public,
                            IsEncrypted = false,
                            MD5Hash = "def456ghi789",
                            CreatedTime = DateTime.Now.AddDays(-2)
                        }
                    }
                }
            };

            lock (_lockObject)
            {
                foreach (var task in sampleTasks)
                {
                    _tasks[task.TaskId] = task;
                }
            }

            _logger.LogInformation("初始化示例数据完成，任务数: {TaskCount}", sampleTasks.Length);
        }
    }
}