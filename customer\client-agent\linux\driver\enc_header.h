/*
 * enc_header.h
 *
 * Linux版本加密文件头定义
 * 与Windows版本保持兼容
 */

#ifndef ENC_HEADER_H
#define ENC_HEADER_H

#include <stdint.h>

// 加密算法类型
typedef enum {
    ALGORITHM_AES = 1,
    ALGORITHM_SM4 = 2
} enc_algorithm_type;

// 加密模式类型
typedef enum {
    MODE_CBC = 1,
    MODE_GCM = 2,
    MODE_CTR = 3
} enc_mode_type;

// 加密文件头结构
typedef struct {
    uint8_t signature[4];           // 文件签名 "ENCF"
    uint32_t version;               // 版本号
    uint32_t algorithm;             // 加密算法
    uint32_t mode;                  // 加密模式
    uint32_t key_version;           // 密钥版本
    uint8_t iv[16];                 // 初始化向量
    uint64_t original_file_size;    // 原始文件大小
    uint32_t checksum;              // 头部校验和
    uint8_t reserved[32];           // 保留字段
} __attribute__((packed)) enc_file_header;

// 文件头大小
#define ENC_FILE_HEADER_SIZE sizeof(enc_file_header)

// 最大支持的文件大小 (4GB)
#define MAX_FILE_SIZE (4ULL * 1024 * 1024 * 1024)

// 加密块大小
#define ENCRYPTION_BLOCK_SIZE 16

#endif // ENC_HEADER_H 