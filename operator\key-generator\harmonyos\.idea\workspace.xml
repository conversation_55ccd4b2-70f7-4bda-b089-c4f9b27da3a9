<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="2d1e6937-7021-46e5-a29a-9a1e01a5241f" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/../../../.developrules" beforeDir="false" afterPath="$PROJECT_DIR$/../../../.developrules" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../.vscode/settings.json" beforeDir="false" afterPath="$PROJECT_DIR$/../../../.vscode/settings.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../DEPLOYMENT_GUIDE.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../Jenkinsfile" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../ProjectTeam/Appendix_A_Adaptability_Quality_Standards.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../ProjectTeam/Appendix_B_Document_Dependencies_Lifecycle.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../ProjectTeam/Appendix_C_Execution_Task_Management.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../ProjectTeam/Appendix_D_Collaboration_Conflict_Management.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../ProjectTeam/Appendix_E_Startup_Workflow.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../ProjectTeam/Appendix_F_MUP_Protocol.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../ProjectTeam/Appendix_G_Logging_Archiving.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../ProjectTeam/Appendix_H_Rule_Change_Management.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../ProjectTeam/RoleIntegrationWorkflow.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../ProjectTeam/Roles/backend_developer_rules.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../ProjectTeam/Roles/business_analyst_rules.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../ProjectTeam/Roles/database_administrator_rules.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../ProjectTeam/Roles/devops_engineer_rules.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../ProjectTeam/Roles/frontend_developer_rules.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../ProjectTeam/Roles/fullstack_developer_rules.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../ProjectTeam/Roles/interaction_designer_rules.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../ProjectTeam/Roles/product_owner_rules.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../ProjectTeam/Roles/qa_engineer_rules.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../ProjectTeam/Roles/scrum_master_rules.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../ProjectTeam/Roles/security_specialist_rules.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../ProjectTeam/Roles/software_developer_rules.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../ProjectTeam/Roles/tech_lead_rules.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../ProjectTeam/Roles/technical_writer_rules.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../ProjectTeam/Roles/ui_designer_rules.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../ProjectTeam/Roles/ux_designer_rules.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../ProjectTeam/develop_docs/dynamic_system.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../ProjectTeam/develop_docs/error_handling.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../ProjectTeam/develop_docs/intervention_system.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../ProjectTeam/develop_docs/project_features.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../ProjectTeam/develop_docs/reference_library.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../ProjectTeam/develop_docs/rule_metadata_system.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../ProjectTeam/develop_docs/rule_optimization.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../ProjectTeam/develop_docs/rule_structure.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../ProjectTeam/develop_docs/summary_structure.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../ProjectTeam/project_templates/README.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../ProjectTeam/project_templates/coding_standards.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../ProjectTeam/project_templates/iteration_tracker.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../ProjectTeam/project_templates/project_structure.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../ProjectTeam/project_templates/version_control_standard.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../ProjectTeam/unified-methodology-framework.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../README.md" beforeDir="false" afterPath="$PROJECT_DIR$/../../../README.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/README.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/common/README.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/common/include/api/api_client.cpp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/common/include/api/api_client.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/common/include/api/crypto_utils.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/common/include/api/encoding_utils.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/common/include/api/key_lifecycle_client.cpp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/common/include/api/key_lifecycle_client.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/common/include/api/key_service_client.cpp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/common/include/api/key_service_client.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/common/include/api/secure_package_generator.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/common/include/api/secure_package_manager.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/common/include/key_management.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/common/include/platform/platform_utils.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/common/src/api/crypto_utils.cpp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/common/src/api/crypto_utils.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/common/src/api/crypto_utils_c_api.cpp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/common/src/api/crypto_utils_c_api.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/common/src/api/encoding_utils.c" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/common/src/platform/linux/platform_utils_linux.c" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/common/src/platform/macos/platform_utils_macos.c" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/common/src/platform/windows/platform_utils_win.c" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/linux/CMakeLists.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/linux/README.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/linux/build.sh" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/linux/client_cli.c" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/linux/driver/client_config.c" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/linux/driver/client_config.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/linux/driver/crypto_manager.c" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/linux/driver/crypto_manager.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/linux/driver/file_filter.c" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/linux/driver/file_filter.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/linux/driver/filter_integration.c" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/linux/driver/filter_operations.c" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/linux/driver/filter_operations.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/linux/driver/fuse_encfs.c" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/linux/driver/key_sync_service.c" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/linux/driver/key_sync_service.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/linux/driver/policy_manager.c" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/linux/driver/policy_manager.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/macos/README.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/macos/driver/crypto/CryptoManager.swift" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/macos/driver/file_filter/FileProviderExtension.swift" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/samples/key_lifecycle_demo.cpp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/windows/CMakeLists.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/windows/CMakeSettings.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/windows/README.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/windows/common/AuditTypes.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/windows/common/CoreTypes.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/windows/driver/CMakeLists.txt" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/windows/driver/context.c" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/windows/driver/crypto.c" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/windows/driver/crypto.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/windows/driver/enc_filter.c" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/windows/driver/enc_filter.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/windows/driver/policy.c" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/windows/driver/policy.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/windows/driver/utils.c" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/windows/driver/utils.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/windows/keymanager/KeyManager.cpp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/windows/keymanager/KeyManager.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/windows/keymanager/PolicyManager.cpp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/windows/keymanager/PolicyManager.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/windows/src/CoreEngine.cpp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/windows/src/CoreEngine.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/windows/src/DeviceControlManager.cpp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/windows/src/DeviceControlManager.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/windows/src/FSDriver.cpp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/windows/src/FSDriver.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/windows/src/Logger.cpp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/windows/src/Logger.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/windows/src/WatermarkManager.cpp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/windows/src/WatermarkManager.h" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../client/windows/src/main.cpp" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../docs/ADMIN_GUIDE.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../docs/API_DOCUMENTATION.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../docs/ARCHITECTURE.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../docs/DEPLOYMENT_GUIDE.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../docs/DEVELOPMENT_GUIDE.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../docs/QUICK_START.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../docs/USER_MANUAL.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../scripts/backup/database-backup.sh" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../scripts/backup/disaster-recovery.sh" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../scripts/backup/file-backup.sh" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../scripts/backup/restore.sh" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../scripts/deploy/deploy.sh" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../scripts/deploy/rollback.sh" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../scripts/deploy_server.sh" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/README.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/auth-service/pom.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/auth-service/src/main/java/com/cryptosystem/auth/AuthServiceApplication.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/auth-service/src/main/java/com/cryptosystem/auth/config/ApplicationProperties.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/auth-service/src/main/java/com/cryptosystem/auth/config/DataInitializer.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/auth-service/src/main/java/com/cryptosystem/auth/config/JwtConfig.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/auth-service/src/main/java/com/cryptosystem/auth/config/RabbitMQConfig.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/auth-service/src/main/java/com/cryptosystem/auth/config/WebSecurityConfig.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/auth-service/src/main/java/com/cryptosystem/auth/controller/AuthController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/auth-service/src/main/java/com/cryptosystem/auth/controller/InternalUserController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/auth-service/src/main/java/com/cryptosystem/auth/controller/MfaController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/auth-service/src/main/java/com/cryptosystem/auth/exception/AuthenticationException.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/auth-service/src/main/java/com/cryptosystem/auth/exception/GlobalExceptionHandler.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/auth-service/src/main/java/com/cryptosystem/auth/model/KeyStatus.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/auth-service/src/main/java/com/cryptosystem/auth/model/dto/AuthRequest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/auth-service/src/main/java/com/cryptosystem/auth/model/dto/AuthResponse.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/auth-service/src/main/java/com/cryptosystem/auth/model/dto/RefreshTokenRequest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/auth-service/src/main/java/com/cryptosystem/auth/model/entity/MfaDevice.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/auth-service/src/main/java/com/cryptosystem/auth/model/entity/Role.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/auth-service/src/main/java/com/cryptosystem/auth/model/entity/User.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/auth-service/src/main/java/com/cryptosystem/auth/persistence/entity/JwtSigningKeyEntity.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/auth-service/src/main/java/com/cryptosystem/auth/persistence/repository/JwtKeyRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/auth-service/src/main/java/com/cryptosystem/auth/repository/MfaDeviceRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/auth-service/src/main/java/com/cryptosystem/auth/repository/RoleRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/auth-service/src/main/java/com/cryptosystem/auth/repository/UserRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/auth-service/src/main/java/com/cryptosystem/auth/security/CustomUserDetailsService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/auth-service/src/main/java/com/cryptosystem/auth/service/AuthEncryptionService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/auth-service/src/main/java/com/cryptosystem/auth/service/AuthService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/auth-service/src/main/java/com/cryptosystem/auth/service/MfaService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/auth-service/src/main/java/com/cryptosystem/auth/service/UserEventListener.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/auth-service/src/main/java/com/cryptosystem/auth/service/UserSyncService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/auth-service/src/main/java/com/cryptosystem/auth/service/impl/AuthServiceImpl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/auth-service/src/main/java/com/cryptosystem/auth/service/impl/MfaServiceImpl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/auth-service/src/main/java/com/cryptosystem/auth/util/EnhancedJwtTokenUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/auth-service/src/main/java/com/cryptosystem/auth/util/JwtTokenUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/auth-service/src/main/resources/META-INF/spring-configuration-metadata.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/auth-service/src/main/resources/application-custom.yml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/auth-service/src/main/resources/application.yml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/auth-service/src/main/resources/bootstrap.yml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/auth-service/src/main/resources/db/migration/V2__Create_MFA_Device_Table.sql" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/pom.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/java/com/cryptosystem/common/api/ApiVersion.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/java/com/cryptosystem/common/audit/AuditEvent.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/java/com/cryptosystem/common/audit/AuditEventCollector.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/java/com/cryptosystem/common/audit/AuditEventType.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/java/com/cryptosystem/common/audit/AuditService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/java/com/cryptosystem/common/audit/BusinessAuditEvent.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/java/com/cryptosystem/common/audit/SecurityAuditEvent.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/java/com/cryptosystem/common/batch/DataImportExportService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/java/com/cryptosystem/common/batch/ExportOptions.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/java/com/cryptosystem/common/batch/FileFormat.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/java/com/cryptosystem/common/batch/ImportOptions.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/java/com/cryptosystem/common/batch/ImportResult.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/java/com/cryptosystem/common/batch/TaskStatus.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/java/com/cryptosystem/common/batch/ValidationResult.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/java/com/cryptosystem/common/cache/CacheConfig.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/java/com/cryptosystem/common/cache/CacheConsistencyService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/java/com/cryptosystem/common/cache/MultiLevelCacheManager.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/java/com/cryptosystem/common/event/EventPublisher.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/java/com/cryptosystem/common/event/UserEvent.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/java/com/cryptosystem/common/jwt/JwtAuthenticationFilter.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/java/com/cryptosystem/common/jwt/JwtAutoConfiguration.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/java/com/cryptosystem/common/jwt/JwtProperties.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/java/com/cryptosystem/common/jwt/JwtTokenProvider.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/java/com/cryptosystem/common/key/KeyManagementClient.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/java/com/cryptosystem/common/key/KeyManagementClientImpl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/java/com/cryptosystem/common/key/KeyManagementFallbackFactory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/java/com/cryptosystem/common/key/KeyManagementFeignClient.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/java/com/cryptosystem/common/monitoring/MonitoringConfig.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/java/com/cryptosystem/common/monitoring/PerformanceMetricsCollector.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/java/com/cryptosystem/common/monitoring/SystemMonitorService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/java/com/cryptosystem/common/security/InternalServiceAuth.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/java/com/cryptosystem/common/security/permission/DefaultPermissionService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/java/com/cryptosystem/common/security/permission/PermissionAspect.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/java/com/cryptosystem/common/security/permission/PermissionDeniedException.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/java/com/cryptosystem/common/security/permission/PermissionService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/java/com/cryptosystem/common/security/permission/Permissions.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/java/com/cryptosystem/common/security/permission/RequiresPermission.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/java/com/cryptosystem/common/user/UserDTO.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/java/com/cryptosystem/common/user/UserEntity.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/java/com/cryptosystem/common/user/UserService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/java/com/cryptosystem/common/util/CryptoUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/java/com/cryptosystem/common/util/HexUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/java/com/cryptosystem/common/util/validation/EnumValueValidator.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/java/com/cryptosystem/common/util/validation/IsValidEnumValue.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/java/com/cryptosystem/common/utils/JwtSecurityUtils.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/java/com/cryptosystem/common/utils/PageUtils.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/java/com/cryptosystem/common/utils/Query.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/resources/META-INF/spring/org.springframework.boot.autoconfigure.AutoConfiguration.imports" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/resources/application-jwt-example.yml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/resources/db/migration/V001__create_unified_users_table.sql" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/common-utils/src/main/resources/db/migration/V002__create_unified_audit_tables.sql" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/config-repo/auth-service.properties" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/config-repo/device-service.yml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/config-repo/document-service.yml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/config-repo/key-service.yml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/config-repo/log-service.yml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/config-repo/policy-service.yml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/config-repo/user-service.yml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/config-service/pom.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/config-service/src/main/java/com/example/cryptosystem/configservice/ConfigServiceApplication.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/config-service/src/main/resources/application-eureka.yml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/config-service/src/main/resources/application.yml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/configs/application.yml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/deploy.bat" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/device-service/README.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/device-service/pom.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/device-service/src/main/java/com/cryptosystem/device/DeviceServiceApplication.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/device-service/src/main/java/com/cryptosystem/device/config/OpenApiConfig.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/device-service/src/main/java/com/cryptosystem/device/config/SecurityConfig.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/device-service/src/main/java/com/cryptosystem/device/controller/ClientDeviceController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/device-service/src/main/java/com/cryptosystem/device/controller/DeviceController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/device-service/src/main/java/com/cryptosystem/device/dto/DeviceAlertDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/device-service/src/main/java/com/cryptosystem/device/dto/DeviceCreateDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/device-service/src/main/java/com/cryptosystem/device/dto/DeviceDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/device-service/src/main/java/com/cryptosystem/device/dto/DeviceGroupDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/device-service/src/main/java/com/cryptosystem/device/dto/DeviceSearchDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/device-service/src/main/java/com/cryptosystem/device/dto/DeviceUpdateDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/device-service/src/main/java/com/cryptosystem/device/exception/DeviceNotFoundException.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/device-service/src/main/java/com/cryptosystem/device/exception/GlobalExceptionHandler.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/device-service/src/main/java/com/cryptosystem/device/exception/InvalidOperationException.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/device-service/src/main/java/com/cryptosystem/device/mapper/DeviceMapper.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/device-service/src/main/java/com/cryptosystem/device/model/Device.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/device-service/src/main/java/com/cryptosystem/device/model/DeviceStatus.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/device-service/src/main/java/com/cryptosystem/device/model/entity/DeviceAlert.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/device-service/src/main/java/com/cryptosystem/device/model/reference/GroupReference.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/device-service/src/main/java/com/cryptosystem/device/model/reference/OnlineStatus.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/device-service/src/main/java/com/cryptosystem/device/model/reference/PolicyReference.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/device-service/src/main/java/com/cryptosystem/device/model/reference/UserReference.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/device-service/src/main/java/com/cryptosystem/device/repository/DeviceAlertRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/device-service/src/main/java/com/cryptosystem/device/repository/DeviceRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/device-service/src/main/java/com/cryptosystem/device/repository/GroupReferenceRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/device-service/src/main/java/com/cryptosystem/device/repository/PolicyReferenceRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/device-service/src/main/java/com/cryptosystem/device/repository/UserReferenceRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/device-service/src/main/java/com/cryptosystem/device/service/DeviceService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/device-service/src/main/java/com/cryptosystem/device/service/impl/DeviceServiceImpl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/device-service/src/main/java/com/cryptosystem/device/task/DeviceStatusMonitorTask.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/device-service/src/main/resources/application.yml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/device-service/src/main/resources/bootstrap.yml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/device-service/src/main/resources/logback-spring.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/pom.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/DocumentServiceApplication.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/batch/DocumentImportExportService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/batch/DocumentImportExportServiceImpl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/client/UserServiceClient.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/client/UserServiceClientFallbackFactory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/client/dto/UserDetailDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/config/DocumentStorageConfig.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/config/JpaAuditingConfig.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/config/MinioConfig.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/config/SecurityConfig.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/config/SwaggerConfig.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/controller/DocumentController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/controller/DocumentImportExportController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/controller/DocumentPreviewController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/controller/DocumentShareController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/controller/DocumentVersionController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/exception/BadRequestException.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/exception/DocumentAccessDeniedException.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/exception/DocumentNotFoundException.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/exception/DocumentVersionException.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/exception/GlobalExceptionHandler.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/exception/InvalidOperationException.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/exception/ResourceNotFoundException.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/exception/ServiceException.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/exception/UnauthorizedException.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/model/SecurePackage.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/model/SecurePackageAccessLog.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/model/SecurePackageFile.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/model/dto/ApiResponse.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/model/dto/DocumentDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/model/dto/DocumentPermissionDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/model/dto/DocumentPreviewDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/model/dto/DocumentSearchDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/model/dto/DocumentShareDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/model/dto/DocumentUploadRequest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/model/dto/DocumentVersionDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/model/dto/ErrorResponse.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/model/dto/ExtractedMetadata.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/model/dto/FolderDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/model/dto/PageResponse.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/model/dto/SecurePackageAccessLogDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/model/dto/SecurePackageCreateRequest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/model/dto/SecurePackageDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/model/dto/SecurePackageFileDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/model/dto/ShareLinkRequest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/model/dto/ShareUserRequest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/model/entity/CollaborationParticipant.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/model/entity/Comment.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/model/entity/Document.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/model/entity/DocumentCollaboration.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/model/entity/DocumentFavorite.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/model/entity/DocumentMetadata.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/model/entity/DocumentPermission.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/model/entity/DocumentShare.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/model/entity/DocumentTag.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/model/entity/DocumentVersion.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/model/entity/Folder.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/model/entity/VersionHistory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/model/enums/DocumentStatus.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/model/enums/PackageStatus.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/model/enums/SharePermission.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/repository/DocumentFavoriteRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/repository/DocumentMetadataRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/repository/DocumentPermissionRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/repository/DocumentRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/repository/DocumentShareRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/repository/DocumentTagRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/repository/DocumentVersionRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/repository/FolderRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/repository/SecurePackageAccessLogRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/repository/SecurePackageFileRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/repository/SecurePackageRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/repository/VersionHistoryRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/search/AdvancedSearchRequest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/search/ElasticsearchConfig.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/search/FullTextSearchService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/search/RecommendationService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/service/DocumentMetadataExtractionService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/service/DocumentPreviewService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/service/DocumentService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/service/DocumentShareService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/service/FolderService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/service/MinioStorageService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/service/SecurePackageService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/service/impl/DocumentMetadataExtractionServiceImpl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/service/impl/DocumentPreviewServiceImpl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/service/impl/DocumentServiceImpl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/service/impl/DocumentShareServiceImpl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/service/impl/DocumentVersionService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/service/impl/MinioStorageServiceImpl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/service/impl/VersionHistoryService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/util/BackupService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/java/com/cryptosystem/document/util/SecurityUtils.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/resources/application.yml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/resources/bootstrap.yml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/document-service/src/main/resources/db/migration/V004__create_document_version_tables.sql" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/gateway-service/pom.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/gateway-service/src/main/java/com/example/cryptosystem/gatewayservice/GatewayServiceApplication.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/gateway-service/src/main/java/com/example/cryptosystem/gatewayservice/config/CircuitBreakerConfig.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/gateway-service/src/main/java/com/example/cryptosystem/gatewayservice/config/LoadBalancerConfig.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/gateway-service/src/main/java/com/example/cryptosystem/gatewayservice/config/SecurityConfig.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/gateway-service/src/main/java/com/example/cryptosystem/gatewayservice/filters/AuthenticationFilter.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/gateway-service/src/main/resources/application.yml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/pom.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/KeyServiceApplication.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/config/OpenApiConfig.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/controller/KeyBackupController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/controller/KeyController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/controller/KeyImportExportController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/controller/KeyLifecycleController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/controller/KeyTypeController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/dto/CreateKeyTypeRequest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/dto/FileKeyDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/dto/KeyDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/dto/KeyExportRequestDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/dto/KeyExportResultDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/dto/KeyImportRequestDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/dto/KeyImportResultDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/dto/KeyRotationDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/dto/KeySearchRequestDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/dto/KeyTypeDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/dto/OrganizationKeyDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/dto/RootKeyDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/dto/UpdateKeyTypeRequest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/dto/UserDeviceKeyDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/entity/CryptoKey.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/entity/KeyBackupPolicy.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/entity/KeyBackupRecord.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/entity/KeyEntity.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/entity/KeyRotationHistory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/entity/KeyRotationPolicy.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/entity/KeyType.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/exception/KeyDecryptionException.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/exception/KeyEncryptionException.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/exception/KeyGenerationException.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/exception/KeyManagementException.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/exception/KeyNotFoundException.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/exception/KeyTypeNotFoundException.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/model/FileKey.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/model/Key.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/model/KeyRotationHistory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/model/OrganizationKey.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/model/RootKey.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/model/UserDeviceKey.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/repository/BaseKeyDomainRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/repository/CryptoKeyRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/repository/FileKeyRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/repository/KeyBackupPolicyRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/repository/KeyBackupRecordRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/repository/KeyRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/repository/KeyRotationHistoryRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/repository/KeyTypeRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/repository/OrganizationKeyRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/repository/RootKeyRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/repository/UserDeviceKeyRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/scheduler/KeyLifecycleScheduler.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/service/KeyBackupService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/service/KeyGenerationService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/service/KeyImportExportService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/service/KeyLifecycleService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/service/KeyQueryService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/service/KeyRotationService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/service/KeyService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/service/KeyTypeService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/service/KeyTypeServiceImpl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/service/VaultService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/service/impl/KeyBackupServiceImpl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/service/impl/KeyGenerationServiceImpl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/service/impl/KeyLifecycleServiceImpl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/service/impl/KeyQueryServiceImpl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/service/impl/KeyRotationServiceImpl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/service/impl/KeyServiceImpl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/service/impl/KeyTypeServiceImpl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/service/impl/VaultServiceImpl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/util/BackupService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/java/com/cryptosystem/key/util/KeyCryptoUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/resources/application.properties" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/resources/application.yml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/resources/bootstrap.yml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/resources/db/migration/V1.1__Create_key_types_table.sql" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/resources/db/migration/V2__add_key_rotation_support.sql" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/key-service/src/main/resources/db/migration/V4__Create_Key_Backup_Tables.sql" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/pom.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/common/utils/PageUtils.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/common/utils/Result.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/LogServiceApplication.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/async/AsyncTaskService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/async/MessageQueueService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/client/OrganizationServiceClient.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/client/dto/OrganizationInfo.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/client/fallback/OrganizationServiceClientFallbackFactory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/config/AsyncConfig.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/config/CorsConfig.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/config/DatabaseConfig.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/config/DatabaseConnectionPoolMonitor.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/config/ElasticsearchConfig.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/config/InfluxDBSecurityConfig.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/config/LogAggregationConfig.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/config/MonitoringConfig.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/config/MybatisPlusConfig.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/config/OpenApiConfig.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/config/RedisConfig.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/config/SecurityAuditConfig.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/config/SecurityConfig.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/config/WebMvcConfig.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/controller/AuditController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/controller/HealthController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/controller/LogAnalyticsController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/controller/LogController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/controller/MonitoringController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/controller/SecurityAuditController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/entity/LogEntity.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/exception/GlobalExceptionHandler.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/mapper/LogMapper.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/model/dto/AuditLogDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/model/dto/AuditLogRequest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/model/dto/BatchAuditLogRequest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/model/dto/BatchAuditLogResponse.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/model/dto/LogAnalyticsRequest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/model/dto/LogAnalyticsResponse.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/model/dto/NotificationRequest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/model/dto/NotificationResponse.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/model/dto/PageResponse.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/model/dto/Response.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/model/dto/SecurityAuditLogDTO.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/model/entity/AuditLog.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/model/entity/NotificationRule.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/model/entity/SecurityAuditLog.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/model/enums/AuditEventType.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/model/enums/AuditLogSeverity.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/model/enums/AuditLogStatus.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/repository/AuditLogRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/repository/NotificationRuleRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/repository/SecurityAuditRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/scheduler/SecurityAuditScheduler.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/security/JwtAuthenticationEntryPoint.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/security/JwtTokenProvider.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/service/AlertEventPublisher.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/service/AlertService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/service/AuditLogFailureHandlingService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/service/AuditService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/service/LogAggregationService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/service/LogAnalysisService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/service/LogContentEncryptionService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/service/LogService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/service/NotificationService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/service/SecurityAuditService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/service/TimeSeriesDbService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/service/impl/AuditLogFailureHandlingServiceImpl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/service/impl/AuditServiceImpl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/service/impl/InfluxDBServiceImpl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/service/impl/LogContentEncryptionServiceImpl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/service/impl/LogServiceImpl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/service/impl/SecurityAuditServiceImpl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/util/GeoIpUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/util/InfluxQLSecurityUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/util/QueryPerformanceAnalyzer.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/util/ValidEnum.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/util/ValidEnumString.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/util/ValidEnumStringValidator.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/java/com/cryptosystem/log/util/ValidEnumValidator.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/resources/application.yml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/resources/bootstrap.yml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/resources/db/migration/V005__create_security_audit_tables.sql" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/log-service/src/main/resources/db/migration/V006__optimize_indexes.sql" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/notification-service/pom.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/notification-service/src/main/java/com/cryptosystem/notification/NotificationServiceApplication.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/notification-service/src/main/java/com/cryptosystem/notification/config/AsyncConfig.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/notification-service/src/main/java/com/cryptosystem/notification/config/RabbitMQConfig.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/notification-service/src/main/java/com/cryptosystem/notification/model/entity/AlertNotification.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/notification-service/src/main/java/com/cryptosystem/notification/model/entity/Notification.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/notification-service/src/main/java/com/cryptosystem/notification/repository/AlertNotificationRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/notification-service/src/main/java/com/cryptosystem/notification/repository/NotificationRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/notification-service/src/main/java/com/cryptosystem/notification/service/AlertNotificationService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/notification-service/src/main/java/com/cryptosystem/notification/service/NotificationChannelService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/notification-service/src/main/resources/application.yml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/pom.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/PolicyServiceApplication.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/advice/ResponseSignatureAdvice.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/aop/IdempotencyInterceptor.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/aop/Idempotent.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/client/KeyServiceClient.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/client/KeyServiceClientFallback.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/config/ApplicationProperties.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/config/EncryptionPolicyConfig.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/config/FeignConfig.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/config/GlobalExceptionHandler.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/controller/AlgorithmConfigController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/controller/ApprovalFlowController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/controller/ApprovalInstanceController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/controller/EncryptionPolicyController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/controller/KeyInfoController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/controller/PolicyAssignmentController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/controller/PolicyAuditController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/controller/PolicyMatchController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/dto/AlgorithmConfigDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/dto/ApprovalActionDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/dto/ApprovalFlowDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/dto/ApprovalInstanceDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/dto/ApprovalStepDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/dto/EncryptionPolicyDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/dto/KeyInfoDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/dto/PolicyAssignmentDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/dto/PolicyAuditDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/dto/PolicyAuditSearchCriteria.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/dto/PolicyMatchResultDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/dto/PolicySearchCriteria.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/dto/PolicyStatisticsDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/dto/PolicyTargetDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/dto/StepApproverDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/entity/PolicyAudit.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/entity/PolicyKeyAssociation.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/enums/ActionType.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/enums/ApprovalFlowType.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/enums/ApprovalStatus.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/enums/ApproverType.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/enums/AuditActionType.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/enums/PolicyStatus.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/enums/StepType.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/enums/TargetType.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/exception/IdempotencyException.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/exception/PolicyException.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/model/Algorithm.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/model/AlgorithmConfig.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/model/ApprovalAction.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/model/ApprovalFlow.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/model/ApprovalInstance.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/model/ApprovalStep.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/model/BlockMode.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/model/EncryptionMode.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/model/EncryptionPolicy.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/model/EncryptionType.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/model/KeyAlgorithm.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/model/KeyFunction.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/model/KeySize.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/model/KeyStatus.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/model/KeyStrength.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/model/KeyType.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/model/KeyUsage.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/model/Mode.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/model/PaddingMode.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/model/PolicyAssignment.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/model/PolicyStatus.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/model/ProtectionLevel.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/model/StepApprover.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/model/TargetType.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/repository/AlgorithmConfigRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/repository/ApprovalFlowRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/repository/ApprovalInstanceRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/repository/EncryptionPolicyRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/repository/PolicyAssignmentRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/repository/PolicyAuditRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/repository/PolicyKeyAssociationRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/service/AlgorithmConfigService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/service/ApprovalFlowService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/service/ApprovalInstanceService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/service/EncryptionPolicyService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/service/KeyInfoService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/service/PolicyAssignmentService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/service/PolicyAuditService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/service/impl/AlgorithmConfigServiceImpl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/service/impl/EncryptionPolicyServiceImpl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/service/impl/KeyInfoServiceImpl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/service/impl/PolicyAssignmentServiceImpl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/java/com/cryptosystem/policy/service/impl/PolicyAuditServiceImpl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/resources/META-INF/spring-configuration-metadata.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/resources/application-custom.yml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/resources/application.yml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/resources/bootstrap.yml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/policy-service/src/main/resources/db/migration/V1__Add_idempotency_constraints.sql" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/pom.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/pom.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/UserServiceApplication.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/aop/AuditLogAspect.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/batch/UserImportExportServiceImpl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/client/AuthServiceClient.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/client/AuthServiceFallbackFactory.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/config/ApiProperties.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/config/ApiSecurityConfig.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/config/AppProperties.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/config/CryptosystemProperties.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/config/FeignProperties.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/config/JwtConfig.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/config/JwtProperties.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/config/RabbitMQConfig.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/config/ServicesProperties.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/config/SessionManagementConfig.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/config/WebConfig.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/config/WebSecurityConfig.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/controller/ApiSecurityController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/controller/AuditLogController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/controller/OrganizationInternalController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/controller/PermissionController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/controller/SessionManagementController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/controller/UserBehaviorAnalysisController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/controller/UserController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/exception/ApiSecurityException.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/exception/DuplicateResourceException.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/exception/GlobalExceptionHandler.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/exception/ResourceNotFoundException.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/exception/UserServiceException.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/model/dto/ApiResponse.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/model/dto/AuditLogDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/model/dto/GroupDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/model/dto/OrganizationDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/model/dto/PageDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/model/dto/PasswordChangeRequest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/model/dto/PermissionDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/model/dto/RoleDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/model/dto/RolePermissionDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/model/dto/UserCreateRequest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/model/dto/UserDto.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/model/dto/UserUpdateRequest.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/model/entity/AuditLog.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/model/entity/Group.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/model/entity/Organization.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/model/entity/Permission.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/model/entity/Role.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/model/entity/RolePermission.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/model/entity/User.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/model/entity/UserBehaviorLog.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/model/entity/UserSession.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/repository/AuditLogRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/repository/GroupRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/repository/OrganizationRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/repository/PermissionRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/repository/RolePermissionRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/repository/RoleRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/repository/UserBehaviorLogRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/repository/UserRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/repository/UserSessionRepository.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/security/AntiReplayService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/security/ApiSecurityInterceptor.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/security/ApiSignatureService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/security/HasPermission.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/security/JwtTokenProvider.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/security/PermissionAspect.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/security/Permissions.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/security/RateLimitService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/service/AuditService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/service/GroupService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/service/OrganizationService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/service/PermissionService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/service/RolePermissionService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/service/RoleService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/service/SessionCleanupService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/service/UserBehaviorAnalysisService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/service/UserEventService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/service/UserService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/service/UserSessionService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/service/impl/AuditServiceImpl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/service/impl/PermissionServiceImpl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/service/impl/UserBehaviorAnalysisServiceImpl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/service/impl/UserServiceImpl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/service/impl/UserSessionServiceImpl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/java/com/cryptosystem/user/validation/UserValidator.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/resources/META-INF/spring-configuration-metadata.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/resources/application.yml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/resources/bootstrap.yml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/resources/db/migration/V1.5__Create_Permission_Tables.sql" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/resources/db/migration/V1__init_schema.sql" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/resources/db/migration/V2__init_data.sql" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/resources/db/migration/V3__Create_User_Sessions_Table.sql" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/resources/db/migration/V4__Create_User_Behavior_Log_Table.sql" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../server/user-service/src/main/resources/db/migration/V4__create_audit_logs_table.sql" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../user_requirements.md" beforeDir="false" afterPath="$PROJECT_DIR$/../../../user_requirements.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/README.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/index.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/nginx.conf" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/package.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/public/manifest.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/public/sw.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/App.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/api/auth.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/api/device.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/api/deviceGroup.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/api/policy.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/api/policyAnalytics.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/api/policyConfig.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/api/reports.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/api/user.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/components/Breadcrumb.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/components/SidebarItem.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/components/mobile/MobileNavbar.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/config/apiEndpoints.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/examples/apiUsageExamples.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/locales/en-US.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/locales/zh-CN.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/main.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/router/index.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/services/apiService.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/services/cryptoService.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/services/secureLocalStorage.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/store/index.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/store/user.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/styles/index.scss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/styles/responsive.scss" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/types/api.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/types/device.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/types/global.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/types/schemas.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/types/user.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/utils/accessibility.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/utils/apiClient.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/utils/dateFormat.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/utils/lazyLoad.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/utils/performance.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/utils/request.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/views/dashboard/index.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/views/dashboard/reports/index.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/views/device/group/index.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/views/device/list/index.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/views/device/policy/index.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/views/error/404.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/views/layout/index.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/views/login/index.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/views/policy/analytics/index.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/views/policy/configuration/VersionHistory.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/views/policy/configuration/index.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/views/policy/device-assignment/index.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/views/policy/encryption/index.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/views/policy/index.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/views/user/group/index.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/src/views/user/list/index.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/tsconfig.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/tsconfig.node.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../../../web/vite.config.ts" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/../../.." />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 6
}</component>
  <component name="ProjectId" id="3009Zhm1MoC6aAZkc9aO1w5P2mw" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "last_opened_file_path": "E:/Projects/my-dev/cryptosystem/operator/key-generator/harmonyos",
    "run.code.analysis.last.selected.profile": "pProject Default",
    "settings.editor.selected.configurable": "preferences.pluginManager"
  }
}]]></component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用级" UseSingleDictionary="true" transferred="true" />
</project>