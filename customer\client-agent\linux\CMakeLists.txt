cmake_minimum_required(VERSION 3.10)
project(cryptosystem_linux VERSION 1.0.0 LANGUAGES C)

# 编译选项
set(CMAKE_C_STANDARD 11)
set(CMAKE_C_STANDARD_REQUIRED ON)
set(CMAKE_POSITION_INDEPENDENT_CODE ON)

# 默认Release编译
if(NOT CMAKE_BUILD_TYPE)
  set(CMAKE_BUILD_TYPE Release)
endif()

# 编译标志
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -Wall -Wextra")
set(CMAKE_C_FLAGS_DEBUG "${CMAKE_C_FLAGS_DEBUG} -g -O0")
set(CMAKE_C_FLAGS_RELEASE "${CMAKE_C_FLAGS_RELEASE} -O2")

# 寻找依赖库
find_package(PkgConfig REQUIRED)
pkg_check_modules(OPENSSL REQUIRED openssl)
pkg_check_modules(FUSE REQUIRED fuse)
pkg_check_modules(JSON-C REQUIRED json-c)
pkg_check_modules(CURL REQUIRED libcurl)
pkg_check_modules(UUID REQUIRED uuid)
find_package(Threads REQUIRED)

# 包含目录
include_directories(
  ${OPENSSL_INCLUDE_DIRS}
  ${FUSE_INCLUDE_DIRS}
  ${JSON-C_INCLUDE_DIRS}
  ${CURL_INCLUDE_DIRS}
  ${UUID_INCLUDE_DIRS}
)

# 链接目录
link_directories(
  ${OPENSSL_LIBRARY_DIRS}
  ${FUSE_LIBRARY_DIRS}
  ${JSON-C_LIBRARY_DIRS}
  ${CURL_LIBRARY_DIRS}
  ${UUID_LIBRARY_DIRS}
)

# 定义源文件
set(CRYPTO_SOURCES
  driver/crypto_manager.c
  driver/file_filter.c
  driver/filter_operations.c
  driver/filter_integration.c
  driver/key_sync_service.c
  driver/policy_manager.c
  driver/client_config.c
  driver/utils.c
)

# 构建共享库
add_library(cryptosystem SHARED ${CRYPTO_SOURCES})
target_link_libraries(cryptosystem
  ${OPENSSL_LIBRARIES}
  ${FUSE_LIBRARIES}
  ${JSON-C_LIBRARIES}
  ${CURL_LIBRARIES}
  ${UUID_LIBRARIES}
  ${CMAKE_THREAD_LIBS_INIT}
)

# 构建命令行客户端
add_executable(cryptosystem_client client_cli.c)
target_link_libraries(cryptosystem_client
  cryptosystem
  ${OPENSSL_LIBRARIES}
  ${JSON-C_LIBRARIES}
  ${CURL_LIBRARIES}
  ${UUID_LIBRARIES}
  ${CMAKE_THREAD_LIBS_INIT}
)

# 构建 FUSE 加密文件系统
add_executable(cryptosystem_fuse driver/fuse_encfs.c)
target_link_libraries(cryptosystem_fuse
  cryptosystem
  ${FUSE_LIBRARIES}
)

# 安装目标
install(TARGETS cryptosystem cryptosystem_client cryptosystem_fuse
  LIBRARY DESTINATION lib
  RUNTIME DESTINATION bin
)

# 输出配置信息
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "OpenSSL libraries: ${OPENSSL_LIBRARIES}")
message(STATUS "FUSE libraries: ${FUSE_LIBRARIES}")
message(STATUS "JSON-C libraries: ${JSON-C_LIBRARIES}")
message(STATUS "CURL libraries: ${CURL_LIBRARIES}")
message(STATUS "UUID libraries: ${UUID_LIBRARIES}") 