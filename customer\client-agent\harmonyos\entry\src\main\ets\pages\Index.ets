import hilog from '@ohos.hilog';

@Entry
@Component
struct Index {
  @State message: string = '客户端代理';

  build() {
    Row() {
      Column() {
        Text(this.message)
          .fontSize(50)
          .fontWeight(FontWeight.Bold)
          .margin({ bottom: 20 })

        Text('企业级文档加密系统客户端代理')
          .fontSize(16)
          .fontColor('#666666')
          .textAlign(TextAlign.Center)
          .margin({ bottom: 40 })

        Button('开始使用')
          .type(ButtonType.Capsule)
          .backgroundColor('#0066cc')
          .fontColor('#ffffff')
          .fontSize(16)
          .width(200)
          .height(44)
          .onClick(() => {
            hilog.info(0x0000, 'ClientAgent', 'Start button clicked');
          })
      }
      .width('100%')
      .justifyContent(FlexAlign.Center)
    }
    .height('100%')
    .backgroundColor('#f5f5f5')
  }
}
