#pragma warning disable CS8632 // 只能在 "#nullable" 注释上下文内的代码中使用可为 null 的引用类型的注释

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;

namespace KeyGenerator.Services
{
    /// <summary>
    /// 密钥层级管理服务
    /// 实现多层密钥体系的管理功能
    /// </summary>
    public class KeyHierarchyService
    {
        private readonly ILogger<KeyHierarchyService> _logger;
        private readonly IConfiguration _configuration;
        private readonly Dictionary<string, KeyHierarchyNode> _keyHierarchy;
        private readonly Dictionary<string, KeyInfo> _keyStore;

        public KeyHierarchyService(ILogger<KeyHierarchyService> logger, IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
            _keyHierarchy = new();
            _keyStore = new();
            
            InitializeHierarchy();
        }

        /// <summary>
        /// 密钥层级节点
        /// </summary>
        public class KeyHierarchyNode
        {
            public string KeyId { get; set; } = "";
            public string KeyName { get; set; } = "";
            public KeyHierarchyLevel Level { get; set; }
            public string ParentKeyId { get; set; } = "";
            public List<string> ChildKeyIds { get; set; } = new();
            public string OrganizationId { get; set; } = "";
            public string UserId { get; set; } = "";
            public string DeviceId { get; set; } = "";
            public DateTime CreatedAt { get; set; } = DateTime.Now;
            public DateTime ExpiresAt { get; set; }
            public KeyStatus Status { get; set; } = KeyStatus.Active;
            public Dictionary<string, object> Metadata { get; set; } = new();
        }

        /// <summary>
        /// 密钥信息
        /// </summary>
        public class KeyInfo
        {
            public string KeyId { get; set; } = "";
            public string KeyName { get; set; } = "";
            public KeyHierarchyLevel Level { get; set; }
            public string Algorithm { get; set; } = "SM4";
            public int KeySize { get; set; } = 256;
            public byte[] KeyMaterial { get; set; } = Array.Empty<byte>();
            public byte[] EncryptedKeyMaterial { get; set; } = Array.Empty<byte>();
            public string ParentKeyId { get; set; } = "";
            public DateTime CreatedAt { get; set; } = DateTime.Now;
            public DateTime ExpiresAt { get; set; }
            public KeyStatus Status { get; set; } = KeyStatus.Active;
            public string CreatedBy { get; set; } = "";
            public Dictionary<string, object> Properties { get; set; } = new();
        }

        /// <summary>
        /// 密钥状态
        /// </summary>
        public enum KeyStatus
        {
            Active,      // 活跃
            Rotating,    // 轮换中
            Expired,     // 已过期
            Revoked,     // 已撤销
            Suspended    // 已暂停
        }

        /// <summary>
        /// 密钥派生请求
        /// </summary>
        public class KeyDerivationRequest
        {
            public string ParentKeyId { get; set; } = "";
            public KeyHierarchyLevel TargetLevel { get; set; }
            public string KeyName { get; set; } = "";
            public string OrganizationId { get; set; } = "";
            public string UserId { get; set; } = "";
            public string DeviceId { get; set; } = "";
            public DateTime? ExpiresAt { get; set; }
            public string Algorithm { get; set; } = "SM4";
            public Dictionary<string, object> Properties { get; set; } = new();
        }

        /// <summary>
        /// 密钥轮换请求
        /// </summary>
        public class KeyRotationRequest
        {
            public string KeyId { get; set; } = "";
            public bool RotateChildren { get; set; } = true;
            public DateTime? NewExpirationTime { get; set; }
            public string Reason { get; set; } = "";
        }

        /// <summary>
        /// 初始化密钥层级
        /// </summary>
        private void InitializeHierarchy()
        {
            // 创建示例主密钥
            var masterKey = CreateMasterKey("企业主密钥", "ORG-001");
            
            // 创建组织密钥
            var orgKey1 = DeriveKey(new KeyDerivationRequest
            {
                ParentKeyId = masterKey.KeyId,
                TargetLevel = KeyHierarchyLevel.Organization,
                KeyName = "财务部门密钥",
                OrganizationId = "DEPT-001"
            }).Result;
            
            var orgKey2 = DeriveKey(new KeyDerivationRequest
            {
                ParentKeyId = masterKey.KeyId,
                TargetLevel = KeyHierarchyLevel.Organization,
                KeyName = "技术部门密钥",
                OrganizationId = "DEPT-002"
            }).Result;
            
            // 创建用户密钥
            var userKey1 = DeriveKey(new KeyDerivationRequest
            {
                ParentKeyId = orgKey1.KeyId,
                TargetLevel = KeyHierarchyLevel.User,
                KeyName = "张三个人密钥",
                UserId = "USER-001",
                OrganizationId = "DEPT-001"
            }).Result;
            
            // 创建李四个人密钥
            _ = DeriveKey(new KeyDerivationRequest
            {
                ParentKeyId = orgKey2.KeyId,
                TargetLevel = KeyHierarchyLevel.User,
                KeyName = "李四个人密钥",
                UserId = "USER-002",
                OrganizationId = "DEPT-002"
            }).Result;
            
            // 创建张三工作电脑密钥
            _ = DeriveKey(new KeyDerivationRequest
            {
                ParentKeyId = userKey1.KeyId,
                TargetLevel = KeyHierarchyLevel.Device,
                KeyName = "张三工作电脑",
                DeviceId = "DEV-001",
                UserId = "USER-001",
                OrganizationId = "DEPT-001"
            }).Result;
            
            _logger.LogInformation("密钥层级初始化完成");
        }

        /// <summary>
        /// 创建主密钥
        /// </summary>
        public KeyInfo CreateMasterKey(string keyName, string organizationId)
        {
            var keyId = GenerateKeyId("MK");
            var keyMaterial = GenerateKeyMaterial(256);
            
            var keyInfo = new KeyInfo
            {
                KeyId = keyId,
                KeyName = keyName,
                Level = KeyHierarchyLevel.Master,
                Algorithm = "SM4",
                KeySize = 256,
                KeyMaterial = keyMaterial,
                EncryptedKeyMaterial = EncryptKeyMaterial(keyMaterial, null),
                CreatedAt = DateTime.Now,
                ExpiresAt = DateTime.Now.AddYears(5),
                Status = KeyStatus.Active,
                CreatedBy = "system",
                Properties = new()
                {
                    ["organizationId"] = organizationId,
                    ["keyType"] = "master"
                }
            };
            
            var hierarchyNode = new KeyHierarchyNode
            {
                KeyId = keyId,
                KeyName = keyName,
                Level = KeyHierarchyLevel.Master,
                OrganizationId = organizationId,
                CreatedAt = DateTime.Now,
                ExpiresAt = DateTime.Now.AddYears(5),
                Status = KeyStatus.Active
            };
            
            _keyStore[keyId] = keyInfo;
            _keyHierarchy[keyId] = hierarchyNode;
            
            _logger.LogInformation("创建主密钥: {KeyId} - {KeyName}", keyId, keyName);
            
            return keyInfo;
        }

        /// <summary>
        /// 派生子密钥
        /// </summary>
        public Task<KeyInfo> DeriveKey(KeyDerivationRequest request)
        {
            try
            {
                // 验证父密钥
                if (!_keyStore.TryGetValue(request.ParentKeyId, out var parentKey))
                {
                    throw new ArgumentException($"父密钥不存在: {request.ParentKeyId}");
                }

                if (parentKey.Status != KeyStatus.Active)
                {
                    throw new InvalidOperationException($"父密钥状态不允许派生: {parentKey.Status}");
                }

                // 验证层级关系
                if (!IsValidHierarchyLevel(parentKey.Level, request.TargetLevel))
                {
                    throw new ArgumentException($"无效的密钥层级派生: {parentKey.Level} -> {request.TargetLevel}");
                }

                var keyId = GenerateKeyId(GetKeyPrefix(request.TargetLevel));
                var derivedKeyMaterial = DeriveKeyMaterial(parentKey.KeyMaterial, keyId, request.TargetLevel);
                
                var keyInfo = new KeyInfo
                {
                    KeyId = keyId,
                    KeyName = request.KeyName,
                    Level = request.TargetLevel,
                    Algorithm = request.Algorithm,
                    KeySize = parentKey.KeySize,
                    KeyMaterial = derivedKeyMaterial,
                    EncryptedKeyMaterial = EncryptKeyMaterial(derivedKeyMaterial, parentKey.KeyMaterial),
                    ParentKeyId = request.ParentKeyId,
                    CreatedAt = DateTime.Now,
                    ExpiresAt = request.ExpiresAt ?? DateTime.Now.AddYears(1),
                    Status = KeyStatus.Active,
                    CreatedBy = "system",
                    Properties = request.Properties
                };

                var hierarchyNode = new KeyHierarchyNode
                {
                    KeyId = keyId,
                    KeyName = request.KeyName,
                    Level = request.TargetLevel,
                    ParentKeyId = request.ParentKeyId,
                    OrganizationId = request.OrganizationId,
                    UserId = request.UserId,
                    DeviceId = request.DeviceId,
                    CreatedAt = DateTime.Now,
                    ExpiresAt = keyInfo.ExpiresAt,
                    Status = KeyStatus.Active
                };

                _keyStore[keyId] = keyInfo;
                _keyHierarchy[keyId] = hierarchyNode;

                // 更新父密钥的子密钥列表
                if (_keyHierarchy.TryGetValue(request.ParentKeyId, out var parentNode))
                {
                    parentNode.ChildKeyIds.Add(keyId);
                }

                _logger.LogInformation("派生密钥: {KeyId} - {KeyName} (父密钥: {ParentKeyId})", keyId, request.KeyName, request.ParentKeyId);

                return Task.FromResult(keyInfo);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "派生密钥失败: {Message}", ex.Message);
                return Task.FromException<KeyInfo>(ex);
            }
        }

        /// <summary>
        /// 轮换密钥
        /// </summary>
        public async Task<KeyInfo> RotateKey(KeyRotationRequest request)
        {
            try
            {
                if (!_keyStore.TryGetValue(request.KeyId, out var oldKey))
                {
                    throw new ArgumentException($"密钥不存在: {request.KeyId}");
                }

                if (!_keyHierarchy.TryGetValue(request.KeyId, out var oldNode))
                {
                    throw new ArgumentException($"密钥层级节点不存在: {request.KeyId}");
                }

                // 标记旧密钥为轮换中
                oldKey.Status = KeyStatus.Rotating;
                oldNode.Status = KeyStatus.Rotating;

                // 创建新密钥
                var newKeyId = GenerateKeyId(GetKeyPrefix(oldKey.Level));
                var newKeyMaterial = GenerateKeyMaterial(oldKey.KeySize);

                var newKey = new KeyInfo
                {
                    KeyId = newKeyId,
                    KeyName = oldKey.KeyName + " (轮换)",
                    Level = oldKey.Level,
                    Algorithm = oldKey.Algorithm,
                    KeySize = oldKey.KeySize,
                    KeyMaterial = newKeyMaterial,
                    EncryptedKeyMaterial = EncryptKeyMaterial(newKeyMaterial, GetParentKeyMaterial(oldKey.ParentKeyId)),
                    ParentKeyId = oldKey.ParentKeyId,
                    CreatedAt = DateTime.Now,
                    ExpiresAt = request.NewExpirationTime ?? DateTime.Now.AddYears(1),
                    Status = KeyStatus.Active,
                    CreatedBy = "system",
                    Properties = new(oldKey.Properties)
                    {
                        ["rotatedFrom"] = request.KeyId,
                        ["rotationReason"] = request.Reason
                    }
                };

                var newNode = new KeyHierarchyNode
                {
                    KeyId = newKeyId,
                    KeyName = newKey.KeyName,
                    Level = oldNode.Level,
                    ParentKeyId = oldNode.ParentKeyId,
                    ChildKeyIds = new(),
                    OrganizationId = oldNode.OrganizationId,
                    UserId = oldNode.UserId,
                    DeviceId = oldNode.DeviceId,
                    CreatedAt = DateTime.Now,
                    ExpiresAt = newKey.ExpiresAt,
                    Status = KeyStatus.Active
                };

                _keyStore[newKeyId] = newKey;
                _keyHierarchy[newKeyId] = newNode;

                // 更新父密钥的子密钥列表
                if (!string.IsNullOrEmpty(oldKey.ParentKeyId) && _keyHierarchy.TryGetValue(oldKey.ParentKeyId, out var parentNode))
                {
                    parentNode.ChildKeyIds.Remove(request.KeyId);
                    parentNode.ChildKeyIds.Add(newKeyId);
                }

                // 如果需要轮换子密钥
                if (request.RotateChildren)
                {
                    foreach (var childKeyId in oldNode.ChildKeyIds.ToList())
                    {
                        await RotateKey(new KeyRotationRequest
                        {
                            KeyId = childKeyId,
                            RotateChildren = true,
                            Reason = $"父密钥轮换: {request.KeyId}"
                        });
                    }
                }

                // 标记旧密钥为已撤销
                oldKey.Status = KeyStatus.Revoked;
                oldNode.Status = KeyStatus.Revoked;

                _logger.LogInformation("密钥轮换完成: {OldKeyId} -> {NewKeyId}", request.KeyId, newKeyId);

                return newKey;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "密钥轮换失败: {ErrorMessage}", ex.Message);
                throw;
            }
        }

        /// <summary>
        /// 撤销密钥
        /// </summary>
        public async Task<bool> RevokeKey(string keyId, string reason = "")
        {
            try
            {
                if (!_keyStore.TryGetValue(keyId, out var key))
                {
                    return false;
                }

                if (!_keyHierarchy.TryGetValue(keyId, out var node))
                {
                    return false;
                }

                // 撤销所有子密钥
                foreach (var childKeyId in node.ChildKeyIds.ToList())
                {
                    await RevokeKey(childKeyId, $"父密钥撤销: {keyId}");
                }

                // 撤销密钥
                key.Status = KeyStatus.Revoked;
                node.Status = KeyStatus.Revoked;
                key.Properties["revokedAt"] = DateTime.Now;
                key.Properties["revokeReason"] = reason;

                // 清除密钥材料
                Array.Clear(key.KeyMaterial, 0, key.KeyMaterial.Length);

                _logger.LogInformation("密钥已撤销: {KeyId} - {Reason}", keyId, reason);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "撤销密钥失败: {KeyId} - {ErrorMessage}", keyId, ex.Message);
                return false;
            }
        }

        /// <summary>
        /// 获取密钥信息
        /// </summary>
        public KeyInfo? GetKeyInfo(string keyId)
        {
            return _keyStore.TryGetValue(keyId, out var key) ? key : null;
        }

        /// <summary>
        /// 获取密钥层级节点
        /// </summary>
        public KeyHierarchyNode? GetHierarchyNode(string keyId)
        {
            return _keyHierarchy.TryGetValue(keyId, out var node) ? node : null;
        }

        /// <summary>
        /// 获取子密钥列表
        /// </summary>
        public List<KeyInfo> GetChildKeys(string parentKeyId)
        {
            if (!_keyHierarchy.TryGetValue(parentKeyId, out var parentNode))
            {
                return new List<KeyInfo>();
            }

            return parentNode.ChildKeyIds
                .Where(id => _keyStore.ContainsKey(id))
                .Select(id => _keyStore[id])
                .ToList();
        }

        /// <summary>
        /// 获取密钥路径
        /// </summary>
        public List<string> GetKeyPath(string keyId)
        {
            var path = new List<string>();
            var currentKeyId = keyId;

            while (!string.IsNullOrEmpty(currentKeyId) && _keyHierarchy.TryGetValue(currentKeyId, out var node))
            {
                path.Insert(0, currentKeyId);
                currentKeyId = node.ParentKeyId;
            }

            return path;
        }

        /// <summary>
        /// 获取所有密钥
        /// </summary>
        public List<KeyInfo> GetAllKeys()
        {
            return _keyStore.Values.ToList();
        }

        /// <summary>
        /// 按层级获取密钥
        /// </summary>
        public List<KeyInfo> GetKeysByLevel(KeyHierarchyLevel level)
        {
            return _keyStore.Values.Where(k => k.Level == level).ToList();
        }

        /// <summary>
        /// 按状态获取密钥
        /// </summary>
        public List<KeyInfo> GetKeysByStatus(KeyStatus status)
        {
            return _keyStore.Values.Where(k => k.Status == status).ToList();
        }

        /// <summary>
        /// 检查密钥是否即将过期
        /// </summary>
        public List<KeyInfo> GetExpiringKeys(TimeSpan threshold)
        {
            var expirationTime = DateTime.Now.Add(threshold);
            return _keyStore.Values
                .Where(k => k.Status == KeyStatus.Active && k.ExpiresAt <= expirationTime)
                .ToList();
        }

        // 私有辅助方法

        /// <summary>
        /// 检查层级关系是否有效
        /// </summary>
        private static bool IsValidHierarchyLevel(KeyHierarchyLevel parentLevel, KeyHierarchyLevel childLevel)
        {
            return parentLevel < childLevel && (int)childLevel - (int)parentLevel == 1;
        }
        
        /// <summary>
        /// 获取密钥前缀
        /// </summary>
        private static string GetKeyPrefix(KeyHierarchyLevel level)
        {
            return level switch
            {
                KeyHierarchyLevel.Master => "MK",
                KeyHierarchyLevel.Organization => "OK",
                KeyHierarchyLevel.User => "UK",
                KeyHierarchyLevel.Device => "DK",
                KeyHierarchyLevel.File => "FK",
                _ => "KEY"
            };
        }
        
        /// <summary>
        /// 生成密钥ID
        /// </summary>
        private static string GenerateKeyId(string prefix)
        {
            return $"{prefix}-{Guid.NewGuid():N}";
        }
        
        /// <summary>
        /// 生成密钥材料
        /// </summary>
        private static byte[] GenerateKeyMaterial(int sizeInBits)
        {
            var bytes = new byte[sizeInBits / 8];
            using var rng = RandomNumberGenerator.Create();
            rng.GetBytes(bytes);
            return bytes;
        }
        
        /// <summary>
        /// 从父密钥派生子密钥材料
        /// </summary>
        private static byte[] DeriveKeyMaterial(byte[] parentKey, string keyId, KeyHierarchyLevel level)
        {
            using var hmac = new HMACSHA256(parentKey);
            byte[] levelBytes = BitConverter.GetBytes((int)level);
            byte[] keyIdBytes = Encoding.UTF8.GetBytes(keyId);
            byte[] combined = new byte[levelBytes.Length + keyIdBytes.Length];
            
            Buffer.BlockCopy(levelBytes, 0, combined, 0, levelBytes.Length);
            Buffer.BlockCopy(keyIdBytes, 0, combined, levelBytes.Length, keyIdBytes.Length);
            
            return hmac.ComputeHash(combined);
        }
        
        /// <summary>
        /// 加密密钥材料
        /// </summary>
        private static byte[] EncryptKeyMaterial(byte[] keyMaterial, byte[]? parentKey)
        {
            if (parentKey == null || parentKey.Length == 0)
            {
                // 如果没有父密钥，则使用模拟加密（实际应用中应使用HSM或KMS）
                byte[] result = new byte[keyMaterial.Length];
                Buffer.BlockCopy(keyMaterial, 0, result, 0, keyMaterial.Length);
                
                // 简单异或操作模拟加密
                byte[] encryptionKey = Encoding.UTF8.GetBytes("EncryptionKeyForSimulation");
                for (int i = 0; i < result.Length; i++)
                {
                    result[i] = (byte)(result[i] ^ encryptionKey[i % encryptionKey.Length]);
                }
                
                return result;
            }
            else
            {
                // 使用父密钥加密
                using var aes = Aes.Create();
                aes.Key = parentKey.Length >= 32 ? parentKey[..32] : parentKey;
                aes.GenerateIV();
                
                using var encryptor = aes.CreateEncryptor();
                byte[] encrypted = encryptor.TransformFinalBlock(keyMaterial, 0, keyMaterial.Length);
                
                // 将IV与加密后的数据组合
                byte[] result = new byte[aes.IV.Length + encrypted.Length];
                Buffer.BlockCopy(aes.IV, 0, result, 0, aes.IV.Length);
                Buffer.BlockCopy(encrypted, 0, result, aes.IV.Length, encrypted.Length);
                
                return result;
            }
        }

        private byte[]? GetParentKeyMaterial(string parentKeyId)
        {
            if (string.IsNullOrEmpty(parentKeyId))
                return null;
                
            return _keyStore.TryGetValue(parentKeyId, out var parentKey) ? parentKey.KeyMaterial : null;
        }
    }
} 