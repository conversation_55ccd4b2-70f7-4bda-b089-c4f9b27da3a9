/*
 * perf_mdl_cache.c
 * 
 * Windows MiniFilter驱动MDL缓存管理实现
 * 解决频繁MDL分配和释放的性能瓶颈
 */

#include <fltKernel.h>
#include <ntstrsafe.h>
#include "perf_optimization.h"

// 外部全局变量
extern PERF_MANAGER g_PerfManager;

// MDL大小阈值常量
#define PERF_MDL_MIN_SIZE       4096        // 4KB
#define PERF_MDL_MAX_SIZE       262144      // 256KB
#define PERF_MDL_ALIGNMENT      4096        // 4KB对齐

// 前向声明
static PPERF_MDL_CACHE_ENTRY
PerfAllocateMdlCacheEntry(
    VOID
    );

static VOID
PerfFreeMdlCacheEntry(
    _In_ PPERF_MDL_CACHE_ENTRY Entry
    );

static BOOLEAN
PerfIsMdlExpired(
    _In_ PPERF_MDL_CACHE_ENTRY Entry
    );

static VOID
PerfEvictExpiredMdls(
    VOID
    );

static ULONG
PerfRoundUpMdlSize(
    _In_ ULONG Size
    );

//
// 初始化MDL缓存
//
NTSTATUS
PerfInitializeMdlCache(
    VOID
    )
{
    PPERF_MDL_CACHE cache = &g_PerfManager.MdlCache;

    PERF_DEBUG_PRINT("Initializing MDL cache...");

    // 初始化缓存结构
    InitializeListHead(&cache->FreeList);
    InitializeListHead(&cache->UsedList);
    KeInitializeSpinLock(&cache->SpinLock);
    
    cache->TotalCount = 0;
    cache->FreeCount = 0;
    cache->UsedCount = 0;
    cache->AllocRequests = 0;
    cache->AllocHits = 0;
    cache->AllocMisses = 0;
    cache->FreeRequests = 0;

    PERF_DEBUG_PRINT("MDL cache initialized");
    return STATUS_SUCCESS;
}

//
// 清理MDL缓存
//
VOID
PerfCleanupMdlCache(
    VOID
    )
{
    PPERF_MDL_CACHE cache = &g_PerfManager.MdlCache;
    PLIST_ENTRY listEntry;
    PPERF_MDL_CACHE_ENTRY entry;
    KIRQL oldIrql;

    PERF_DEBUG_PRINT("Cleaning up MDL cache...");

    KeAcquireSpinLock(&cache->SpinLock, &oldIrql);

    // 清理空闲列表
    while (!IsListEmpty(&cache->FreeList)) {
        listEntry = RemoveHeadList(&cache->FreeList);
        entry = CONTAINING_RECORD(listEntry, PERF_MDL_CACHE_ENTRY, ListEntry);
        PerfFreeMdlCacheEntry(entry);
    }

    // 清理使用列表
    while (!IsListEmpty(&cache->UsedList)) {
        listEntry = RemoveHeadList(&cache->UsedList);
        entry = CONTAINING_RECORD(listEntry, PERF_MDL_CACHE_ENTRY, ListEntry);
        PerfFreeMdlCacheEntry(entry);
    }

    cache->TotalCount = 0;
    cache->FreeCount = 0;
    cache->UsedCount = 0;

    KeReleaseSpinLock(&cache->SpinLock, oldIrql);

    PERF_DEBUG_PRINT("MDL cache cleaned up");
}

//
// 分配MDL
//
NTSTATUS
PerfAllocateMdl(
    _In_ PVOID Buffer,
    _In_ ULONG Size,
    _Out_ PMDL *Mdl
    )
{
    NTSTATUS status = STATUS_SUCCESS;
    PPERF_MDL_CACHE cache = &g_PerfManager.MdlCache;
    PPERF_MDL_CACHE_ENTRY entry = NULL;
    PLIST_ENTRY listEntry;
    KIRQL oldIrql;
    BOOLEAN found = FALSE;
    ULONG alignedSize;

    if (Buffer == NULL || Mdl == NULL || Size == 0) {
        return STATUS_INVALID_PARAMETER;
    }

    *Mdl = NULL;

    // 检查大小是否在缓存范围内
    if (Size < PERF_MDL_MIN_SIZE || Size > PERF_MDL_MAX_SIZE) {
        // 直接分配，不使用缓存
        *Mdl = IoAllocateMdl(Buffer, Size, FALSE, FALSE, NULL);
        if (*Mdl == NULL) {
            return STATUS_INSUFFICIENT_RESOURCES;
        }
        
        __try {
            MmProbeAndLockPages(*Mdl, KernelMode, IoReadAccess);
        } __except(EXCEPTION_EXECUTE_HANDLER) {
            IoFreeMdl(*Mdl);
            *Mdl = NULL;
            return STATUS_INVALID_USER_BUFFER;
        }
        
        PERF_DEBUG_PRINT("MDL direct allocation: size=%lu", Size);
        return STATUS_SUCCESS;
    }

    alignedSize = PerfRoundUpMdlSize(Size);

    KeAcquireSpinLock(&cache->SpinLock, &oldIrql);

    __try {
        cache->AllocRequests++;

        // 首先清理过期的MDL条目
        if (cache->FreeCount > 0) {
            PerfEvictExpiredMdls();
        }

        // 尝试从空闲列表中找到合适大小的MDL
        for (listEntry = cache->FreeList.Flink; 
             listEntry != &cache->FreeList; 
             listEntry = listEntry->Flink) {
            
            entry = CONTAINING_RECORD(listEntry, PERF_MDL_CACHE_ENTRY, ListEntry);
            
            // 检查大小是否匹配（允许一定的大小余量）
            if (entry->Size >= alignedSize && entry->Size <= (alignedSize * 2)) {
                // 找到合适的MDL
                RemoveEntryList(&entry->ListEntry);
                InsertTailList(&cache->UsedList, &entry->ListEntry);
                
                entry->InUse = TRUE;
                KeQuerySystemTime(&entry->LastUsed);
                
                cache->FreeCount--;
                cache->UsedCount++;
                cache->AllocHits++;
                
                // 重新初始化MDL
                MmInitializeMdl(entry->Mdl, Buffer, Size);
                
                __try {
                    MmProbeAndLockPages(entry->Mdl, KernelMode, IoReadAccess);
                    *Mdl = entry->Mdl;
                    found = TRUE;
                } __except(EXCEPTION_EXECUTE_HANDLER) {
                    // 如果锁定失败，移回空闲列表
                    RemoveEntryList(&entry->ListEntry);
                    InsertTailList(&cache->FreeList, &entry->ListEntry);
                    entry->InUse = FALSE;
                    cache->FreeCount++;
                    cache->UsedCount--;
                    status = STATUS_INVALID_USER_BUFFER;
                }
                
                PERF_DEBUG_PRINT("MDL cache hit: size=%lu, cached_size=%lu", Size, entry->Size);
                break;
            }
        }

        if (!found && NT_SUCCESS(status)) {
            // 缓存中没有合适的MDL，直接分配
            cache->AllocMisses++;
            
            *Mdl = IoAllocateMdl(Buffer, Size, FALSE, FALSE, NULL);
            if (*Mdl == NULL) {
                status = STATUS_INSUFFICIENT_RESOURCES;
                __leave;
            }
            
            __try {
                MmProbeAndLockPages(*Mdl, KernelMode, IoReadAccess);
            } __except(EXCEPTION_EXECUTE_HANDLER) {
                IoFreeMdl(*Mdl);
                *Mdl = NULL;
                status = STATUS_INVALID_USER_BUFFER;
                __leave;
            }
            
            PERF_DEBUG_PRINT("MDL cache miss, direct allocation: size=%lu", Size);
        }
    }
    __finally {
        KeReleaseSpinLock(&cache->SpinLock, oldIrql);
    }

    return status;
}

//
// 释放MDL
//
VOID
PerfFreeMdl(
    _In_ PMDL Mdl
    )
{
    PPERF_MDL_CACHE cache = &g_PerfManager.MdlCache;
    PPERF_MDL_CACHE_ENTRY entry;
    PLIST_ENTRY listEntry;
    KIRQL oldIrql;
    BOOLEAN found = FALSE;
    ULONG mdlSize;

    if (Mdl == NULL) {
        return;
    }

    mdlSize = MmGetMdlByteCount(Mdl);

    // 检查大小是否在缓存范围内
    if (mdlSize < PERF_MDL_MIN_SIZE || mdlSize > PERF_MDL_MAX_SIZE) {
        // 直接释放，不放入缓存
        MmUnlockPages(Mdl);
        IoFreeMdl(Mdl);
        PERF_DEBUG_PRINT("MDL direct free: size=%lu", mdlSize);
        return;
    }

    KeAcquireSpinLock(&cache->SpinLock, &oldIrql);

    __try {
        cache->FreeRequests++;

        // 查找是否是来自缓存的MDL
        for (listEntry = cache->UsedList.Flink; 
             listEntry != &cache->UsedList; 
             listEntry = listEntry->Flink) {
            
            entry = CONTAINING_RECORD(listEntry, PERF_MDL_CACHE_ENTRY, ListEntry);
            
            if (entry->Mdl == Mdl) {
                // 找到匹配的缓存条目
                MmUnlockPages(Mdl);
                
                // 移动到空闲列表
                RemoveEntryList(&entry->ListEntry);
                InsertTailList(&cache->FreeList, &entry->ListEntry);
                
                entry->InUse = FALSE;
                KeQuerySystemTime(&entry->LastUsed);
                
                cache->UsedCount--;
                cache->FreeCount++;
                found = TRUE;
                
                PERF_DEBUG_PRINT("MDL returned to cache: size=%lu", entry->Size);
                break;
            }
        }

        if (!found) {
            // 不是来自缓存的MDL，检查是否可以加入缓存
            if (cache->TotalCount < PERF_MDL_CACHE_SIZE) {
                entry = PerfAllocateMdlCacheEntry();
                if (entry != NULL) {
                    MmUnlockPages(Mdl);
                    
                    entry->Mdl = Mdl;
                    entry->Size = mdlSize;
                    entry->InUse = FALSE;
                    KeQuerySystemTime(&entry->LastUsed);
                    
                    InsertTailList(&cache->FreeList, &entry->ListEntry);
                    cache->TotalCount++;
                    cache->FreeCount++;
                    
                    PERF_DEBUG_PRINT("MDL added to cache: size=%lu", mdlSize);
                } else {
                    // 无法分配缓存条目，直接释放
                    MmUnlockPages(Mdl);
                    IoFreeMdl(Mdl);
                    PERF_DEBUG_PRINT("MDL direct free (cache entry allocation failed): size=%lu", mdlSize);
                }
            } else {
                // 缓存已满，直接释放
                MmUnlockPages(Mdl);
                IoFreeMdl(Mdl);
                PERF_DEBUG_PRINT("MDL direct free (cache full): size=%lu", mdlSize);
            }
        }
    }
    __finally {
        KeReleaseSpinLock(&cache->SpinLock, oldIrql);
    }
}

//
// 获取MDL缓存统计信息
//
VOID
PerfGetMdlCacheStats(
    _Out_ PULONG TotalCount,
    _Out_ PULONG FreeCount,
    _Out_ PULONG UsedCount,
    _Out_ PULONG AllocHits,
    _Out_ PULONG AllocMisses
    )
{
    PPERF_MDL_CACHE cache = &g_PerfManager.MdlCache;
    KIRQL oldIrql;

    KeAcquireSpinLock(&cache->SpinLock, &oldIrql);

    if (TotalCount) *TotalCount = cache->TotalCount;
    if (FreeCount) *FreeCount = cache->FreeCount;
    if (UsedCount) *UsedCount = cache->UsedCount;
    if (AllocHits) *AllocHits = cache->AllocHits;
    if (AllocMisses) *AllocMisses = cache->AllocMisses;

    KeReleaseSpinLock(&cache->SpinLock, oldIrql);
}

//
// 分配MDL缓存条目
//
static PPERF_MDL_CACHE_ENTRY
PerfAllocateMdlCacheEntry(
    VOID
    )
{
    PPERF_MDL_CACHE_ENTRY entry;

    entry = (PPERF_MDL_CACHE_ENTRY)ExAllocatePoolWithTag(
        NonPagedPool,
        sizeof(PERF_MDL_CACHE_ENTRY),
        'ldmE'
    );

    if (entry != NULL) {
        RtlZeroMemory(entry, sizeof(PERF_MDL_CACHE_ENTRY));
        InitializeListHead(&entry->ListEntry);
    }

    return entry;
}

//
// 释放MDL缓存条目
//
static VOID
PerfFreeMdlCacheEntry(
    _In_ PPERF_MDL_CACHE_ENTRY Entry
    )
{
    if (Entry != NULL) {
        if (Entry->Mdl != NULL) {
            // 确保MDL已解锁
            if (Entry->Mdl->MdlFlags & MDL_PAGES_LOCKED) {
                MmUnlockPages(Entry->Mdl);
            }
            IoFreeMdl(Entry->Mdl);
        }
        ExFreePoolWithTag(Entry, 'ldmE');
    }
}

//
// 检查MDL是否过期
//
static BOOLEAN
PerfIsMdlExpired(
    _In_ PPERF_MDL_CACHE_ENTRY Entry
    )
{
    LARGE_INTEGER currentTime;
    LARGE_INTEGER expireTime;

    KeQuerySystemTime(&currentTime);
    
    // MDL缓存超时时间：5分钟
    expireTime.QuadPart = Entry->LastUsed.QuadPart + (5 * 60 * 10000000LL);

    return (currentTime.QuadPart > expireTime.QuadPart);
}

//
// 清理过期的MDL条目
//
static VOID
PerfEvictExpiredMdls(
    VOID
    )
{
    PPERF_MDL_CACHE cache = &g_PerfManager.MdlCache;
    PLIST_ENTRY listEntry, nextEntry;
    PPERF_MDL_CACHE_ENTRY entry;
    ULONG evictedCount = 0;

    // 注意：调用者应该已经获取了缓存的自旋锁

    listEntry = cache->FreeList.Flink;
    while (listEntry != &cache->FreeList) {
        nextEntry = listEntry->Flink;
        entry = CONTAINING_RECORD(listEntry, PERF_MDL_CACHE_ENTRY, ListEntry);
        
        if (PerfIsMdlExpired(entry)) {
            RemoveEntryList(listEntry);
            cache->FreeCount--;
            cache->TotalCount--;
            PerfFreeMdlCacheEntry(entry);
            evictedCount++;
        }
        
        listEntry = nextEntry;
    }

    if (evictedCount > 0) {
        PERF_DEBUG_PRINT("Evicted %lu expired MDL entries", evictedCount);
    }
}

//
// 将大小向上取整到对齐边界
//
static ULONG
PerfRoundUpMdlSize(
    _In_ ULONG Size
    )
{
    return (Size + PERF_MDL_ALIGNMENT - 1) & ~(PERF_MDL_ALIGNMENT - 1);
} 