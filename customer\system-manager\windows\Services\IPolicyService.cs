using CryptoSystem.SystemManager.Models;

namespace CryptoSystem.SystemManager.Services
{
    public interface IPolicyService
    {
        Task<IEnumerable<Policy>> GetAllPoliciesAsync();
        Task<Policy?> GetPolicyByIdAsync(string policyId);
        Task<(bool Success, string Message)> CreatePolicyAsync(Policy policy, string createdBy);
        Task<(bool Success, string Message)> UpdatePolicyAsync(Policy policy, string modifiedBy);
        Task<(bool Success, string Message)> DeletePolicyAsync(string policyId, string deletedBy);
        Task<(bool Success, string Message)> DeployPolicyAsync(string policyId, IEnumerable<string> targetDeviceIds);
        Task<PolicyStatistics> GetPolicyStatisticsAsync();
    }
} 