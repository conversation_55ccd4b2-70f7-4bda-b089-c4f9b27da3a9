#pragma once

#include <string>
#include <vector>
#include <optional>

namespace crypto {
namespace api {

/**
 * 加密工具类
 * 提供常用的加密、解密、散列和编码功能
 */
class CryptoUtils {
public:
    /**
     * 散列算法枚举
     */
    enum class HashAlgorithm {
        SHA256,
        SHA512,
        MD5,  // 仅用于向后兼容，不推荐用于安全敏感场景
    };

    /**
     * 对称加密算法枚举
     */
    enum class SymmetricAlgorithm {
        AES_256_GCM,  // AES-256-GCM，带认证的加密模式
        AES_256_CBC,  // AES-256-CBC
        ChaCha20Poly1305  // ChaCha20-Poly1305
    };

    /**
     * 计算字符串的散列值
     * 
     * @param data 要计算散列的输入数据
     * @param algorithm 散列算法
     * @return 十六进制格式的散列值
     */
    static std::string ComputeHash(
        const std::string& data, 
        HashAlgorithm algorithm = HashAlgorithm::SHA256);

    /**
     * 计算文件的散列值
     * 
     * @param filePath 文件路径
     * @param algorithm 散列算法
     * @return 成功时返回十六进制格式的散列值，失败时返回空
     */
    static std::optional<std::string> ComputeFileHash(
        const std::string& filePath,
        HashAlgorithm algorithm = HashAlgorithm::SHA256);

    /**
     * 生成随机字节
     * 
     * @param length 要生成的随机字节数
     * @return 包含随机字节的向量
     */
    static std::vector<uint8_t> GenerateRandomBytes(size_t length);
    
    /**
     * 生成加密密钥
     * 
     * @param algorithm 加密算法
     * @return 适合指定算法的随机密钥
     */
    static std::vector<uint8_t> GenerateKey(SymmetricAlgorithm algorithm);
    
    /**
     * 对称加密数据
     * 
     * @param plaintext 明文数据
     * @param key 加密密钥
     * @param algorithm 加密算法
     * @param iv 初始化向量（如果为空，将自动生成）
     * @return 加密后的数据，格式：[IV(如果自动生成)][加密数据][认证标签(如果适用)]
     */
    static std::vector<uint8_t> EncryptSymmetric(
        const std::vector<uint8_t>& plaintext,
        const std::vector<uint8_t>& key,
        SymmetricAlgorithm algorithm = SymmetricAlgorithm::AES_256_GCM,
        const std::vector<uint8_t>& iv = {});
    
    /**
     * 对称解密数据
     * 
     * @param ciphertext 密文数据，格式：[IV(如果自动生成)][加密数据][认证标签(如果适用)]
     * @param key 解密密钥
     * @param algorithm 解密算法
     * @param iv 初始化向量（如果为空，则从密文中提取）
     * @return 解密后的明文数据，失败时抛出异常
     */
    static std::vector<uint8_t> DecryptSymmetric(
        const std::vector<uint8_t>& ciphertext,
        const std::vector<uint8_t>& key,
        SymmetricAlgorithm algorithm = SymmetricAlgorithm::AES_256_GCM,
        const std::vector<uint8_t>& iv = {});
    
    /**
     * Base64编码
     * 
     * @param data 要编码的二进制数据
     * @return Base64编码的字符串
     */
    static std::string Base64Encode(const std::vector<uint8_t>& data);
    
    /**
     * Base64解码
     * 
     * @param base64String Base64编码的字符串
     * @return 解码后的二进制数据
     */
    static std::vector<uint8_t> Base64Decode(const std::string& base64String);
    
    /**
     * 十六进制编码
     * 
     * @param data 要编码的二进制数据
     * @return 十六进制编码的字符串
     */
    static std::string HexEncode(const std::vector<uint8_t>& data);
    
    /**
     * 十六进制解码
     * 
     * @param hexString 十六进制编码的字符串
     * @return 解码后的二进制数据
     */
    static std::vector<uint8_t> HexDecode(const std::string& hexString);
    
    /**
     * 字符串转二进制数据
     * 
     * @param str 输入字符串
     * @return 二进制数据
     */
    static std::vector<uint8_t> StringToBytes(const std::string& str);
    
    /**
     * 二进制数据转字符串
     * 
     * @param bytes 二进制数据
     * @return 字符串
     */
    static std::string BytesToString(const std::vector<uint8_t>& bytes);
};

} // namespace api
} // namespace crypto 