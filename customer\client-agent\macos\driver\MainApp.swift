import Cocoa
import SwiftUI
import Combine
import OSLog
import ServiceManagement

/**
 * 主应用程序类
 * 负责应用生命周期管理、后台服务协调和系统集成
 */
@main
class MainApp: NSApplication {
    
    // MARK: - Properties
    
    private let logger = Logger(subsystem: "com.cryptosystem.macos", category: "MainApp")
    private var cryptoManager: CryptoManager?
    private var policyManager: PolicyManager?
    private var keySyncService: KeySyncService?
    private var fileMonitorEngine: FileMonitorEngine?
    private var statusItem: NSStatusItem?
    private var cancellables = Set<AnyCancellable>()
    
    // 应用状态
    @Published private var isRunning = false
    @Published private var statistics = SystemStatistics()
    
    // MARK: - Application Lifecycle
    
    override func finishLaunching() {
        super.finishLaunching()
        
        logger.info("Cryptosystem macOS客户端启动")
        
        // 初始化核心服务
        initializeServices()
        
        // 设置状态栏图标
        setupStatusBarItem()
        
        // 注册系统通知
        registerSystemNotifications()
        
        // 启动服务
        startServices()
        
        logger.info("应用启动完成")
    }
    
    override func terminate(_ sender: Any?) {
        logger.info("应用程序终止")
        
        // 停止服务
        stopServices()
        
        // 清理资源
        cleanup()
        
        super.terminate(sender)
    }
    
    // MARK: - Service Management
    
    private func initializeServices() {
        do {
            // 初始化策略管理器
            policyManager = PolicyManager()
            
            // 初始化加密管理器
            cryptoManager = try CryptoManager()
            
            // 初始化密钥同步服务
            keySyncService = KeySyncService(cryptoManager: cryptoManager!)
            
            // 初始化文件监控引擎
            fileMonitorEngine = FileMonitorEngine(
                cryptoManager: cryptoManager!,
                policyManager: policyManager!
            )
            
            logger.info("核心服务初始化成功")
        } catch {
            logger.error("服务初始化失败: \(error.localizedDescription)")
        }
    }
    
    private func startServices() {
        guard let policyManager = policyManager,
              let cryptoManager = cryptoManager,
              let keySyncService = keySyncService,
              let fileMonitorEngine = fileMonitorEngine else {
            logger.error("服务未初始化，无法启动")
            return
        }
        
        do {
            // 启动策略管理器
            try policyManager.start()
            
            // 启动密钥同步服务
            keySyncService.start()
            
            // 启动文件监控引擎
            try fileMonitorEngine.start()
            
            isRunning = true
            logger.info("所有服务启动成功")
            
            // 开始统计信息收集
            startStatisticsCollection()
            
        } catch {
            logger.error("服务启动失败: \(error.localizedDescription)")
        }
    }
    
    private func stopServices() {
        fileMonitorEngine?.stop()
        keySyncService?.stop()
        policyManager?.stop()
        
        isRunning = false
        logger.info("所有服务已停止")
    }
    
    private func cleanup() {
        // 移除状态栏项
        if let statusItem = statusItem {
            NSStatusBar.system.removeStatusItem(statusItem)
        }
        
        // 取消订阅
        cancellables.removeAll()
        
        logger.info("资源清理完成")
    }
    
    // MARK: - Status Bar
    
    private func setupStatusBarItem() {
        statusItem = NSStatusBar.system.statusItem(withLength: NSStatusItem.variableLength)
        
        if let button = statusItem?.button {
            button.image = NSImage(systemSymbolName: "lock.shield", accessibilityDescription: "Cryptosystem")
            button.action = #selector(statusBarButtonClicked)
            button.target = self
        }
        
        updateStatusBarMenu()
    }
    
    @objc private func statusBarButtonClicked() {
        updateStatusBarMenu()
    }
    
    private func updateStatusBarMenu() {
        let menu = NSMenu()
        
        // 状态信息
        menu.addItem(withTitle: isRunning ? "运行中" : "已停止", action: nil, keyEquivalent: "")
        menu.addItem(NSMenuItem.separator())
        
        // 统计信息
        let statsItem = NSMenuItem(title: "统计信息", action: nil, keyEquivalent: "")
        let statsSubmenu = NSMenu()
        statsSubmenu.addItem(withTitle: "加密文件: \(statistics.encryptedFiles)", action: nil, keyEquivalent: "")
        statsSubmenu.addItem(withTitle: "解密文件: \(statistics.decryptedFiles)", action: nil, keyEquivalent: "")
        statsSubmenu.addItem(withTitle: "数据大小: \(formatFileSize(statistics.totalDataSize))", action: nil, keyEquivalent: "")
        statsSubmenu.addItem(withTitle: "运行时间: \(formatUptime(statistics.uptime))", action: nil, keyEquivalent: "")
        statsItem.submenu = statsSubmenu
        menu.addItem(statsItem)
        
        menu.addItem(NSMenuItem.separator())
        
        // 控制操作
        if isRunning {
            menu.addItem(withTitle: "重启服务", action: #selector(restartServices), keyEquivalent: "")
            menu.addItem(withTitle: "停止服务", action: #selector(stopServicesAction), keyEquivalent: "")
        } else {
            menu.addItem(withTitle: "启动服务", action: #selector(startServicesAction), keyEquivalent: "")
        }
        
        menu.addItem(NSMenuItem.separator())
        
        // 其他操作
        menu.addItem(withTitle: "同步策略", action: #selector(syncPolicies), keyEquivalent: "")
        menu.addItem(withTitle: "清理缓存", action: #selector(clearCache), keyEquivalent: "")
        menu.addItem(withTitle: "显示日志", action: #selector(showLogs), keyEquivalent: "")
        
        menu.addItem(NSMenuItem.separator())
        menu.addItem(withTitle: "退出", action: #selector(quit), keyEquivalent: "q")
        
        statusItem?.menu = menu
    }
    
    // MARK: - Menu Actions
    
    @objc private func startServicesAction() {
        startServices()
        updateStatusBarMenu()
    }
    
    @objc private func stopServicesAction() {
        stopServices()
        updateStatusBarMenu()
    }
    
    @objc private func restartServices() {
        logger.info("重启服务")
        stopServices()
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.startServices()
            self.updateStatusBarMenu()
        }
    }
    
    @objc private func syncPolicies() {
        logger.info("手动同步策略")
        policyManager?.forceSync()
    }
    
    @objc private func clearCache() {
        logger.info("清理缓存")
        cryptoManager?.clearCache()
    }
    
    @objc private func showLogs() {
        logger.info("显示日志")
        let logPath = "/var/log/cryptosystem/macos.log"
        NSWorkspace.shared.openFile(logPath, withApplication: "Console")
    }
    
    @objc private func quit() {
        NSApplication.shared.terminate(self)
    }
    
    // MARK: - System Notifications
    
    private func registerSystemNotifications() {
        let notificationCenter = NSWorkspace.shared.notificationCenter
        
        // 系统睡眠通知
        notificationCenter.addObserver(
            self,
            selector: #selector(systemWillSleep),
            name: NSWorkspace.willSleepNotification,
            object: nil
        )
        
        // 系统唤醒通知
        notificationCenter.addObserver(
            self,
            selector: #selector(systemDidWake),
            name: NSWorkspace.didWakeNotification,
            object: nil
        )
        
        // 用户切换通知
        notificationCenter.addObserver(
            self,
            selector: #selector(sessionDidBecomeActive),
            name: NSWorkspace.sessionDidBecomeActiveNotification,
            object: nil
        )
        
        notificationCenter.addObserver(
            self,
            selector: #selector(sessionDidResignActive),
            name: NSWorkspace.sessionDidResignActiveNotification,
            object: nil
        )
    }
    
    @objc private func systemWillSleep() {
        logger.info("系统即将睡眠")
        fileMonitorEngine?.handleSystemSleep()
    }
    
    @objc private func systemDidWake() {
        logger.info("系统唤醒")
        fileMonitorEngine?.handleSystemWake()
        
        // 唤醒后重新同步策略
        DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) {
            self.policyManager?.forceSync()
        }
    }
    
    @objc private func sessionDidBecomeActive() {
        logger.info("用户会话激活")
    }
    
    @objc private func sessionDidResignActive() {
        logger.info("用户会话非激活")
    }
    
    // MARK: - Statistics Collection
    
    private func startStatisticsCollection() {
        Timer.publish(every: 30.0, on: .main, in: .common)
            .autoconnect()
            .sink { [weak self] _ in
                self?.updateStatistics()
            }
            .store(in: &cancellables)
    }
    
    private func updateStatistics() {
        guard let cryptoManager = cryptoManager,
              let fileMonitorEngine = fileMonitorEngine else {
            return
        }
        
        let cryptoStats = cryptoManager.getStatistics()
        let monitorStats = fileMonitorEngine.getStatistics()
        
        statistics = SystemStatistics(
            encryptedFiles: cryptoStats.encryptedFiles,
            decryptedFiles: cryptoStats.decryptedFiles,
            totalDataSize: cryptoStats.totalDataSize,
            uptime: Int(Date().timeIntervalSince(launchDate)),
            keysSynced: keySyncService?.getSyncCount() ?? 0,
            policiesApplied: policyManager?.getAppliedPolicyCount() ?? 0,
            monitoredPaths: monitorStats.monitoredPaths,
            lastActivity: monitorStats.lastActivity
        )
        
        // 更新状态栏菜单
        if statusItem?.menu != nil {
            updateStatusBarMenu()
        }
    }
    
    // MARK: - Utility Methods
    
    private var launchDate = Date()
    
    private func formatFileSize(_ bytes: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.countStyle = .file
        return formatter.string(fromByteCount: bytes)
    }
    
    private func formatUptime(_ seconds: Int) -> String {
        let hours = seconds / 3600
        let minutes = (seconds % 3600) / 60
        return String(format: "%02d:%02d", hours, minutes)
    }
}

// MARK: - System Statistics

struct SystemStatistics {
    var encryptedFiles: Int = 0
    var decryptedFiles: Int = 0
    var totalDataSize: Int64 = 0
    var uptime: Int = 0
    var keysSynced: Int = 0
    var policiesApplied: Int = 0
    var monitoredPaths: Int = 0
    var lastActivity: Date = Date()
}