<UserControl x:Class="KeyGenerator.Views.KeyDistributionView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:KeyGenerator.Views"
    xmlns:viewmodels="clr-namespace:KeyGenerator.ViewModels" mc:Ignorable="d" d:DataContext="{d:DesignInstance Type=viewmodels:KeyDistributionViewModel, IsDesignTimeCreatable=False}" d:DesignHeight="600" d:DesignWidth="800" Background="#F5F5F5">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题和返回按钮 -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,20">
            <Button Content="&lt; 返回" Command="{Binding GoBackCommand}" VerticalAlignment="Center"/>
            <TextBlock Text="{Binding MasterKey.KeyName, StringFormat='密钥分发管理: {0}'}" FontSize="24" FontWeight="Bold" Margin="20,0,0,0" VerticalAlignment="Center"/>
        </StackPanel>

        <!-- 操作栏 -->
        <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,10">
            <Button Content="新建分发任务" Command="{Binding CreateNewTaskCommand}" Style="{StaticResource AccentButtonStyle}"/>
        </StackPanel>

        <!-- 任务列表 -->
        <ListView Grid.Row="2" ItemsSource="{Binding DistributionTasks}" SelectedItem="{Binding SelectedTask}" HorizontalContentAlignment="Stretch">
            <ListView.View>
                <GridView>
                    <GridViewColumn Header="任务ID" DisplayMemberBinding="{Binding TaskId}" Width="150"/>
                    <GridViewColumn Header="任务名称" DisplayMemberBinding="{Binding TaskName}" Width="200"/>
                    <GridViewColumn Header="状态" DisplayMemberBinding="{Binding Status}" Width="100"/>
                    <GridViewColumn Header="进度">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <ProgressBar Value="{Binding Progress}" Maximum="100" Height="15" Width="100"/>
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                    <GridViewColumn Header="创建时间" DisplayMemberBinding="{Binding CreatedAt, StringFormat='yyyy-MM-dd HH:mm'}" Width="120"/>
                    <GridViewColumn Header="操作">
                        <GridViewColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <Button Content="启动" Command="{Binding DataContext.StartTaskCommand, RelativeSource={RelativeSource AncestorType=ListView}}" CommandParameter="{Binding}" IsEnabled="{Binding Status, Converter={StaticResource StatusToStartEnabledConverter}}"/>
                                </StackPanel>
                            </DataTemplate>
                        </GridViewColumn.CellTemplate>
                    </GridViewColumn>
                </GridView>
            </ListView.View>
        </ListView>

        <TextBlock Grid.Row="3" Text="{Binding StatusText}" Margin="0,10,0,0" FontStyle="Italic"/>
    </Grid>
</UserControl> 