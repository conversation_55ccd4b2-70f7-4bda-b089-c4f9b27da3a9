namespace CryptoSystem.DeclassificationClient.Services
{
    /// <summary>
    /// 加密算法类型
    /// </summary>
    public enum CryptoAlgorithm
    {
        /// <summary>
        /// AES-256
        /// </summary>
        AES256 = 0,

        /// <summary>
        /// 国密SM4
        /// </summary>
        SM4 = 1,

        /// <summary>
        /// 国密SM2
        /// </summary>
        SM2 = 2
    }

    /// <summary>
    /// 加密服务接口
    /// </summary>
    public interface ICryptoService
    {
        /// <summary>
        /// 解密文件
        /// </summary>
        /// <param name="encryptedFilePath">加密文件路径</param>
        /// <param name="decryptedFilePath">解密后文件路径</param>
        /// <param name="key">解密密钥</param>
        /// <param name="algorithm">加密算法</param>
        /// <returns>是否解密成功</returns>
        Task<bool> DecryptFileAsync(string encryptedFilePath, string decryptedFilePath, byte[] key, CryptoAlgorithm algorithm = CryptoAlgorithm.AES256);

        /// <summary>
        /// 加密文件
        /// </summary>
        /// <param name="plainFilePath">明文文件路径</param>
        /// <param name="encryptedFilePath">加密后文件路径</param>
        /// <param name="key">加密密钥</param>
        /// <param name="algorithm">加密算法</param>
        /// <returns>是否加密成功</returns>
        Task<bool> EncryptFileAsync(string plainFilePath, string encryptedFilePath, byte[] key, CryptoAlgorithm algorithm = CryptoAlgorithm.AES256);

        /// <summary>
        /// 生成随机密钥
        /// </summary>
        /// <param name="algorithm">加密算法</param>
        /// <returns>密钥字节数组</returns>
        byte[] GenerateKey(CryptoAlgorithm algorithm = CryptoAlgorithm.AES256);

        /// <summary>
        /// 生成随机初始化向量
        /// </summary>
        /// <param name="algorithm">加密算法</param>
        /// <returns>IV字节数组</returns>
        byte[] GenerateIV(CryptoAlgorithm algorithm = CryptoAlgorithm.AES256);

        /// <summary>
        /// 从密码派生密钥
        /// </summary>
        /// <param name="password">密码</param>
        /// <param name="salt">盐值</param>
        /// <param name="iterations">迭代次数</param>
        /// <param name="keyLength">密钥长度</param>
        /// <returns>派生的密钥</returns>
        byte[] DeriveKeyFromPassword(string password, byte[] salt, int iterations = 10000, int keyLength = 32);

        /// <summary>
        /// 生成随机盐值
        /// </summary>
        /// <param name="length">盐值长度</param>
        /// <returns>盐值字节数组</returns>
        byte[] GenerateSalt(int length = 16);

        /// <summary>
        /// 计算文件哈希
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="algorithm">哈希算法（MD5, SHA256, SM3等）</param>
        /// <returns>哈希值</returns>
        Task<string> CalculateFileHashAsync(string filePath, string algorithm = "MD5");

        /// <summary>
        /// 验证文件数字签名
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="signature">数字签名</param>
        /// <param name="publicKey">公钥</param>
        /// <returns>签名是否有效</returns>
        Task<bool> VerifyDigitalSignatureAsync(string filePath, byte[] signature, byte[] publicKey);

        /// <summary>
        /// 创建文件数字签名
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="privateKey">私钥</param>
        /// <returns>数字签名</returns>
        Task<byte[]> CreateDigitalSignatureAsync(string filePath, byte[] privateKey);

        /// <summary>
        /// 检测文件是否已加密
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否已加密</returns>
        Task<bool> IsFileEncryptedAsync(string filePath);

        /// <summary>
        /// 获取文件加密信息
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>加密信息（算法、密钥长度等）</returns>
        Task<(CryptoAlgorithm Algorithm, int KeyLength, bool IsEncrypted)> GetFileEncryptionInfoAsync(string filePath);

        /// <summary>
        /// 安全删除文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="passes">覆写次数</param>
        /// <returns>是否删除成功</returns>
        Task<bool> SecureDeleteFileAsync(string filePath, int passes = 3);

        /// <summary>
        /// 生成安全随机数
        /// </summary>
        /// <param name="length">随机数长度</param>
        /// <returns>随机数字节数组</returns>
        byte[] GenerateSecureRandom(int length);

        /// <summary>
        /// 压缩并加密文件
        /// </summary>
        /// <param name="sourceFiles">源文件列表</param>
        /// <param name="targetPath">目标压缩包路径</param>
        /// <param name="password">压缩包密码</param>
        /// <param name="compressionLevel">压缩级别（0-9）</param>
        /// <returns>是否成功</returns>
        Task<bool> CompressAndEncryptFilesAsync(string[] sourceFiles, string targetPath, string password, int compressionLevel = 6);

        /// <summary>
        /// 解密并解压文件
        /// </summary>
        /// <param name="archivePath">压缩包路径</param>
        /// <param name="extractPath">解压目录</param>
        /// <param name="password">压缩包密码</param>
        /// <returns>是否成功</returns>
        Task<bool> DecryptAndExtractFilesAsync(string archivePath, string extractPath, string password);

        /// <summary>
        /// 验证密码强度
        /// </summary>
        /// <param name="password">密码</param>
        /// <returns>密码强度评分（0-100）</returns>
        int ValidatePasswordStrength(string password);

        /// <summary>
        /// 生成安全密码
        /// </summary>
        /// <param name="length">密码长度</param>
        /// <param name="includeSpecialChars">是否包含特殊字符</param>
        /// <returns>生成的密码</returns>
        string GenerateSecurePassword(int length = 12, bool includeSpecialChars = true);
    }
} 