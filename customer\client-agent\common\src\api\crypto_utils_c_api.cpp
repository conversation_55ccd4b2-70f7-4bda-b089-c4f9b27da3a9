#include "crypto_utils_c_api.h"
#include "crypto_utils.h" // The C++ header
#include <vector>
#include <stdexcept> // For exception handling
#include <iostream>  // For basic error logging if needed, though CryptoUtils has its own

// 错误代码定义 (可以扩展)
#define C_API_SUCCESS 0
#define C_API_ERROR_INVALID_PARAM 1
#define C_API_ERROR_ENCRYPTION_FAILED 3
#define C_API_ERROR_DECRYPTION_FAILED 3
#define C_API_ERROR_AUTH_FAILED 4
#define C_API_ERROR_BUFFER_TOO_SMALL 5 // 新增，用于输出缓冲区不足

#ifdef __cplusplus
extern "C" {
#endif

int sm4_gcm_encrypt_c_api(
    const uint8_t* plaintext,
    size_t plaintext_len,
    const uint8_t* key,
    size_t key_len,
    const uint8_t* iv,
    size_t iv_len,
    uint8_t* out_combined_data,
    size_t* p_out_combined_data_len
) {
    if (!plaintext && plaintext_len > 0) return C_API_ERROR_INVALID_PARAM; // 明文指针为空但长度大于0
    if (!key || !iv || !out_combined_data || !p_out_combined_data_len) return C_API_ERROR_INVALID_PARAM;
    if (key_len != 16) return C_API_ERROR_INVALID_PARAM; // SM4 key size
    if (iv_len != 12) return C_API_ERROR_INVALID_PARAM;   // GCM recommended IV size

    try {
        std::vector<uint8_t> pt_vec(plaintext, plaintext + plaintext_len);
        std::vector<uint8_t> key_vec(key, key + key_len);
        std::vector<uint8_t> iv_vec(iv, iv + iv_len);

        std::vector<uint8_t> combined_output = crypto::api::CryptoUtils::EncryptSymmetric(
            pt_vec, key_vec, crypto::api::SymmetricAlgorithm::SM4_128_GCM, iv_vec
        );

        if (combined_output.empty() && plaintext_len > 0) { // 加密结果为空通常表示错误，除非明文也为空（但即便如此，GCM也会有IV和Tag）
            // CryptoUtils::EncryptSymmetric 内部会抛异常，这里理论上不会到
            // 但为了安全，添加检查
             return C_API_ERROR_ENCRYPTION_FAILED;
        }

        if (*p_out_combined_data_len < combined_output.size()) {
            *p_out_combined_data_len = combined_output.size(); // 告知所需的最小大小
            return C_API_ERROR_BUFFER_TOO_SMALL;
        }

        std::copy(combined_output.begin(), combined_output.end(), out_combined_data);
        *p_out_combined_data_len = combined_output.size();
        return C_API_SUCCESS;

    } catch (const std::invalid_argument& e) {
        // Log e.what() if a logging mechanism is available
        std::cerr << "C_API Encrypt Error (Invalid Argument): " << e.what() << std::endl;
        return C_API_ERROR_INVALID_PARAM;
    } catch (const std::runtime_error& e) {
        // Log e.what()
        std::cerr << "C_API Encrypt Error (Runtime Error): " << e.what() << std::endl;
        return C_API_ERROR_ENCRYPTION_FAILED;
    } catch (...) {
        std::cerr << "C_API Encrypt Error (Unknown Exception)" << std::endl;
        return C_API_ERROR_ENCRYPTION_FAILED;
    }
}

int sm4_gcm_decrypt_c_api(
    const uint8_t* combined_data,
    size_t combined_data_len,
    const uint8_t* key,
    size_t key_len,
    uint8_t* out_plaintext,
    size_t* p_out_plaintext_len
) {
    if (!combined_data || !key || !out_plaintext || !p_out_plaintext_len) return C_API_ERROR_INVALID_PARAM;
    if (key_len != 16) return C_API_ERROR_INVALID_PARAM;
    // combined_data_len 的下限检查 (IV_SIZE + TAG_SIZE)
    const size_t MIN_COMBINED_LEN = 12 + 16; 
    if (combined_data_len < MIN_COMBINED_LEN) return C_API_ERROR_INVALID_PARAM;

    try {
        std::vector<uint8_t> cd_vec(combined_data, combined_data + combined_data_len);
        std::vector<uint8_t> key_vec(key, key + key_len);

        std::vector<uint8_t> decrypted_plaintext = crypto::api::CryptoUtils::DecryptSymmetric(
            cd_vec, key_vec, crypto::api::SymmetricAlgorithm::SM4_128_GCM
        );

        // DecryptSymmetric 抛出异常表示认证失败或解密错误。
        // 如果返回空vector但没有抛异常，可能表示明文本身就是空的（需要CryptoUtils的行为明确）。
        // 我们的CryptoUtils::DecryptSymmetric设计为认证失败时抛出异常。
        // 如果明文确实是空的，decrypted_plaintext.empty()会为true。

        if (*p_out_plaintext_len < decrypted_plaintext.size()) {
            *p_out_plaintext_len = decrypted_plaintext.size();
            return C_API_ERROR_BUFFER_TOO_SMALL;
        }

        if (!decrypted_plaintext.empty()) {
            std::copy(decrypted_plaintext.begin(), decrypted_plaintext.end(), out_plaintext);
        }
        *p_out_plaintext_len = decrypted_plaintext.size();
        return C_API_SUCCESS;

    } catch (const std::invalid_argument& e) {
        std::cerr << "C_API Decrypt Error (Invalid Argument): " << e.what() << std::endl;
        return C_API_ERROR_INVALID_PARAM;
    } catch (const std::runtime_error& e) {
        // std::runtime_error("Authentication failed during decryption (tag mismatch).");
        // 检查错误信息判断是否为认证失败
        std::string error_msg = e.what();
        if (error_msg.find("Authentication failed") != std::string::npos || 
            error_msg.find("TAG MISMATCH") != std::string::npos) {
            std::cerr << "C_API Decrypt Error (Authentication Failed): " << e.what() << std::endl;
            return C_API_ERROR_AUTH_FAILED;
        }
        std::cerr << "C_API Decrypt Error (Runtime Error): " << e.what() << std::endl;
        return C_API_ERROR_DECRYPTION_FAILED;
    } catch (...) {
        std::cerr << "C_API Decrypt Error (Unknown Exception)" << std::endl;
        return C_API_ERROR_DECRYPTION_FAILED;
    }
}

#ifdef __cplusplus
}
#endif 