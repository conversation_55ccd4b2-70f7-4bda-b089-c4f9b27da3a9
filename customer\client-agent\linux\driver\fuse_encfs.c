/*
 * fuse_encfs.c
 *
 * Cryptosystem Linux 加密文件系统 FUSE 实现
 * 提供透明的文件加解密功能
 */

#define FUSE_USE_VERSION 31

#include <fuse.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <errno.h>
#include <fcntl.h>
#include <unistd.h>
#include <assert.h>
#include <pthread.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/file.h>
#include <openssl/evp.h>
#include <openssl/aes.h>
#include <openssl/err.h>
#include <openssl/rand.h>
#include <search.h> // For hcreate, hsearch

#include "crypto_manager.hh"
#include "policy_manager.h"
#include "utils.h"
#include "enc_header.h"

// 加密文件头签名
static const unsigned char ENC_SIGNATURE[4] = {'E', 'N', 'C', 'F'};

// 文件锁节点
typedef struct {
    char *path;
    pthread_rwlock_t lock;
    int open_count; // 引用计数
} file_lock_node;

// 全局配置
static struct {
    char *source_dir;       // 源目录（实际存储的目录）
    char *mount_point;      // 挂载点（虚拟文件系统目录）
    int debug_mode;         // 调试模式
    struct hsearch_data file_locks_htab; // 锁的哈希表
    pthread_mutex_t htab_lock; // 保护哈希表的互斥锁
} global_config;

// 辅助函数: 获取或创建文件锁
static file_lock_node* get_or_create_file_lock(const char *path) {
    pthread_mutex_lock(&global_config.htab_lock);

    ENTRY item;
    item.key = (char*)path;
    ENTRY *found_item;
    
    hsearch_r(item, FIND, &found_item, &global_config.file_locks_htab);

    if (found_item) {
        file_lock_node *node = (file_lock_node*)found_item->data;
        node->open_count++;
        pthread_mutex_unlock(&global_config.htab_lock);
        return node;
    }
    
    // Not found, create a new one
    file_lock_node *new_node = (file_lock_node*)malloc(sizeof(file_lock_node));
    if (!new_node) {
        pthread_mutex_unlock(&global_config.htab_lock);
        return NULL;
    }
    
    new_node->path = strdup(path);
    pthread_rwlock_init(&new_node->lock, NULL);
    new_node->open_count = 1;

    item.key = new_node->path;
    item.data = new_node;
    hsearch_r(item, ENTER, &found_item, &global_config.file_locks_htab);
    
    pthread_mutex_unlock(&global_config.htab_lock);
    return new_node;
}

// 辅助函数: 释放文件锁
static void release_file_lock(const char *path) {
    pthread_mutex_lock(&global_config.htab_lock);

    ENTRY item;
    item.key = (char*)path;
    ENTRY *found_item;
    
    hsearch_r(item, FIND, &found_item, &global_config.file_locks_htab);

    if (found_item) {
        file_lock_node *node = (file_lock_node*)found_item->data;
        node->open_count--;
        if (node->open_count == 0) {
            // Can't easily remove from hsearch, so we just destroy lock
            // In a real-world scenario, a more dynamic hash table would be better
            pthread_rwlock_destroy(&node->lock);
            free(node->path);
            // We are not freeing the node itself or removing from hsearch to avoid complexity
            // This is a memory leak, but simplifies the logic for this example.
        }
    }
    
    pthread_mutex_unlock(&global_config.htab_lock);
}

// 文件扩展名白名单
static const char *encrypted_extensions[] = {
    ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx",
    ".pdf", ".txt", ".csv", ".rtf", ".zip", ".rar",
    ".jpg", ".jpeg", ".png", ".gif", ".dwg", ".dxf",
    ".psd", ".ai", ".java", ".c", ".cpp", ".py", ".js",
    NULL  // 终止标记
};

/*
 * 辅助函数: 检查文件是否应该被加密
 */
static int should_encrypt_file(const char *path)
{
    // 首先检查策略
    if (!policy_should_encrypt_file(path)) {
        return 0;
    }
    
    // 检查扩展名白名单
    const char *dot = strrchr(path, '.');
    if (!dot) {
        return 0; // 没有扩展名
    }
    
    for (int i = 0; encrypted_extensions[i] != NULL; i++) {
        if (strcasecmp(dot, encrypted_extensions[i]) == 0) {
            return 1;
        }
    }
    
    return 0;
}

/*
 * 辅助函数: 获取真实文件路径
 */
static void get_real_path(char *real_path, const char *path)
{
    strcpy(real_path, global_config.source_dir);
    strcat(real_path, path);
}

/*
 * 辅助函数: 检查文件是否已加密
 */
static int is_encrypted_file(const char *real_path)
{
    FILE *file = fopen(real_path, "rb");
    if (!file) {
        return 0;
    }
    
    unsigned char signature[4];
    size_t read_size = fread(signature, 1, 4, file);
    fclose(file);
    
    if (read_size != 4) {
        return 0;
    }
    
    return memcmp(signature, ENC_SIGNATURE, 4) == 0;
}

/*
 * FUSE回调: 获取文件属性
 */
static int encfs_getattr(const char *path, struct stat *stbuf)
{
    char real_path[PATH_MAX];
    get_real_path(real_path, path);
    
    int res = lstat(real_path, stbuf);
    if (res == -1) {
        return -errno;
    }
    
    // 如果是文件并且已加密, 减去文件头大小获得原始大小
    if (S_ISREG(stbuf->st_mode) && is_encrypted_file(real_path)) {
        // 打开文件读取文件头中的原始大小信息
        FILE *file = fopen(real_path, "rb");
        if (file) {
            enc_file_header header;
            if (fread(&header, sizeof(enc_file_header), 1, file) == 1) {
                stbuf->st_size = header.original_file_size;
            }
            fclose(file);
        }
    }
    
    return 0;
}

/*
 * FUSE回调: 读取目录
 */
static int encfs_readdir(const char *path, void *buf, fuse_fill_dir_t filler,
                         off_t offset, struct fuse_file_info *fi)
{
    (void) offset;
    (void) fi;
    
    char real_path[PATH_MAX];
    get_real_path(real_path, path);
    
    DIR *dp = opendir(real_path);
    if (!dp) {
        return -errno;
    }
    
    struct dirent *de;
    while ((de = readdir(dp)) != NULL) {
        struct stat st;
        memset(&st, 0, sizeof(st));
        st.st_ino = de->d_ino;
        st.st_mode = de->d_type << 12;
        
        if (filler(buf, de->d_name, &st, 0)) {
            break;
        }
    }
    
    closedir(dp);
    return 0;
}

/*
 * FUSE回调: 打开文件
 */
static int encfs_open(const char *path, struct fuse_file_info *fi)
{
    char real_path[PATH_MAX];
    get_real_path(real_path, path);
    
    int fd = open(real_path, fi->flags);
    if (fd == -1) {
        return -errno;
    }
    
    fi->fh = fd;
    return 0;
}

/*
 * FUSE回调: 读取文件
 */
static int encfs_read(const char *path, char *buf, size_t size, off_t offset,
                      struct fuse_file_info *fi)
{
    char real_path[PATH_MAX];
    get_real_path(real_path, path);
    
    // 检查文件是否已加密
    int is_encrypted = is_encrypted_file(real_path);
    if (!is_encrypted) {
        // 非加密文件，直接读取
        int res = pread(fi->fh, buf, size, offset);
        if (res == -1) {
            return -errno;
        }
        return res;
    }
    
    // 加密文件，需要解密
    file_lock_node *lock_node = get_or_create_file_lock(path);
    if (!lock_node) return -ENOMEM;
    
    pthread_rwlock_rdlock(&lock_node->lock);
    
    // 从文件中读取加密数据
    FILE *file = fdopen(dup(fi->fh), "rb");
    if (!file) {
        pthread_rwlock_unlock(&lock_node->lock);
        release_file_lock(path); // Must release lock if we created it
        return -errno;
    }
    
    // 读取文件头
    enc_file_header header;
    if (fread(&header, sizeof(enc_file_header), 1, file) != 1) {
        fclose(file);
        pthread_rwlock_unlock(&lock_node->lock);
        release_file_lock(path);
        return -EIO;
    }
    
    // 验证文件头
    if (memcmp(header.signature, ENC_SIGNATURE, 4) != 0) {
        fclose(file);
        pthread_rwlock_unlock(&lock_node->lock);
        release_file_lock(path);
        return -EIO;
    }
    
    // 计算加密文件中的偏移量 (加上文件头大小)
    off_t crypto_offset = offset + sizeof(enc_file_header);
    
    // 分配加密数据缓冲区
    char *encrypted_buf = malloc(size);
    if (!encrypted_buf) {
        fclose(file);
        pthread_rwlock_unlock(&lock_node->lock);
        release_file_lock(path);
        return -ENOMEM;
    }
    
    // 定位到正确的偏移
    if (fseek(file, crypto_offset, SEEK_SET) == -1) {
        free(encrypted_buf);
        fclose(file);
        pthread_rwlock_unlock(&lock_node->lock);
        release_file_lock(path);
        return -errno;
    }
    
    // 读取加密数据
    size_t read_size = fread(encrypted_buf, 1, size, file);
    fclose(file);
    
    // 解密
    int decrypted_size = size;
    int ret = crypto_manager_decrypt(encrypted_buf, read_size, buf, &decrypted_size, header.iv, header.key_version);
    
    free(encrypted_buf);
    
    if (ret != 0) {
        pthread_rwlock_unlock(&lock_node->lock);
        release_file_lock(path);
        return -EIO; // 解密失败
    }
    
    pthread_rwlock_unlock(&lock_node->lock);
    release_file_lock(path);
    
    return decrypted_size;
}

/*
 * FUSE回调: 写入文件
 */
static int encfs_write(const char *path, const char *buf, size_t size,
                       off_t offset, struct fuse_file_info *fi)
{
    char real_path[PATH_MAX];
    get_real_path(real_path, path);

    // 如果文件不满足加密条件，则直接写入
    if (!should_encrypt_file(path) && !is_encrypted_file(real_path)) {
        int res = pwrite(fi->fh, buf, size, offset);
        if (res == -1) {
            return -errno;
        }
        return res;
    }

    file_lock_node *lock_node = get_or_create_file_lock(path);
    if (!lock_node) return -ENOMEM;

    pthread_rwlock_wrlock(&lock_node->lock);

    // 加密并写入的复杂逻辑...
    // 为了简化，我们先从临时文件开始
    char temp_path[PATH_MAX];
    sprintf(temp_path, "%s.tmp", real_path);

    int temp_fd = open(temp_path, O_WRONLY | O_CREAT | O_TRUNC, 0644);
    if (temp_fd == -1) {
        pthread_rwlock_unlock(&lock_node->lock);
        release_file_lock(path);
        return -errno;
    }

    // 加密数据
    size_t encrypted_size = size + 256; // 预留足够空间给IV, tag等
    char *encrypted_buf = malloc(encrypted_size);
    if (!encrypted_buf) {
        close(temp_fd);
        unlink(temp_path);
        pthread_rwlock_unlock(&lock_node->lock);
        release_file_lock(path);
        return -ENOMEM;
    }

    unsigned char iv[16];
    // 使用crypto_manager生成IV
    int ret = crypto_manager_encrypt(buf, size, encrypted_buf, (int*)&encrypted_size, iv, 0); // 假设key version 0

    if (ret != 0) {
        free(encrypted_buf);
        close(temp_fd);
        unlink(temp_path);
        pthread_rwlock_unlock(&lock_node->lock);
        release_file_lock(path);
        return -EIO;
    }

    // 创建文件头
    enc_file_header header;
    memcpy(header.signature, ENC_SIGNATURE, 4);
    header.version = 1;
    header.key_version = 0; // 示例
    header.original_file_size = size + offset; // 假设从头开始写
    memcpy(header.iv, iv, 16);
    // ...填充其他header字段

    // 写入文件头和加密数据到临时文件
    if (write(temp_fd, &header, sizeof(header)) != sizeof(header)) {
        // 错误处理
    }
    if (write(temp_fd, encrypted_buf, encrypted_size) != encrypted_size) {
        // 错误处理
    }
    
    free(encrypted_buf);
    close(temp_fd);

    // 原子地替换原文件
    if (rename(temp_path, real_path) == -1) {
        // 错误处理
        unlink(temp_path);
        pthread_rwlock_unlock(&lock_node->lock);
        release_file_lock(path);
        return -errno;
    }

    pthread_rwlock_unlock(&lock_node->lock);
    release_file_lock(path);
    return size;
}

/*
 * FUSE回调: 创建文件
 */
static int encfs_create(const char *path, mode_t mode, struct fuse_file_info *fi)
{
    char real_path[PATH_MAX];
    get_real_path(real_path, path);
    
    int fd = open(real_path, fi->flags, mode);
    if (fd == -1) {
        return -errno;
    }
    
    fi->fh = fd;
    return 0;
}

/*
 * FUSE回调: 截断文件
 */
static int encfs_truncate(const char *path, off_t size)
{
    char real_path[PATH_MAX];
    get_real_path(real_path, path);

    // 如果是加密文件，这个操作很复杂，需要重新加密
    // 为简单起见，如果文件已加密，我们暂时禁止这个操作
    if (is_encrypted_file(real_path)) {
        // A proper implementation would need to read, decrypt, truncate, and re-encrypt.
        // This is very complex. For now, we can either disallow it or
        // simply truncate the underlying file, which will corrupt it.
        // Disallowing is safer.
        return -EPERM; // Operation not permitted on encrypted files for now.
    }

    // 对于非加密文件，直接截断
    int res = truncate(real_path, size);
    if (res == -1) {
        return -errno;
    }

    return 0;
}

/*
 * FUSE回调: 关闭文件
 */
static int encfs_release(const char *path, struct fuse_file_info *fi)
{
    close(fi->fh);
    return 0;
}

/*
 * FUSE回调: 创建目录
 */
static int encfs_mkdir(const char *path, mode_t mode)
{
    char real_path[PATH_MAX];
    get_real_path(real_path, path);
    
    int res = mkdir(real_path, mode);
    if (res == -1) {
        return -errno;
    }
    
    return 0;
}

/*
 * FUSE回调: 删除文件
 */
static int encfs_unlink(const char *path)
{
    char real_path[PATH_MAX];
    get_real_path(real_path, path);
    
    int res = unlink(real_path);
    if (res == -1) {
        return -errno;
    }
    
    return 0;
}

/*
 * FUSE回调: 删除目录
 */
static int encfs_rmdir(const char *path)
{
    char real_path[PATH_MAX];
    get_real_path(real_path, path);
    
    int res = rmdir(real_path);
    if (res == -1) {
        return -errno;
    }
    
    return 0;
}

/*
 * FUSE回调: 重命名文件
 */
static int encfs_rename(const char *from, const char *to)
{
    char real_from[PATH_MAX];
    char real_to[PATH_MAX];
    
    get_real_path(real_from, from);
    get_real_path(real_to, to);
    
    int res = rename(real_from, real_to);
    if (res == -1) {
        return -errno;
    }
    
    return 0;
}

/*
 * FUSE回调: 修改文件权限
 */
static int encfs_chmod(const char *path, mode_t mode)
{
    char real_path[PATH_MAX];
    get_real_path(real_path, path);
    
    int res = chmod(real_path, mode);
    if (res == -1) {
        return -errno;
    }
    
    return 0;
}

/*
 * FUSE回调: 修改文件所有者
 */
static int encfs_chown(const char *path, uid_t uid, gid_t gid)
{
    char real_path[PATH_MAX];
    get_real_path(real_path, path);
    
    int res = chown(real_path, uid, gid);
    if (res == -1) {
        return -errno;
    }
    
    return 0;
}

/*
 * FUSE回调: 获取文件系统信息
 */
static int encfs_statfs(const char *path, struct statvfs *stbuf)
{
    char real_path[PATH_MAX];
    get_real_path(real_path, path);
    
    int res = statvfs(real_path, stbuf);
    if (res == -1) {
        return -errno;
    }
    
    return 0;
}

// FUSE操作函数表
static struct fuse_operations encfs_oper = {
    .getattr    = encfs_getattr,
    .readdir    = encfs_readdir,
    .open       = encfs_open,
    .read       = encfs_read,
    .write      = encfs_write,
    .create     = encfs_create,
    .truncate   = encfs_truncate,
    .release    = encfs_release,
    .mkdir      = encfs_mkdir,
    .unlink     = encfs_unlink,
    .rmdir      = encfs_rmdir,
    .rename     = encfs_rename,
    .chmod      = encfs_chmod,
    .chown      = encfs_chown,
    .statfs     = encfs_statfs,
};

/*
 * 显示使用帮助
 */
static void show_help(const char *progname)
{
    printf("使用方法: %s [选项] <源目录> <挂载点>\n\n", progname);
    printf("Cryptosystem Linux 客户端FUSE文件系统\n\n");
    printf("选项:\n");
    printf("    -h, --help            显示此帮助\n");
    printf("    -d, --debug           启用调试输出\n");
    printf("    -f                    前台运行\n");
    printf("\n");
}

/*
 * 主函数
 */
int main(int argc, char *argv[])
{
    int ret;
    struct fuse_args args = FUSE_ARGS_INIT(argc, argv);
    
    // 初始化全局配置
    memset(&global_config, 0, sizeof(global_config));
    global_config.source_dir = "/tmp/source"; // 默认源目录
    global_config.mount_point = "/tmp/mount";  // 默认挂载点
    
    // TODO: 解析命令行参数以设置 source_dir 和 mount_point
    
    // 初始化锁机制
    pthread_mutex_init(&global_config.htab_lock, NULL);
    if (hcreate_r(1024, &global_config.file_locks_htab) == 0) {
        perror("hcreate_r failed");
        return 1;
    }
    
    // 初始化加密模块
    crypto_init();
    
    // 初始化策略模块
    policy_init();
    
    // 准备FUSE参数
    fuse_opt_add_arg(&args, argv[0]);
    
    // 处理参数
    int foreground = 0;
    int i;
    for (i = 1; i < argc; i++) {
        if (strcmp(argv[i], "-h") == 0 || strcmp(argv[i], "--help") == 0) {
            show_help(argv[0]);
            fuse_opt_free_args(&args);
            return 0;
        } else if (strcmp(argv[i], "-d") == 0 || strcmp(argv[i], "--debug") == 0) {
            global_config.debug_mode = 1;
            fuse_opt_add_arg(&args, "-d");
        } else if (strcmp(argv[i], "-f") == 0) {
            foreground = 1;
            fuse_opt_add_arg(&args, "-f");
        } else if (global_config.source_dir == NULL) {
            global_config.source_dir = strdup(argv[i]);
        } else if (global_config.mount_point == NULL) {
            global_config.mount_point = strdup(argv[i]);
            fuse_opt_add_arg(&args, argv[i]);
        } else {
            fprintf(stderr, "未知参数: %s\n", argv[i]);
            show_help(argv[0]);
            fuse_opt_free_args(&args);
            return 1;
        }
    }
    
    // 验证必要参数
    if (global_config.source_dir == NULL || global_config.mount_point == NULL) {
        fprintf(stderr, "错误: 必须指定源目录和挂载点\n");
        show_help(argv[0]);
        fuse_opt_free_args(&args);
        return 1;
    }
    
    // 添加FUSE选项
    fuse_opt_add_arg(&args, "-o");
    fuse_opt_add_arg(&args, "default_permissions");
    
    // 运行FUSE
    ret = fuse_main(args.argc, args.argv, &encfs_oper, NULL);
    
    // 清理
    fuse_opt_free_args(&args);
    hdestroy_r(&global_config.file_locks_htab);
    pthread_mutex_destroy(&global_config.htab_lock);
    
    // 清理加密和策略模块
    crypto_cleanup();
    policy_cleanup();
    
    return ret;
} 