import { KeyGenerationRequest, KeyInfo } from '../../model/KeyModels';

/**
 * 密钥生成服务接口
 */
export interface IKeyGenerationService {
  /**
   * 生成主密钥
   */
  generateMasterKeyAsync(request: KeyGenerationRequest): Promise<KeyInfo>;

  /**
   * 生成工作密钥
   */
  generateWorkKeyAsync(request: KeyGenerationRequest): Promise<KeyInfo>;

  /**
   * 验证密钥
   */
  validateKeyAsync(keyId: string): Promise<boolean>;

  /**
   * 销毁密钥
   */
  destroyKeyAsync(keyId: string): Promise<boolean>;
}
