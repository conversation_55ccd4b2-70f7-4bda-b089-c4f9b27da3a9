# 鸿蒙密钥生成器 (HarmonyOS Key Generator)

企业文档加密系统的鸿蒙平台密钥生成工具，提供安全、高效的密钥管理和分发功能。

## 功能特性

### 核心功能

- **密钥生成**: 生成各种类型的主密钥和会话密钥
- **密钥管理**: 查看、编辑、删除和搜索密钥
- **客户端管理**: 管理已注册的客户端设备信息
- **密钥分发**: 将生成的密钥安全地分发给客户端
- **审计日志**: 记录所有密钥操作和系统事件
- **系统设置**: 配置应用程序的行为和安全选项

### 安全特性

- **密钥加密存储**: 密钥数据使用强加密算法存储
- **访问控制**: 基于用户角色的权限管理
- **事件审计**: 完整的操作日志，支持追溯
- **加密算法**: 支持国密SM4等标准加密算法

## 技术架构

### 开发框架

- **开发语言**: ArkTS/ArkUI
- **系统版本**: HarmonyOS 4.0+
- **数据存储**: HarmonyOS Preferences, SQLite (待集成)

### 核心组件

#### 视图层 (Views)

- `Index`: 主界面，包含底部导航栏和主要内容区域
- `ClientManagementView`: 客户端管理界面
- `KeyDistributionView`: 密钥分发界面
- `AuditLogView`: 审计日志界面
- `SettingsView`: 系统设置界面

#### 视图模型层 (ViewModels)

- `KeyGenerationViewModel`: 密钥生成相关逻辑
- `KeyManagementViewModel`: 密钥管理相关逻辑
- `ClientManagementViewModel`: 客户端管理业务逻辑
- `KeyDistributionViewModel`: 密钥分发业务逻辑
- `AuditLogViewModel`: 审计日志业务逻辑
- `SettingsViewModel`: 系统设置业务逻辑

#### 服务层 (Services)

- `KeyGenerationService`: 密钥生成服务
- `KeyManagementService`: 密钥管理服务
- `ClientService`: 客户端管理服务
- `KeyDistributionService`: 密钥分发服务
- `AuditService`: 审计服务
- `ConfigService`: 配置服务
- `DatabaseManager`: 数据库管理服务 (待集成)

## 项目结构

```
harmonyos/
├── module.json5                    # 模块配置
├── package.json                   # 项目配置
├── README.md                      # 本文档
└── src/main/
    ├── ets/
    │   ├── entryability/
    │   │   └── EntryAbility.ets    # 主入口能力
    │   ├── models/
    │   │   └── KeyModels.ts           # 密钥和客户端数据模型
    │   ├── pages/
    │   │   ├── Index.ets                  # 主界面
    │   │   ├── ClientManagementView.ets   # 客户端管理界面
    │   │   ├── KeyDistributionView.ets    # 密钥分发界面
    │   │   ├── AuditLogView.ets           # 审计日志界面
    │   │   └── SettingsView.ets           # 系统设置界面
    │   ├── services/
    │   │   ├── IKeyGenerationService.ts   # 密钥生成服务接口
    │   │   ├── KeyGenerationService.ts    # 密钥生成服务实现
    │   │   ├── ClientService.ts           # 客户端服务
    │   │   ├── KeyDistributionService.ts  # 密钥分发服务
    │   │   ├── AuditService.ts            # 审计服务
    │   │   └── ConfigService.ts           # 配置服务
    │   └── viewmodels/
    │       ├── KeyGenerationViewModel.ts  # 密钥生成视图模型
    │       ├── KeyManagementViewModel.ts  # 密钥管理视图模型
    │       ├── ClientManagementViewModel.ts # 客户端管理视图模型
    │       ├── KeyDistributionViewModel.ts  # 密钥分发视图模型
    │       ├── AuditLogViewModel.ts       # 审计日志视图模型
    │       └── SettingsViewModel.ts       # 系统设置视图模型
    └── resources/
        └── base/profile/
            └── main_pages.json            # 页面配置
```

## 开发环境

### 前置要求

- DevEco Studio 4.0+
- HarmonyOS SDK API 10+
- Node.js 16+

### 环境配置

1. 安装DevEco Studio
2. 配置HarmonyOS SDK
3. 创建签名证书
4. 配置设备连接

## 构建与部署

### 开发构建

```bash
# 安装依赖
npm install

# 构建项目
npm run build

# 清理项目
npm run clean
```

### 生产部署

```bash
# 构建HAP包
hvigor assembleHap --mode module -p product=default

# 安装到设备
hdc install entry-default-signed.hap
```

## 配置说明

### 权限配置

应用需要以下权限：

- `ohos.permission.INTERNET`: 网络通信权限
- `ohos.permission.GET_DISTRIBUTED_DEVICE_INFO`: 获取分布式设备信息 (用于客户端管理)

### 应用设置

- **服务器地址**: 用于API通信的后端服务地址
- **密钥存储路径**: 密钥文件存储的本地路径

## 安全注意事项

### 密钥安全

- 生成的密钥在内存中加密处理
- 分发过程采用端到端加密

### 访问安全

- API请求进行身份验证和授权
- 敏感操作均记录在审计日志中

### 数据完整性

- 传输数据通过哈希校验确保完整性

## 故障排除

### 常见问题

1. **服务无法启动**
    - 检查权限配置是否正确
    - 确认HarmonyOS版本兼容性
    - 查看系统日志获取详细错误信息

2. **密钥分发失败**
    - 检查网络连接状态
    - 确认客户端设备在线且可访问
    - 验证密钥信息是否完整且有效

3. **界面显示异常**
    - 尝试重启应用或设备
    - 检查DevEco Studio控制台输出是否有报错

### 日志调试

```typescript
// 启用详细日志
hilog.info(0x0000, 'KeyGenerator', '调试信息');
hilog.error(0x0000, 'KeyGenerator', '错误信息: %{public}s', error.message);
```

## 版本历史

### v1.0.0 (2025-01-20)

- 初始版本发布
- 基础密钥生成和管理功能
- 鸿蒙原生界面

### v1.1.0 (2025-07-26)

- 扩展客户端管理功能
- 新增密钥分发模块
- 引入审计日志功能
- 增加系统设置页面 