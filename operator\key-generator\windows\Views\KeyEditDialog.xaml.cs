using System;
using System.Windows;
using KeyGenerator.Models;

namespace KeyGenerator.Views
{
    /// <summary>
    /// 密钥编辑对话框
    /// </summary>
    public partial class KeyEditDialog : Window
    {
        private readonly MasterKeyEntity _originalKey;
        public MasterKeyEntity? EditedKey { get; private set; }

        public KeyEditDialog(MasterKeyEntity key)
        {
            InitializeComponent();
            
            _originalKey = key ?? throw new ArgumentNullException(nameof(key));
            
            // 创建编辑副本
            EditedKey = new MasterKeyEntity
            {
                KeyId = key.KeyId,
                KeyName = key.KeyName,
                ClientName = key.ClientName,
                ContactInfo = key.ContactInfo,
                EffectiveDate = key.EffectiveDate,
                ExpirationDate = key.ExpirationDate,
                Remarks = key.Remarks,
                CreatedTime = key.CreatedTime,
                UpdatedTime = key.UpdatedTime, // 将 LastModified 改为 UpdatedTime
                Status = key.Status,
                KeyData = key.KeyData,
                UsagePolicy = key.UsagePolicy, // 确保 UsagePolicy 被赋值
            };
            
            DataContext = EditedKey;
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 验证输入
                if (!ValidateInput())
                {
                    return;
                }

                // 更新编辑后的密钥信息
                EditedKey!.ClientName = ClientNameTextBox.Text.Trim();
                EditedKey.ContactInfo = ContactInfoTextBox.Text.Trim();
                EditedKey.ExpirationDate = ExpirationDatePicker.SelectedDate ?? EditedKey.ExpirationDate;
                EditedKey.Remarks = RemarksTextBox.Text.Trim();

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存时发生错误：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            EditedKey = null;
            DialogResult = false;
            Close();
        }

        private bool ValidateInput()
        {
            // 验证客户名称
            if (string.IsNullOrWhiteSpace(ClientNameTextBox.Text))
            {
                MessageBox.Show("请输入客户名称", "验证失败", MessageBoxButton.OK, MessageBoxImage.Warning);
                ClientNameTextBox.Focus();
                return false;
            }

            // 验证过期日期
            if (ExpirationDatePicker.SelectedDate == null)
            {
                MessageBox.Show("请选择过期日期", "验证失败", MessageBoxButton.OK, MessageBoxImage.Warning);
                ExpirationDatePicker.Focus();
                return false;
            }

            // 验证过期日期不能早于生效日期
            if (ExpirationDatePicker.SelectedDate <= EditedKey!.EffectiveDate)
            {
                MessageBox.Show("过期日期必须晚于生效日期", "验证失败", MessageBoxButton.OK, MessageBoxImage.Warning);
                ExpirationDatePicker.Focus();
                return false;
            }

            // 验证过期日期不能早于当前日期
            if (ExpirationDatePicker.SelectedDate <= DateTime.Now.Date)
            {
                MessageBox.Show("过期日期不能早于当前日期", "验证失败", MessageBoxButton.OK, MessageBoxImage.Warning);
                ExpirationDatePicker.Focus();
                return false;
            }

            return true;
        }
    }
} 