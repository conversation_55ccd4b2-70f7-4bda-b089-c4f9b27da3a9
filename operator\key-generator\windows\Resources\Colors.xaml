<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- 主要颜色 -->
    <SolidColorBrush x:Key="PrimaryBrush" Color="#2563EB"/>
    <SolidColorBrush x:Key="PrimaryHoverBrush" Color="#1D4ED8"/>
    <SolidColorBrush x:Key="PrimaryPressedBrush" Color="#1E40AF"/>
    <SolidColorBrush x:Key="PrimaryLightBrush" Color="#DBEAFE"/>

    <!-- 次要颜色 -->
    <SolidColorBrush x:Key="SecondaryBrush" Color="#64748B"/>
    <SolidColorBrush x:Key="SecondaryHoverBrush" Color="#475569"/>
    <SolidColorBrush x:Key="SecondaryLightBrush" Color="#F1F5F9"/>

    <!-- 成功状态 -->
    <SolidColorBrush x:Key="SuccessBrush" Color="#10B981"/>
    <SolidColorBrush x:Key="SuccessHoverBrush" Color="#059669"/>
    <SolidColorBrush x:Key="SuccessLightBrush" Color="#D1FAE5"/>

    <!-- 警告状态 -->
    <SolidColorBrush x:Key="WarningBrush" Color="#F59E0B"/>
    <SolidColorBrush x:Key="WarningHoverBrush" Color="#D97706"/>
    <SolidColorBrush x:Key="WarningLightBrush" Color="#FEF3C7"/>

    <!-- 错误状态 -->
    <SolidColorBrush x:Key="ErrorBrush" Color="#EF4444"/>
    <SolidColorBrush x:Key="ErrorHoverBrush" Color="#DC2626"/>
    <SolidColorBrush x:Key="ErrorLightBrush" Color="#FEE2E2"/>

    <!-- 信息状态 -->
    <SolidColorBrush x:Key="InfoBrush" Color="#3B82F6"/>
    <SolidColorBrush x:Key="InfoHoverBrush" Color="#2563EB"/>
    <SolidColorBrush x:Key="InfoLightBrush" Color="#DBEAFE"/>

    <!-- 背景颜色 -->
    <SolidColorBrush x:Key="BackgroundBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="SurfaceBrush" Color="#F8FAFC"/>
    <SolidColorBrush x:Key="CardBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="SidebarBrush" Color="#F1F5F9"/>
    <SolidColorBrush x:Key="HeaderBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="ContentBackgroundBrush" Color="#FAFAFA"/>
    <SolidColorBrush x:Key="StatusBarBrush" Color="#F8FAFC"/>

    <!-- 文本颜色 -->
    <SolidColorBrush x:Key="PrimaryTextBrush" Color="#1F2937"/>
    <SolidColorBrush x:Key="SecondaryTextBrush" Color="#6B7280"/>
    <SolidColorBrush x:Key="MutedTextBrush" Color="#9CA3AF"/>
    <SolidColorBrush x:Key="HeaderTextBrush" Color="#111827"/>
    <SolidColorBrush x:Key="StatusTextBrush" Color="#4B5563"/>
    <SolidColorBrush x:Key="LinkTextBrush" Color="#2563EB"/>
    <SolidColorBrush x:Key="WhiteTextBrush" Color="#FFFFFF"/>

    <!-- 边框颜色 -->
    <SolidColorBrush x:Key="BorderBrush" Color="#E5E7EB"/>
    <SolidColorBrush x:Key="BorderLightBrush" Color="#F3F4F6"/>
    <SolidColorBrush x:Key="BorderDarkBrush" Color="#D1D5DB"/>
    <SolidColorBrush x:Key="FocusBorderBrush" Color="#2563EB"/>

    <!-- 输入控件颜色 -->
    <SolidColorBrush x:Key="InputBackgroundBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="InputFocusBackgroundBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="InputDisabledBackgroundBrush" Color="#F9FAFB"/>
    <SolidColorBrush x:Key="InputHoverBrush" Color="#F9FAFB"/>

    <!-- 按钮颜色 -->
    <SolidColorBrush x:Key="ButtonDefaultBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="ButtonHoverBrush" Color="#F9FAFB"/>
    <SolidColorBrush x:Key="ButtonPressedBrush" Color="#F3F4F6"/>
    <SolidColorBrush x:Key="ButtonDisabledBrush" Color="#F9FAFB"/>

    <!-- 导航按钮颜色 -->
    <SolidColorBrush x:Key="HoverBrush" Color="#E2E8F0"/>
    <SolidColorBrush x:Key="PressedBrush" Color="#CBD5E1"/>
    <SolidColorBrush x:Key="ActiveButtonBrush" Color="#E0E7FF"/>

    <!-- 数据网格颜色 -->
    <SolidColorBrush x:Key="DataGridHeaderBrush" Color="#F8FAFC"/>
    <SolidColorBrush x:Key="DataGridAlternatingRowBrush" Color="#F9FAFB"/>
    <SolidColorBrush x:Key="DataGridSelectedBrush" Color="#EFF6FF"/>
    <SolidColorBrush x:Key="DataGridHoverBrush" Color="#F0F9FF"/>

    <!-- 阴影颜色 -->
    <Color x:Key="ShadowColor">#40000000</Color>
    <Color x:Key="LightShadowColor">#20000000</Color>

    <!-- 信息面板和附加颜色 -->
    <SolidColorBrush x:Key="InfoPanelBrush" Color="#F8FAFC"/>
    <SolidColorBrush x:Key="TertiaryTextBrush" Color="#9CA3AF"/>
    <SolidColorBrush x:Key="WarningTextBrush" Color="#92400E"/>

    <!-- 渐变画刷 -->
    <LinearGradientBrush x:Key="HeaderGradientBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#FFFFFF" Offset="0"/>
        <GradientStop Color="#F8FAFC" Offset="1"/>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="ButtonGradientBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#FFFFFF" Offset="0"/>
        <GradientStop Color="#F9FAFB" Offset="1"/>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="PrimaryGradientBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#3B82F6" Offset="0"/>
        <GradientStop Color="#2563EB" Offset="1"/>
    </LinearGradientBrush>

    <!-- 国密算法专用颜色 -->
    <SolidColorBrush x:Key="NationalCryptoBrush" Color="#FEE2E2"/>
    <SolidColorBrush x:Key="NationalCryptoLightBrush" Color="#FEE2E2"/>
    <SolidColorBrush x:Key="NationalCryptoTextBrush" Color="#991B1B"/>

    <!-- 密钥状态颜色 -->
    <SolidColorBrush x:Key="KeyActiveBrush" Color="#10B981"/>
    <SolidColorBrush x:Key="KeyPendingBrush" Color="#F59E0B"/>
    <SolidColorBrush x:Key="KeyExpiredBrush" Color="#EF4444"/>
    <SolidColorBrush x:Key="KeyRevokedBrush" Color="#6B7280"/>
    <SolidColorBrush x:Key="KeySuspendedBrush" Color="#F97316"/>

    <!-- 透明度变体 -->
    <SolidColorBrush x:Key="OverlayBrush" Color="#80000000"/>
    <SolidColorBrush x:Key="LightOverlayBrush" Color="#40FFFFFF"/>
    <SolidColorBrush x:Key="TransparentBrush" Color="Transparent"/>

</ResourceDictionary> 