/*
 * utils.h
 *
 * Linux版本工具函数定义
 */

#ifndef UTILS_H
#define UTILS_H

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <errno.h>
#include <syslog.h>

// 路径最大长度
#ifndef PATH_MAX
#define PATH_MAX 4096
#endif

// 日志级别
typedef enum {
    LOG_LEVEL_ERROR = 0,
    LOG_LEVEL_WARN = 1,
    LOG_LEVEL_INFO = 2,
    LOG_LEVEL_DEBUG = 3
} log_level_t;

// 错误码定义
#define CRYPTO_SUCCESS          0
#define CRYPTO_ERROR_INVALID    -1
#define CRYPTO_ERROR_MEMORY     -2
#define CRYPTO_ERROR_CRYPTO     -3
#define CRYPTO_ERROR_IO         -4

// 调试宏
#ifdef DEBUG
#define DEBUG_PRINT(fmt, ...) \
    fprintf(stderr, "[DEBUG] %s:%d: " fmt "\n", __FILE__, __LINE__, ##__VA_ARGS__)
#else
#define DEBUG_PRINT(fmt, ...) do {} while(0)
#endif

// 错误日志宏
#define ERROR_PRINT(fmt, ...) \
    fprintf(stderr, "[ERROR] %s:%d: " fmt "\n", __FILE__, __LINE__, ##__VA_ARGS__)

// 信息日志宏
#define INFO_PRINT(fmt, ...) \
    fprintf(stdout, "[INFO] " fmt "\n", ##__VA_ARGS__)

// 警告日志宏
#define WARN_PRINT(fmt, ...) \
    fprintf(stderr, "[WARN] " fmt "\n", ##__VA_ARGS__)

// 函数声明

/**
 * 计算CRC32校验和
 */
uint32_t calculate_crc32(const void *data, size_t length);

/**
 * 安全的字符串复制
 */
int safe_strcpy(char *dest, size_t dest_size, const char *src);

/**
 * 安全的字符串连接
 */
int safe_strcat(char *dest, size_t dest_size, const char *src);

/**
 * 检查路径是否安全（防止路径遍历攻击）
 */
int is_safe_path(const char *path);

/**
 * 获取文件扩展名
 */
const char* get_file_extension(const char *filename);

/**
 * 创建目录（递归创建）
 */
int create_directory_recursive(const char *path, mode_t mode);

/**
 * 获取文件大小
 */
long get_file_size(const char *filepath);

/**
 * 检查文件是否存在
 */
int file_exists(const char *filepath);

/**
 * 生成随机字节
 */
int generate_random_bytes(uint8_t *buffer, size_t length);

/**
 * 十六进制转换函数
 */
void bytes_to_hex(const uint8_t *bytes, size_t length, char *hex_string);
int hex_to_bytes(const char *hex_string, uint8_t *bytes, size_t max_length);

/**
 * 内存安全清理
 */
void secure_memzero(void *ptr, size_t length);

#endif // UTILS_H 