using CommunityToolkit.Mvvm.ComponentModel;

namespace KeyGenerator.ViewModels;

/// <summary>
/// ViewModel 基类，提供 INotifyPropertyChanged 实现
/// </summary>
public abstract partial class ViewModelBase : ObservableObject
{
    // CommunityToolkit.Mvvm 的 ObservableObject 已经实现了 INotifyPropertyChanged。
    // 手动实现的 PropertyChanged、OnPropertyChanged 和 SetProperty 方法可以被移除。
    // 继承的 ViewModel 将使用 ObservableObject 提供的 SetProperty 方法。
} 