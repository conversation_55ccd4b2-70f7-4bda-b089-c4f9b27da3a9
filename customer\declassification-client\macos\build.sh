#!/bin/bash

# ==============================================================================
# Build Script for macOS Declassification Client
#
# This script automates the build process for the Xcode project.
# It cleans and builds the project for the Release configuration.
#
# Usage:
# ./build.sh
# ==============================================================================

# --- Configuration ---
PROJECT_NAME="DeclassificationClient"
PROJECT_FILE="${PROJECT_NAME}.xcodeproj"
SCHEME_NAME="${PROJECT_NAME}"
CONFIGURATION="Release"
BUILD_DIR="build"

# --- Functions ---

# Function to print colored output
print_info() {
    echo -e "\033[1;34m[INFO]\033[0m $1"
}

print_success() {
    echo -e "\033[1;32m[SUCCESS]\033[0m $1"
}

print_error() {
    echo -e "\033[1;31m[ERROR]\033[0m $1" >&2
}

# --- Main Script ---

# Navigate to the script's directory to ensure correct relative paths
cd "$(dirname "$0")"

# 1. Check for prerequisites
if ! command -v xcodebuild &> /dev/null; then
    print_error "xcodebuild could not be found. Please ensure Xcode Command Line Tools are installed."
    exit 1
fi

if [ ! -d "$PROJECT_FILE" ]; then
    print_error "Project file not found: $PROJECT_FILE. Make sure you are in the correct directory."
    exit 1
fi

print_info "Starting build process for $PROJECT_NAME..."

# 2. Clean the project
print_info "Cleaning the project (Configuration: $CONFIGURATION)..."
xcodebuild clean \
    -project "$PROJECT_FILE" \
    -scheme "$SCHEME_NAME" \
    -configuration "$CONFIGURATION" \
    -derivedDataPath "$BUILD_DIR"
if [ $? -ne 0 ]; then
    print_error "Clean failed. Aborting."
    exit 1
fi
print_success "Project cleaned successfully."

# 3. Build the project
print_info "Building the project (Configuration: $CONFIGURATION)..."
# The archive path is a temporary location for the build artifacts
ARCHIVE_PATH="$BUILD_DIR/Archives/${PROJECT_NAME}.xcarchive"

xcodebuild archive \
    -project "$PROJECT_FILE" \
    -scheme "$SCHEME_NAME" \
    -configuration "$CONFIGURATION" \
    -archivePath "$ARCHIVE_PATH" \
    -derivedDataPath "$BUILD_DIR" \
    SKIP_INSTALL=NO
    # BUILD_LIBRARY_FOR_DISTRIBUTION=YES # Uncomment if creating a distributable library

if [ $? -ne 0 ]; then
    print_error "Build failed. Check the logs above for details."
    exit 1
fi

# The .app bundle is inside the archive
APP_BUNDLE_PATH="$ARCHIVE_PATH/Products/Applications/${PROJECT_NAME}.app"

if [ ! -d "$APP_BUNDLE_PATH" ]; then
    print_error "Application bundle not found at expected path: $APP_BUNDLE_PATH"
    exit 1
fi

# 4. Copy the final artifact
FINAL_OUTPUT_DIR="$BUILD_DIR/$CONFIGURATION"
print_info "Copying application bundle to $FINAL_OUTPUT_DIR..."
mkdir -p "$FINAL_OUTPUT_DIR"
cp -R "$APP_BUNDLE_PATH" "$FINAL_OUTPUT_DIR/"

if [ $? -ne 0 ]; then
    print_error "Failed to copy the application bundle."
    exit 1
fi

print_success "Build finished successfully!"
print_info "The final application is located at: $FINAL_OUTPUT_DIR/${PROJECT_NAME}.app" 