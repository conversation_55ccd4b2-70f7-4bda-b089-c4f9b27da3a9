using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using KeyGenerator.Models;
// using KeyGenerator.Services; - No longer needed directly
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;

namespace KeyGenerator.ViewModels
{
    public partial class SettingsViewModel : ViewModelBase
    {
        private readonly ILogger<SettingsViewModel> _logger;
        private const string AppSettingsFileName = "appsettings.json";

        [ObservableProperty]
        private AppSettings _appSettings;

        [ObservableProperty]
        private string _statusText = "";

        public SettingsViewModel(ILogger<SettingsViewModel> logger)
        {
            _logger = logger;
            _appSettings = new AppSettings();
            LoadSettingsCommand = new AsyncRelayCommand(LoadSettingsAsync);
            SaveSettingsCommand = new AsyncRelayCommand(SaveSettingsAsync);
        }

        public IAsyncRelayCommand LoadSettingsCommand { get; }
        public IAsyncRelayCommand SaveSettingsCommand { get; }

        private async Task LoadSettingsAsync()
        {
            _logger.LogInformation("Loading application settings from {FileName}", AppSettingsFileName);
            StatusText = "正在加载设置...";
            try
            {
                var root = new ConfigurationBuilder()
                    .SetBasePath(Directory.GetCurrentDirectory())
                    .AddJsonFile(AppSettingsFileName, optional: false)
                    .Build();

                var settings = new AppSettings();
                root.Bind(settings);
                AppSettings = settings;
                StatusText = "设置加载完成。";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载设置文件失败");
                StatusText = "加载设置失败！";
            }
            await Task.CompletedTask;
        }

        private async Task SaveSettingsAsync()
        {
            _logger.LogInformation("Saving application settings to {FileName}", AppSettingsFileName);
            StatusText = "正在保存设置...";
            try
            {
                var settingsContent = new
                {
                    ConnectionStrings = AppSettings.ConnectionStrings,
                    Logging = AppSettings.Logging
                };

                var options = new JsonSerializerOptions { WriteIndented = true, PropertyNameCaseInsensitive = true };
                var jsonString = JsonSerializer.Serialize(settingsContent, options);
                
                await File.WriteAllTextAsync(AppSettingsFileName, jsonString);
                StatusText = "设置已成功保存。";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存设置文件失败");
                StatusText = "保存设置失败！";
            }
        }
    }
} 