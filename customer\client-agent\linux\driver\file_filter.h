/*
 * file_filter.h
 *
 * Linux客户端文件过滤器头文件
 * 负责拦截文件操作并实现透明加解密
 */

#ifndef FILE_FILTER_H
#define FILE_FILTER_H

#include <stdint.h>
#include <stdlib.h>
#include <stdbool.h>

// 文件过滤器状态
typedef enum {
    FILTER_STATUS_SUCCESS = 0,
    FILTER_STATUS_ERROR = -1,
    FILTER_STATUS_NOT_INIT = -2,
    FILTER_STATUS_ALREADY_INIT = -3,
    FILTER_STATUS_INVALID_PARAM = -4,
    FILTER_STATUS_PATH_NOT_FOUND = -5,
    FILTER_STATUS_ACCESS_DENIED = -6,
    FILTER_STATUS_ENCRYPTION_ERROR = -7,
    FILTER_STATUS_DECRYPTION_ERROR = -8,
    FILTER_STATUS_NOT_ENCRYPTED = -9,
    FILTER_STATUS_ALREADY_ENCRYPTED = -10,
    FILTER_STATUS_MEMORY_ERROR = -11
} filter_status;

// 文件过滤器操作类型
typedef enum {
    FILTER_OPERATION_READ = 1,
    FILTER_OPERATION_WRITE = 2,
    FILTER_OPERATION_CREATE = 3,
    FILTER_OPERATION_DELETE = 4,
    FILTER_OPERATION_RENAME = 5,
    FILTER_OPERATION_ATTRIBUTE = 6
} filter_operation;

// 文件过滤器选项
typedef struct {
    char *protected_paths[32];     // 受保护的路径列表
    int protected_paths_count;     // 路径数量
    bool encrypt_file_names;       // 是否加密文件名
    bool encrypt_metadata;         // 是否加密元数据
    uint32_t default_key_version;  // 默认密钥版本
    char log_file[256];            // 日志文件路径
    int log_level;                 // 日志级别
} filter_options;

// 过滤器回调函数类型
typedef int (*filter_callback)(filter_operation operation, const char *path, void *context);

/**
 * 初始化文件过滤器
 * 必须在使用任何其他函数前调用
 * 
 * @param options 配置选项，可以为NULL使用默认配置
 * @return FILTER_STATUS_SUCCESS 成功时
 */
filter_status filter_init(const filter_options *options);

/**
 * 清理文件过滤器
 * 应用程序退出前调用以释放资源
 */
void filter_cleanup(void);

/**
 * 注册过滤器回调函数
 * 此回调在文件操作被过滤时触发
 * 
 * @param operation_mask 要监听的操作掩码
 * @param callback 回调函数
 * @param context 回调上下文，将在回调时传递给callback
 * @return FILTER_STATUS_SUCCESS 成功时
 */
filter_status filter_register_callback(
    uint32_t operation_mask,
    filter_callback callback,
    void *context
);

/**
 * 添加受保护的路径
 * 
 * @param path 要保护的路径
 * @param recursive 是否递归保护子目录
 * @return FILTER_STATUS_SUCCESS 成功时
 */
filter_status filter_add_protected_path(const char *path, bool recursive);

/**
 * 移除受保护的路径
 * 
 * @param path 要移除保护的路径
 * @return FILTER_STATUS_SUCCESS 成功时
 */
filter_status filter_remove_protected_path(const char *path);

/**
 * 检查文件是否已加密
 * 
 * @param file_path 文件路径
 * @param is_encrypted 输出参数，true表示已加密
 * @return FILTER_STATUS_SUCCESS 成功时
 */
filter_status filter_is_file_encrypted(const char *file_path, bool *is_encrypted);

/**
 * 手动加密文件
 * 
 * @param file_path 要加密的文件路径
 * @param key_version 密钥版本，0表示使用默认版本
 * @return FILTER_STATUS_SUCCESS 成功时
 */
filter_status filter_encrypt_file(const char *file_path, uint32_t key_version);

/**
 * 手动解密文件
 * 
 * @param file_path 要解密的文件路径
 * @return FILTER_STATUS_SUCCESS 成功时
 */
filter_status filter_decrypt_file(const char *file_path);

/**
 * 设置默认加密密钥版本
 * 
 * @param key_version 密钥版本
 * @return FILTER_STATUS_SUCCESS 成功时
 */
filter_status filter_set_default_key_version(uint32_t key_version);

/**
 * 获取默认加密密钥版本
 * 
 * @param key_version 输出参数，当前密钥版本
 * @return FILTER_STATUS_SUCCESS 成功时
 */
filter_status filter_get_default_key_version(uint32_t *key_version);

/**
 * 设置日志级别
 * 
 * @param level 日志级别 (0-关闭，1-错误，2-警告，3-信息，4-调试)
 * @return FILTER_STATUS_SUCCESS 成功时
 */
filter_status filter_set_log_level(int level);

/**
 * 获取过滤器版本信息
 * 
 * @param version 输出参数，版本字符串缓冲区
 * @param version_size 缓冲区大小
 * @return FILTER_STATUS_SUCCESS 成功时
 */
filter_status filter_get_version(char *version, size_t version_size);

/**
 * 检查文件路径是否受保护
 * 
 * @param file_path 文件路径
 * @param is_protected 输出参数，true表示路径受保护
 * @return FILTER_STATUS_SUCCESS 成功时
 */
filter_status filter_is_path_protected(const char *file_path, bool *is_protected);

#endif /* FILE_FILTER_H */ 