<Application x:Class="CryptoSystem.SystemManager.App"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes" StartupUri="Views/MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design 主题 -->
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Indigo" SecondaryColor="Cyan" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />

                <!-- 自定义样式 -->
                <ResourceDictionary Source="Themes/Generic.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- 全局样式定义 -->
            <Style x:Key="PageHeaderTextStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="24"/>
                <Setter Property="FontWeight" Value="Bold"/>
                <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                <Setter Property="Margin" Value="0,0,0,16"/>
            </Style>

            <Style x:Key="SectionHeaderTextStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="18"/>
                <Setter Property="FontWeight" Value="SemiBold"/>
                <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                <Setter Property="Margin" Value="0,16,0,8"/>
            </Style>

            <Style x:Key="DataGridHeaderStyle" TargetType="DataGridColumnHeader">
                <Setter Property="Background" Value="{DynamicResource MaterialDesignPaper}"/>
                <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                <Setter Property="FontWeight" Value="SemiBold"/>
                <Setter Property="Height" Value="48"/>
                <Setter Property="HorizontalContentAlignment" Value="Left"/>
                <Setter Property="VerticalContentAlignment" Value="Center"/>
                <Setter Property="Padding" Value="16,0"/>
            </Style>

            <!-- 自定义转换器 -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

            <!-- 图标资源 -->
            <DrawingImage x:Key="UserIcon">
                <DrawingImage.Drawing>
                    <DrawingGroup ClipGeometry="M0,0 V24 H24 V0 H0 Z">
                        <GeometryDrawing Brush="{DynamicResource MaterialDesignBody}" Geometry="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z" />
                    </DrawingGroup>
                </DrawingImage.Drawing>
            </DrawingImage>

            <DrawingImage x:Key="DeviceIcon">
                <DrawingImage.Drawing>
                    <DrawingGroup ClipGeometry="M0,0 V24 H24 V0 H0 Z">
                        <GeometryDrawing Brush="{DynamicResource MaterialDesignBody}" Geometry="M4,6H20V16H4M20,18A2,2 0 0,0 22,16V6C22,4.89 21.1,4 20,4H4C2.89,4 2,4.89 2,6V16A2,2 0 0,0 4,18H0V20H24V18H20Z" />
                    </DrawingGroup>
                </DrawingImage.Drawing>
            </DrawingImage>

            <DrawingImage x:Key="PolicyIcon">
                <DrawingImage.Drawing>
                    <DrawingGroup ClipGeometry="M0,0 V24 H24 V0 H0 Z">
                        <GeometryDrawing Brush="{DynamicResource MaterialDesignBody}" Geometry="M17,7H22V17H17V19A1,1 0 0,0 18,20H20V22H17.5C16.95,22 16,21.55 16,21C16,21.55 15.05,22 14.5,22H12V20H14A1,1 0 0,0 15,19V5A1,1 0 0,0 14,4H12V2H14.5C15.05,2 16,2.45 16,3C16,2.45 16.95,2 17.5,2H20V4H18A1,1 0 0,0 17,5V7M2,7H13V9H4V15H13V17H2V7M20,9H17V15H20V9M8.5,12A0.5,0.5 0 0,1 9,12.5A0.5,0.5 0 0,1 8.5,13A0.5,0.5 0 0,1 8,12.5A0.5,0.5 0 0,1 8.5,12Z" />
                    </DrawingGroup>
                </DrawingImage.Drawing>
            </DrawingImage>

            <DrawingImage x:Key="KeyIcon">
                <DrawingImage.Drawing>
                    <DrawingGroup ClipGeometry="M0,0 V24 H24 V0 H0 Z">
                        <GeometryDrawing Brush="{DynamicResource MaterialDesignBody}" Geometry="M7,14A3,3 0 0,1 10,17A3,3 0 0,1 7,20A3,3 0 0,1 4,17A3,3 0 0,1 7,14M7,10A7,7 0 0,1 14,17A7,7 0 0,1 7,24A7,7 0 0,1 0,17A7,7 0 0,1 7,10M21,3V5H23V11H21V7H17V11H15V3H21Z" />
                    </DrawingGroup>
                </DrawingImage.Drawing>
            </DrawingImage>

            <DrawingImage x:Key="AuditIcon">
                <DrawingImage.Drawing>
                    <DrawingGroup ClipGeometry="M0,0 V24 H24 V0 H0 Z">
                        <GeometryDrawing Brush="{DynamicResource MaterialDesignBody}" Geometry="M19,3H5C3.9,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.9 20.1,3 19,3M9,17H7V10H9V17M13,17H11V7H13V17M17,17H15V13H17V17Z" />
                    </DrawingGroup>
                </DrawingImage.Drawing>
            </DrawingImage>
        </ResourceDictionary>
    </Application.Resources>
</Application> 