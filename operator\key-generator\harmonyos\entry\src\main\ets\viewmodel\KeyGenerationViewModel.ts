import hilog from '@ohos.hilog';
import { CryptoAlgorithm, KeyGenerationRequest, KeyInfo, KeyStatus, KeyType } from '../model/KeyModels';

/**
 * 密钥生成ViewModel
 * 专注于密钥生成功能的数据绑定和业务逻辑
 */
export class KeyGenerationViewModel {
  private static readonly TAG = 'KeyGenerationViewModel';
  private static readonly DOMAIN = 0x0000;

  // 生成状态
  public isGenerating: boolean = false;
  public lastGenerationResult: KeyInfo | null = null;

  // 表单数据
  public clientId: string = '';
  public clientName: string = '';
  public selectedAlgorithm: number = 0; // 0: SM4, 1: AES256, 2: AES128
  public effectiveDate: string = '';
  public expirationDate: string = '';
  public keyName: string = '';
  public keyType: KeyType = KeyType.WorkKey;
  public keyLength: number = 256;
  public purpose: string = '';
  public remarks: string = '';

  // 验证状态
  public validationErrors: string[] = [];

  // 数据库连接状态
  public isDatabaseConnected: boolean = false;
  public databaseStatus: string = '未连接';

  constructor() {
    hilog.info(KeyGenerationViewModel.DOMAIN, KeyGenerationViewModel.TAG, 'KeyGenerationViewModel initialized');
    this.initializeDefaults();
  }

  /**
   * 验证表单数据
   */
  public validateForm(): boolean {
    this.validationErrors = [];

    if (!this.clientName.trim()) {
      this.validationErrors.push('客户名称不能为空');
    }

    if (!this.keyName.trim()) {
      this.validationErrors.push('密钥名称不能为空');
    }

    if (!this.effectiveDate) {
      this.validationErrors.push('生效日期不能为空');
    }

    if (!this.expirationDate) {
      this.validationErrors.push('过期日期不能为空');
    }

    if (this.effectiveDate && this.expirationDate) {
      const effective = new Date(this.effectiveDate);
      const expiration = new Date(this.expirationDate);

      if (effective >= expiration) {
        this.validationErrors.push('过期日期必须晚于生效日期');
      }
    }

    return this.validationErrors.length === 0;
  }

  /**
   * 生成密钥
   */
  public async generateKey(): Promise<boolean> {
    try {
      if (!this.validateForm()) {
        hilog.warn(KeyGenerationViewModel.DOMAIN, KeyGenerationViewModel.TAG, 'Form validation failed');
        return false;
      }

      this.isGenerating = true;
      hilog.info(KeyGenerationViewModel.DOMAIN, KeyGenerationViewModel.TAG, 'Starting key generation...');

      // 创建密钥生成请求
      const request = new KeyGenerationRequest({
        keyName: this.keyName,
        keyType: this.keyType,
        algorithm: this.getSelectedAlgorithm(),
        keyLength: this.keyLength,
        validityDays: this.calculateValidityDays(),
        clientUnit: this.clientName,
        purpose: this.purpose,
        remarks: this.remarks
      });

      // 模拟密钥生成过程
      await this.simulateKeyGeneration(request);

      hilog.info(KeyGenerationViewModel.DOMAIN, KeyGenerationViewModel.TAG, 'Key generation completed successfully');
      return true;

    } catch (error) {
      hilog.error(KeyGenerationViewModel.DOMAIN, KeyGenerationViewModel.TAG, 'Key generation failed: %{public}s',
        error.message);
      return false;
    } finally {
      this.isGenerating = false;
    }
  }

  /**
   * 重置表单
   */
  public resetForm(): void {
    this.clientId = '';
    this.clientName = '';
    this.keyName = '';
    this.purpose = '';
    this.remarks = '';
    this.selectedAlgorithm = 0;
    this.keyType = KeyType.WorkKey;
    this.keyLength = 256;
    this.validationErrors = [];
    this.lastGenerationResult = null;
    this.initializeDefaults();

    hilog.info(KeyGenerationViewModel.DOMAIN, KeyGenerationViewModel.TAG, 'Form reset');
  }

  /**
   * 检查数据库连接状态
   */
  public async checkDatabaseConnection(): Promise<void> {
    try {
      // 模拟数据库连接检查
      await new Promise(resolve => setTimeout(resolve, 500));

      this.isDatabaseConnected = true;
      this.databaseStatus = '已连接';

      hilog.info(KeyGenerationViewModel.DOMAIN, KeyGenerationViewModel.TAG, 'Database connection established');
    } catch (error) {
      this.isDatabaseConnected = false;
      this.databaseStatus = '连接失败';

      hilog.error(KeyGenerationViewModel.DOMAIN, KeyGenerationViewModel.TAG, 'Database connection failed: %{public}s',
        error.message);
    }
  }

  /**
   * 获取算法显示名称
   */
  public getAlgorithmName(index: number): string {
    switch (index) {
      case 0:
        return 'SM4 (国密)';
      case 1:
        return 'AES-256';
      case 2:
        return 'AES-128';
      default:
        return 'Unknown';
    }
  }

  /**
   * 获取密钥类型显示名称
   */
  public getKeyTypeName(keyType: KeyType): string {
    switch (keyType) {
      case KeyType.MasterKey:
        return '主密钥';
      case KeyType.WorkKey:
        return '工作密钥';
      case KeyType.TransportKey:
        return '传输密钥';
      case KeyType.StorageKey:
        return '存储密钥';
      default:
        return '未知类型';
    }
  }

  /**
   * 初始化默认值
   */
  private initializeDefaults(): void {
    const now = new Date();
    const nextYear = new Date(now.getFullYear() + 1, now.getMonth(), now.getDate());

    this.effectiveDate = this.formatDate(now);
    this.expirationDate = this.formatDate(nextYear);
  }

  /**
   * 格式化日期为YYYY-MM-DD格式
   */
  private formatDate(date: Date): string {
    return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString()
      .padStart(2, '0')}`;
  }

  /**
   * 模拟密钥生成过程
   */
  private async simulateKeyGeneration(request: KeyGenerationRequest): Promise<void> {
    // 模拟生成延迟
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 创建生成结果
    this.lastGenerationResult = new KeyInfo({
      keyId: this.generateKeyId(),
      keyName: request.keyName,
      keyType: request.keyType,
      algorithm: request.algorithm,
      keyLength: request.keyLength,
      status: KeyStatus.Active,
      createdTime: new Date(),
      effectiveTime: new Date(this.effectiveDate),
      expiryTime: new Date(this.expirationDate),
      createdBy: 'current_user',
      clientUnit: request.clientUnit,
      purpose: request.purpose,
      remarks: request.remarks,
      keyValue: this.generateMockKeyValue(),
      fingerprint: this.generateFingerprint()
    });
  }

  /**
   * 获取选中的算法
   */
  private getSelectedAlgorithm(): CryptoAlgorithm {
    switch (this.selectedAlgorithm) {
      case 0:
        return CryptoAlgorithm.SM4;
      case 1:
        return CryptoAlgorithm.AES256;
      case 2:
        return CryptoAlgorithm.AES128;
      default:
        return CryptoAlgorithm.AES256;
    }
  }

  /**
   * 计算有效期天数
   */
  private calculateValidityDays(): number {
    const effective = new Date(this.effectiveDate);
    const expiration = new Date(this.expirationDate);
    const diffTime = expiration.getTime() - effective.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  /**
   * 生成密钥ID
   */
  private generateKeyId(): string {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `KEY-${timestamp}-${random}`;
  }

  /**
   * 生成模拟密钥值
   */
  private generateMockKeyValue(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < 64; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * 生成指纹
   */
  private generateFingerprint(): string {
    const chars = '0123456789ABCDEF';
    let result = '';
    for (let i = 0; i < 40; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
      if (i > 0 && (i + 1) % 2 === 0 && i < 39) {
        result += ':';
      }
    }
    return result;
  }
}
