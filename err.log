严重性	代码	说明	项目	文件	行	抑制状态	详细信息
错误(活动)	CS1061	“NavigationRequestMessage”未包含“TargetViewModel”的定义，并且找不到可接受第一个“NavigationRequestMessage”类型参数的可访问扩展方法“TargetViewModel”(是否缺少 using 指令或程序集引用?)	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\ViewModels\MainWindowViewModel.cs	316		
警告(活动)	CS8604	“object Enum.Parse(Type enumType, string value)”中的形参“value”可能传入 null 引用实参。	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\Services\AuditService.cs	373		
消息(活动)	IDE0079	请删除不必要的忽略	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\Services\AuditService.cs	1		
消息(活动)	IDE0028	可以简化集合初始化	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\Services\AuditService.cs	135		
消息(活动)	IDE0028	可以简化集合初始化	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\Services\AuditService.cs	136		
消息(活动)	IDE0028	可以简化集合初始化	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\Services\AuditService.cs	137		
消息(活动)	IDE0028	可以简化集合初始化	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\Services\AuditService.cs	138		
消息(活动)	IDE0028	可以简化集合初始化	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\Services\AuditService.cs	139		
消息(活动)	IDE0028	可以简化集合初始化	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\Services\AuditService.cs	140		
消息(活动)	IDE0028	可以简化集合初始化	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\Services\AuditService.cs	159		
消息(活动)	IDE0028	可以简化集合初始化	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\Services\AuditService.cs	160		
消息(活动)	IDE0028	可以简化集合初始化	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\Services\AuditService.cs	184		
消息(活动)	IDE0028	可以简化集合初始化	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\Services\AuditService.cs	208		
消息(活动)	IDE0090	可简化 "new" 表达式	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\Services\AuditService.cs	225		
消息(活动)	IDE0090	可简化 "new" 表达式	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\Services\AuditService.cs	250		
消息(活动)	IDE0090	可简化 "new" 表达式	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\Services\AuditService.cs	276		
消息(活动)	IDE0090	可简化 "new" 表达式	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\Services\AuditService.cs	302		
消息(活动)	IDE0090	可简化 "new" 表达式	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\Services\AuditService.cs	328		
消息(活动)	CA2263	当类型已知时，泛型重载比 "System.Type" 重载更适合，通过改进的编译时检查来提升更简洁且更具类型安全性的代码。	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\Services\AuditService.cs	372		
消息(活动)	CA2263	当类型已知时，泛型重载比 "System.Type" 重载更适合，通过改进的编译时检查来提升更简洁且更具类型安全性的代码。	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\Services\AuditService.cs	373		
消息(活动)	IDE0028	可以简化集合初始化	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\Services\AuditService.cs	386		
消息(活动)	CA1860	首选使用可用的 'IsEmpty'、'Count' 或 'Length' 属性，而不是调用 'Enumerable.Any()'。意向更清晰，其性能高于使用 'Enumerable.Any() 扩展方法。	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\Services\AuditService.cs	437		
消息(活动)	CA1860	首选使用可用的 'IsEmpty'、'Count' 或 'Length' 属性，而不是调用 'Enumerable.Any()'。意向更清晰，其性能高于使用 'Enumerable.Any() 扩展方法。	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\Services\AuditService.cs	440		
消息(活动)	IDE0305	可以简化集合初始化	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\Services\AuditService.cs	472		
消息(活动)	IDE0305	可以简化集合初始化	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\Services\AuditService.cs	503		
消息(活动)	IDE0305	可以简化集合初始化	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\Services\AuditService.cs	505		
消息(活动)	CA1869	避免为每个序列化操作创建新的“JsonSerializerOptions”实例。请改为缓存和重用实例。仅使用“JsonSerializerOptions”实例可能会显著降低应用程序的性能。	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\Services\AuditService.cs	544		
消息(活动)	IDE0305	可以简化集合初始化	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\Services\AuditService.cs	569		
消息(活动)	IDE0305	可以简化集合初始化	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\Services\AuditService.cs	579		
消息(活动)	CA1860	首选使用可用的 'IsEmpty'、'Count' 或 'Length' 属性，而不是调用 'Enumerable.Any()'。意向更清晰，其性能高于使用 'Enumerable.Any() 扩展方法。	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\Services\AuditService.cs	588		
消息(活动)	CA1869	避免为每个序列化操作创建新的“JsonSerializerOptions”实例。请改为缓存和重用实例。仅使用“JsonSerializerOptions”实例可能会显著降低应用程序的性能。	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\Services\AuditService.cs	590		
消息(活动)	IDE0028	可以简化集合初始化	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\Services\AuditService.cs	621		
消息(活动)	CA1860	首选使用可用的 'IsEmpty'、'Count' 或 'Length' 属性，而不是调用 'Enumerable.Any()'。意向更清晰，其性能高于使用 'Enumerable.Any() 扩展方法。	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\Services\AuditService.cs	695		
错误(活动)	CS0101	命名空间“KeyGenerator.Services”已经包含“IAuditService”的定义	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\Services\IAuditService.cs	11		
错误(活动)	CS0246	未能找到类型或命名空间名“AuditLogEntry”(是否缺少 using 指令或程序集引用?)	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\Services\IAuditService.cs	16		
错误(活动)	CS0111	类型“IAuditService”已定义了一个名为“GetLogsAsync”的具有相同参数类型的成员	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\Services\IAuditService.cs	21		
错误(活动)	CS0236	字段初始值设定项无法引用非静态字段、方法或属性“KeyManagementViewModel.RefreshAsync()”	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\ViewModels\KeyManagementViewModel.cs	31		
错误(活动)	CS0236	字段初始值设定项无法引用非静态字段、方法或属性“KeyManagementViewModel.EditKeyAsync(MasterKeyEntity?)”	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\ViewModels\KeyManagementViewModel.cs	32		
错误(活动)	CS1660	无法将 lambda 表达式 转换为类型“AsyncRelayCommandOptions”，原因是它不是委托类型	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\ViewModels\KeyManagementViewModel.cs	32		
错误(活动)	CS0236	字段初始值设定项无法引用非静态字段、方法或属性“KeyManagementViewModel.DeleteKeyAsync(MasterKeyEntity?)”	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\ViewModels\KeyManagementViewModel.cs	33		
错误(活动)	CS1660	无法将 lambda 表达式 转换为类型“AsyncRelayCommandOptions”，原因是它不是委托类型	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\ViewModels\KeyManagementViewModel.cs	33		
错误(活动)	CS0236	字段初始值设定项无法引用非静态字段、方法或属性“KeyManagementViewModel.DistributeKey(MasterKeyEntity?)”	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\ViewModels\KeyManagementViewModel.cs	34		
错误(活动)	CS1519	Invalid token '{' in a member declaration	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\ViewModels\KeyManagementViewModel.cs	37		
错误(活动)	IDE1007	当前上下文中不存在名称“_”。	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\ViewModels\KeyManagementViewModel.cs	38		
错误(活动)	CS1519	Invalid token '=' in a member declaration	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\ViewModels\KeyManagementViewModel.cs	38		
错误(活动)	IDE1007	当前上下文中不存在名称“Task.Run”。	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\ViewModels\KeyManagementViewModel.cs	38		
错误(活动)	IDE1007	当前上下文中不存在名称“Run”。	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\ViewModels\KeyManagementViewModel.cs	38		
错误(活动)	CS1519	Invalid token '(' in a member declaration	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\ViewModels\KeyManagementViewModel.cs	38		
错误(活动)	IDE1007	当前上下文中不存在名称“async”。	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\ViewModels\KeyManagementViewModel.cs	38		
错误(活动)	CS8124	元组必须包含至少两个元素。	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\ViewModels\KeyManagementViewModel.cs	38		
错误(活动)	CS1026	应输入 )	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\ViewModels\KeyManagementViewModel.cs	38		
错误(活动)	CS8124	元组必须包含至少两个元素。	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\ViewModels\KeyManagementViewModel.cs	38		
错误(活动)	CS1519	Invalid token '=>' in a member declaration	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\ViewModels\KeyManagementViewModel.cs	38		
错误(活动)	CS0246	未能找到类型或命名空间名“await”(是否缺少 using 指令或程序集引用?)	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\ViewModels\KeyManagementViewModel.cs	38		
错误(活动)	CS0501	“KeyManagementViewModel.InitializeAsync()”必须声明主体，因为它未标记为 abstract、extern 或 partial	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\ViewModels\KeyManagementViewModel.cs	38		
错误(活动)	CS1002	应输入 ;	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\ViewModels\KeyManagementViewModel.cs	38		
错误(活动)	CS1519	Invalid token ')' in a member declaration	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\ViewModels\KeyManagementViewModel.cs	38		
错误(活动)	CS1519	Invalid token '}' in a member declaration	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\ViewModels\KeyManagementViewModel.cs	39		
错误(活动)	CS0229	在“KeyManagementViewModel.AllKeys”和“KeyManagementViewModel.AllKeys”之间具有二义性	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\ViewModels\KeyManagementViewModel.cs	50		
错误(活动)	CS0229	在“KeyManagementViewModel.AllKeys”和“KeyManagementViewModel.AllKeys”之间具有二义性	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\ViewModels\KeyManagementViewModel.cs	196		
错误(活动)	CS0111	类型“KeyManagementViewModel”已定义了一个名为“InitializeAsync”的具有相同参数类型的成员	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\ViewModels\KeyManagementViewModel.cs	212		
错误(活动)	CS0229	在“KeyManagementViewModel.AllKeys”和“KeyManagementViewModel.AllKeys”之间具有二义性	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\ViewModels\KeyManagementViewModel.cs	233		
错误(活动)	CS0229	在“KeyManagementViewModel.AllKeys”和“KeyManagementViewModel.AllKeys”之间具有二义性	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\ViewModels\KeyManagementViewModel.cs	236		
错误(活动)	CS0229	在“KeyManagementViewModel.AllKeys”和“KeyManagementViewModel.AllKeys”之间具有二义性	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\ViewModels\KeyManagementViewModel.cs	251		
错误(活动)	CS0229	在“KeyManagementViewModel.FilteredKeys”和“KeyManagementViewModel.FilteredKeys”之间具有二义性	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\ViewModels\KeyManagementViewModel.cs	262		
错误(活动)	CS0229	在“KeyManagementViewModel.FilteredKeys”和“KeyManagementViewModel.FilteredKeys”之间具有二义性	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\ViewModels\KeyManagementViewModel.cs	265		
消息(活动)	IDE0028	可以简化集合初始化	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\ViewModels\KeyManagementViewModel.cs	28		
消息(活动)	IDE0028	可以简化集合初始化	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\ViewModels\KeyManagementViewModel.cs	29		
错误(活动)	CS1061	“IDatabaseService”未包含“CheckConnectionAsync”的定义，并且找不到可接受第一个“IDatabaseService”类型参数的可访问扩展方法“CheckConnectionAsync”(是否缺少 using 指令或程序集引用?)	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\ViewModels\MainWindowViewModel.cs	275		
错误(活动)	CS1061	“NavigationRequestMessage”未包含“TargetViewModel”的定义，并且找不到可接受第一个“NavigationRequestMessage”类型参数的可访问扩展方法“TargetViewModel”(是否缺少 using 指令或程序集引用?)	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\ViewModels\MainWindowViewModel.cs	317		
错误(活动)	CS1061	“NavigationRequestMessage”未包含“TargetViewModel”的定义，并且找不到可接受第一个“NavigationRequestMessage”类型参数的可访问扩展方法“TargetViewModel”(是否缺少 using 指令或程序集引用?)	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\ViewModels\MainWindowViewModel.cs	321		
警告(活动)	CS8618	在退出构造函数时，不可为 null 的 字段 "_connectionCheckTimer" 必须包含非 null 值。请考虑添加 "required" 修饰符或将该 字段 声明为可为 null。	KeyGenerator	E:\Projects\my-dev\cryptosystem\operator\key-generator\windows\ViewModels\MainWindowViewModel.cs	54		
