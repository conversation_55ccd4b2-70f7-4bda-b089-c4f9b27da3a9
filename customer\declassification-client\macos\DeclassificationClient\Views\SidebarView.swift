import SwiftUI

struct SidebarView: View {
    @Binding var selectedTab: Int
    @EnvironmentObject var declassificationService: DeclassificationService
    @EnvironmentObject var configurationManager: ConfigurationManager
    @EnvironmentObject var auditLogger: AuditLogger
    @State private var showingNewTaskDialog = false
    @State private var showingSettings = false
    
    var body: some View {
        VStack(spacing: 0) {
            // 应用标题和logo
            AppHeaderView()
            
            // 主要导航
            NavigationList(selectedTab: $selectedTab)
            
            Spacer()
            
            // 快速统计
            QuickStatsView()
            
            // 底部操作按钮
            BottomActionsView(
                showingNewTaskDialog: $showingNewTaskDialog,
                showingSettings: $showingSettings
            )
        }
        .background(Color(NSColor.controlBackgroundColor))
        .sheet(isPresented: $showingNewTaskDialog) {
            NewTaskDialog()
        }
        .sheet(isPresented: $showingSettings) {
            SettingsView()
        }
    }
}

struct AppHeaderView: View {
    var body: some View {
        VStack(spacing: 8) {
            // 应用图标
            Image(systemName: "shield.lefthalf.filled")
                .font(.largeTitle)
                .foregroundColor(.blue)
            
            // 应用名称
            Text("脱密客户端")
                .font(.headline)
                .fontWeight(.semibold)
            
            Text("macOS版本")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(.vertical, 16)
        .padding(.horizontal, 8)
    }
}

struct NavigationList: View {
    @Binding var selectedTab: Int
    @EnvironmentObject var declassificationService: DeclassificationService
    @EnvironmentObject var auditLogger: AuditLogger
    
    var body: some View {
        List(selection: $selectedTab) {
            NavigationItem(
                title: "任务列表",
                icon: "list.bullet.rectangle",
                tag: 0,
                badge: declassificationService.totalTasks > 0 ? "\(declassificationService.totalTasks)" : nil
            )
            
            NavigationItem(
                title: "正在处理",
                icon: "gearshape.2",
                tag: 1,
                badge: declassificationService.processingTasks > 0 ? "\(declassificationService.processingTasks)" : nil,
                badgeColor: .orange
            )
            
            NavigationItem(
                title: "已完成",
                icon: "checkmark.circle",
                tag: 2,
                badge: declassificationService.completedTasks > 0 ? "\(declassificationService.completedTasks)" : nil,
                badgeColor: .green
            )
            
            NavigationItem(
                title: "失败任务",
                icon: "xmark.circle",
                tag: 3,
                badge: declassificationService.failedTasks > 0 ? "\(declassificationService.failedTasks)" : nil,
                badgeColor: .red
            )
            
            Section("监控") {
                NavigationItem(
                    title: "审计日志",
                    icon: "doc.text",
                    tag: 4,
                    badge: auditLogger.recentLogs.count > 0 ? "\(auditLogger.recentLogs.count)" : nil
                )
                
                NavigationItem(
                    title: "系统状态",
                    icon: "chart.line.uptrend.xyaxis",
                    tag: 5
                )
            }
            
            Section("管理") {
                NavigationItem(
                    title: "文件管理",
                    icon: "folder",
                    tag: 6
                )
                
                NavigationItem(
                    title: "用户设置",
                    icon: "gearshape",
                    tag: 7
                )
            }
        }
        .listStyle(.sidebar)
        .frame(minWidth: 200)
    }
}

struct NavigationItem: View {
    let title: String
    let icon: String
    let tag: Int
    let badge: String?
    let badgeColor: Color
    
    init(title: String, icon: String, tag: Int, badge: String? = nil, badgeColor: Color = .blue) {
        self.title = title
        self.icon = icon
        self.tag = tag
        self.badge = badge
        self.badgeColor = badgeColor
    }
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .frame(width: 20)
                .foregroundColor(.blue)
            
            Text(title)
                .font(.body)
            
            Spacer()
            
            if let badge = badge {
                Text(badge)
                    .font(.caption)
                    .fontWeight(.medium)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(badgeColor.opacity(0.2))
                    .foregroundColor(badgeColor)
                    .cornerRadius(8)
            }
        }
        .tag(tag)
    }
}

struct QuickStatsView: View {
    @EnvironmentObject var declassificationService: DeclassificationService
    
    var body: some View {
        VStack(spacing: 8) {
            Text("今日统计")
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.secondary)
            
            HStack(spacing: 16) {
                StatBadge(
                    value: "\(declassificationService.todayTasks)",
                    label: "任务",
                    color: .blue
                )
                
                StatBadge(
                    value: "\(declassificationService.successRate)%",
                    label: "成功率",
                    color: .green
                )
            }
        }
        .padding(.horizontal)
        .padding(.vertical, 12)
        .background(Color(NSColor.windowBackgroundColor))
        .cornerRadius(8)
        .padding(.horizontal)
    }
}

struct StatBadge: View {
    let value: String
    let label: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 2) {
            Text(value)
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(color)
            
            Text(label)
                .font(.caption2)
                .foregroundColor(.secondary)
        }
    }
}

struct BottomActionsView: View {
    @Binding var showingNewTaskDialog: Bool
    @Binding var showingSettings: Bool
    @EnvironmentObject var declassificationService: DeclassificationService
    
    var body: some View {
        VStack(spacing: 8) {
            // 新建任务按钮
            Button {
                showingNewTaskDialog = true
            } label: {
                HStack {
                    Image(systemName: "plus.circle.fill")
                    Text("新建任务")
                        .fontWeight(.medium)
                }
                .frame(maxWidth: .infinity)
            }
            .buttonStyle(.borderedProminent)
            .controlSize(.large)
            
            // 快速操作按钮
            HStack(spacing: 8) {
                Button {
                    Task {
                        await declassificationService.refreshTasks()
                    }
                } label: {
                    Image(systemName: "arrow.clockwise")
                }
                .buttonStyle(.bordered)
                .help("刷新任务列表")
                
                Button {
                    showingSettings = true
                } label: {
                    Image(systemName: "gearshape")
                }
                .buttonStyle(.bordered)
                .help("应用设置")
                
                Button {
                    openLogsFolder()
                } label: {
                    Image(systemName: "folder")
                }
                .buttonStyle(.bordered)
                .help("打开日志文件夹")
                
                Button {
                    exportAllLogs()
                } label: {
                    Image(systemName: "square.and.arrow.up")
                }
                .buttonStyle(.bordered)
                .help("导出审计日志")
            }
        }
        .padding()
    }
    
    private func openLogsFolder() {
        let appSupport = FileManager.default.urls(for: .applicationSupportDirectory, in: .userDomainMask).first!
        let logDirectory = appSupport.appendingPathComponent("DeclassificationClient/Logs")
        
        // 确保目录存在
        try? FileManager.default.createDirectory(at: logDirectory, withIntermediateDirectories: true)
        
        NSWorkspace.shared.open(logDirectory)
    }
    
    private func exportAllLogs() {
        let savePanel = NSSavePanel()
        savePanel.title = "导出审计日志"
        savePanel.nameFieldStringValue = "audit_logs_\(Date().formatted(.iso8601.day().month().year())).json"
        savePanel.allowedContentTypes = [.json]
        
        if savePanel.runModal() == .OK, let url = savePanel.url {
            Task {
                await AuditLogger.shared.exportLogs(to: url)
            }
        }
    }
}

struct NewTaskDialog: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var declassificationService: DeclassificationService
    
    @State private var taskName = ""
    @State private var selectedFiles: [URL] = []
    @State private var sendMethod: SendMethod = .email
    @State private var recipients = ""
    @State private var notes = ""
    @State private var isCreating = false
    
    var isValid: Bool {
        !taskName.isEmpty && !selectedFiles.isEmpty && 
        (!sendMethod.requiresRecipient || !recipients.isEmpty)
    }
    
    var body: some View {
        VStack(spacing: 20) {
            // 标题
            HStack {
                Text("创建新的脱密任务")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button("取消") {
                    dismiss()
                }
                .buttonStyle(.plain)
            }
            
            // 表单内容
            VStack(alignment: .leading, spacing: 16) {
                // 任务名称
                VStack(alignment: .leading, spacing: 4) {
                    Text("任务名称")
                        .font(.headline)
                    TextField("输入任务名称...", text: $taskName)
                        .textFieldStyle(.roundedBorder)
                }
                
                // 文件选择
                VStack(alignment: .leading, spacing: 4) {
                    Text("选择文件")
                        .font(.headline)
                    
                    HStack {
                        Button("选择文件...") {
                            selectFiles()
                        }
                        .buttonStyle(.bordered)
                        
                        Text("\(selectedFiles.count) 个文件已选择")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Spacer()
                    }
                    
                    if !selectedFiles.isEmpty {
                        ScrollView {
                            LazyVStack(alignment: .leading, spacing: 4) {
                                ForEach(selectedFiles, id: \.self) { file in
                                    HStack {
                                        Image(systemName: "doc.fill")
                                            .foregroundColor(.blue)
                                        Text(file.lastPathComponent)
                                            .font(.caption)
                                        Spacer()
                                        Button {
                                            selectedFiles.removeAll { $0 == file }
                                        } label: {
                                            Image(systemName: "xmark.circle.fill")
                                                .foregroundColor(.secondary)
                                        }
                                        .buttonStyle(.plain)
                                    }
                                }
                            }
                        }
                        .frame(maxHeight: 100)
                        .padding(8)
                        .background(Color(NSColor.controlBackgroundColor))
                        .cornerRadius(6)
                    }
                }
                
                // 发送方式
                VStack(alignment: .leading, spacing: 4) {
                    Text("发送方式")
                        .font(.headline)
                    
                    Picker("发送方式", selection: $sendMethod) {
                        ForEach(SendMethod.allCases, id: \.self) { method in
                            HStack {
                                Image(systemName: method.iconName)
                                Text(method.displayName)
                            }
                            .tag(method)
                        }
                    }
                    .pickerStyle(.menu)
                }
                
                // 收件人（如果需要）
                if sendMethod.requiresRecipient {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("收件人")
                            .font(.headline)
                        TextField("输入收件人地址，多个地址用逗号分隔...", text: $recipients)
                            .textFieldStyle(.roundedBorder)
                    }
                }
                
                // 备注
                VStack(alignment: .leading, spacing: 4) {
                    Text("备注（可选）")
                        .font(.headline)
                    TextField("输入备注信息...", text: $notes, axis: .vertical)
                        .textFieldStyle(.roundedBorder)
                        .lineLimit(3...6)
                }
            }
            
            Spacer()
            
            // 操作按钮
            HStack {
                Spacer()
                
                Button("取消") {
                    dismiss()
                }
                .buttonStyle(.bordered)
                
                Button("创建任务") {
                    createTask()
                }
                .buttonStyle(.borderedProminent)
                .disabled(!isValid || isCreating)
                
                if isCreating {
                    ProgressView()
                        .scaleEffect(0.8)
                }
            }
        }
        .padding(24)
        .frame(width: 500, height: 600)
    }
    
    private func selectFiles() {
        let panel = NSOpenPanel()
        panel.title = "选择要脱密的文件"
        panel.allowsMultipleSelection = true
        panel.canChooseDirectories = false
        panel.canChooseFiles = true
        
        if panel.runModal() == .OK {
            selectedFiles = panel.urls
        }
    }
    
    private func createTask() {
        isCreating = true
        
        Task {
            await declassificationService.createTask(
                name: taskName,
                files: selectedFiles,
                sendMethod: sendMethod,
                recipients: recipients,
                notes: notes
            )
            
            await MainActor.run {
                isCreating = false
                dismiss()
            }
        }
    }
}

#Preview {
    SidebarView(selectedTab: .constant(0))
        .environmentObject(DeclassificationService())
        .environmentObject(ConfigurationManager.shared)
        .environmentObject(AuditLogger.shared)
        .frame(width: 250, height: 600)
} 