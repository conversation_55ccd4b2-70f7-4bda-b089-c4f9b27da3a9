using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CryptoSystem.SystemManager.Models
{
    /// <summary>
    /// 设备类型枚举
    /// </summary>
    public enum DeviceType
    {
        [Description("客户端终端")]
        ClientTerminal = 1,
        
        [Description("服务器")]
        Server = 2,
        
        [Description("移动设备")]
        MobileDevice = 3,
        
        [Description("虚拟机")]
        VirtualMachine = 4
    }

    /// <summary>
    /// 设备状态枚举
    /// </summary>
    public enum DeviceStatus
    {
        [Description("在线")]
        Online = 1,
        
        [Description("离线")]
        Offline = 2,
        
        [Description("已注册")]
        Registered = 3,
        
        [Description("未激活")]
        Inactive = 4,
        
        [Description("已锁定")]
        Locked = 5,
        
        [Description("已禁用")]
        Disabled = 6
    }

    /// <summary>
    /// 操作系统类型枚举
    /// </summary>
    public enum OSType
    {
        [Description("Windows")]
        Windows = 1,
        
        [Description("Linux")]
        Linux = 2,
        
        [Description("macOS")]
        MacOS = 3,
        
        [Description("鸿蒙OS")]
        HarmonyOS = 4,
        
        [Description("国产化OS")]
        DomesticOS = 5
    }

    /// <summary>
    /// 设备信息实体
    /// </summary>
    [Table("sys_devices")]
    public class Device
    {
        /// <summary>
        /// 设备ID
        /// </summary>
        [Key]
        [Column("device_id")]
        public string DeviceId { get; set; } = string.Empty;

        /// <summary>
        /// 设备名称
        /// </summary>
        [Required]
        [MaxLength(200)]
        [Column("device_name")]
        public string DeviceName { get; set; } = string.Empty;

        /// <summary>
        /// 设备类型
        /// </summary>
        [Column("device_type")]
        public DeviceType DeviceType { get; set; } = DeviceType.ClientTerminal;

        /// <summary>
        /// 设备状态
        /// </summary>
        [Column("status")]
        public DeviceStatus Status { get; set; } = DeviceStatus.Registered;

        /// <summary>
        /// 操作系统类型
        /// </summary>
        [Column("os_type")]
        public OSType OSType { get; set; } = OSType.Windows;

        /// <summary>
        /// 操作系统版本
        /// </summary>
        [MaxLength(200)]
        [Column("os_version")]
        public string OSVersion { get; set; } = string.Empty;

        /// <summary>
        /// 硬件指纹
        /// </summary>
        [MaxLength(500)]
        [Column("hardware_fingerprint")]
        public string HardwareFingerprint { get; set; } = string.Empty;

        /// <summary>
        /// CPU信息
        /// </summary>
        [MaxLength(500)]
        [Column("cpu_info")]
        public string CpuInfo { get; set; } = string.Empty;

        /// <summary>
        /// 内存大小（MB）
        /// </summary>
        [Column("memory_mb")]
        public long MemoryMB { get; set; } = 0;

        /// <summary>
        /// 磁盘大小（GB）
        /// </summary>
        [Column("disk_gb")]
        public long DiskGB { get; set; } = 0;

        /// <summary>
        /// MAC地址
        /// </summary>
        [MaxLength(200)]
        [Column("mac_address")]
        public string MacAddress { get; set; } = string.Empty;

        /// <summary>
        /// IP地址
        /// </summary>
        [MaxLength(100)]
        [Column("ip_address")]
        public string IpAddress { get; set; } = string.Empty;

        /// <summary>
        /// 计算机名
        /// </summary>
        [MaxLength(200)]
        [Column("computer_name")]
        public string ComputerName { get; set; } = string.Empty;

        /// <summary>
        /// 域名
        /// </summary>
        [MaxLength(200)]
        [Column("domain_name")]
        public string DomainName { get; set; } = string.Empty;

        /// <summary>
        /// 客户端版本
        /// </summary>
        [MaxLength(50)]
        [Column("client_version")]
        public string ClientVersion { get; set; } = string.Empty;

        /// <summary>
        /// 安装时间
        /// </summary>
        [Column("install_time")]
        public DateTime? InstallTime { get; set; }

        /// <summary>
        /// 首次注册时间
        /// </summary>
        [Column("first_register_time")]
        public DateTime? FirstRegisterTime { get; set; }

        /// <summary>
        /// 最后在线时间
        /// </summary>
        [Column("last_online_time")]
        public DateTime? LastOnlineTime { get; set; }

        /// <summary>
        /// 最后离线时间
        /// </summary>
        [Column("last_offline_time")]
        public DateTime? LastOfflineTime { get; set; }

        /// <summary>
        /// 最后心跳时间
        /// </summary>
        [Column("last_heartbeat_time")]
        public DateTime? LastHeartbeatTime { get; set; }

        /// <summary>
        /// 授权用户ID
        /// </summary>
        [Column("authorized_user_id")]
        public string? AuthorizedUserId { get; set; }

        /// <summary>
        /// 授权用户名
        /// </summary>
        [MaxLength(100)]
        [Column("authorized_username")]
        public string AuthorizedUsername { get; set; } = string.Empty;

        /// <summary>
        /// 部门ID
        /// </summary>
        [Column("department_id")]
        public string? DepartmentId { get; set; }

        /// <summary>
        /// 部门名称
        /// </summary>
        [MaxLength(200)]
        [Column("department_name")]
        public string DepartmentName { get; set; } = string.Empty;

        /// <summary>
        /// 物理位置
        /// </summary>
        [MaxLength(500)]
        [Column("location")]
        public string Location { get; set; } = string.Empty;

        /// <summary>
        /// 资产编号
        /// </summary>
        [MaxLength(100)]
        [Column("asset_number")]
        public string AssetNumber { get; set; } = string.Empty;

        /// <summary>
        /// 是否允许离线工作
        /// </summary>
        [Column("allow_offline")]
        public bool AllowOffline { get; set; } = true;

        /// <summary>
        /// 离线工作时长限制（小时）
        /// </summary>
        [Column("offline_limit_hours")]
        public int OfflineLimitHours { get; set; } = 24;

        /// <summary>
        /// 注册者
        /// </summary>
        [MaxLength(100)]
        [Column("registered_by")]
        public string RegisteredBy { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        [Column("created_time")]
        public DateTime CreatedTime { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 最后修改时间
        /// </summary>
        [Column("last_modified_time")]
        public DateTime LastModifiedTime { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 最后修改者
        /// </summary>
        [MaxLength(100)]
        [Column("last_modified_by")]
        public string LastModifiedBy { get; set; } = string.Empty;

        /// <summary>
        /// 备注信息
        /// </summary>
        [MaxLength(1000)]
        [Column("remarks")]
        public string Remarks { get; set; } = string.Empty;

        /// <summary>
        /// 扩展属性（JSON格式）
        /// </summary>
        [Column("extended_properties")]
        public string ExtendedProperties { get; set; } = "{}";

        // 导航属性
        public virtual User? AuthorizedUser { get; set; }
        public virtual Department? Department { get; set; }
        public virtual ICollection<UserDevice> UserDevices { get; set; } = new List<UserDevice>();
        public virtual ICollection<DevicePolicy> DevicePolicies { get; set; } = new List<DevicePolicy>();
    }

    /// <summary>
    /// 用户设备关联实体
    /// </summary>
    [Table("sys_user_devices")]
    public class UserDevice
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Key]
        [Column("user_id")]
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// 设备ID
        /// </summary>
        [Key]
        [Column("device_id")]
        public string DeviceId { get; set; } = string.Empty;

        /// <summary>
        /// 关联时间
        /// </summary>
        [Column("associated_time")]
        public DateTime AssociatedTime { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 关联者
        /// </summary>
        [MaxLength(100)]
        [Column("associated_by")]
        public string AssociatedBy { get; set; } = string.Empty;

        /// <summary>
        /// 是否主设备
        /// </summary>
        [Column("is_primary")]
        public bool IsPrimary { get; set; } = false;

        /// <summary>
        /// 最后使用时间
        /// </summary>
        [Column("last_used_time")]
        public DateTime? LastUsedTime { get; set; }

        // 导航属性
        public virtual User User { get; set; } = null!;
        public virtual Device Device { get; set; } = null!;
    }

    /// <summary>
    /// 设备组实体
    /// </summary>
    [Table("sys_device_groups")]
    public class DeviceGroup
    {
        /// <summary>
        /// 设备组ID
        /// </summary>
        [Key]
        [Column("group_id")]
        public string GroupId { get; set; } = string.Empty;

        /// <summary>
        /// 设备组名称
        /// </summary>
        [Required]
        [MaxLength(200)]
        [Column("group_name")]
        public string GroupName { get; set; } = string.Empty;

        /// <summary>
        /// 设备组描述
        /// </summary>
        [MaxLength(1000)]
        [Column("description")]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 父组ID
        /// </summary>
        [Column("parent_group_id")]
        public string? ParentGroupId { get; set; }

        /// <summary>
        /// 组层级
        /// </summary>
        [Column("level")]
        public int Level { get; set; } = 1;

        /// <summary>
        /// 排序号
        /// </summary>
        [Column("sort_order")]
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 是否启用
        /// </summary>
        [Column("is_enabled")]
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        [Column("created_time")]
        public DateTime CreatedTime { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 创建者
        /// </summary>
        [MaxLength(100)]
        [Column("created_by")]
        public string CreatedBy { get; set; } = string.Empty;

        // 导航属性
        public virtual DeviceGroup? ParentGroup { get; set; }
        public virtual ICollection<DeviceGroup> ChildGroups { get; set; } = new List<DeviceGroup>();
        public virtual ICollection<DeviceGroupMember> Members { get; set; } = new List<DeviceGroupMember>();
    }

    /// <summary>
    /// 设备组成员关系
    /// </summary>
    [Table("sys_device_group_members")]
    public class DeviceGroupMember
    {
        /// <summary>
        /// 设备组ID
        /// </summary>
        [Key]
        [Column("group_id")]
        public string GroupId { get; set; } = string.Empty;

        /// <summary>
        /// 设备ID
        /// </summary>
        [Key]
        [Column("device_id")]
        public string DeviceId { get; set; } = string.Empty;

        /// <summary>
        /// 加入时间
        /// </summary>
        [Column("joined_time")]
        public DateTime JoinedTime { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 加入者
        /// </summary>
        [MaxLength(100)]
        [Column("joined_by")]
        public string JoinedBy { get; set; } = string.Empty;

        // 导航属性
        public virtual DeviceGroup Group { get; set; } = null!;
        public virtual Device Device { get; set; } = null!;
    }

    /// <summary>
    /// 设备统计信息
    /// </summary>
    public class DeviceStatistics
    {
        /// <summary>
        /// 总设备数
        /// </summary>
        public int TotalDevices { get; set; }

        /// <summary>
        /// 在线设备数
        /// </summary>
        public int OnlineDevices { get; set; }

        /// <summary>
        /// 离线设备数
        /// </summary>
        public int OfflineDevices { get; set; }

        /// <summary>
        /// 活动设备数（最近7天有活动）
        /// </summary>
        public int ActiveDevices { get; set; }

        /// <summary>
        /// 按操作系统分组的设备数
        /// </summary>
        public Dictionary<OSType, int> DevicesByOS { get; set; } = new();

        /// <summary>
        /// 按设备类型分组的设备数
        /// </summary>
        public Dictionary<DeviceType, int> DevicesByType { get; set; } = new();

        /// <summary>
        /// 按状态分组的设备数
        /// </summary>
        public Dictionary<DeviceStatus, int> DevicesByStatus { get; set; } = new();
    }
} 