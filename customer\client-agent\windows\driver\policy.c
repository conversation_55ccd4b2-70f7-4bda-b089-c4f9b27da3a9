/*
 * policy.c
 * 
 * 策略管理实现文件
 * 实现加密策略相关的功能
 */

#include <fltKernel.h>
#include <ntstrsafe.h>
#include "policy.h"
#include "utils.h"

// 全局策略管理器
static ENC_POLICY_MANAGER g_PolicyManager;
static BOOLEAN g_PolicyInitialized = FALSE;

//
// 策略管理器初始化
//
NTSTATUS
PolicyInitialize(
    VOID
    )
{
    if (g_PolicyInitialized) {
        return STATUS_SUCCESS;
    }

    // 初始化策略管理器
    RtlZeroMemory(&g_PolicyManager, sizeof(ENC_POLICY_MANAGER));
    InitializeListHead(&g_PolicyManager.RuleListHead);
    ExInitializeResourceLite(&g_PolicyManager.PolicyLock);
    
    g_PolicyManager.RuleCount = 0;
    g_PolicyManager.PolicyVersion = 1;
    KeQuerySystemTime(&g_PolicyManager.LastUpdateTime);
    
    g_PolicyInitialized = TRUE;
    
    return STATUS_SUCCESS;
}

//
// 策略管理器清理
//
VOID
PolicyCleanup(
    VOID
    )
{
    if (!g_PolicyInitialized) {
        return;
    }

    // 清理所有规则
    PolicyClearAllRules(TRUE);
    
    // 删除资源锁
    ExDeleteResourceLite(&g_PolicyManager.PolicyLock);
    g_PolicyInitialized = FALSE;
}

//
// 评估文件是否应该加密
//
BOOLEAN
PolicyShouldEncryptFile(
    _In_ PCUNICODE_STRING FileName,
    _In_opt_ PCUNICODE_STRING ProcessName,
    _In_opt_ PCUNICODE_STRING UserSid
    )
{
    PLIST_ENTRY entry;
    PENC_POLICY_RULE rule;
    BOOLEAN shouldEncrypt = FALSE;
    BOOLEAN matchFound = FALSE;
    
    if (!g_PolicyInitialized || FileName == NULL) {
        return FALSE;
    }
    
    // 获取共享锁
    ExAcquireResourceSharedLite(&g_PolicyManager.PolicyLock, TRUE);
    
    // 如果没有规则，默认不加密
    if (IsListEmpty(&g_PolicyManager.RuleListHead)) {
        ExReleaseResourceLite(&g_PolicyManager.PolicyLock);
        return FALSE;
    }
    
    // 遍历所有规则，优先级顺序（链表中的顺序）
    for (entry = g_PolicyManager.RuleListHead.Flink;
         entry != &g_PolicyManager.RuleListHead;
         entry = entry->Flink) {
        
        rule = CONTAINING_RECORD(entry, ENC_POLICY_RULE, RuleListEntry);
        
        // 检查是否匹配规则
        switch (rule->MatchType) {
        case MatchTypeFileExtension:
            if (UtilIsFileTypeMatch(FileName, &rule->MatchData)) {
                matchFound = TRUE;
            }
            break;
            
        case MatchTypePath:
            if (UtilIsPathMatch(FileName, &rule->MatchData)) {
                matchFound = TRUE;
            }
            break;
            
        case MatchTypeProcess:
            if (ProcessName != NULL && 
                RtlCompareUnicodeString(ProcessName, &rule->MatchData, TRUE) == 0) {
                matchFound = TRUE;
            }
            break;
            
        case MatchTypeUser:
            if (UserSid != NULL && 
                RtlCompareUnicodeString(UserSid, &rule->MatchData, TRUE) == 0) {
                matchFound = TRUE;
            }
            break;
            
        default:
            break;
        }
        
        // 如果匹配规则，根据规则类型确定是否加密
        if (matchFound) {
            if (rule->PolicyType == PolicyTypeEncrypt) {
                shouldEncrypt = TRUE;
            } else if (rule->PolicyType == PolicyTypeExempt) {
                shouldEncrypt = FALSE;
            }
            
            // 不再继续检查其他规则
            break;
        }
    }
    
    ExReleaseResourceLite(&g_PolicyManager.PolicyLock);
    return shouldEncrypt;
}

//
// 评估文件是否豁免加密
//
BOOLEAN
PolicyIsFileExempted(
    _In_ PCUNICODE_STRING FileName,
    _In_opt_ PCUNICODE_STRING ProcessName,
    _In_opt_ PCUNICODE_STRING UserSid
    )
{
    PLIST_ENTRY entry;
    PENC_POLICY_RULE rule;
    BOOLEAN isExempted = FALSE;
    
    if (!g_PolicyInitialized || FileName == NULL) {
        return FALSE;
    }
    
    // 获取共享锁
    ExAcquireResourceSharedLite(&g_PolicyManager.PolicyLock, TRUE);
    
    // 遍历所有豁免规则
    for (entry = g_PolicyManager.RuleListHead.Flink;
         entry != &g_PolicyManager.RuleListHead;
         entry = entry->Flink) {
        
        rule = CONTAINING_RECORD(entry, ENC_POLICY_RULE, RuleListEntry);
        
        // 只检查豁免规则
        if (rule->PolicyType != PolicyTypeExempt) {
            continue;
        }
        
        // 检查是否匹配规则
        switch (rule->MatchType) {
        case MatchTypeFileExtension:
            if (UtilIsFileTypeMatch(FileName, &rule->MatchData)) {
                isExempted = TRUE;
            }
            break;
            
        case MatchTypePath:
            if (UtilIsPathMatch(FileName, &rule->MatchData)) {
                isExempted = TRUE;
            }
            break;
            
        case MatchTypeProcess:
            if (ProcessName != NULL && 
                RtlCompareUnicodeString(ProcessName, &rule->MatchData, TRUE) == 0) {
                isExempted = TRUE;
            }
            break;
            
        case MatchTypeUser:
            if (UserSid != NULL && 
                RtlCompareUnicodeString(UserSid, &rule->MatchData, TRUE) == 0) {
                isExempted = TRUE;
            }
            break;
            
        default:
            break;
        }
        
        // 如果匹配任何豁免规则，立即返回
        if (isExempted) {
            break;
        }
    }
    
    ExReleaseResourceLite(&g_PolicyManager.PolicyLock);
    return isExempted;
}

//
// 添加策略规则
//
NTSTATUS
PolicyAddRule(
    _In_ PENC_POLICY_RULE Rule
    )
{
    NTSTATUS status = STATUS_SUCCESS;
    PENC_POLICY_RULE newRule = NULL;
    PLIST_ENTRY entry = NULL;
    PENC_POLICY_RULE existingRule = NULL;
    BOOLEAN ruleExists = FALSE;
    
    if (!g_PolicyInitialized || Rule == NULL) {
        return STATUS_INVALID_PARAMETER;
    }
    
    // 获取独占锁
    ExAcquireResourceExclusiveLite(&g_PolicyManager.PolicyLock, TRUE);
    
    // 检查规则是否已存在
    for (entry = g_PolicyManager.RuleListHead.Flink;
         entry != &g_PolicyManager.RuleListHead;
         entry = entry->Flink) {
        
        existingRule = CONTAINING_RECORD(entry, ENC_POLICY_RULE, RuleListEntry);
        if (existingRule->RuleId == Rule->RuleId) {
            ruleExists = TRUE;
            break;
        }
    }
    
    if (ruleExists) {
        // 更新现有规则
        existingRule->PolicyType = Rule->PolicyType;
        existingRule->MatchType = Rule->MatchType;
        existingRule->Priority = Rule->Priority;
        
        // 更新匹配数据
        if (existingRule->MatchData.Buffer != NULL) {
            ExFreePool(existingRule->MatchData.Buffer);
            existingRule->MatchData.Length = 0;
            existingRule->MatchData.MaximumLength = 0;
        }
        
        status = UtilDuplicateUnicodeString(&Rule->MatchData, &existingRule->MatchData);
    } else {
        // 创建新规则
        newRule = (PENC_POLICY_RULE)ExAllocatePoolWithTag(
            NonPagedPool,
            sizeof(ENC_POLICY_RULE),
            'lurP'
        );
        
        if (newRule == NULL) {
            status = STATUS_INSUFFICIENT_RESOURCES;
            goto Exit;
        }
        
        // 初始化新规则
        RtlZeroMemory(newRule, sizeof(ENC_POLICY_RULE));
        newRule->RuleId = Rule->RuleId;
        newRule->PolicyType = Rule->PolicyType;
        newRule->MatchType = Rule->MatchType;
        newRule->Priority = Rule->Priority;
        
        // 复制匹配数据
        status = UtilDuplicateUnicodeString(&Rule->MatchData, &newRule->MatchData);
        if (!NT_SUCCESS(status)) {
            ExFreePool(newRule);
            goto Exit;
        }
        
        // 将规则插入到链表中，按优先级排序
        BOOLEAN inserted = FALSE;
        for (entry = g_PolicyManager.RuleListHead.Flink;
             entry != &g_PolicyManager.RuleListHead;
             entry = entry->Flink) {
            
            existingRule = CONTAINING_RECORD(entry, ENC_POLICY_RULE, RuleListEntry);
            if (newRule->Priority < existingRule->Priority) {
                // 优先级更高，插入到此规则之前
                InsertHeadList(entry->Blink, &newRule->RuleListEntry);
                inserted = TRUE;
                break;
            }
        }
        
        // 如果没有插入（链表为空或优先级最低），则添加到链表尾部
        if (!inserted) {
            InsertTailList(&g_PolicyManager.RuleListHead, &newRule->RuleListEntry);
        }
        
        g_PolicyManager.RuleCount++;
    }
    
    // 更新策略版本和时间
    g_PolicyManager.PolicyVersion++;
    KeQuerySystemTime(&g_PolicyManager.LastUpdateTime);
    
Exit:
    ExReleaseResourceLite(&g_PolicyManager.PolicyLock);
    return status;
}

//
// 移除策略规则
//
NTSTATUS
PolicyRemoveRule(
    _In_ ULONG RuleId
    )
{
    NTSTATUS status = STATUS_NOT_FOUND;
    PLIST_ENTRY entry = NULL;
    PENC_POLICY_RULE rule = NULL;
    
    if (!g_PolicyInitialized) {
        return STATUS_INVALID_PARAMETER;
    }
    
    // 获取独占锁
    ExAcquireResourceExclusiveLite(&g_PolicyManager.PolicyLock, TRUE);
    
    // 查找并移除指定规则
    for (entry = g_PolicyManager.RuleListHead.Flink;
         entry != &g_PolicyManager.RuleListHead;
         entry = entry->Flink) {
        
        rule = CONTAINING_RECORD(entry, ENC_POLICY_RULE, RuleListEntry);
        if (rule->RuleId == RuleId) {
            // 从链表中移除
            RemoveEntryList(&rule->RuleListEntry);
            
            // 释放匹配数据
            if (rule->MatchData.Buffer != NULL) {
                ExFreePool(rule->MatchData.Buffer);
            }
            
            // 释放规则内存
            ExFreePool(rule);
            
            g_PolicyManager.RuleCount--;
            
            // 更新策略版本和时间
            g_PolicyManager.PolicyVersion++;
            KeQuerySystemTime(&g_PolicyManager.LastUpdateTime);
            
            status = STATUS_SUCCESS;
            break;
        }
    }
    
    ExReleaseResourceLite(&g_PolicyManager.PolicyLock);
    return status;
}

//
// 清除所有规则
//
NTSTATUS
PolicyClearAllRules(
    _In_ BOOLEAN ForceUnlock
    )
{
    PLIST_ENTRY entry = NULL;
    PENC_POLICY_RULE rule = NULL;
    
    if (!g_PolicyInitialized) {
        return STATUS_INVALID_PARAMETER;
    }
    
    // 获取独占锁
    ExAcquireResourceExclusiveLite(&g_PolicyManager.PolicyLock, TRUE);
    
    // 清除所有规则
    while (!IsListEmpty(&g_PolicyManager.RuleListHead)) {
        entry = RemoveHeadList(&g_PolicyManager.RuleListHead);
        rule = CONTAINING_RECORD(entry, ENC_POLICY_RULE, RuleListEntry);
        
        // 释放匹配数据
        if (rule->MatchData.Buffer != NULL) {
            ExFreePool(rule->MatchData.Buffer);
        }
        
        // 释放规则内存
        ExFreePool(rule);
    }
    
    g_PolicyManager.RuleCount = 0;
    
    // 更新策略版本和时间
    g_PolicyManager.PolicyVersion++;
    KeQuerySystemTime(&g_PolicyManager.LastUpdateTime);
    
    ExReleaseResourceLite(&g_PolicyManager.PolicyLock);
    return STATUS_SUCCESS;
}

//
// 从注册表加载策略
//
NTSTATUS
PolicyLoadFromRegistry(
    _In_ PUNICODE_STRING RegistryPath
    )
{
    // 注册表读取函数实现
    // 这里需要实现从注册表读取策略规则的逻辑
    // 由于实现较为复杂，在此省略具体实现
    // 实际应用中，应该读取注册表中的规则配置，并通过PolicyAddRule添加到策略管理器
    
    // 示例：添加默认规则
    ENC_POLICY_RULE defaultRule;
    UNICODE_STRING matchData;
    
    // 初始化匹配数据
    RtlInitUnicodeString(&matchData, L".doc");
    
    // 初始化规则
    RtlZeroMemory(&defaultRule, sizeof(ENC_POLICY_RULE));
    defaultRule.RuleId = 1;
    defaultRule.PolicyType = PolicyTypeEncrypt;
    defaultRule.MatchType = MatchTypeFileExtension;
    defaultRule.Priority = 100;
    defaultRule.MatchData = matchData;
    
    // 添加规则
    return PolicyAddRule(&defaultRule);
}

//
// 处理设备控制请求
//
NTSTATUS
PolicyHandleDeviceControl(
    _In_ PFLT_CALLBACK_DATA Data,
    _In_ PCFLT_RELATED_OBJECTS FltObjects
    )
{
    // 此函数用于处理用户模式应用程序发送的IOCTL请求
    // 可以通过此函数实现策略的动态更新
    // 由于实现较为复杂，在此省略具体实现
    
    return STATUS_SUCCESS;
} 