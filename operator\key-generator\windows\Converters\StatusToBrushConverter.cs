using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;
using System.Windows.Media;

namespace KeyGenerator.Converters
{
    /// <summary>
    /// 状态到画刷的转换器
    /// </summary>
    public class StatusToBrushConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            // 处理 null 值和DependencyProperty.UnsetValue
            if (value == null)
            {
                return new SolidColorBrush(Color.FromRgb(158, 158, 158)); // 灰色为默认
            }
            
            if (value == DependencyProperty.UnsetValue)
            {
                return new SolidColorBrush(Color.FromRgb(158, 158, 158)); // 灰色为默认
            }
            
            if (value is string status)
            {
                return status switch
                {
                    "活跃" or "活动" => new SolidColorBrush(Color.FromRgb(76, 175, 80)),     // 绿色
                    "待激活" => new SolidColorBrush(Color.FromRgb(255, 193, 7)),   // 黄色
                    "已吊销" => new SolidColorBrush(Color.FromRgb(244, 67, 54)),   // 红色
                    "已过期" => new SolidColorBrush(Color.FromRgb(158, 158, 158)), // 灰色
                    _ => new SolidColorBrush(Color.FromRgb(96, 125, 139))          // 默认蓝灰色
                };
            }
            else if (value is KeyGenerator.Models.KeyStatus keyStatus)
            {
                try
                {
                    return keyStatus switch
                    {
                        KeyGenerator.Models.KeyStatus.Active => new SolidColorBrush(Color.FromRgb(76, 175, 80)),    // 绿色
                        KeyGenerator.Models.KeyStatus.Pending => new SolidColorBrush(Color.FromRgb(255, 193, 7)),   // 黄色
                        KeyGenerator.Models.KeyStatus.Revoked => new SolidColorBrush(Color.FromRgb(244, 67, 54)),   // 红色
                        KeyGenerator.Models.KeyStatus.Expired => new SolidColorBrush(Color.FromRgb(158, 158, 158)), // 灰色
                        KeyGenerator.Models.KeyStatus.Suspended => new SolidColorBrush(Color.FromRgb(255, 152, 0)), // 橙色
                        KeyGenerator.Models.KeyStatus.Destroyed => new SolidColorBrush(Color.FromRgb(66, 66, 66)), // 深灰色
                        _ => new SolidColorBrush(Color.FromRgb(96, 125, 139))                                       // 默认蓝灰色
                    };
                }
                catch (Exception)
                {
                    // 如果枚举转换失败，返回默认颜色
                    return new SolidColorBrush(Color.FromRgb(96, 125, 139));
                }
            }

            // 确保总是返回一个有效的 Brush，而不是 DependencyProperty.UnsetValue
            return new SolidColorBrush(Color.FromRgb(96, 125, 139)); // 默认蓝灰色
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 