#pragma once

#include <string>
#include <vector>
#include <memory>
#include <future>
#include "../key_management.h"

namespace crypto {
namespace api {

/**
 * 密钥服务客户端配置
 */
struct KeyServiceConfig {
    std::string serviceUrl;     // 服务URL
    std::string apiKey;         // API密钥
    int timeoutMs = 5000;       // 超时(毫秒)
    bool useTls = true;         // 是否使用TLS
};

/**
 * 密钥服务API客户端
 * 与服务器端密钥管理服务交互
 */
class KeyServiceClient : public key::KeyManager {
public:
    /**
     * 构造函数
     * @param config 客户端配置
     */
    explicit KeyServiceClient(const KeyServiceConfig& config);

    /**
     * 析构函数
     */
    ~KeyServiceClient() override;

    // 禁止复制
    KeyServiceClient(const KeyServiceClient&) = delete;
    KeyServiceClient& operator=(const KeyServiceClient&) = delete;

    // KeyManager接口实现

    /**
     * 创建文件密钥
     * @param parentKeyId 父密钥ID (用户/设备密钥)
     * @param fileId 文件ID
     * @param filePath 文件路径
     * @param algorithm 加密算法
     * @return 文件密钥对象
     */
    std::shared_ptr<key::FileKey> CreateFileKey(
        const std::string& parentKeyId,
        const std::string& fileId,
        const std::string& filePath,
        const std::string& algorithm) override;

    /**
     * 获取文件密钥 (通过密钥ID)
     * @param keyId 密钥ID
     * @return 文件密钥对象
     */
    std::shared_ptr<key::FileKey> GetFileKey(const std::string& keyId) override;

    /**
     * 获取文件密钥 (通过文件ID)
     * @param fileId 文件ID
     * @return 文件密钥对象
     */
    std::shared_ptr<key::FileKey> GetFileKeyByFileId(const std::string& fileId) override;

    /**
     * 获取文件密钥 (通过文件路径)
     * @param filePath 文件路径
     * @return 文件密钥对象
     */
    std::shared_ptr<key::FileKey> GetFileKeyByFilePath(const std::string& filePath) override;

    /**
     * 吊销密钥
     * @param keyId 密钥ID
     * @param recursive 是否递归吊销子密钥
     * @return 操作是否成功
     */
    bool RevokeKey(const std::string& keyId, bool recursive = false) override;

    // 异步API

    /**
     * 异步创建文件密钥
     * @param parentKeyId 父密钥ID
     * @param fileId 文件ID
     * @param filePath 文件路径
     * @param algorithm 加密算法
     * @return 异步结果
     */
    std::future<std::shared_ptr<key::FileKey>> CreateFileKeyAsync(
        const std::string& parentKeyId,
        const std::string& fileId,
        const std::string& filePath,
        const std::string& algorithm);

    /**
     * 异步获取文件密钥
     * @param keyId 密钥ID
     * @return 异步结果
     */
    std::future<std::shared_ptr<key::FileKey>> GetFileKeyAsync(const std::string& keyId);

private:
    class Impl;
    std::unique_ptr<Impl> pImpl_;
};

} // namespace api
} // namespace crypto 