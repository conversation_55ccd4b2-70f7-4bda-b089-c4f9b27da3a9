/*
 * filter_operations.h
 *
 * Cryptosystem Linux 文件操作过滤器接口
 * 定义文件读写操作拦截和处理函数
 */

#ifndef FILTER_OPERATIONS_H
#define FILTER_OPERATIONS_H

#include <sys/types.h>
#include <sys/stat.h>
#include <unistd.h>

/**
 * 初始化过滤操作模块
 * 
 * @return 成功返回0，失败返回-1
 */
int filter_operations_init(void);

/**
 * 清理过滤操作模块
 */
void filter_operations_cleanup(void);

/**
 * 文件打开操作拦截
 * 
 * @param path 文件路径
 * @param flags 打开标志
 * @param mode 创建模式
 * @return 成功返回文件描述符，失败返回-1
 */
int filter_on_open(const char *path, int flags, mode_t mode);

/**
 * 文件关闭操作拦截
 * 
 * @param fd 文件描述符
 * @return 成功返回0，失败返回-1
 */
int filter_on_close(int fd);

/**
 * 文件读取操作拦截
 * 
 * @param fd 文件描述符
 * @param buf 缓冲区
 * @param count 读取字节数
 * @return 成功返回读取的字节数，失败返回-1
 */
ssize_t filter_on_read(int fd, void *buf, size_t count);

/**
 * 文件写入操作拦截
 * 
 * @param fd 文件描述符
 * @param buf 缓冲区
 * @param count 写入字节数
 * @return 成功返回写入的字节数，失败返回-1
 */
ssize_t filter_on_write(int fd, const void *buf, size_t count);

/**
 * 文件定位操作拦截
 * 
 * @param fd 文件描述符
 * @param offset 偏移量
 * @param whence 起始位置
 * @return 成功返回新的文件位置，失败返回-1
 */
off_t filter_on_lseek(int fd, off_t offset, int whence);

/**
 * 文件状态查询拦截
 * 
 * @param path 文件路径
 * @param statbuf 状态缓冲区
 * @return 成功返回0，失败返回-1
 */
int filter_on_stat(const char *path, struct stat *statbuf);

/**
 * 文件删除操作拦截
 * 
 * @param path 文件路径
 * @return 成功返回0，失败返回-1
 */
int filter_on_unlink(const char *path);

/**
 * 文件重命名操作拦截
 * 
 * @param oldpath 原路径
 * @param newpath 新路径
 * @return 成功返回0，失败返回-1
 */
int filter_on_rename(const char *oldpath, const char *newpath);

#endif /* FILTER_OPERATIONS_H */ 