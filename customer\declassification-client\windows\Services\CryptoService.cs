using System.IO;
using System.Security.Cryptography;
using Microsoft.Extensions.Logging;

namespace CryptoSystem.DeclassificationClient.Services
{
    public class CryptoService : ICryptoService
    {
        private readonly ILogger<CryptoService> _logger;
        private const int BUFFER_SIZE = 8192;

        public CryptoService(ILogger<CryptoService> logger)
        {
            _logger = logger;
        }

        public async Task<bool> DecryptFileAsync(string encryptedFilePath, string decryptedFilePath, byte[] key, CryptoAlgorithm algorithm = CryptoAlgorithm.AES256)
        {
            try
            {
                _logger.LogInformation("开始解密文件: {FilePath}", encryptedFilePath);
                
                if (!File.Exists(encryptedFilePath))
                {
                    _logger.LogError("加密文件不存在: {FilePath}", encryptedFilePath);
                    return false;
                }

                using var inputStream = new FileStream(encryptedFilePath, FileMode.Open, FileAccess.Read);
                using var outputStream = new FileStream(decryptedFilePath, FileMode.Create, FileAccess.Write);

                switch (algorithm)
                {
                    case CryptoAlgorithm.AES256:
                        return await DecryptAESAsync(inputStream, outputStream, key);
                    case CryptoAlgorithm.SM4:
                        return await DecryptSM4Async(inputStream, outputStream, key);
                    case CryptoAlgorithm.SM2:
                        return await DecryptSM2Async(inputStream, outputStream, key);
                    default:
                        _logger.LogError("不支持的加密算法: {Algorithm}", algorithm);
                        return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "解密文件失败: {FilePath}", encryptedFilePath);
                return false;
            }
        }

        public async Task<bool> EncryptFileAsync(string plainFilePath, string encryptedFilePath, byte[] key, CryptoAlgorithm algorithm = CryptoAlgorithm.AES256)
        {
            try
            {
                _logger.LogInformation("开始加密文件: {FilePath}", plainFilePath);
                
                if (!File.Exists(plainFilePath))
                {
                    _logger.LogError("明文文件不存在: {FilePath}", plainFilePath);
                    return false;
                }

                using var inputStream = new FileStream(plainFilePath, FileMode.Open, FileAccess.Read);
                using var outputStream = new FileStream(encryptedFilePath, FileMode.Create, FileAccess.Write);

                switch (algorithm)
                {
                    case CryptoAlgorithm.AES256:
                        return await EncryptAESAsync(inputStream, outputStream, key);
                    case CryptoAlgorithm.SM4:
                        return await EncryptSM4Async(inputStream, outputStream, key);
                    case CryptoAlgorithm.SM2:
                        return await EncryptSM2Async(inputStream, outputStream, key);
                    default:
                        _logger.LogError("不支持的加密算法: {Algorithm}", algorithm);
                        return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加密文件失败: {FilePath}", plainFilePath);
                return false;
            }
        }

        private async Task<bool> DecryptAESAsync(Stream inputStream, Stream outputStream, byte[] key)
        {
            try
            {
                using var aes = Aes.Create();
                aes.Key = key;
                aes.Mode = CipherMode.CBC;
                aes.Padding = PaddingMode.PKCS7;

                // 读取IV
                var iv = new byte[16];
                await inputStream.ReadAsync(iv, 0, 16);
                aes.IV = iv;

                using var decryptor = aes.CreateDecryptor();
                using var cryptoStream = new CryptoStream(inputStream, decryptor, CryptoStreamMode.Read);
                
                var buffer = new byte[BUFFER_SIZE];
                int bytesRead;
                
                while ((bytesRead = await cryptoStream.ReadAsync(buffer, 0, buffer.Length)) > 0)
                {
                    await outputStream.WriteAsync(buffer, 0, bytesRead);
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "AES解密失败");
                return false;
            }
        }

        private async Task<bool> EncryptAESAsync(Stream inputStream, Stream outputStream, byte[] key)
        {
            try
            {
                using var aes = Aes.Create();
                aes.Key = key;
                aes.Mode = CipherMode.CBC;
                aes.Padding = PaddingMode.PKCS7;
                aes.GenerateIV();

                // 写入IV
                await outputStream.WriteAsync(aes.IV, 0, aes.IV.Length);

                using var encryptor = aes.CreateEncryptor();
                using var cryptoStream = new CryptoStream(outputStream, encryptor, CryptoStreamMode.Write);
                
                var buffer = new byte[BUFFER_SIZE];
                int bytesRead;
                
                while ((bytesRead = await inputStream.ReadAsync(buffer, 0, buffer.Length)) > 0)
                {
                    await cryptoStream.WriteAsync(buffer, 0, bytesRead);
                }

                await cryptoStream.FlushFinalBlockAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "AES加密失败");
                return false;
            }
        }

        private async Task<bool> DecryptSM4Async(Stream inputStream, Stream outputStream, byte[] key)
        {
            // SM4算法实现（简化版本，实际应使用专业SM4库）
            _logger.LogWarning("SM4解密功能需要专业密码库支持，当前使用AES替代");
            return await DecryptAESAsync(inputStream, outputStream, key);
        }

        private async Task<bool> EncryptSM4Async(Stream inputStream, Stream outputStream, byte[] key)
        {
            // SM4算法实现（简化版本，实际应使用专业SM4库）
            _logger.LogWarning("SM4加密功能需要专业密码库支持，当前使用AES替代");
            return await EncryptAESAsync(inputStream, outputStream, key);
        }

        private async Task<bool> DecryptSM2Async(Stream inputStream, Stream outputStream, byte[] key)
        {
            // SM2算法实现（简化版本，实际应使用专业SM2库）
            _logger.LogWarning("SM2解密功能需要专业密码库支持，当前使用RSA替代");
            return await DecryptRSAAsync(inputStream, outputStream, key);
        }

        private async Task<bool> EncryptSM2Async(Stream inputStream, Stream outputStream, byte[] key)
        {
            // SM2算法实现（简化版本，实际应使用专业SM2库）
            _logger.LogWarning("SM2加密功能需要专业密码库支持，当前使用RSA替代");
            return await EncryptRSAAsync(inputStream, outputStream, key);
        }

        private async Task<bool> DecryptRSAAsync(Stream inputStream, Stream outputStream, byte[] key)
        {
            try
            {
                using var rsa = RSA.Create();
                rsa.ImportRSAPrivateKey(key, out _);

                var buffer = new byte[rsa.KeySize / 8]; // RSA块大小
                int bytesRead;
                
                while ((bytesRead = await inputStream.ReadAsync(buffer, 0, buffer.Length)) > 0)
                {
                    var decryptedBlock = rsa.Decrypt(buffer.AsSpan(0, bytesRead).ToArray(), RSAEncryptionPadding.OaepSHA256);
                    await outputStream.WriteAsync(decryptedBlock, 0, decryptedBlock.Length);
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "RSA解密失败");
                return false;
            }
        }

        private async Task<bool> EncryptRSAAsync(Stream inputStream, Stream outputStream, byte[] key)
        {
            try
            {
                using var rsa = RSA.Create();
                rsa.ImportRSAPublicKey(key, out _);

                var maxBlockSize = (rsa.KeySize / 8) - 42; // OAEP padding overhead
                var buffer = new byte[maxBlockSize];
                int bytesRead;
                
                while ((bytesRead = await inputStream.ReadAsync(buffer, 0, buffer.Length)) > 0)
                {
                    var encryptedBlock = rsa.Encrypt(buffer.AsSpan(0, bytesRead).ToArray(), RSAEncryptionPadding.OaepSHA256);
                    await outputStream.WriteAsync(encryptedBlock, 0, encryptedBlock.Length);
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "RSA加密失败");
                return false;
            }
        }

        public byte[] GenerateKey(CryptoAlgorithm algorithm = CryptoAlgorithm.AES256)
        {
            return algorithm switch
            {
                CryptoAlgorithm.AES256 => GenerateSecureRandom(32),
                CryptoAlgorithm.SM4 => GenerateSecureRandom(16),
                CryptoAlgorithm.SM2 => GenerateSecureRandom(32),
                _ => GenerateSecureRandom(32)
            };
        }

        public byte[] GenerateIV(CryptoAlgorithm algorithm = CryptoAlgorithm.AES256)
        {
            return algorithm switch
            {
                CryptoAlgorithm.AES256 => GenerateSecureRandom(16),
                CryptoAlgorithm.SM4 => GenerateSecureRandom(16),
                CryptoAlgorithm.SM2 => GenerateSecureRandom(16),
                _ => GenerateSecureRandom(16)
            };
        }

        public byte[] DeriveKeyFromPassword(string password, byte[] salt, int iterations = 10000, int keyLength = 32)
        {
            using var pbkdf2 = new Rfc2898DeriveBytes(password, salt, iterations, HashAlgorithmName.SHA256);
            return pbkdf2.GetBytes(keyLength);
        }

        public byte[] GenerateSalt(int length = 16) => GenerateSecureRandom(length);

        public async Task<string> CalculateFileHashAsync(string filePath, string algorithm = "MD5")
        {
            try
            {
                using var stream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
                using var hashAlg = algorithm.ToUpper() switch
                {
                    "MD5" => MD5.Create(),
                    "SHA256" => SHA256.Create(),
                    "SHA1" => SHA1.Create(),
                    _ => MD5.Create()
                };

                var hashBytes = await hashAlg.ComputeHashAsync(stream);
                return Convert.ToHexString(hashBytes);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算文件哈希失败: {FilePath}", filePath);
                return string.Empty;
            }
        }

        public async Task<bool> VerifyDigitalSignatureAsync(string filePath, byte[] signature, byte[] publicKey)
        {
            try
            {
                using var rsa = RSA.Create();
                rsa.ImportRSAPublicKey(publicKey, out _);

                var fileData = await File.ReadAllBytesAsync(filePath);
                return rsa.VerifyData(fileData, signature, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证数字签名失败: {FilePath}", filePath);
                return false;
            }
        }

        public async Task<byte[]> CreateDigitalSignatureAsync(string filePath, byte[] privateKey)
        {
            try
            {
                using var rsa = RSA.Create();
                rsa.ImportRSAPrivateKey(privateKey, out _);

                var fileData = await File.ReadAllBytesAsync(filePath);
                return rsa.SignData(fileData, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建数字签名失败: {FilePath}", filePath);
                return Array.Empty<byte>();
            }
        }

        public async Task<bool> IsFileEncryptedAsync(string filePath)
        {
            try
            {
                if (!File.Exists(filePath)) return false;

                using var stream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
                var header = new byte[16];
                await stream.ReadAsync(header, 0, 16);

                // 检查文件头部特征（简化检测）
                return header[0] == 0x43 && header[1] == 0x52 && header[2] == 0x59 && header[3] == 0x50; // "CRYP"
            }
            catch
            {
                return false;
            }
        }

        public async Task<(CryptoAlgorithm Algorithm, int KeyLength, bool IsEncrypted)> GetFileEncryptionInfoAsync(string filePath)
        {
            var isEncrypted = await IsFileEncryptedAsync(filePath);
            if (!isEncrypted)
                return (CryptoAlgorithm.AES256, 0, false);

            try
            {
                using var stream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
                var header = new byte[8];
                await stream.ReadAsync(header, 0, 8);

                // 解析算法信息（简化版本）
                var algorithm = header[4] switch
                {
                    1 => CryptoAlgorithm.AES256,
                    2 => CryptoAlgorithm.SM4,
                    3 => CryptoAlgorithm.SM2,
                    _ => CryptoAlgorithm.AES256
                };

                var keyLength = header[5] * 8; // 密钥长度（位）
                return (algorithm, keyLength, true);
            }
            catch
            {
                return (CryptoAlgorithm.AES256, 256, true);
            }
        }

        public async Task<bool> SecureDeleteFileAsync(string filePath, int passes = 3)
        {
            try
            {
                if (!File.Exists(filePath)) return true;

                var fileInfo = new FileInfo(filePath);
                var fileSize = fileInfo.Length;

                // 多次覆盖文件内容
                using var stream = new FileStream(filePath, FileMode.Open, FileAccess.Write);
                var buffer = new byte[BUFFER_SIZE];

                for (int pass = 0; pass < passes; pass++)
                {
                    stream.Seek(0, SeekOrigin.Begin);
                    
                    // 使用随机数据覆盖
                    using var rng = RandomNumberGenerator.Create();
                    long remaining = fileSize;
                    
                    while (remaining > 0)
                    {
                        var bytesToWrite = (int)Math.Min(buffer.Length, remaining);
                        rng.GetBytes(buffer.AsSpan(0, bytesToWrite));
                        await stream.WriteAsync(buffer, 0, bytesToWrite);
                        remaining -= bytesToWrite;
                    }
                    
                    await stream.FlushAsync();
                }

                // 删除文件
                File.Delete(filePath);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "安全删除文件失败: {FilePath}", filePath);
                return false;
            }
        }

        public byte[] GenerateSecureRandom(int length)
        {
            var buffer = new byte[length];
            using var rng = RandomNumberGenerator.Create();
            rng.GetBytes(buffer);
            return buffer;
        }

        public async Task<bool> CompressAndEncryptFilesAsync(string[] sourceFiles, string targetPath, string password, int compressionLevel = 6)
        {
            try
            {
                using var tempStream = new MemoryStream();
                
                // 压缩文件
                using (var archive = new ZipArchive(tempStream, ZipArchiveMode.Create, true))
                {
                    foreach (var sourceFile in sourceFiles)
                    {
                        if (!File.Exists(sourceFile)) continue;
                        
                        var entry = archive.CreateEntry(Path.GetFileName(sourceFile), (CompressionLevel)compressionLevel);
                        using var entryStream = entry.Open();
                        using var fileStream = new FileStream(sourceFile, FileMode.Open, FileAccess.Read);
                        await fileStream.CopyToAsync(entryStream);
                    }
                }

                // 加密压缩后的数据
                tempStream.Seek(0, SeekOrigin.Begin);
                var salt = GenerateSalt();
                var key = DeriveKeyFromPassword(password, salt);

                using var outputStream = new FileStream(targetPath, FileMode.Create, FileAccess.Write);
                await outputStream.WriteAsync(salt, 0, salt.Length); // 写入盐值
                
                return await EncryptAESAsync(tempStream, outputStream, key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "压缩加密文件失败");
                return false;
            }
        }

        public async Task<bool> DecryptAndExtractFilesAsync(string archivePath, string extractPath, string password)
        {
            try
            {
                using var inputStream = new FileStream(archivePath, FileMode.Open, FileAccess.Read);
                
                // 读取盐值
                var salt = new byte[16];
                await inputStream.ReadAsync(salt, 0, 16);
                var key = DeriveKeyFromPassword(password, salt);

                // 解密数据
                using var decryptedStream = new MemoryStream();
                if (!await DecryptAESAsync(inputStream, decryptedStream, key))
                    return false;

                // 解压缩文件
                decryptedStream.Seek(0, SeekOrigin.Begin);
                using var archive = new ZipArchive(decryptedStream, ZipArchiveMode.Read);
                
                foreach (var entry in archive.Entries)
                {
                    var entryPath = Path.Combine(extractPath, entry.FullName);
                    Directory.CreateDirectory(Path.GetDirectoryName(entryPath)!);
                    
                    using var entryStream = entry.Open();
                    using var outputStream = new FileStream(entryPath, FileMode.Create, FileAccess.Write);
                    await entryStream.CopyToAsync(outputStream);
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "解密解压文件失败");
                return false;
            }
        }

        public int ValidatePasswordStrength(string password)
        {
            if (string.IsNullOrEmpty(password)) return 0;

            int score = 0;
            
            // 长度评分
            score += Math.Min(password.Length * 4, 25);
            
            // 字符类型评分
            if (password.Any(char.IsLower)) score += 5;
            if (password.Any(char.IsUpper)) score += 5;
            if (password.Any(char.IsDigit)) score += 5;
            if (password.Any(ch => !char.IsLetterOrDigit(ch))) score += 10;
            
            // 复杂度评分
            if (password.Length >= 8) score += 10;
            if (password.Length >= 12) score += 15;
            
            return Math.Min(score, 100);
        }

        public string GenerateSecurePassword(int length = 12, bool includeSpecialChars = true)
        {
            const string lowercase = "abcdefghijklmnopqrstuvwxyz";
            const string uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
            const string digits = "0123456789";
            const string special = "!@#$%^&*()_+-=[]{}|;:,.<>?";

            var chars = lowercase + uppercase + digits;
            if (includeSpecialChars) chars += special;

            var password = new StringBuilder();
            using var rng = RandomNumberGenerator.Create();

            // 确保至少包含每种字符类型
            password.Append(GetRandomChar(lowercase, rng));
            password.Append(GetRandomChar(uppercase, rng));
            password.Append(GetRandomChar(digits, rng));
            if (includeSpecialChars) password.Append(GetRandomChar(special, rng));

            // 填充剩余长度
            for (int i = password.Length; i < length; i++)
            {
                password.Append(GetRandomChar(chars, rng));
            }

            // 随机打乱顺序
            return new string(password.ToString().ToCharArray().OrderBy(x => GetRandomInt(rng)).ToArray());
        }

        private char GetRandomChar(string chars, RandomNumberGenerator rng)
        {
            var randomBytes = new byte[4];
            rng.GetBytes(randomBytes);
            var randomInt = BitConverter.ToUInt32(randomBytes, 0);
            return chars[(int)(randomInt % chars.Length)];
        }

        private int GetRandomInt(RandomNumberGenerator rng)
        {
            var randomBytes = new byte[4];
            rng.GetBytes(randomBytes);
            return BitConverter.ToInt32(randomBytes, 0);
        }
    }
} 