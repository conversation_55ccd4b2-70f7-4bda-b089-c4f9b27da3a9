import FileProvider
import Foundation
import os.log

/// 文件提供者扩展 - 实现透明加密
class FileProviderExtension: NSFileProviderExtension {
    
    private let logger = OSLog(subsystem: "com.cryptosystem.fileprovider", category: "Extension")
    
    // 策略管理器
    private let policyManager = PolicyManager.shared
    
    // 加密管理器
    private let cryptoManager = CryptoManager.shared
    
    // 默认加密算法
    private let defaultAlgorithm: EncryptionAlgorithm = .aesGCM
    
    // 默认密钥版本
    private var defaultKeyVersion: UInt32 = 1
    
    // 文件路径到加密状态的映射
    private var encryptedFiles: [NSFileProviderItemIdentifier: Bool] = [:]
    
    // 文件路径到加密上下文的映射
    private var fileContexts: [NSFileProviderItemIdentifier: CryptoContext] = [:]
    
    override init() {
        super.init()
        os_log("FileProviderExtension 初始化", log: logger, type: .info)
        
        // 加载配置
        loadConfiguration()
        
        // 注册通知
        registerForNotifications()
    }
    
    deinit {
        unregisterForNotifications()
    }
    
    // MARK: - 配置管理
    
    private func loadConfiguration() {
        os_log("加载配置", log: logger, type: .debug)
        
        // 从配置中加载默认密钥版本
        if let keyVersion = UserDefaults.standard.object(forKey: "default_key_version") as? UInt32 {
            defaultKeyVersion = keyVersion
        }
        
        // 加载已加密文件列表
        loadEncryptedFilesList()
    }
    
    private func loadEncryptedFilesList() {
        // 从持久存储加载加密文件列表
        // 实际实现应该从数据库或安全存储中加载
    }
    
    private func saveEncryptedFilesList() {
        // 保存加密文件列表到持久存储
        // 实际实现应该保存到数据库或安全存储
    }
    
    // MARK: - 通知管理
    
    private func registerForNotifications() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(policyDidChange(_:)),
            name: NSNotification.Name("PolicyDidChangeNotification"),
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(keyDidChange(_:)),
            name: NSNotification.Name("KeyDidChangeNotification"),
            object: nil
        )
    }
    
    private func unregisterForNotifications() {
        NotificationCenter.default.removeObserver(self)
    }
    
    @objc private func policyDidChange(_ notification: Notification) {
        os_log("策略发生变化", log: logger, type: .info)
        // 刷新策略缓存
        // 可能需要重新评估某些文件的加密状态
    }
    
    @objc private func keyDidChange(_ notification: Notification) {
        os_log("密钥发生变化", log: logger, type: .info)
        // 处理密钥变更
        if let keyVersion = notification.userInfo?["version"] as? UInt32 {
            // 如果是默认密钥更新，需要更新默认版本
            if let isDefault = notification.userInfo?["is_default"] as? Bool, isDefault {
                defaultKeyVersion = keyVersion
            }
        }
    }
    
    // MARK: - 文件操作
    
    override func providePlaceholder(at url: URL, completionHandler: @escaping (Error?) -> Void) {
        // 提供占位符
        os_log("提供占位符: %{public}@", log: logger, type: .debug, url.path)
        
        do {
            // 创建占位符目录
            try FileManager.default.createDirectory(
                at: NSFileProviderManager.default.placeholderURL(for: NSFileProviderItemIdentifier(url.path)),
                withIntermediateDirectories: true,
                attributes: nil
            )
            completionHandler(nil)
        } catch {
            os_log("提供占位符失败: %{public}@", log: logger, type: .error, error.localizedDescription)
            completionHandler(error)
        }
    }
    
    override func startProvidingItem(at url: URL, completionHandler: @escaping (Error?) -> Void) {
        // 开始提供项目
        os_log("开始提供项目: %{public}@", log: logger, type: .debug, url.path)
        
        let itemIdentifier = NSFileProviderItemIdentifier(url.path)
        
        // 检查文件是否应该加密
        let shouldEncrypt = shouldEncryptFile(at: url)
        
        if shouldEncrypt {
            // 文件应该加密，记录状态
            encryptedFiles[itemIdentifier] = true
            
            // 创建加密上下文
            let contextResult = cryptoManager.createContext(algorithm: defaultAlgorithm, keyVersion: defaultKeyVersion)
            if contextResult.result == .success, let context = contextResult.context {
                fileContexts[itemIdentifier] = context
            } else {
                os_log("创建加密上下文失败: %{public}d", log: logger, type: .error, contextResult.result.rawValue)
                completionHandler(NSError(domain: NSFileProviderErrorDomain, code: NSFileProviderError.serverUnreachable.rawValue, userInfo: nil))
                return
            }
        } else {
            // 文件不需要加密
            encryptedFiles[itemIdentifier] = false
        }
        
        saveEncryptedFilesList()
        completionHandler(nil)
    }
    
    override func stopProvidingItem(at url: URL) {
        // 停止提供项目
        os_log("停止提供项目: %{public}@", log: logger, type: .debug, url.path)
        
        let itemIdentifier = NSFileProviderItemIdentifier(url.path)
        
        // 清理资源
        fileContexts.removeValue(forKey: itemIdentifier)
        
        do {
            // 删除占位符
            try FileManager.default.removeItem(at: NSFileProviderManager.default.placeholderURL(for: NSFileProviderItemIdentifier(url.path)))
        } catch {
            os_log("删除占位符失败: %{public}@", log: logger, type: .error, error.localizedDescription)
        }
    }
    
    // MARK: - 文件读写

    override func itemChanged(at url: URL) {
        // 文件变更通知
        os_log("文件变更: %{public}@", log: logger, type: .debug, url.path)
        
        let itemIdentifier = NSFileProviderItemIdentifier(url.path)
        
        // 如果文件需要加密，但尚未处理
        if shouldEncryptFile(at: url) && encryptedFiles[itemIdentifier] != true {
            encryptFile(at: url)
        }
        
        // 通知系统文件发生变化
        NSFileProviderManager.default.signalEnumerator(for: .workingSet) { error in
            if let error = error {
                os_log("信号枚举器失败: %{public}@", log: logger, type: .error, error.localizedDescription)
            }
        }
    }
    
    // MARK: - 加密相关
    
    private func shouldEncryptFile(at url: URL) -> Bool {
        // 根据策略判断文件是否应该加密
        return policyManager.shouldEncryptFile(at: url.path)
    }
    
    private func encryptFile(at url: URL) {
        os_log("开始加密文件: %{public}@", log: logger, type: .info, url.path)
        
        let itemIdentifier = NSFileProviderItemIdentifier(url.path)
        
        // 检查文件是否存在
        guard FileManager.default.fileExists(atPath: url.path) else {
            os_log("文件不存在: %{public}@", log: logger, type: .error, url.path)
            return
        }
        
        do {
            // 读取原始文件内容
            let originalData = try Data(contentsOf: url)
            let originalSize = UInt64(originalData.count)
            
            // 创建加密上下文
            let contextResult = cryptoManager.createContext(algorithm: defaultAlgorithm, keyVersion: defaultKeyVersion)
            guard contextResult.result == .success, let context = contextResult.context else {
                os_log("创建加密上下文失败: %{public}d", log: logger, type: .error, contextResult.result.rawValue)
                return
            }
            
            // 加密文件内容
            let encryptResult = cryptoManager.encrypt(context: context, plainText: originalData)
            guard encryptResult.result == .success, let encryptedData = encryptResult.cipherText else {
                os_log("加密失败: %{public}d", log: logger, type: .error, encryptResult.result.rawValue)
                return
            }
            
            // 创建文件头
            let headerResult = cryptoManager.createFileHeader(context: context, originalFileSize: originalSize)
            guard headerResult.result == .success else {
                os_log("创建文件头失败: %{public}d", log: logger, type: .error, headerResult.result.rawValue)
                return
            }
            
            // 组装最终文件：文件头 + 加密数据
            var finalData = Data()
            finalData.append(headerResult.header.toData())
            finalData.append(encryptedData)
            
            // 备份原始文件
            let backupUrl = url.appendingPathExtension("backup")
            try FileManager.default.moveItem(at: url, to: backupUrl)
            
            // 写入加密文件
            try finalData.write(to: url)
            
            // 删除备份文件
            try FileManager.default.removeItem(at: backupUrl)
            
            // 记录加密状态
            encryptedFiles[itemIdentifier] = true
            fileContexts[itemIdentifier] = context
            saveEncryptedFilesList()
            
            os_log("文件加密成功: %{public}@", log: logger, type: .info, url.path)
            
        } catch {
            os_log("加密文件失败: %{public}@", log: logger, type: .error, error.localizedDescription)
        }
    }
    
    private func decryptFile(at url: URL) -> Data? {
        os_log("开始解密文件: %{public}@", log: logger, type: .debug, url.path)
        
        do {
            // 读取加密文件
            let encryptedFileData = try Data(contentsOf: url)
            
            // 检查文件大小
            guard encryptedFileData.count > 64 else { // 文件头最小大小
                os_log("文件太小，不是有效的加密文件: %{public}@", log: logger, type: .error, url.path)
                return nil
            }
            
            // 提取文件头
            let headerData = encryptedFileData.prefix(64) // 假设文件头大小为64字节
            let encryptedData = encryptedFileData.suffix(from: 64)
            
            // 验证文件头并创建上下文
            let verifyResult = cryptoManager.verifyFileHeader(headerData: headerData)
            guard verifyResult.result == .success,
                  let header = verifyResult.header,
                  let context = verifyResult.context else {
                os_log("验证文件头失败: %{public}d", log: logger, type: .error, verifyResult.result.rawValue)
                return nil
            }
            
            // 解密数据
            let decryptResult = cryptoManager.decrypt(context: context, cipherText: encryptedData)
            guard decryptResult.result == .success, let decryptedData = decryptResult.plainText else {
                os_log("解密失败: %{public}d", log: logger, type: .error, decryptResult.result.rawValue)
                return nil
            }
            
            // 验证解密后的数据大小
            if UInt64(decryptedData.count) != header.originalFileSize {
                os_log("解密后的数据大小不匹配: 期望 %lld，实际 %d", 
                       log: logger, type: .warning, header.originalFileSize, decryptedData.count)
            }
            
            os_log("文件解密成功: %{public}@", log: logger, type: .debug, url.path)
            return decryptedData
            
        } catch {
            os_log("解密文件失败: %{public}@", log: logger, type: .error, error.localizedDescription)
            return nil
        }
    }
    
    // MARK: - 文件内容提供
    
    override func contents(for url: URL) throws -> Data {
        os_log("请求文件内容: %{public}@", log: logger, type: .debug, url.path)
        
        let itemIdentifier = NSFileProviderItemIdentifier(url.path)
        
        // 检查文件是否加密
        if let isEncrypted = encryptedFiles[itemIdentifier], isEncrypted {
            // 文件已加密，需要解密后返回
            if let decryptedData = decryptFile(at: url) {
                return decryptedData
            } else {
                throw NSError(domain: NSFileProviderErrorDomain, 
                            code: NSFileProviderError.serverUnreachable.rawValue, 
                            userInfo: [NSLocalizedDescriptionKey: "解密文件失败"])
            }
        } else {
            // 文件未加密，直接返回
            return try Data(contentsOf: url)
        }
    }
    
    // MARK: - 文件写入
    
    override func write(_ data: Data, to url: URL) throws {
        os_log("写入文件: %{public}@", log: logger, type: .debug, url.path)
        
        let itemIdentifier = NSFileProviderItemIdentifier(url.path)
        
        // 检查是否需要加密
        if shouldEncryptFile(at: url) {
            // 需要加密
            let contextResult = cryptoManager.createContext(algorithm: defaultAlgorithm, keyVersion: defaultKeyVersion)
            guard contextResult.result == .success, let context = contextResult.context else {
                throw NSError(domain: NSFileProviderErrorDomain, 
                            code: NSFileProviderError.serverUnreachable.rawValue, 
                            userInfo: [NSLocalizedDescriptionKey: "创建加密上下文失败"])
            }
            
            // 加密数据
            let encryptResult = cryptoManager.encrypt(context: context, plainText: data)
            guard encryptResult.result == .success, let encryptedData = encryptResult.cipherText else {
                throw NSError(domain: NSFileProviderErrorDomain, 
                            code: NSFileProviderError.serverUnreachable.rawValue, 
                            userInfo: [NSLocalizedDescriptionKey: "加密数据失败"])
            }
            
            // 创建文件头
            let headerResult = cryptoManager.createFileHeader(context: context, originalFileSize: UInt64(data.count))
            guard headerResult.result == .success else {
                throw NSError(domain: NSFileProviderErrorDomain, 
                            code: NSFileProviderError.serverUnreachable.rawValue, 
                            userInfo: [NSLocalizedDescriptionKey: "创建文件头失败"])
            }
            
            // 组装最终数据
            var finalData = Data()
            finalData.append(headerResult.header.toData())
            finalData.append(encryptedData)
            
            // 写入加密文件
            try finalData.write(to: url)
            
            // 记录状态
            encryptedFiles[itemIdentifier] = true
            fileContexts[itemIdentifier] = context
            saveEncryptedFilesList()
            
        } else {
            // 不需要加密，直接写入
            try data.write(to: url)
            encryptedFiles[itemIdentifier] = false
        }
    }
}

// MARK: - 策略管理器 (简化版)
class PolicyManager {
    static let shared = PolicyManager()
    
    private init() {
        // 加载策略
    }
    
    func shouldEncryptFile(at path: String) -> Bool {
        // 实际实现应该根据策略配置决定
        // 这里是简化版，仅用于演示
        
        // 加密所有文档和图片
        let documentsExtensions = [".doc", ".docx", ".pdf", ".txt", ".xls", ".xlsx", ".ppt", ".pptx"]
        let imageExtensions = [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff"]
        
        for ext in documentsExtensions + imageExtensions {
            if path.lowercased().hasSuffix(ext) {
                return true
            }
        }
        
        return false
    }
} 