{"format": 1, "restore": {"E:\\Projects\\my-dev\\cryptosystem\\operator\\key-generator\\windows\\KeyGenerator.csproj": {}}, "projects": {"E:\\Projects\\my-dev\\cryptosystem\\operator\\key-generator\\windows\\KeyGenerator.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Projects\\my-dev\\cryptosystem\\operator\\key-generator\\windows\\KeyGenerator.csproj", "projectName": "KeyGenerator", "projectPath": "E:\\Projects\\my-dev\\cryptosystem\\operator\\key-generator\\windows\\KeyGenerator.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Projects\\my-dev\\cryptosystem\\operator\\key-generator\\windows\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.4.0, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[9.0.0, )"}, "MySql.Data": {"target": "Package", "version": "[9.1.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Npgsql": {"target": "Package", "version": "[9.0.2, )"}, "System.Data.SqlClient": {"target": "Package", "version": "[4.8.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}