<UserControl x:Class="KeyGenerator.Views.KeyManagementView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008" mc:Ignorable="d" d:DesignHeight="800" d:DesignWidth="1200">

    <UserControl.Resources>
        <!-- 样式资源 -->
        <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
            <Setter Property="Margin" Value="0,0,0,16"/>
        </Style>

        <Style x:Key="ActionButtonStyle" TargetType="Button">
            <Setter Property="Height" Value="36"/>
            <Setter Property="MinWidth" Value="100"/>
            <Setter Property="Margin" Value="8,0,0,0"/>
            <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
        </Style>

        <Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource ActionButtonStyle}">
            <Setter Property="Background" Value="{StaticResource SecondaryBrush}"/>
            <Setter Property="Foreground" Value="{StaticResource PrimaryTextBrush}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        </Style>

        <Style x:Key="DataGridStyle" TargetType="DataGrid">
            <Setter Property="AutoGenerateColumns" Value="False"/>
            <Setter Property="CanUserAddRows" Value="False"/>
            <Setter Property="CanUserDeleteRows" Value="False"/>
            <Setter Property="SelectionMode" Value="Extended"/>
            <Setter Property="GridLinesVisibility" Value="Horizontal"/>
            <Setter Property="HeadersVisibility" Value="Column"/>
            <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="RowBackground" Value="Transparent"/>
            <Setter Property="AlternatingRowBackground" Value="{StaticResource DataGridAlternatingRowBrush}"/>
        </Style>
    </UserControl.Resources>

    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题和主要操作 -->
        <Grid Grid.Row="0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <TextBlock Text="密钥管理" Style="{StaticResource HeaderTextStyle}"/>

            <StackPanel Grid.Column="1" Orientation="Horizontal">
                <Button Content="生成密钥" Style="{StaticResource ActionButtonStyle}" Command="{Binding GenerateKeyCommand}" ToolTip="创建新的密钥"/>
                <Button Content="刷新" Style="{StaticResource SecondaryButtonStyle}" Command="{Binding RefreshCommand}" ToolTip="重新加载密钥列表"/>
            </StackPanel>
        </Grid>

        <!-- 搜索 -->
        <Grid Grid.Row="1" Margin="0,0,0,16">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="400"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <TextBox Grid.Column="0" Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}" Tag="搜索客户名称或密钥名称..." Height="36" VerticalContentAlignment="Center" Padding="12,0" ToolTip="输入关键字搜索客户名称或密钥名称"/>
        </Grid>

        <!-- 简化的操作提示 -->
        <Grid Grid.Row="2" Margin="0,0,0,16">
            <TextBlock Text="双击密钥行进行编辑" Foreground="{StaticResource SecondaryTextBrush}" FontSize="12" VerticalAlignment="Center"/>
        </Grid>

        <!-- 密钥列表 -->
        <DataGrid Grid.Row="3" ItemsSource="{Binding FilteredKeys}" SelectedItem="{Binding SelectedKey}" Style="{StaticResource DataGridStyle}">
            <DataGrid.Columns>
                <DataGridTextColumn Header="密钥ID" Binding="{Binding KeyId}" Width="200"/>
                <DataGridTextColumn Header="密钥名称" Binding="{Binding KeyName}" Width="200"/>
                <DataGridTextColumn Header="客户名称" Binding="{Binding ClientName}" Width="200"/>
                <DataGridTextColumn Header="生效日期" Binding="{Binding EffectiveDate, StringFormat=yyyy-MM-dd}" Width="120"/>
                <DataGridTextColumn Header="过期日期" Binding="{Binding ExpirationDate, StringFormat=yyyy-MM-dd}" Width="120"/>
                <DataGridTemplateColumn Header="状态" Width="100">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <Border Background="{Binding KeyStatus, Converter={StaticResource StatusToBrushConverter}}" CornerRadius="12" Padding="8,4">
                                <TextBlock Text="{Binding KeyStatus, StringFormat='{}{0}', FallbackValue='未知'}" Foreground="White" FontSize="12" HorizontalAlignment="Center"/>
                            </Border>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                <DataGridTemplateColumn Header="操作" Width="160">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <Button Content="编辑" Style="{StaticResource SecondaryButtonStyle}" Width="60" Height="28" Margin="2" Command="{Binding DataContext.EditKeyCommand, RelativeSource={RelativeSource AncestorType=UserControl}}" CommandParameter="{Binding}" ToolTip="编辑密钥信息"/>
                                <Button Content="删除" Style="{StaticResource SecondaryButtonStyle}" Width="60" Height="28" Margin="2" Foreground="{StaticResource ErrorBrush}" Command="{Binding DataContext.DeleteKeyCommand, RelativeSource={RelativeSource AncestorType=UserControl}}" CommandParameter="{Binding}" ToolTip="删除此密钥"/>
                                <Button Content="分发" Style="{StaticResource AccentButtonStyle}" Width="60" Height="28" Margin="2" Command="{Binding DataContext.DistributeKeyCommand, RelativeSource={RelativeSource AncestorType=UserControl}}" CommandParameter="{Binding}" ToolTip="分发密钥"/>
                            </StackPanel>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>

        <!-- 状态栏 -->
        <Grid Grid.Row="4" Margin="0,16,0,0">
            <TextBlock Text="{Binding TotalCount, StringFormat=共 {0} 个密钥}" Foreground="{StaticResource SecondaryTextBrush}" VerticalAlignment="Center"/>
        </Grid>
    </Grid>
</UserControl> 