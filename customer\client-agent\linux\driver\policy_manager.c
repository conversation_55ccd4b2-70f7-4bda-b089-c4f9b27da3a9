/*
 * policy_manager.c
 *
 * Cryptosystem Linux 策略管理器实现
 * 负责解析和应用加密策略规则
 */

#include "policy_manager.h"
#include "key_sync_service.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <pthread.h>
#include <fnmatch.h>
#include <unistd.h>
#include <time.h>
#include <syslog.h>

// 最大策略数量
#define MAX_POLICIES 1000

// 默认策略
#define DEFAULT_ALGORITHM ALGORITHM_AES
#define DEFAULT_MODE MODE_GCM
#define DEFAULT_KEY_VERSION 1

// 策略数据
static struct {
    policy_rule *rules;            // 策略规则数组
    size_t count;                  // 当前策略数量
    size_t capacity;               // 数组容量
    time_t last_update;            // 最后更新时间
    pthread_rwlock_t lock;         // 读写锁
    int initialized;               // 是否已初始化
} policy_data = {
    .rules = NULL,
    .count = 0,
    .capacity = 0,
    .last_update = 0,
    .initialized = 0
};

// 初始化策略管理器
int policy_init(void) {
    if (policy_data.initialized) {
        return 0;  // 已初始化
    }
    
    // 初始化读写锁
    if (pthread_rwlock_init(&policy_data.lock, NULL) != 0) {
        return -1;
    }
    
    // 分配策略缓冲区
    policy_data.rules = malloc(MAX_POLICIES * sizeof(policy_rule));
    if (!policy_data.rules) {
        pthread_rwlock_destroy(&policy_data.lock);
        return -1;
    }
    
    policy_data.capacity = MAX_POLICIES;
    policy_data.count = 0;
    policy_data.initialized = 1;
    
    // 首次加载策略
    if (policy_reload() != 0) {
        syslog(LOG_WARNING, "策略管理器: 初始策略加载失败, 将使用默认策略");
    }
    
    return 0;
}

// 清理策略管理器
void policy_cleanup(void) {
    if (!policy_data.initialized) {
        return;
    }
    
    pthread_rwlock_wrlock(&policy_data.lock);
    
    free(policy_data.rules);
    policy_data.rules = NULL;
    policy_data.count = 0;
    policy_data.capacity = 0;
    policy_data.initialized = 0;
    
    pthread_rwlock_unlock(&policy_data.lock);
    pthread_rwlock_destroy(&policy_data.lock);
}

// 重新加载策略
int policy_reload(void) {
    if (!policy_data.initialized) {
        return -1;
    }
    
    // 从服务器获取最新策略
    policy_rule temp_rules[MAX_POLICIES];
    size_t rule_count = 0;
    
    if (key_sync_get_policies(temp_rules, MAX_POLICIES, &rule_count) != SYNC_STATUS_OK) {
        return -1;
    }
    
    // 检查数量
    if (rule_count > policy_data.capacity) {
        rule_count = policy_data.capacity;
    }
    
    // 更新到策略缓冲区
    pthread_rwlock_wrlock(&policy_data.lock);
    
    memcpy(policy_data.rules, temp_rules, rule_count * sizeof(policy_rule));
    policy_data.count = rule_count;
    policy_data.last_update = time(NULL);
    
    pthread_rwlock_unlock(&policy_data.lock);
    
    syslog(LOG_INFO, "策略管理器: 已加载 %zu 条策略规则", rule_count);
    return 0;
}

// 路径匹配函数
static int path_match(const char *pattern, const char *path) {
    // 使用fnmatch进行通配符匹配
    return fnmatch(pattern, path, FNM_PATHNAME) == 0;
}

// 为指定路径和用户/应用做策略决策
int policy_evaluate(const char *path, const char *app_id, 
                   const char *user_id, policy_decision *decision) {
    if (!policy_data.initialized || !path || !decision) {
        return -1;
    }
    
    // 初始化决策结果为默认值
    memset(decision, 0, sizeof(policy_decision));
    decision->result = POLICY_RESULT_NO_MATCH;
    decision->algorithm = DEFAULT_ALGORITHM;
    decision->mode = DEFAULT_MODE;
    decision->key_version = DEFAULT_KEY_VERSION;
    decision->rule_priority = 0;
    
    pthread_rwlock_rdlock(&policy_data.lock);
    
    // 如果没有策略，使用默认值
    if (policy_data.count == 0) {
        pthread_rwlock_unlock(&policy_data.lock);
        return 0;
    }
    
    // 寻找优先级最高的匹配规则
    int found = 0;
    uint32_t highest_priority = 0;
    
    for (size_t i = 0; i < policy_data.count; i++) {
        policy_rule *rule = &policy_data.rules[i];
        int matches = 0;
        
        // 根据规则类型检查匹配
        switch (rule->type) {
            case POLICY_TYPE_PATH:
                matches = path_match(rule->target, path);
                break;
                
            case POLICY_TYPE_APP:
                if (app_id) {
                    matches = (strcmp(rule->target, app_id) == 0);
                }
                break;
                
            case POLICY_TYPE_USER:
                if (user_id) {
                    matches = (strcmp(rule->target, user_id) == 0);
                }
                break;
                
            case POLICY_TYPE_GROUP:
                // 组匹配需要额外的组成员查询，这里简化处理
                // 实际实现应查询用户所属组
                if (user_id) {
                    matches = 0;  // 需要真实实现
                }
                break;
                
            case POLICY_TYPE_DEVICE:
                // 设备匹配，通常针对当前设备的全局策略
                matches = 1;  // 当前设备始终匹配
                break;
                
            default:
                matches = 0;
                break;
        }
        
        // 如果匹配且优先级更高，更新决策
        if (matches && rule->priority >= highest_priority) {
            highest_priority = rule->priority;
            
            // 将规则动作映射到决策结果
            switch (rule->action) {
                case POLICY_ACTION_ENCRYPT:
                    decision->result = POLICY_RESULT_ENCRYPT;
                    break;
                    
                case POLICY_ACTION_DECRYPT:
                    decision->result = POLICY_RESULT_DECRYPT;
                    break;
                    
                case POLICY_ACTION_DENY:
                    decision->result = POLICY_RESULT_DENY;
                    break;
                    
                case POLICY_ACTION_ALLOW:
                    decision->result = POLICY_RESULT_ALLOW;
                    break;
                    
                default:
                    continue;  // 跳过无效动作
            }
            
            // 复制其他决策详情
            decision->algorithm = rule->algorithm;
            decision->mode = rule->mode;
            decision->key_version = rule->key_version;
            decision->rule_priority = rule->priority;
            strncpy(decision->rule_target, rule->target, sizeof(decision->rule_target) - 1);
            decision->rule_target[sizeof(decision->rule_target) - 1] = '\0';
            
            found = 1;
        }
    }
    
    pthread_rwlock_unlock(&policy_data.lock);
    
    // 如果找到匹配的规则，上报活动日志
    if (found) {
        const char *operation = NULL;
        switch (decision->result) {
            case POLICY_RESULT_ENCRYPT:
                operation = "encrypt";
                break;
            case POLICY_RESULT_DECRYPT:
                operation = "decrypt";
                break;
            case POLICY_RESULT_DENY:
                operation = "deny";
                break;
            case POLICY_RESULT_ALLOW:
                operation = "allow";
                break;
            default:
                operation = "unknown";
                break;
        }
        
        // 异步上报活动（不阻塞策略评估）
        key_sync_report_activity(operation, path, user_id, app_id, 0);
    }
    
    return 0;
}

// 获取策略数量
size_t policy_get_count(void) {
    if (!policy_data.initialized) {
        return 0;
    }
    
    pthread_rwlock_rdlock(&policy_data.lock);
    size_t count = policy_data.count;
    pthread_rwlock_unlock(&policy_data.lock);
    
    return count;
} 