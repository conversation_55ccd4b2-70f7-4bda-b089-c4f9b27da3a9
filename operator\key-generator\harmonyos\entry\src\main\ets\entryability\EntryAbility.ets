import UIAbility from '@ohos.app.ability.UIAbility';
import hilog from '@ohos.hilog';
import window from '@ohos.window';
import { DatabaseManager } from '../common/database/DatabaseManager';
import { KeyGenerationService } from '../services/KeyGenerationService';
import { SecurityService } from '../services/SecurityService';

export default class EntryAbility extends UIAbility {
  private databaseManager: DatabaseManager = new DatabaseManager();
  private keyGenerationService: KeyGenerationService = new KeyGenerationService();
  private securityService: SecurityService = new SecurityService();

  onCreate(want, launchParam) {
    hilog.info(0x0000, 'KeyGenerator', '%{public}s', 'Ability onCreate');

    // 初始化数据库
    this.initializeDatabase();

    // 初始化安全服务
    this.initializeSecurity();
  }

  onDestroy() {
    hilog.info(0x0000, 'KeyGenerator', '%{public}s', 'Ability onDestroy');

    // 清理资源
    this.databaseManager.close();
    this.securityService.cleanup();
  }

  onWindowStageCreate(windowStage: window.WindowStage) {
    hilog.info(0x0000, 'KeyGenerator', '%{public}s', 'Ability onWindowStageCreate');

    // 设置窗口属性
    windowStage.getMainWindow((err, data) => {
      if (err.code) {
        hilog.error(0x0000, 'KeyGenerator', 'Failed to obtain the main window. Error: %{public}s',
          JSON.stringify(err) ?? '');
        return;
      }

      // 设置窗口大小和标题
      data.setWindowLayoutFullScreen(false);
      data.setWindowSize(1200, 800);
      data.setWindowTitle('企业密钥生成器 v1.4.0');

      hilog.info(0x0000, 'KeyGenerator', 'Succeeded in obtaining the main window. Data: %{public}s',
        JSON.stringify(data) ?? '');
    });

    windowStage.loadContent('pages/Index', (err, data) => {
      if (err.code) {
        hilog.error(0x0000, 'KeyGenerator', 'Failed to load the content. Error: %{public}s',
          JSON.stringify(err) ?? '');
        return;
      }
      hilog.info(0x0000, 'KeyGenerator', 'Succeeded in loading the content. Data: %{public}s',
        JSON.stringify(data) ?? '');
    });
  }

  onWindowStageDestroy() {
    hilog.info(0x0000, 'KeyGenerator', '%{public}s', 'Ability onWindowStageDestroy');
  }

  onForeground() {
    hilog.info(0x0000, 'KeyGenerator', '%{public}s', 'Ability onForeground');
  }

  onBackground() {
    hilog.info(0x0000, 'KeyGenerator', '%{public}s', 'Ability onBackground');
  }

  /**
   * 初始化数据库
   */
  private async initializeDatabase() {
    try {
      await this.databaseManager.initialize();
      hilog.info(0x0000, 'KeyGenerator', 'Database initialized successfully');
    } catch (error) {
      hilog.error(0x0000, 'KeyGenerator', 'Failed to initialize database: %{public}s',
        error.message);
    }
  }

  /**
   * 初始化安全服务
   */
  private async initializeSecurity() {
    try {
      await this.securityService.initialize();
      hilog.info(0x0000, 'KeyGenerator', 'Security service initialized successfully');
    } catch (error) {
      hilog.error(0x0000, 'KeyGenerator', 'Failed to initialize security service: %{public}s',
        error.message);
    }
  }
}
