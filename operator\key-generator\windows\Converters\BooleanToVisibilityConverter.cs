using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace KeyGenerator.Converters
{
    /// <summary>
    /// 布尔值到可见性的转换器
    /// </summary>
    public class BooleanToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                // 如果参数为 "Inverse"，则反转逻辑
                bool inverse = parameter?.ToString()?.ToLower() == "inverse";
                
                if (inverse)
                {
                    return boolValue ? Visibility.Collapsed : Visibility.Visible;
                }
                else
                {
                    return boolValue ? Visibility.Visible : Visibility.Collapsed;
                }
            }
            
            // 默认返回可见
            return Visibility.Visible;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is Visibility visibility)
            {
                // 如果参数为 "Inverse"，则反转逻辑
                bool inverse = parameter?.ToString()?.ToLower() == "inverse";
                
                if (inverse)
                {
                    return visibility != Visibility.Visible;
                }
                else
                {
                    return visibility == Visibility.Visible;
                }
            }
            
            // 默认返回 false
            return false;
        }
    }
} 