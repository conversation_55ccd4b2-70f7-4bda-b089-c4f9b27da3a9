using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CryptoSystem.SystemManager.Models
{
    /// <summary>
    /// 用户状态枚举
    /// </summary>
    public enum UserStatus
    {
        [Description("活动")]
        Active = 1,
        
        [Description("已禁用")]
        Disabled = 2,
        
        [Description("已锁定")]
        Locked = 3,
        
        [Description("已删除")]
        Deleted = 4
    }

    /// <summary>
    /// 用户角色枚举
    /// </summary>
    public enum UserRole
    {
        [Description("系统管理员")]
        SystemAdmin = 1,
        
        [Description("安全管理员")]
        SecurityAdmin = 2,
        
        [Description("审计管理员")]
        AuditAdmin = 3,
        
        [Description("部门管理员")]
        DepartmentAdmin = 4,
        
        [Description("普通用户")]
        NormalUser = 5
    }

    /// <summary>
    /// 认证方式枚举
    /// </summary>
    public enum AuthenticationType
    {
        [Description("用户名密码")]
        UsernamePassword = 1,
        
        [Description("域认证")]
        DomainAuth = 2,
        
        [Description("硬件Key")]
        HardwareKey = 3,
        
        [Description("U-Key")]
        UKey = 4,
        
        [Description("多因素认证")]
        MFA = 5
    }

    /// <summary>
    /// 用户信息实体
    /// </summary>
    [Table("sys_users")]
    public class User
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Key]
        [Column("user_id")]
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// 用户名
        /// </summary>
        [Required]
        [MaxLength(100)]
        [Column("username")]
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// 显示名称
        /// </summary>
        [MaxLength(200)]
        [Column("display_name")]
        public string DisplayName { get; set; } = string.Empty;

        /// <summary>
        /// 邮箱地址
        /// </summary>
        [MaxLength(300)]
        [Column("email")]
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// 手机号码
        /// </summary>
        [MaxLength(50)]
        [Column("phone")]
        public string Phone { get; set; } = string.Empty;

        /// <summary>
        /// 部门ID
        /// </summary>
        [Column("department_id")]
        public string? DepartmentId { get; set; }

        /// <summary>
        /// 部门名称
        /// </summary>
        [MaxLength(200)]
        [Column("department_name")]
        public string DepartmentName { get; set; } = string.Empty;

        /// <summary>
        /// 职位
        /// </summary>
        [MaxLength(200)]
        [Column("position")]
        public string Position { get; set; } = string.Empty;

        /// <summary>
        /// 用户角色
        /// </summary>
        [Column("role")]
        public UserRole Role { get; set; } = UserRole.NormalUser;

        /// <summary>
        /// 用户状态
        /// </summary>
        [Column("status")]
        public UserStatus Status { get; set; } = UserStatus.Active;

        /// <summary>
        /// 认证方式
        /// </summary>
        [Column("auth_type")]
        public AuthenticationType AuthType { get; set; } = AuthenticationType.UsernamePassword;

        /// <summary>
        /// 密码哈希
        /// </summary>
        [MaxLength(256)]
        [Column("password_hash")]
        public string PasswordHash { get; set; } = string.Empty;

        /// <summary>
        /// 密码盐值
        /// </summary>
        [MaxLength(128)]
        [Column("password_salt")]
        public string PasswordSalt { get; set; } = string.Empty;

        /// <summary>
        /// 上次登录时间
        /// </summary>
        [Column("last_login_time")]
        public DateTime? LastLoginTime { get; set; }

        /// <summary>
        /// 上次登录IP
        /// </summary>
        [MaxLength(100)]
        [Column("last_login_ip")]
        public string LastLoginIp { get; set; } = string.Empty;

        /// <summary>
        /// 登录失败次数
        /// </summary>
        [Column("failed_login_count")]
        public int FailedLoginCount { get; set; } = 0;

        /// <summary>
        /// 账户锁定到期时间
        /// </summary>
        [Column("locked_until")]
        public DateTime? LockedUntil { get; set; }

        /// <summary>
        /// 密码过期时间
        /// </summary>
        [Column("password_expires_at")]
        public DateTime? PasswordExpiresAt { get; set; }

        /// <summary>
        /// 是否必须更改密码
        /// </summary>
        [Column("must_change_password")]
        public bool MustChangePassword { get; set; } = false;

        /// <summary>
        /// AD/LDAP域名
        /// </summary>
        [MaxLength(200)]
        [Column("domain_name")]
        public string DomainName { get; set; } = string.Empty;

        /// <summary>
        /// AD/LDAP用户DN
        /// </summary>
        [MaxLength(500)]
        [Column("domain_dn")]
        public string DomainDn { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        [Column("created_time")]
        public DateTime CreatedTime { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 创建者
        /// </summary>
        [MaxLength(100)]
        [Column("created_by")]
        public string CreatedBy { get; set; } = string.Empty;

        /// <summary>
        /// 最后修改时间
        /// </summary>
        [Column("last_modified_time")]
        public DateTime LastModifiedTime { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 最后修改者
        /// </summary>
        [MaxLength(100)]
        [Column("last_modified_by")]
        public string LastModifiedBy { get; set; } = string.Empty;

        /// <summary>
        /// 备注信息
        /// </summary>
        [MaxLength(1000)]
        [Column("remarks")]
        public string Remarks { get; set; } = string.Empty;

        /// <summary>
        /// 扩展属性（JSON格式）
        /// </summary>
        [Column("extended_properties")]
        public string ExtendedProperties { get; set; } = "{}";

        // 导航属性
        public virtual Department? Department { get; set; }
        public virtual ICollection<UserDevice> UserDevices { get; set; } = new List<UserDevice>();
        public virtual ICollection<UserPolicy> UserPolicies { get; set; } = new List<UserPolicy>();
    }

    /// <summary>
    /// 用户组实体
    /// </summary>
    [Table("sys_user_groups")]
    public class UserGroup
    {
        /// <summary>
        /// 用户组ID
        /// </summary>
        [Key]
        [Column("group_id")]
        public string GroupId { get; set; } = string.Empty;

        /// <summary>
        /// 用户组名称
        /// </summary>
        [Required]
        [MaxLength(200)]
        [Column("group_name")]
        public string GroupName { get; set; } = string.Empty;

        /// <summary>
        /// 用户组描述
        /// </summary>
        [MaxLength(1000)]
        [Column("description")]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 父组ID
        /// </summary>
        [Column("parent_group_id")]
        public string? ParentGroupId { get; set; }

        /// <summary>
        /// 组层级
        /// </summary>
        [Column("level")]
        public int Level { get; set; } = 1;

        /// <summary>
        /// 排序号
        /// </summary>
        [Column("sort_order")]
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 是否启用
        /// </summary>
        [Column("is_enabled")]
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        [Column("created_time")]
        public DateTime CreatedTime { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 创建者
        /// </summary>
        [MaxLength(100)]
        [Column("created_by")]
        public string CreatedBy { get; set; } = string.Empty;

        // 导航属性
        public virtual UserGroup? ParentGroup { get; set; }
        public virtual ICollection<UserGroup> ChildGroups { get; set; } = new List<UserGroup>();
        public virtual ICollection<UserGroupMember> Members { get; set; } = new List<UserGroupMember>();
    }

    /// <summary>
    /// 用户组成员关系
    /// </summary>
    [Table("sys_user_group_members")]
    public class UserGroupMember
    {
        /// <summary>
        /// 用户组ID
        /// </summary>
        [Key]
        [Column("group_id")]
        public string GroupId { get; set; } = string.Empty;

        /// <summary>
        /// 用户ID
        /// </summary>
        [Key]
        [Column("user_id")]
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// 加入时间
        /// </summary>
        [Column("joined_time")]
        public DateTime JoinedTime { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 加入者
        /// </summary>
        [MaxLength(100)]
        [Column("joined_by")]
        public string JoinedBy { get; set; } = string.Empty;

        // 导航属性
        public virtual UserGroup Group { get; set; } = null!;
        public virtual User User { get; set; } = null!;
    }

    /// <summary>
    /// 部门实体
    /// </summary>
    [Table("sys_departments")]
    public class Department
    {
        /// <summary>
        /// 部门ID
        /// </summary>
        [Key]
        [Column("department_id")]
        public string DepartmentId { get; set; } = string.Empty;

        /// <summary>
        /// 部门名称
        /// </summary>
        [Required]
        [MaxLength(200)]
        [Column("department_name")]
        public string DepartmentName { get; set; } = string.Empty;

        /// <summary>
        /// 部门代码
        /// </summary>
        [MaxLength(50)]
        [Column("department_code")]
        public string DepartmentCode { get; set; } = string.Empty;

        /// <summary>
        /// 父部门ID
        /// </summary>
        [Column("parent_department_id")]
        public string? ParentDepartmentId { get; set; }

        /// <summary>
        /// 部门层级
        /// </summary>
        [Column("level")]
        public int Level { get; set; } = 1;

        /// <summary>
        /// 排序号
        /// </summary>
        [Column("sort_order")]
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 部门负责人ID
        /// </summary>
        [Column("manager_id")]
        public string? ManagerId { get; set; }

        /// <summary>
        /// 部门负责人姓名
        /// </summary>
        [MaxLength(100)]
        [Column("manager_name")]
        public string ManagerName { get; set; } = string.Empty;

        /// <summary>
        /// 联系电话
        /// </summary>
        [MaxLength(50)]
        [Column("phone")]
        public string Phone { get; set; } = string.Empty;

        /// <summary>
        /// 邮箱地址
        /// </summary>
        [MaxLength(200)]
        [Column("email")]
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// 部门地址
        /// </summary>
        [MaxLength(500)]
        [Column("address")]
        public string Address { get; set; } = string.Empty;

        /// <summary>
        /// 是否启用
        /// </summary>
        [Column("is_enabled")]
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        [Column("created_time")]
        public DateTime CreatedTime { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 创建者
        /// </summary>
        [MaxLength(100)]
        [Column("created_by")]
        public string CreatedBy { get; set; } = string.Empty;

        /// <summary>
        /// 备注信息
        /// </summary>
        [MaxLength(1000)]
        [Column("remarks")]
        public string Remarks { get; set; } = string.Empty;

        // 导航属性
        public virtual Department? ParentDepartment { get; set; }
        public virtual ICollection<Department> ChildDepartments { get; set; } = new List<Department>();
        public virtual ICollection<User> Users { get; set; } = new List<User>();
    }
} 