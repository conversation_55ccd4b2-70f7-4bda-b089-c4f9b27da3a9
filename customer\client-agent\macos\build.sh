#!/bin/bash

# macOS CryptoSystem 构建脚本
# 支持Debug和Release构建模式

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目配置
PROJECT_NAME="CryptoSystemMac"
SCHEME_NAME="CryptoSystemMac"
WORKSPACE_PATH="CryptoSystemMac.xcodeproj"
BUILD_DIR="build"
ARCHIVE_DIR="archive"
EXPORT_DIR="export"

# 版本信息
VERSION="1.4.0"
BUILD_NUMBER=$(date +%Y%m%d%H%M)

# 函数：打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}[$(date '+%Y-%m-%d %H:%M:%S')] ${message}${NC}"
}

print_info() {
    print_message $BLUE "$1"
}

print_success() {
    print_message $GREEN "$1"
}

print_warning() {
    print_message $YELLOW "$1"
}

print_error() {
    print_message $RED "$1"
}

# 函数：显示帮助信息
show_help() {
    echo "macOS CryptoSystem 构建脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -c, --config CONFIG    构建配置 (Debug|Release) [默认: Debug]"
    echo "  -a, --archive         创建归档文件"
    echo "  -e, --export          导出应用程序"
    echo "  -t, --test            运行单元测试"
    echo "  -l, --lint            运行代码检查"
    echo "  --clean               清理构建目录"
    echo "  --version             显示版本信息"
    echo "  -h, --help            显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                    # Debug构建"
    echo "  $0 -c Release         # Release构建"
    echo "  $0 -c Release -a -e   # Release构建、归档并导出"
    echo "  $0 --clean            # 清理构建目录"
}

# 函数：检查依赖
check_dependencies() {
    print_info "检查构建依赖..."
    
    # 检查Xcode
    if ! command -v xcodebuild &> /dev/null; then
        print_error "错误: 未找到xcodebuild命令，请确保已安装Xcode"
        exit 1
    fi
    
    # 检查Xcode版本
    local xcode_version=$(xcodebuild -version | head -n 1 | cut -d ' ' -f 2)
    print_info "Xcode版本: $xcode_version"
    
    # 检查项目文件
    if [ ! -d "$WORKSPACE_PATH" ]; then
        print_error "错误: 未找到项目文件 $WORKSPACE_PATH"
        exit 1
    fi
    
    print_success "依赖检查完成"
}

# 函数：清理构建目录
clean_build() {
    print_info "清理构建目录..."
    
    # 清理Xcode构建缓存
    xcodebuild clean -project "$WORKSPACE_PATH" -scheme "$SCHEME_NAME" -configuration Debug
    xcodebuild clean -project "$WORKSPACE_PATH" -scheme "$SCHEME_NAME" -configuration Release
    
    # 删除构建目录
    rm -rf "$BUILD_DIR"
    rm -rf "$ARCHIVE_DIR"
    rm -rf "$EXPORT_DIR"
    rm -rf "DerivedData"
    
    print_success "构建目录已清理"
}

# 函数：构建项目
build_project() {
    local config=$1
    print_info "开始构建项目 (配置: $config)..."
    
    # 创建构建目录
    mkdir -p "$BUILD_DIR"
    
    # 设置构建参数
    local build_settings=(
        "MARKETING_VERSION=$VERSION"
        "CURRENT_PROJECT_VERSION=$BUILD_NUMBER"
        "CODE_SIGN_IDENTITY=-"
        "CODE_SIGNING_REQUIRED=NO"
        "CODE_SIGNING_ALLOWED=NO"
    )
    
    # 执行构建
    xcodebuild \
        -project "$WORKSPACE_PATH" \
        -scheme "$SCHEME_NAME" \
        -configuration "$config" \
        -derivedDataPath "DerivedData" \
        "${build_settings[@]/#/-}" \
        build
    
    if [ $? -eq 0 ]; then
        print_success "构建成功 (配置: $config)"
    else
        print_error "构建失败"
        exit 1
    fi
}

# 函数：运行测试
run_tests() {
    print_info "运行单元测试..."
    
    xcodebuild \
        -project "$WORKSPACE_PATH" \
        -scheme "$SCHEME_NAME" \
        -configuration Debug \
        -derivedDataPath "DerivedData" \
        test
    
    if [ $? -eq 0 ]; then
        print_success "测试通过"
    else
        print_warning "测试失败或未找到测试目标"
    fi
}

# 函数：代码检查
run_lint() {
    print_info "运行代码检查..."
    
    # 检查Swift代码格式
    if command -v swiftformat &> /dev/null; then
        print_info "运行SwiftFormat..."
        swiftformat --lint .
    else
        print_warning "SwiftFormat未安装，跳过格式检查"
    fi
    
    # 检查Swift代码质量
    if command -v swiftlint &> /dev/null; then
        print_info "运行SwiftLint..."
        swiftlint
    else
        print_warning "SwiftLint未安装，跳过代码质量检查"
    fi
    
    print_success "代码检查完成"
}

# 函数：创建归档
create_archive() {
    local config=$1
    print_info "创建归档文件 (配置: $config)..."
    
    # 创建归档目录
    mkdir -p "$ARCHIVE_DIR"
    
    local archive_path="$ARCHIVE_DIR/${PROJECT_NAME}_${VERSION}_${BUILD_NUMBER}.xcarchive"
    
    # 设置归档参数
    local archive_settings=(
        "MARKETING_VERSION=$VERSION"
        "CURRENT_PROJECT_VERSION=$BUILD_NUMBER"
    )
    
    # 执行归档
    xcodebuild \
        -project "$WORKSPACE_PATH" \
        -scheme "$SCHEME_NAME" \
        -configuration "$config" \
        -derivedDataPath "DerivedData" \
        -archivePath "$archive_path" \
        "${archive_settings[@]/#/-}" \
        archive
    
    if [ $? -eq 0 ]; then
        print_success "归档创建成功: $archive_path"
        echo "$archive_path" > "$ARCHIVE_DIR/latest_archive.txt"
    else
        print_error "归档创建失败"
        exit 1
    fi
}

# 函数：导出应用程序
export_app() {
    print_info "导出应用程序..."
    
    # 检查是否存在归档文件
    if [ ! -f "$ARCHIVE_DIR/latest_archive.txt" ]; then
        print_error "错误: 未找到归档文件，请先创建归档"
        exit 1
    fi
    
    local archive_path=$(cat "$ARCHIVE_DIR/latest_archive.txt")
    
    # 创建导出目录
    mkdir -p "$EXPORT_DIR"
    
    # 创建导出配置文件
    cat > "$EXPORT_DIR/ExportOptions.plist" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>method</key>
    <string>developer-id</string>
    <key>teamID</key>
    <string></string>
    <key>signingStyle</key>
    <string>automatic</string>
    <key>stripSwiftSymbols</key>
    <true/>
    <key>thinning</key>
    <string>&lt;none&gt;</string>
</dict>
</plist>
EOF
    
    # 执行导出
    xcodebuild \
        -exportArchive \
        -archivePath "$archive_path" \
        -exportPath "$EXPORT_DIR" \
        -exportOptionsPlist "$EXPORT_DIR/ExportOptions.plist"
    
    if [ $? -eq 0 ]; then
        print_success "应用程序导出成功: $EXPORT_DIR"
        
        # 显示导出的文件
        print_info "导出的文件:"
        ls -la "$EXPORT_DIR"/*.app 2>/dev/null || true
        ls -la "$EXPORT_DIR"/*.pkg 2>/dev/null || true
    else
        print_warning "应用程序导出失败，可能需要配置代码签名"
    fi
}

# 函数：显示构建信息
show_build_info() {
    print_info "构建信息:"
    echo "  项目名称: $PROJECT_NAME"
    echo "  版本号: $VERSION"
    echo "  构建号: $BUILD_NUMBER"
    echo "  项目路径: $WORKSPACE_PATH"
    echo "  构建目录: $BUILD_DIR"
    echo "  归档目录: $ARCHIVE_DIR"
    echo "  导出目录: $EXPORT_DIR"
}

# 函数：显示版本信息
show_version() {
    echo "macOS CryptoSystem 构建脚本"
    echo "版本: $VERSION"
    echo "构建号: $BUILD_NUMBER"
    echo ""
    echo "系统信息:"
    echo "  操作系统: $(uname -s) $(uname -r)"
    echo "  架构: $(uname -m)"
    if command -v xcodebuild &> /dev/null; then
        echo "  Xcode: $(xcodebuild -version | head -n 1)"
    fi
}

# 主函数
main() {
    local config="Debug"
    local should_archive=false
    local should_export=false
    local should_test=false
    local should_lint=false
    local should_clean=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -c|--config)
                config="$2"
                shift 2
                ;;
            -a|--archive)
                should_archive=true
                shift
                ;;
            -e|--export)
                should_export=true
                shift
                ;;
            -t|--test)
                should_test=true
                shift
                ;;
            -l|--lint)
                should_lint=true
                shift
                ;;
            --clean)
                should_clean=true
                shift
                ;;
            --version)
                show_version
                exit 0
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 验证配置参数
    if [[ "$config" != "Debug" && "$config" != "Release" ]]; then
        print_error "错误: 无效的配置 '$config'，必须是 Debug 或 Release"
        exit 1
    fi
    
    # 显示构建信息
    show_build_info
    
    # 检查依赖
    check_dependencies
    
    # 清理构建目录（如果需要）
    if [ "$should_clean" = true ]; then
        clean_build
        print_success "清理完成"
        exit 0
    fi
    
    # 代码检查（如果需要）
    if [ "$should_lint" = true ]; then
        run_lint
    fi
    
    # 运行测试（如果需要）
    if [ "$should_test" = true ]; then
        run_tests
    fi
    
    # 构建项目
    build_project "$config"
    
    # 创建归档（如果需要）
    if [ "$should_archive" = true ]; then
        create_archive "$config"
    fi
    
    # 导出应用程序（如果需要）
    if [ "$should_export" = true ]; then
        if [ "$should_archive" = false ]; then
            print_warning "警告: 导出需要先创建归档，自动创建归档..."
            create_archive "$config"
        fi
        export_app
    fi
    
    print_success "构建流程完成!"
    
    # 显示最终状态
    print_info "最终状态:"
    echo "  构建配置: $config"
    echo "  构建状态: 成功"
    if [ "$should_archive" = true ]; then
        echo "  归档状态: 已创建"
    fi
    if [ "$should_export" = true ]; then
        echo "  导出状态: 已完成"
    fi
}

# 脚本入口点
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi 