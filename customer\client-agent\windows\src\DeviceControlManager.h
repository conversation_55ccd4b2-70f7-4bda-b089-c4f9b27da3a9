#pragma once

#include <string>
#include <vector>
#include <map>
#include <mutex>
#include <memory>
#include <Windows.h>
#include <SetupAPI.h>
#include <Dbt.h>

#pragma comment(lib, "setupapi.lib")

/**
 * 设备类型枚举
 */
enum class DeviceType {
    UNKNOWN,
    USB_STORAGE,      // USB存储设备
    CDROM,            // CD/DVD驱动器
    FLOPPY,           // 软盘驱动器
    NETWORK_ADAPTER,  // 网络适配器
    BLUETOOTH,        // 蓝牙设备
    PRINTER,          // 打印机
    SCANNER,          // 扫描仪
    CAMERA,           // 摄像头
    AUDIO,            // 音频设备
    MOBILE_PHONE,     // 手机
    CUSTOM            // 自定义类型
};

/**
 * 设备访问权限枚举
 */
enum class DeviceAccess {
    ALLOW,            // 完全允许
    READ_ONLY,        // 只读访问
    DENY,             // 完全禁止
    FORCE_ENCRYPT,    // 强制加密（对USB存储设备）
    CUSTOM            // 自定义策略
};

/**
 * 设备信息结构
 */
struct DeviceInfo {
    std::wstring deviceId;       // 设备ID
    std::wstring instanceId;     // 实例ID
    std::wstring description;    // 设备描述
    std::wstring manufacturer;   // 制造商
    std::wstring serialNumber;   // 序列号
    DeviceType type;             // 设备类型
    bool isConnected;            // 是否已连接
    std::wstring lastConnectTime;// 最后连接时间
    DeviceAccess currentAccess;  // 当前访问权限
};

/**
 * 设备控制管理器类
 */
class DeviceControlManager {
public:
    // 单例访问
    static DeviceControlManager& getInstance();
    
    // 初始化与关闭
    bool initialize();
    void shutdown();
    
    // 监控与权限控制
    bool startMonitoring(HWND hwndNotification);
    bool stopMonitoring();
    void setDefaultPolicy(DeviceType type, DeviceAccess access);
    void setDevicePolicy(const std::wstring& deviceId, DeviceAccess access);
    
    // 设备白名单/黑名单管理
    void addToWhitelist(const std::wstring& deviceId);
    void addToBlacklist(const std::wstring& deviceId);
    void removeFromWhitelist(const std::wstring& deviceId);
    void removeFromBlacklist(const std::wstring& deviceId);
    bool isWhitelisted(const std::wstring& deviceId) const;
    bool isBlacklisted(const std::wstring& deviceId) const;
    
    // 设备列表管理
    std::vector<DeviceInfo> getConnectedDevices() const;
    std::vector<DeviceInfo> getConnectedDevicesByType(DeviceType type) const;
    std::shared_ptr<DeviceInfo> getDeviceInfo(const std::wstring& deviceId) const;
    
    // 回调设置
    using DeviceCallbackType = std::function<void(const DeviceInfo&, bool /*isConnected*/)>;
    void setDeviceCallback(DeviceCallbackType callback);
    
    // 设备访问拦截
    bool allowAccess(const std::wstring& deviceId, bool isWriteAccess = false);
    
    // 处理Windows设备通知消息
    bool handleDeviceMessage(HWND hwnd, UINT message, WPARAM wParam, LPARAM lParam);

private:
    DeviceControlManager(); // 私有构造函数(单例模式)
    ~DeviceControlManager();
    
    // 禁止复制
    DeviceControlManager(const DeviceControlManager&) = delete;
    DeviceControlManager& operator=(const DeviceControlManager&) = delete;
    
    // 内部方法
    bool loadPolicies();
    bool savePolicies();
    bool scanForDevices();
    DeviceInfo createDeviceInfo(const std::wstring& deviceId);
    DeviceType determineDeviceType(const std::wstring& deviceId);
    void handleDeviceArrival(const std::wstring& deviceId);
    void handleDeviceRemoval(const std::wstring& deviceId);
    bool blockDeviceAccess(const std::wstring& deviceId, bool isWriteAccess);
    void logDeviceEvent(const std::wstring& deviceId, const std::wstring& action, bool success);
    
    // 数据成员
    bool m_initialized;
    HWND m_hwndNotification;
    HDEVNOTIFY m_hDevNotify;
    std::map<DeviceType, DeviceAccess> m_defaultPolicies;
    std::map<std::wstring, DeviceAccess> m_devicePolicies;
    std::map<std::wstring, std::shared_ptr<DeviceInfo>> m_connectedDevices;
    std::vector<std::wstring> m_whitelist;
    std::vector<std::wstring> m_blacklist;
    DeviceCallbackType m_deviceCallback;
    mutable std::mutex m_devicesMutex;
}; 