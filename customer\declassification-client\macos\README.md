# Declassification Client for macOS

## 1. Overview

This is the macOS version of the Declassification Client, a key component of the enterprise document encryption system. It provides a secure and controlled way for users to declassify encrypted files for external sharing. The application is built as a modern, native macOS app using the latest Apple technologies to ensure performance, security, and a seamless user experience.

## 2. Technology Stack

- **UI Framework:** SwiftUI for a declarative and modern user interface.
- **App Lifecycle:** SwiftUI App Life Cycle.
- **Asynchronous Operations:** Combine framework and Swift Concurrency (async/await).
- **Cryptography:** CryptoKit and CommonCrypto for all cryptographic operations, ensuring the use of hardware-accelerated and secure system APIs.
- **Networking:** `URLSession` for communicating with backend services.
- **Security:** Keychain Services for securely storing sensitive data like API keys.
- **Language:** Swift 5.7+

## 3. Prerequisites

- **Hardware:** An Apple Silicon or Intel-based Mac.
- **Operating System:** macOS 13.0 (Ventura) or later.
- **Development Environment:** Xcode 14.3 or later.

## 4. How to Build

### 4.1. Build using Xcode

1.  Navigate to the `customer/declassification-client/macos/` directory.
2.  Open the `DeclassificationClient.xcodeproj` file in Xcode.
3.  Select the `DeclassificationClient` scheme.
4.  Choose a target device (e.g., "My Mac").
5.  Click the "Run" button (or press `Cmd+R`) to build and run the application.

### 4.2. Build using the Command Line

A helper script is provided to automate the build process.

1.  Open a Terminal window.
2.  Navigate to the `customer/declassification-client/macos/` directory.
3.  Make the build script executable:
    ```sh
    chmod +x build.sh
    ```
4.  Run the build script:
    ```sh
    ./build.sh
    ```
5.  The compiled application bundle (`DeclassificationClient.app`) will be located in the `customer/declassification-client/macos/build/Release/` directory upon successful completion.

## 5. Project Structure

-   `DeclassificationClient.xcodeproj/`: The Xcode project file.
-   `DeclassificationClient/`: The main application source code directory.
    -   `DeclassificationClientApp.swift`: The main entry point for the SwiftUI application.
    -   `ContentView.swift`: The root view of the application.
    -   `Info.plist`: Application metadata and configuration.
    -   `Models/`: Data models used throughout the application (e.g., `TaskModels.swift`).
    -   `Services/`: Core logic for handling declassification, security, networking, etc.
    -   `Utils/`: Utility and helper classes (e.g., `CryptoUtils.swift`).
    -   `Views/`: SwiftUI views that compose the user interface.
    -   `config/`: Contains the `config.json` for runtime configuration.
-   `build.sh`: The command-line build script.
-   `.gitignore`: Specifies files and directories to be ignored by Git.
-   `README.md`: This file.

## 6. Configuration

The application's behavior can be configured via the `DeclassificationClient/config/config.json` file. This includes the server URL, API keys, and logging levels. See `Services/ConfigurationManager.swift` for details on the structure. 