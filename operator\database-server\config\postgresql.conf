# =====================================================
# PostgreSQL 配置文件 - 企业文档加密系统
# 版本: 1.4.0
# 优化配置用于生产环境
# =====================================================

# -----------------------------------------------------
# 连接和认证配置
# -----------------------------------------------------
listen_addresses = '*'
port = 5432
max_connections = 200
superuser_reserved_connections = 3

# 认证配置
authentication_timeout = 1min
password_encryption = scram-sha-256

# SSL配置
ssl = on
ssl_ciphers = 'HIGH:MEDIUM:+3DES:!aNULL'
ssl_prefer_server_ciphers = on

# -----------------------------------------------------
# 内存配置
# -----------------------------------------------------
shared_buffers = 256MB
huge_pages = try
temp_buffers = 8MB
max_prepared_transactions = 0
work_mem = 4MB
maintenance_work_mem = 64MB
autovacuum_work_mem = -1
max_stack_depth = 2MB
dynamic_shared_memory_type = posix

# -----------------------------------------------------
# 磁盘配置
# -----------------------------------------------------
temp_file_limit = -1

# -----------------------------------------------------
# 内核资源使用
# -----------------------------------------------------
max_files_per_process = 1000
shared_preload_libraries = ''

# -----------------------------------------------------
# 成本配置
# -----------------------------------------------------
seq_page_cost = 1.0
random_page_cost = 4.0
cpu_tuple_cost = 0.01
cpu_index_tuple_cost = 0.005
cpu_operator_cost = 0.0025
parallel_tuple_cost = 0.1
parallel_setup_cost = 1000.0
min_parallel_table_scan_size = 8MB
min_parallel_index_scan_size = 512kB
effective_cache_size = 1GB

# -----------------------------------------------------
# 错误报告和日志
# -----------------------------------------------------
log_destination = 'stderr,csvlog'
logging_collector = on
log_directory = '/var/log/postgresql'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_file_mode = 0600
log_truncate_on_rotation = off
log_rotation_age = 1d
log_rotation_size = 100MB
log_min_messages = warning
log_min_error_statement = error
log_min_duration_statement = 2s

# 详细日志配置
log_checkpoints = on
log_connections = on
log_disconnections = on
log_duration = on
log_error_verbosity = default
log_hostname = off
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
log_lock_waits = on
log_statement = 'mod'
log_replication_commands = off
log_temp_files = 10MB
log_timezone = 'Asia/Shanghai'

# -----------------------------------------------------
# 运行时统计
# -----------------------------------------------------
track_activities = on
track_counts = on
track_io_timing = on
track_functions = none
track_activity_query_size = 1024
stats_temp_directory = 'pg_stat_tmp'

# -----------------------------------------------------
# 自动清理配置
# -----------------------------------------------------
autovacuum = on
log_autovacuum_min_duration = 0
autovacuum_max_workers = 3
autovacuum_naptime = 1min
autovacuum_vacuum_threshold = 50
autovacuum_analyze_threshold = 50
autovacuum_vacuum_scale_factor = 0.2
autovacuum_analyze_scale_factor = 0.1
autovacuum_freeze_max_age = 200000000
autovacuum_multixact_freeze_max_age = 400000000
autovacuum_vacuum_cost_delay = 20ms
autovacuum_vacuum_cost_limit = -1

# -----------------------------------------------------
# 客户端连接默认值
# -----------------------------------------------------
search_path = '"$user", public'
default_tablespace = ''
temp_tablespaces = ''
check_function_bodies = on
default_transaction_isolation = 'read committed'
default_transaction_read_only = off
default_transaction_deferrable = off
session_replication_role = 'origin'
statement_timeout = 0
lock_timeout = 0
idle_in_transaction_session_timeout = 0
vacuum_freeze_min_age = 50000000
vacuum_freeze_table_age = 150000000
vacuum_multixact_freeze_min_age = 5000000
vacuum_multixact_freeze_table_age = 150000000
bytea_output = 'hex'
xmlbinary = 'base64'
xmloption = 'content'
gin_fuzzy_search_limit = 0
gin_pending_list_limit = 4MB

# -----------------------------------------------------
# 锁管理
# -----------------------------------------------------
deadlock_timeout = 1s
max_locks_per_transaction = 64
max_pred_locks_per_transaction = 64
max_pred_locks_per_relation = -2
max_pred_locks_per_page = 2

# -----------------------------------------------------
# 版本和平台兼容性
# -----------------------------------------------------
array_nulls = on
backslash_quote = safe_encoding
default_with_oids = off
escape_string_warning = on
lo_compat_privileges = off
operator_precedence_warning = off
quote_all_identifiers = off
sql_inheritance = on
standard_conforming_strings = on
synchronize_seqscans = on
transform_null_equals = off

# -----------------------------------------------------
# 错误处理
# -----------------------------------------------------
exit_on_error = off
restart_after_crash = on

# -----------------------------------------------------
# 配置文件包含
# -----------------------------------------------------
# include_dir = 'conf.d'
# include_if_exists = 'exists.conf'
# include = 'special.conf'

# -----------------------------------------------------
# 自定义变量（可以在这里添加）
# -----------------------------------------------------
# custom.variable_name = 'value'

# =====================================================
# 企业文档加密系统专用配置
# =====================================================

# 时区设置
timezone = 'Asia/Shanghai'
log_timezone = 'Asia/Shanghai'

# 字符集设置
lc_messages = 'C'
lc_monetary = 'C'
lc_numeric = 'C'
lc_time = 'C'

# 默认文本搜索配置
default_text_search_config = 'pg_catalog.simple'

# WAL配置（用于复制和备份）
wal_level = replica
archive_mode = off
archive_command = ''
max_wal_senders = 10
max_replication_slots = 10
wal_keep_segments = 32
wal_sender_timeout = 60s
max_wal_size = 1GB
min_wal_size = 80MB
wal_compression = off
wal_log_hints = off
wal_buffers = 16MB
wal_writer_delay = 200ms
commit_delay = 0
commit_siblings = 5
checkpoint_timeout = 5min
checkpoint_completion_target = 0.7
checkpoint_flush_after = 256kB
checkpoint_warning = 30s
max_wal_size = 1GB
min_wal_size = 80MB

# 并行查询配置
max_worker_processes = 8
max_parallel_workers_per_gather = 2
max_parallel_workers = 8
parallel_leader_participation = on

# JIT配置（PostgreSQL 11+）
jit = on
jit_above_cost = 100000
jit_inline_above_cost = 500000
jit_optimize_above_cost = 500000

# 扩展和模块
shared_preload_libraries = 'pg_stat_statements'

# 统计信息配置
compute_query_id = on
pg_stat_statements.max = 10000
pg_stat_statements.track = all
pg_stat_statements.track_utility = off
pg_stat_statements.save = on 