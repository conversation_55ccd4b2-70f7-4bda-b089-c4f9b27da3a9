/*
 * perf_key_hash.c
 * 
 * Windows MiniFilter驱动密钥哈希表管理实现
 * 解决密钥频繁查找的性能瓶颈
 */

#include <fltKernel.h>
#include <ntstrsafe.h>
#include "perf_optimization.h"

// 外部全局变量
extern PERF_MANAGER g_PerfManager;

// 密钥超时常量 (100纳秒为单位，10分钟)
#define PERF_KEY_TIMEOUT_100NS (10 * 60 * 10000000LL)

// 前向声明
static PPERF_KEY_HASH_ENTRY
PerfAllocateKeyHashEntry(
    VOID
    );

static VOID
PerfFreeKeyHashEntry(
    _In_ PPERF_KEY_HASH_ENTRY Entry
    );

static BOOLEAN
PerfIsKeyExpired(
    _In_ PPERF_KEY_HASH_ENTRY Entry
    );

static VOID
PerfEvictExpiredKeys(
    _In_ ULONG BucketIndex
    );

//
// 初始化密钥哈希表
//
NTSTATUS
PerfInitializeKeyHashTable(
    VOID
    )
{
    PPERF_KEY_HASH_TABLE hashTable = &g_PerfManager.KeyHashTable;
    ULONG i;

    PERF_DEBUG_PRINT("Initializing key hash table...");

    // 初始化所有桶
    for (i = 0; i < PERF_KEY_HASH_TABLE_SIZE; i++) {
        InitializeListHead(&hashTable->Buckets[i].ListHead);
        KeInitializeSpinLock(&hashTable->Buckets[i].SpinLock);
        hashTable->Buckets[i].EntryCount = 0;
    }

    // 初始化统计信息
    hashTable->TotalEntries = 0;
    hashTable->LookupRequests = 0;
    hashTable->LookupHits = 0;
    hashTable->LookupMisses = 0;
    hashTable->InsertRequests = 0;
    hashTable->DeleteRequests = 0;

    PERF_DEBUG_PRINT("Key hash table initialized with %lu buckets", PERF_KEY_HASH_TABLE_SIZE);
    return STATUS_SUCCESS;
}

//
// 清理密钥哈希表
//
VOID
PerfCleanupKeyHashTable(
    VOID
    )
{
    PPERF_KEY_HASH_TABLE hashTable = &g_PerfManager.KeyHashTable;
    PPERF_KEY_HASH_BUCKET bucket;
    PLIST_ENTRY listEntry;
    PPERF_KEY_HASH_ENTRY entry;
    KIRQL oldIrql;
    ULONG i;

    PERF_DEBUG_PRINT("Cleaning up key hash table...");

    // 清理所有桶
    for (i = 0; i < PERF_KEY_HASH_TABLE_SIZE; i++) {
        bucket = &hashTable->Buckets[i];
        
        KeAcquireSpinLock(&bucket->SpinLock, &oldIrql);

        while (!IsListEmpty(&bucket->ListHead)) {
            listEntry = RemoveHeadList(&bucket->ListHead);
            entry = CONTAINING_RECORD(listEntry, PERF_KEY_HASH_ENTRY, ListEntry);
            PerfFreeKeyHashEntry(entry);
        }

        bucket->EntryCount = 0;
        KeReleaseSpinLock(&bucket->SpinLock, oldIrql);
    }

    hashTable->TotalEntries = 0;
    PERF_DEBUG_PRINT("Key hash table cleaned up");
}

//
// 计算密钥版本哈希值
//
ULONG
PerfHashKeyVersion(
    _In_ ULONG KeyVersion
    )
{
    // 使用简单的哈希算法，可以根据需要优化
    ULONG hash = KeyVersion;
    hash ^= (hash >> 16);
    hash ^= (hash >> 8);
    return hash % PERF_KEY_HASH_TABLE_SIZE;
}

//
// 查找密钥
//
NTSTATUS
PerfLookupKey(
    _In_ ULONG KeyVersion,
    _Out_ PUCHAR Key,
    _Inout_ PULONG KeyLength
    )
{
    NTSTATUS status = STATUS_NOT_FOUND;
    PPERF_KEY_HASH_TABLE hashTable = &g_PerfManager.KeyHashTable;
    ULONG bucketIndex;
    PPERF_KEY_HASH_BUCKET bucket;
    PLIST_ENTRY listEntry;
    PPERF_KEY_HASH_ENTRY entry;
    KIRQL oldIrql;

    if (Key == NULL || KeyLength == NULL) {
        return STATUS_INVALID_PARAMETER;
    }

    bucketIndex = PerfHashKeyVersion(KeyVersion);
    bucket = &hashTable->Buckets[bucketIndex];

    KeAcquireSpinLock(&bucket->SpinLock, &oldIrql);

    __try {
        hashTable->LookupRequests++;

        // 遍历桶中的条目
        for (listEntry = bucket->ListHead.Flink; 
             listEntry != &bucket->ListHead; 
             listEntry = listEntry->Flink) {
            
            entry = CONTAINING_RECORD(listEntry, PERF_KEY_HASH_ENTRY, ListEntry);
            
            if (entry->KeyVersion == KeyVersion) {
                // 检查是否过期
                if (PerfIsKeyExpired(entry)) {
                    PERF_DEBUG_PRINT("Key version %lu expired, removing", KeyVersion);
                    RemoveEntryList(&entry->ListEntry);
                    bucket->EntryCount--;
                    hashTable->TotalEntries--;
                    PerfFreeKeyHashEntry(entry);
                    hashTable->LookupMisses++;
                    __leave;
                }

                // 找到匹配的密钥
                if (*KeyLength < entry->KeyLength) {
                    status = STATUS_BUFFER_TOO_SMALL;
                    *KeyLength = entry->KeyLength;
                    __leave;
                }

                // 复制密钥数据
                RtlCopyMemory(Key, entry->Key, entry->KeyLength);
                *KeyLength = entry->KeyLength;
                
                // 更新访问时间和计数
                KeQuerySystemTime(&entry->LastUsed);
                entry->AccessCount++;
                
                hashTable->LookupHits++;
                status = STATUS_SUCCESS;
                
                PERF_DEBUG_PRINT("Key lookup hit: version=%lu, length=%lu", 
                               KeyVersion, entry->KeyLength);
                __leave;
            }
        }

        // 未找到密钥
        hashTable->LookupMisses++;
        PERF_DEBUG_PRINT("Key lookup miss: version=%lu", KeyVersion);
    }
    __finally {
        KeReleaseSpinLock(&bucket->SpinLock, oldIrql);
    }

    return status;
}

//
// 插入密钥
//
NTSTATUS
PerfInsertKey(
    _In_ ULONG KeyVersion,
    _In_ PUCHAR Key,
    _In_ ULONG KeyLength
    )
{
    NTSTATUS status = STATUS_SUCCESS;
    PPERF_KEY_HASH_TABLE hashTable = &g_PerfManager.KeyHashTable;
    ULONG bucketIndex;
    PPERF_KEY_HASH_BUCKET bucket;
    PPERF_KEY_HASH_ENTRY entry;
    PLIST_ENTRY listEntry;
    KIRQL oldIrql;
    BOOLEAN found = FALSE;

    if (Key == NULL || KeyLength == 0 || KeyLength > sizeof(entry->Key)) {
        return STATUS_INVALID_PARAMETER;
    }

    bucketIndex = PerfHashKeyVersion(KeyVersion);
    bucket = &hashTable->Buckets[bucketIndex];

    // 分配新条目
    entry = PerfAllocateKeyHashEntry();
    if (entry == NULL) {
        return STATUS_INSUFFICIENT_RESOURCES;
    }

    // 填充条目信息
    entry->KeyVersion = KeyVersion;
    entry->KeyLength = KeyLength;
    RtlCopyMemory(entry->Key, Key, KeyLength);
    KeQuerySystemTime(&entry->LastUsed);
    entry->AccessCount = 1;

    KeAcquireSpinLock(&bucket->SpinLock, &oldIrql);

    __try {
        hashTable->InsertRequests++;

        // 检查是否已存在相同版本的密钥
        for (listEntry = bucket->ListHead.Flink; 
             listEntry != &bucket->ListHead; 
             listEntry = listEntry->Flink) {
            
            PPERF_KEY_HASH_ENTRY existingEntry = 
                CONTAINING_RECORD(listEntry, PERF_KEY_HASH_ENTRY, ListEntry);
            
            if (existingEntry->KeyVersion == KeyVersion) {
                // 更新现有条目
                if (KeyLength <= sizeof(existingEntry->Key)) {
                    existingEntry->KeyLength = KeyLength;
                    RtlCopyMemory(existingEntry->Key, Key, KeyLength);
                    KeQuerySystemTime(&existingEntry->LastUsed);
                    existingEntry->AccessCount++;
                    found = TRUE;
                    
                    PERF_DEBUG_PRINT("Key updated: version=%lu, length=%lu", 
                                   KeyVersion, KeyLength);
                } else {
                    status = STATUS_INVALID_PARAMETER;
                }
                __leave;
            }
        }

        if (!found) {
            // 检查是否需要清理过期条目
            if (bucket->EntryCount > (PERF_KEY_HASH_TABLE_SIZE / 4)) {
                PerfEvictExpiredKeys(bucketIndex);
            }

            // 插入新条目
            InsertTailList(&bucket->ListHead, &entry->ListEntry);
            bucket->EntryCount++;
            hashTable->TotalEntries++;
            
            PERF_DEBUG_PRINT("Key inserted: version=%lu, length=%lu, bucket=%lu", 
                           KeyVersion, KeyLength, bucketIndex);
        }
    }
    __finally {
        KeReleaseSpinLock(&bucket->SpinLock, oldIrql);
        
        if (found || !NT_SUCCESS(status)) {
            // 如果找到现有条目或发生错误，释放新分配的条目
            PerfFreeKeyHashEntry(entry);
        }
    }

    return status;
}

//
// 删除密钥
//
NTSTATUS
PerfDeleteKey(
    _In_ ULONG KeyVersion
    )
{
    NTSTATUS status = STATUS_NOT_FOUND;
    PPERF_KEY_HASH_TABLE hashTable = &g_PerfManager.KeyHashTable;
    ULONG bucketIndex;
    PPERF_KEY_HASH_BUCKET bucket;
    PLIST_ENTRY listEntry;
    PPERF_KEY_HASH_ENTRY entry;
    KIRQL oldIrql;

    bucketIndex = PerfHashKeyVersion(KeyVersion);
    bucket = &hashTable->Buckets[bucketIndex];

    KeAcquireSpinLock(&bucket->SpinLock, &oldIrql);

    __try {
        hashTable->DeleteRequests++;

        // 查找并删除条目
        for (listEntry = bucket->ListHead.Flink; 
             listEntry != &bucket->ListHead; 
             listEntry = listEntry->Flink) {
            
            entry = CONTAINING_RECORD(listEntry, PERF_KEY_HASH_ENTRY, ListEntry);
            
            if (entry->KeyVersion == KeyVersion) {
                RemoveEntryList(&entry->ListEntry);
                bucket->EntryCount--;
                hashTable->TotalEntries--;
                PerfFreeKeyHashEntry(entry);
                
                status = STATUS_SUCCESS;
                PERF_DEBUG_PRINT("Key deleted: version=%lu", KeyVersion);
                __leave;
            }
        }

        PERF_DEBUG_PRINT("Key not found for deletion: version=%lu", KeyVersion);
    }
    __finally {
        KeReleaseSpinLock(&bucket->SpinLock, oldIrql);
    }

    return status;
}

//
// 获取密钥哈希表统计信息
//
VOID
PerfGetKeyHashStats(
    _Out_ PULONG TotalEntries,
    _Out_ PULONG LookupHits,
    _Out_ PULONG LookupMisses,
    _Out_ PULONG InsertRequests,
    _Out_ PULONG DeleteRequests
    )
{
    PPERF_KEY_HASH_TABLE hashTable = &g_PerfManager.KeyHashTable;

    if (TotalEntries) *TotalEntries = hashTable->TotalEntries;
    if (LookupHits) *LookupHits = hashTable->LookupHits;
    if (LookupMisses) *LookupMisses = hashTable->LookupMisses;
    if (InsertRequests) *InsertRequests = hashTable->InsertRequests;
    if (DeleteRequests) *DeleteRequests = hashTable->DeleteRequests;
}

//
// 分配密钥哈希条目
//
static PPERF_KEY_HASH_ENTRY
PerfAllocateKeyHashEntry(
    VOID
    )
{
    PPERF_KEY_HASH_ENTRY entry;

    entry = (PPERF_KEY_HASH_ENTRY)ExAllocatePoolWithTag(
        NonPagedPool,
        sizeof(PERF_KEY_HASH_ENTRY),
        'hkyE'
    );

    if (entry != NULL) {
        RtlZeroMemory(entry, sizeof(PERF_KEY_HASH_ENTRY));
        InitializeListHead(&entry->ListEntry);
    }

    return entry;
}

//
// 释放密钥哈希条目
//
static VOID
PerfFreeKeyHashEntry(
    _In_ PPERF_KEY_HASH_ENTRY Entry
    )
{
    if (Entry != NULL) {
        // 清零密钥数据以确保安全
        RtlSecureZeroMemory(Entry->Key, sizeof(Entry->Key));
        ExFreePoolWithTag(Entry, 'hkyE');
    }
}

//
// 检查密钥是否过期
//
static BOOLEAN
PerfIsKeyExpired(
    _In_ PPERF_KEY_HASH_ENTRY Entry
    )
{
    LARGE_INTEGER currentTime;
    LARGE_INTEGER expireTime;

    KeQuerySystemTime(&currentTime);
    expireTime.QuadPart = Entry->LastUsed.QuadPart + PERF_KEY_TIMEOUT_100NS;

    return (currentTime.QuadPart > expireTime.QuadPart);
}

//
// 清理过期的密钥条目
//
static VOID
PerfEvictExpiredKeys(
    _In_ ULONG BucketIndex
    )
{
    PPERF_KEY_HASH_TABLE hashTable = &g_PerfManager.KeyHashTable;
    PPERF_KEY_HASH_BUCKET bucket = &hashTable->Buckets[BucketIndex];
    PLIST_ENTRY listEntry, nextEntry;
    PPERF_KEY_HASH_ENTRY entry;
    ULONG evictedCount = 0;

    // 注意：调用者应该已经获取了桶的自旋锁

    listEntry = bucket->ListHead.Flink;
    while (listEntry != &bucket->ListHead) {
        nextEntry = listEntry->Flink;
        entry = CONTAINING_RECORD(listEntry, PERF_KEY_HASH_ENTRY, ListEntry);
        
        if (PerfIsKeyExpired(entry)) {
            RemoveEntryList(listEntry);
            bucket->EntryCount--;
            hashTable->TotalEntries--;
            PerfFreeKeyHashEntry(entry);
            evictedCount++;
        }
        
        listEntry = nextEntry;
    }

    if (evictedCount > 0) {
        PERF_DEBUG_PRINT("Evicted %lu expired keys from bucket %lu", 
                       evictedCount, BucketIndex);
    }
} 