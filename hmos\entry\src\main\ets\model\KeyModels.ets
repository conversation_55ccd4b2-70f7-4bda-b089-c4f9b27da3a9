/**
 * 密钥生成器数据模型定义
 */

/**
 * 密钥类型枚举
 */
export enum KeyType {
    AES = 'AES',
    RSA = 'RSA',
    DES = 'DES',
    SM4 = 'SM4'
}

/**
 * 密钥用途枚举
 */
export enum KeyUsage {
    Encryption = '加密',
    Decryption = '解密',
    Signing = '签名',
    Verification = '验证',
    KeyExchange = '密钥交换'
}

/**
 * 密钥状态枚举
 */
export enum KeyStatus {
    Pending = '待激活',
    Active = '活跃',
    Suspended = '已暂停',
    Revoked = '已吊销',
    Expired = '过期',
    Destroyed = '已销毁'
}

/**
 * 分发状态枚举
 */
export enum DistributionStatus {
    Pending = 'pending',       // 待执行
    InProgress = 'in_progress', // 执行中
    Completed = 'completed',   // 已完成
    Failed = 'failed',         // 失败
    Cancelled = 'cancelled'    // 已取消
}

/**
 * 日志级别枚举
 */
export enum LogLevel {
    Info = 'info',         // 信息
    Warning = 'warning',   // 警告
    Error = 'error',       // 错误
    Critical = 'critical'  // 严重
}

/**
 * 密钥信息实体
 */
export class KeyInfo {
    /** 密钥ID */
    keyId: string = '';

    /** 密钥名称 */
    keyName: string = '';

    /** 密钥类型 */
    keyType: KeyType = KeyType.AES;

    /** 密钥长度 */
    keyLength: number = 256;

    /** 密钥用途 */
    keyUsage: KeyUsage[] = [];

    /** 密钥状态 */
    status: KeyStatus = KeyStatus.Pending;

    /** 创建时间 */
    createdTime: Date = new Date();

    /** 过期时间 */
    expiryTime?: Date;

    /** 创建者 */
    createdBy: string = '';

    /** 关联客户 */
    clientId?: string;

    /** 密钥值（加密存储） */
    keyValue?: string;

    /** 备注 */
    remarks?: string;

    /** 最后使用时间 */
    lastUsedTime?: Date;

    /** 使用次数 */
    usageCount: number = 0;

    constructor(data?: Partial<KeyInfo>) {
        if (data) {
            Object.assign(this, data);
        }
    }
}

/**
 * 数据库连接状态
 */
export interface DatabaseStatus {
    isConnected: boolean;
    connectionString: string;
    lastConnectedTime?: Date;
    errorMessage?: string;
}

/**
 * 密钥生成表单数据
 */
export interface KeyGenerationForm {
    keyName: string;
    keyType: KeyType;
    keyLength: number;
    keyUsage: KeyUsage[];
    clientId?: string;
    expiryDays?: number;
    remarks?: string;
}

/**
 * 密钥搜索条件
 */
export interface KeySearchCriteria {
    searchText?: string;
    keyType?: KeyType;
    status?: KeyStatus;
    clientId?: string;
    dateRange?: {
        start: Date;
        end: Date;
    };
}

/**
 * 客户信息实体
 */
export class ClientInfo {
    /** 客户ID */
    clientId: string = '';

    /** 客户名称 */
    clientName: string = '';

    /** 客户代码 */
    clientCode?: string;

    /** 联系人 */
    contactPerson?: string;

    /** 联系电话 */
    contactPhone?: string;

    /** 联系邮箱 */
    contactEmail?: string;

    /** 地址 */
    address?: string;

    /** 描述信息 */
    description?: string;

    /** 创建时间 */
    createdTime: Date = new Date();

    /** 最后修改时间 */
    lastModified: Date = new Date();

    /** 是否激活 */
    isActive: boolean = true;

    /** 备注 */
    remarks?: string;

    constructor(data?: Partial<ClientInfo>) {
        if (data) {
            Object.assign(this, data);
        }
    }
}

/**
 * 密钥分发任务实体
 */
export class KeyDistributionTask {
    /** 任务ID */
    taskId: string = '';

    /** 任务名称 */
    taskName: string = '';

    /** 目标客户列表 */
    targetClients: string[] = [];

    /** 密钥ID列表 */
    keyIds: string[] = [];

    /** 任务描述 */
    description?: string;

    /** 分发状态 */
    status: DistributionStatus = DistributionStatus.Pending;

    /** 创建者 */
    createdBy: string = '';

    /** 创建时间 */
    createdTime: Date = new Date();

    /** 计划执行时间 */
    scheduledTime?: Date;

    /** 实际执行时间 */
    executedTime?: Date;

    /** 完成时间 */
    completedTime?: Date;

    /** 最后修改时间 */
    lastModified: Date = new Date();

    /** 执行结果 */
    result?: string;

    /** 错误信息 */
    errorMessage?: string;

    /** 进度百分比 */
    progress: number = 0;

    constructor(data?: Partial<KeyDistributionTask>) {
        if (data) {
            Object.assign(this, data);
        }
    }
}

/**
 * 审计日志条目实体
 */
export class AuditLogEntry {
    /** 日志ID */
    logId: string = '';

    /** 时间戳 */
    timestamp: Date = new Date();

    /** 日志级别 */
    level: LogLevel = LogLevel.Info;

    /** 操作类型 */
    operation: string = '';

    /** 用户ID */
    userId: string = '';

    /** 用户名 */
    userName?: string;

    /** 客户端信息 */
    clientInfo?: string;

    /** IP地址 */
    ipAddress?: string;

    /** 资源ID */
    resourceId?: string;

    /** 操作描述 */
    description?: string;

    /** 操作结果 */
    result?: string;

    /** 是否成功 */
    success: boolean = true;

    /** 错误代码 */
    errorCode?: string;

    /** 会话ID */
    sessionId?: string;

    /** 附加数据 */
    metadata?: Record<string, any>;

    constructor(data?: Partial<AuditLogEntry>) {
        if (data) {
            Object.assign(this, data);
        }
    }
}

/**
 * 系统设置实体
 */
export class SystemSettings {
    /** 安全设置 */
    security: {
        /** 启用强密码策略 */
        enforceStrongPassword: boolean;
        /** 会话超时时间（分钟） */
        sessionTimeoutMinutes: number;
        /** 启用登录失败锁定 */
        enableLoginLockout: boolean;
        /** 最大登录失败次数 */
        maxLoginAttempts: number;
    } = {
        enforceStrongPassword: true,
        sessionTimeoutMinutes: 30,
        enableLoginLockout: true,
        maxLoginAttempts: 5
    };

    /** 密钥生成设置 */
    keyGeneration: {
        /** 默认密钥长度 */
        defaultKeyLength: number;
        /** 默认有效期（天） */
        defaultValidityDays: number;
        /** 启用自动备份 */
        enableAutoBackup: boolean;
    } = {
        defaultKeyLength: 256,
        defaultValidityDays: 365,
        enableAutoBackup: true
    };

    /** 系统设置 */
    system: {
        /** 系统名称 */
        systemName: string;
        /** 数据库连接超时（秒） */
        databaseTimeoutSeconds: number;
        /** 启用调试模式 */
        enableDebugMode: boolean;
    } = {
        systemName: '企业密钥生成器',
        databaseTimeoutSeconds: 30,
        enableDebugMode: false
    };

    /** 网络设置 */
    network: {
        /** 服务端口 */
        serverPort: number;
        /** 启用HTTPS */
        enableHttps: boolean;
        /** 连接超时（秒） */
        connectionTimeoutSeconds: number;
    } = {
        serverPort: 8443,
        enableHttps: true,
        connectionTimeoutSeconds: 30
    };

    /** 日志设置 */
    logging: {
        /** 日志级别 */
        logLevel: string;
        /** 日志保留天数 */
        retentionDays: number;
        /** 启用审计日志 */
        enableAuditLog: boolean;
    } = {
        logLevel: 'info',
        retentionDays: 90,
        enableAuditLog: true
    };

    /** 最后修改时间 */
    lastModified: Date = new Date();

    constructor(data?: Partial<SystemSettings>) {
        if (data) {
            Object.assign(this, data);
        }
    }
}
