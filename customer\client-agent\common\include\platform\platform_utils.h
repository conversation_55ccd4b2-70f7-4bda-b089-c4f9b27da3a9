#pragma once

#include <stddef.h>
#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * 跨平台工具函数接口
 * 提供平台无关的公共功能
 */

/* 文件与路径操作 */

/**
 * 获取文件扩展名
 * 
 * @param filePath 文件路径
 * @param extension 返回的扩展名（调用者负责释放）
 * @return 成功返回true，失败返回false
 */
bool platform_get_file_extension(const char* filePath, char** extension);

/**
 * 判断文件类型是否匹配
 * 
 * @param filePath 文件路径
 * @param extension 文件扩展名
 * @return 匹配返回true，不匹配返回false
 */
bool platform_is_file_type_match(const char* filePath, const char* extension);

/**
 * 判断路径是否匹配模式
 * 
 * @param filePath 文件路径
 * @param pattern 路径模式（支持 * 通配符）
 * @return 匹配返回true，不匹配返回false
 */
bool platform_is_path_match(const char* filePath, const char* pattern);

/* 进程相关操作 */

/**
 * 获取当前进程名
 * 
 * @param processName 返回的进程名（调用者负责释放）
 * @return 成功返回true，失败返回false
 */
bool platform_get_process_name(char** processName);

/**
 * 获取当前进程路径
 * 
 * @param processPath 返回的进程路径（调用者负责释放）
 * @return 成功返回true，失败返回false
 */
bool platform_get_process_path(char** processPath);

/* 用户相关操作 */

/**
 * 获取当前用户标识
 * 
 * @param userId 返回的用户标识（调用者负责释放）
 * @return 成功返回true，失败返回false
 */
bool platform_get_current_user_id(char** userId);

/* 日志与调试 */

/**
 * 记录日志
 * 
 * @param level 日志级别（0: 调试, 1: 信息, 2: 警告, 3: 错误）
 * @param message 日志消息
 */
void platform_log_message(int level, const char* message);

/* 内存管理 */

/**
 * 分配内存
 * 
 * @param size 内存大小
 * @return 分配的内存指针，失败返回NULL
 */
void* platform_allocate_memory(size_t size);

/**
 * 释放内存
 * 
 * @param memory 要释放的内存指针
 */
void platform_free_memory(void* memory);

/* 字符串处理 */

/**
 * 复制字符串
 * 
 * @param source 源字符串
 * @return 复制的字符串（调用者负责释放）
 */
char* platform_strdup(const char* source);

/**
 * 不区分大小写比较字符串
 * 
 * @param s1 第一个字符串
 * @param s2 第二个字符串
 * @return 相等返回0，s1<s2返回<0，s1>s2返回>0
 */
int platform_stricmp(const char* s1, const char* s2);

#ifdef __cplusplus
}
#endif 