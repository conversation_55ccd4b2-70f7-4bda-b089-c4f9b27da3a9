using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using CryptoSystem.SystemManager.Models;
using CryptoSystem.SystemManager.Data;

namespace CryptoSystem.SystemManager.Services
{
    /// <summary>
    /// 设备服务实现
    /// </summary>
    public class DeviceService : IDeviceService
    {
        private readonly SystemManagerDbContext _context;
        private readonly ILogger<DeviceService> _logger;

        public DeviceService(SystemManagerDbContext context, ILogger<DeviceService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<IEnumerable<Device>> GetAllDevicesAsync(bool includeOffline = true)
        {
            var query = _context.Devices.Include(d => d.UserDevices).ThenInclude(ud => ud.User);
            
            if (!includeOffline)
            {
                query = query.Where(d => d.Status == DeviceStatus.Online);
            }

            return await query.OrderBy(d => d.DeviceName).ToListAsync();
        }

        public async Task<Device?> GetDeviceByIdAsync(string deviceId)
        {
            return await _context.Devices
                .Include(d => d.UserDevices).ThenInclude(ud => ud.User)
                .Include(d => d.DevicePolicies).ThenInclude(dp => dp.Policy)
                .FirstOrDefaultAsync(d => d.DeviceId == deviceId);
        }

        public async Task<(IEnumerable<Device> Devices, int TotalCount)> GetDevicesPagedAsync(
            int pageIndex, int pageSize, string? searchKeyword = null,
            DeviceType? deviceType = null, DeviceStatus? status = null)
        {
            var query = _context.Devices.Include(d => d.UserDevices).ThenInclude(ud => ud.User);

            // 搜索关键词过滤
            if (!string.IsNullOrWhiteSpace(searchKeyword))
            {
                query = query.Where(d => d.DeviceName.Contains(searchKeyword) ||
                                    d.MacAddress.Contains(searchKeyword) ||
                                    d.IpAddress.Contains(searchKeyword) ||
                                    d.ComputerName.Contains(searchKeyword));
            }

            // 设备类型过滤
            if (deviceType.HasValue)
            {
                query = query.Where(d => d.DeviceType == deviceType.Value);
            }

            // 状态过滤
            if (status.HasValue)
            {
                query = query.Where(d => d.Status == status.Value);
            }

            var totalCount = await query.CountAsync();
            var devices = await query
                .OrderBy(d => d.DeviceName)
                .Skip(pageIndex * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return (devices, totalCount);
        }

        public async Task<(bool Success, string Message)> RegisterDeviceAsync(Device device, string registeredBy)
        {
            try
            {
                // 验证设备唯一性
                if (await _context.Devices.AnyAsync(d => d.MacAddress == device.MacAddress))
                {
                    return (false, "MAC地址已注册");
                }

                if (!string.IsNullOrWhiteSpace(device.SerialNumber) &&
                    await _context.Devices.AnyAsync(d => d.SerialNumber == device.SerialNumber))
                {
                    return (false, "序列号已注册");
                }

                // 设置设备属性
                device.DeviceId = Guid.NewGuid().ToString("N");
                device.Status = DeviceStatus.Online;
                device.RegisterTime = DateTime.UtcNow;
                device.RegisteredBy = registeredBy;
                device.LastHeartbeatTime = DateTime.UtcNow;

                _context.Devices.Add(device);
                await _context.SaveChangesAsync();

                _logger.LogInformation("设备 {DeviceName} 注册成功，注册者：{RegisteredBy}", device.DeviceName, registeredBy);
                return (true, "设备注册成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "注册设备失败：{DeviceName}", device.DeviceName);
                return (false, "注册设备时发生异常");
            }
        }

        public async Task<(bool Success, string Message)> UpdateDeviceAsync(Device device, string modifiedBy)
        {
            try
            {
                var existingDevice = await _context.Devices.FindAsync(device.DeviceId);
                if (existingDevice == null)
                {
                    return (false, "设备不存在");
                }

                // 验证MAC地址唯一性（排除当前设备）
                if (await _context.Devices.AnyAsync(d => d.MacAddress == device.MacAddress && d.DeviceId != device.DeviceId))
                {
                    return (false, "MAC地址已存在");
                }

                // 更新字段
                existingDevice.DeviceName = device.DeviceName;
                existingDevice.DeviceType = device.DeviceType;
                existingDevice.OperatingSystem = device.OperatingSystem;
                existingDevice.IpAddress = device.IpAddress;
                existingDevice.ComputerName = device.ComputerName;
                existingDevice.Location = device.Location;
                existingDevice.Remark = device.Remark;
                existingDevice.ModifiedTime = DateTime.UtcNow;
                existingDevice.ModifiedBy = modifiedBy;

                await _context.SaveChangesAsync();

                _logger.LogInformation("设备 {DeviceName} 更新成功，修改者：{ModifiedBy}", device.DeviceName, modifiedBy);
                return (true, "设备更新成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新设备失败：{DeviceName}", device.DeviceName);
                return (false, "更新设备时发生异常");
            }
        }

        public async Task<(bool Success, string Message)> DeleteDeviceAsync(string deviceId, string deletedBy)
        {
            try
            {
                var device = await _context.Devices.FindAsync(deviceId);
                if (device == null)
                {
                    return (false, "设备不存在");
                }

                // 检查是否有关联用户
                var hasUsers = await _context.UserDevices.AnyAsync(ud => ud.DeviceId == deviceId);
                if (hasUsers)
                {
                    return (false, "设备已关联用户，无法删除");
                }

                _context.Devices.Remove(device);
                await _context.SaveChangesAsync();

                _logger.LogInformation("设备 {DeviceName} 删除成功，删除者：{DeletedBy}", device.DeviceName, deletedBy);
                return (true, "设备删除成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除设备失败：{DeviceId}", deviceId);
                return (false, "删除设备时发生异常");
            }
        }

        public async Task<(bool Success, string Message)> SetDeviceStatusAsync(string deviceId, DeviceStatus status, string operatedBy)
        {
            try
            {
                var device = await _context.Devices.FindAsync(deviceId);
                if (device == null)
                {
                    return (false, "设备不存在");
                }

                device.Status = status;
                device.ModifiedTime = DateTime.UtcNow;
                device.ModifiedBy = operatedBy;

                if (status == DeviceStatus.Online)
                {
                    device.LastHeartbeatTime = DateTime.UtcNow;
                }

                await _context.SaveChangesAsync();

                _logger.LogInformation("设备 {DeviceName} 状态设置为 {Status}，操作者：{OperatedBy}", device.DeviceName, status, operatedBy);
                return (true, "设备状态设置成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "设置设备状态失败：{DeviceId}", deviceId);
                return (false, "设置设备状态时发生异常");
            }
        }

        public async Task<DeviceStatistics> GetDeviceStatisticsAsync()
        {
            var totalDevices = await _context.Devices.CountAsync();
            var onlineDevices = await _context.Devices.CountAsync(d => d.Status == DeviceStatus.Online);
            var offlineDevices = await _context.Devices.CountAsync(d => d.Status == DeviceStatus.Offline);
            var lockedDevices = await _context.Devices.CountAsync(d => d.Status == DeviceStatus.Locked);
            
            var devicesByType = await _context.Devices
                .GroupBy(d => d.DeviceType)
                .ToDictionaryAsync(g => g.Key, g => g.Count());

            var devicesByStatus = await _context.Devices
                .GroupBy(d => d.Status)
                .ToDictionaryAsync(g => g.Key, g => g.Count());

            var devicesByOs = await _context.Devices
                .GroupBy(d => d.OperatingSystem)
                .ToDictionaryAsync(g => g.Key, g => g.Count());

            return new DeviceStatistics
            {
                TotalDevices = totalDevices,
                OnlineDevices = onlineDevices,
                OfflineDevices = offlineDevices,
                LockedDevices = lockedDevices,
                DevicesByType = devicesByType,
                DevicesByStatus = devicesByStatus,
                DevicesByOperatingSystem = devicesByOs,
                LastUpdated = DateTime.UtcNow
            };
        }

        public async Task<IEnumerable<Device>> GetOnlineDevicesAsync()
        {
            return await _context.Devices
                .Where(d => d.Status == DeviceStatus.Online)
                .Include(d => d.UserDevices).ThenInclude(ud => ud.User)
                .OrderBy(d => d.DeviceName)
                .ToListAsync();
        }

        public async Task<IEnumerable<Device>> GetOfflineDevicesAsync(int hoursThreshold = 24)
        {
            var thresholdTime = DateTime.UtcNow.AddHours(-hoursThreshold);
            return await _context.Devices
                .Where(d => d.Status == DeviceStatus.Offline || 
                           d.LastHeartbeatTime < thresholdTime)
                .Include(d => d.UserDevices).ThenInclude(ud => ud.User)
                .OrderBy(d => d.DeviceName)
                .ToListAsync();
        }

        public async Task<bool> SendCommandToDeviceAsync(string deviceId, string command, object? parameters = null)
        {
            try
            {
                var device = await _context.Devices.FindAsync(deviceId);
                if (device == null || device.Status != DeviceStatus.Online)
                {
                    return false;
                }

                // 这里应该实现实际的设备通信逻辑
                // 暂时只记录命令发送
                _logger.LogInformation("向设备 {DeviceId} 发送命令：{Command}", deviceId, command);
                
                // 更新最后通信时间
                device.LastHeartbeatTime = DateTime.UtcNow;
                await _context.SaveChangesAsync();

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "向设备发送命令失败：{DeviceId}, {Command}", deviceId, command);
                return false;
            }
        }

        public async Task<(bool Success, string Message)> LockDeviceAsync(string deviceId, string reason, string operatedBy)
        {
            try
            {
                var device = await _context.Devices.FindAsync(deviceId);
                if (device == null)
                {
                    return (false, "设备不存在");
                }

                device.Status = DeviceStatus.Locked;
                device.ModifiedTime = DateTime.UtcNow;
                device.ModifiedBy = operatedBy;
                device.Remark = $"锁定原因：{reason}";

                await _context.SaveChangesAsync();

                // 发送锁定命令到设备
                await SendCommandToDeviceAsync(deviceId, "LOCK", new { reason, operatedBy });

                _logger.LogInformation("设备 {DeviceName} 锁定成功，原因：{Reason}，操作者：{OperatedBy}", 
                    device.DeviceName, reason, operatedBy);
                return (true, "设备锁定成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "锁定设备失败：{DeviceId}", deviceId);
                return (false, "锁定设备时发生异常");
            }
        }

        public async Task<(bool Success, string Message)> UnlockDeviceAsync(string deviceId, string operatedBy)
        {
            try
            {
                var device = await _context.Devices.FindAsync(deviceId);
                if (device == null)
                {
                    return (false, "设备不存在");
                }

                device.Status = DeviceStatus.Online;
                device.ModifiedTime = DateTime.UtcNow;
                device.ModifiedBy = operatedBy;

                await _context.SaveChangesAsync();

                // 发送解锁命令到设备
                await SendCommandToDeviceAsync(deviceId, "UNLOCK", new { operatedBy });

                _logger.LogInformation("设备 {DeviceName} 解锁成功，操作者：{OperatedBy}", device.DeviceName, operatedBy);
                return (true, "设备解锁成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "解锁设备失败：{DeviceId}", deviceId);
                return (false, "解锁设备时发生异常");
            }
        }
    }

    /// <summary>
    /// 设备统计信息
    /// </summary>
    public class DeviceStatistics
    {
        public int TotalDevices { get; set; }
        public int OnlineDevices { get; set; }
        public int OfflineDevices { get; set; }
        public int LockedDevices { get; set; }
        public Dictionary<DeviceType, int> DevicesByType { get; set; } = new();
        public Dictionary<DeviceStatus, int> DevicesByStatus { get; set; } = new();
        public Dictionary<string, int> DevicesByOperatingSystem { get; set; } = new();
        public DateTime LastUpdated { get; set; }
    }
} 