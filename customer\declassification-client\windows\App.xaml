<Application x:Class="CryptoSystem.DeclassificationClient.App"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes" StartupUri="Views/MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design -->
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Indigo" SecondaryColor="Blue" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />

                <!-- 自定义资源 -->
                <ResourceDictionary Source="Resources/Colors.xaml" />
                <ResourceDictionary Source="Resources/Styles.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- 全局样式 -->
            <Style TargetType="Window">
                <Setter Property="Background" Value="{DynamicResource MaterialDesignPaper}" />
                <Setter Property="FontFamily" Value="Microsoft YaHei UI" />
                <Setter Property="FontSize" Value="14" />
            </Style>

            <!-- 按钮样式 -->
            <Style x:Key="PrimaryButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
                <Setter Property="Background" Value="{StaticResource PrimaryBrush}" />
                <Setter Property="Foreground" Value="White" />
                <Setter Property="Height" Value="36" />
                <Setter Property="Margin" Value="5" />
                <Setter Property="FontWeight" Value="Medium" />
            </Style>

            <Style x:Key="SecondaryButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignOutlinedButton}">
                <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}" />
                <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}" />
                <Setter Property="Height" Value="36" />
                <Setter Property="Margin" Value="5" />
            </Style>

            <!-- 卡片样式 -->
            <Style x:Key="InfoCard" TargetType="materialDesign:Card">
                <Setter Property="Margin" Value="10" />
                <Setter Property="Padding" Value="16" />
                <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth2" />
            </Style>

            <!-- 图标样式 */
            <Style x:Key="StatusIcon" TargetType="materialDesign:PackIcon">
                <Setter Property="Width" Value="24" />
                <Setter Property="Height" Value="24" />
                <Setter Property="VerticalAlignment" Value="Center" />
                <Setter Property="HorizontalAlignment" Value="Center" />
            </Style>

        </ResourceDictionary>
    </Application.Resources>
</Application> 