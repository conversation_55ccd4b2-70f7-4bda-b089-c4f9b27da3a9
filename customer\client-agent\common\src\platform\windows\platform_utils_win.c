#include "platform/platform_utils.h"

#ifdef _WIN32

#include <Windows.h>
#include <Shlwapi.h>
#include <Psapi.h>
#include <strsafe.h>

#pragma comment(lib, "Shlwapi.lib")

/* 文件与路径操作 */

bool platform_get_file_extension(const char* filePath, char** extension) {
    if (!filePath || !extension) {
        return false;
    }
    
    const char* ext = strrchr(filePath, '.');
    if (!ext) {
        *extension = platform_strdup("");
        return true;
    }
    
    *extension = platform_strdup(ext);
    return (*extension != NULL);
}

bool platform_is_file_type_match(const char* filePath, const char* extension) {
    if (!filePath || !extension) {
        return false;
    }
    
    // 转换为宽字符
    int filePathLen = MultiByteToWideChar(CP_UTF8, 0, filePath, -1, NULL, 0);
    int extensionLen = MultiByteToWideChar(CP_UTF8, 0, extension, -1, NULL, 0);
    
    if (filePathLen <= 0 || extensionLen <= 0) {
        return false;
    }
    
    WCHAR* wFilePath = (WCHAR*)platform_allocate_memory(filePathLen * sizeof(WCHAR));
    WCHAR* wExtension = (WCHAR*)platform_allocate_memory(extensionLen * sizeof(WCHAR));
    
    if (!wFilePath || !wExtension) {
        if (wFilePath) platform_free_memory(wFilePath);
        if (wExtension) platform_free_memory(wExtension);
        return false;
    }
    
    MultiByteToWideChar(CP_UTF8, 0, filePath, -1, wFilePath, filePathLen);
    MultiByteToWideChar(CP_UTF8, 0, extension, -1, wExtension, extensionLen);
    
    bool result = PathMatchSpecW(wFilePath, wExtension);
    
    platform_free_memory(wFilePath);
    platform_free_memory(wExtension);
    
    return result;
}

bool platform_is_path_match(const char* filePath, const char* pattern) {
    if (!filePath || !pattern) {
        return false;
    }
    
    // 转换为宽字符
    int filePathLen = MultiByteToWideChar(CP_UTF8, 0, filePath, -1, NULL, 0);
    int patternLen = MultiByteToWideChar(CP_UTF8, 0, pattern, -1, NULL, 0);
    
    if (filePathLen <= 0 || patternLen <= 0) {
        return false;
    }
    
    WCHAR* wFilePath = (WCHAR*)platform_allocate_memory(filePathLen * sizeof(WCHAR));
    WCHAR* wPattern = (WCHAR*)platform_allocate_memory(patternLen * sizeof(WCHAR));
    
    if (!wFilePath || !wPattern) {
        if (wFilePath) platform_free_memory(wFilePath);
        if (wPattern) platform_free_memory(wPattern);
        return false;
    }
    
    MultiByteToWideChar(CP_UTF8, 0, filePath, -1, wFilePath, filePathLen);
    MultiByteToWideChar(CP_UTF8, 0, pattern, -1, wPattern, patternLen);
    
    bool result = PathMatchSpecW(wFilePath, wPattern);
    
    platform_free_memory(wFilePath);
    platform_free_memory(wPattern);
    
    return result;
}

/* 进程相关操作 */

bool platform_get_process_name(char** processName) {
    if (!processName) {
        return false;
    }
    
    WCHAR processPath[MAX_PATH];
    DWORD pathLength = GetModuleFileNameW(NULL, processPath, MAX_PATH);
    if (pathLength == 0) {
        return false;
    }
    
    WCHAR* fileName = PathFindFileNameW(processPath);
    
    int nameLength = WideCharToMultiByte(CP_UTF8, 0, fileName, -1, NULL, 0, NULL, NULL);
    if (nameLength <= 0) {
        return false;
    }
    
    *processName = (char*)platform_allocate_memory(nameLength);
    if (!*processName) {
        return false;
    }
    
    WideCharToMultiByte(CP_UTF8, 0, fileName, -1, *processName, nameLength, NULL, NULL);
    return true;
}

bool platform_get_process_path(char** processPath) {
    if (!processPath) {
        return false;
    }
    
    WCHAR wProcessPath[MAX_PATH];
    DWORD pathLength = GetModuleFileNameW(NULL, wProcessPath, MAX_PATH);
    if (pathLength == 0) {
        return false;
    }
    
    int pathBufferSize = WideCharToMultiByte(CP_UTF8, 0, wProcessPath, -1, NULL, 0, NULL, NULL);
    if (pathBufferSize <= 0) {
        return false;
    }
    
    *processPath = (char*)platform_allocate_memory(pathBufferSize);
    if (!*processPath) {
        return false;
    }
    
    WideCharToMultiByte(CP_UTF8, 0, wProcessPath, -1, *processPath, pathBufferSize, NULL, NULL);
    return true;
}

/* 用户相关操作 */

bool platform_get_current_user_id(char** userId) {
    if (!userId) {
        return false;
    }
    
    DWORD userNameSize = 0;
    GetUserNameW(NULL, &userNameSize);
    
    if (userNameSize == 0) {
        return false;
    }
    
    WCHAR* userName = (WCHAR*)platform_allocate_memory(userNameSize * sizeof(WCHAR));
    if (!userName) {
        return false;
    }
    
    if (!GetUserNameW(userName, &userNameSize)) {
        platform_free_memory(userName);
        return false;
    }
    
    int userIdSize = WideCharToMultiByte(CP_UTF8, 0, userName, -1, NULL, 0, NULL, NULL);
    if (userIdSize <= 0) {
        platform_free_memory(userName);
        return false;
    }
    
    *userId = (char*)platform_allocate_memory(userIdSize);
    if (!*userId) {
        platform_free_memory(userName);
        return false;
    }
    
    WideCharToMultiByte(CP_UTF8, 0, userName, -1, *userId, userIdSize, NULL, NULL);
    platform_free_memory(userName);
    
    return true;
}

/* 日志与调试 */

void platform_log_message(int level, const char* message) {
    if (!message) {
        return;
    }
    
    // 将级别转换为字符串
    const char* levelStr;
    switch (level) {
    case 0: levelStr = "DEBUG"; break;
    case 1: levelStr = "INFO "; break;
    case 2: levelStr = "WARN "; break;
    case 3: levelStr = "ERROR"; break;
    default: levelStr = "?????"; break;
    }
    
    // 获取时间
    SYSTEMTIME st;
    GetLocalTime(&st);
    
    // 格式化日志消息
    char logBuffer[1024];
    StringCchPrintfA(
        logBuffer, 
        sizeof(logBuffer), 
        "[%02d:%02d:%02d.%03d][%s] %s\r\n",
        st.wHour, st.wMinute, st.wSecond, st.wMilliseconds,
        levelStr,
        message
    );
    
    // 输出到调试器
    OutputDebugStringA(logBuffer);
    
    // 对于警告和错误，也输出到控制台
    if (level >= 2) {
        HANDLE hConsole = GetStdHandle(STD_ERROR_HANDLE);
        DWORD written;
        WriteConsoleA(hConsole, logBuffer, (DWORD)strlen(logBuffer), &written, NULL);
    }
}

/* 安全相关操作 */

// 安全内存清零
void platform_secure_zero_memory(void* data, size_t size) {
    if (!data || size == 0) {
        return;
    }
    volatile unsigned char* p = (volatile unsigned char*)data;
    // 使用 SecureZeroMemory 或等效的编译器/平台特定实现来防止优化
#ifdef _WIN32
    SecureZeroMemory(p, size);
#else
    // 备选方案：手动循环，并确保编译器不会优化掉
    // 使用 volatile 指针有助于防止优化
    while (size--) {
        *p++ = 0;
    }
    // 可以在这里添加内存屏障，进一步防止优化
    // __asm__ volatile("" ::: "memory"); // GCC/Clang 示例
#endif
}

// 使用 DPAPI 保护数据 (针对当前用户)
bool platform_protect_data(const unsigned char* plain_data, size_t plain_data_size,
                         unsigned char** protected_data, size_t* protected_data_size) {
    if (!plain_data || plain_data_size == 0 || !protected_data || !protected_data_size) {
        return false;
    }

    DATA_BLOB data_in;
    DATA_BLOB data_out;
    // 可选的熵，增加安全性，但需要一致性
    // DATA_BLOB entropy_blob;
    // entropy_blob.pbData = (BYTE*)"some_constant_entropy";
    // entropy_blob.cbData = (DWORD)strlen("some_constant_entropy") + 1;

    data_in.pbData = (BYTE*)plain_data;
    data_in.cbData = (DWORD)plain_data_size;

    *protected_data = NULL;
    *protected_data_size = 0;

    // 使用 CryptProtectData 进行加密
    if (CryptProtectData(
            &data_in,             // 输入数据
            L"User Data Key",     // 描述（可选）
            NULL,                 // 可选熵
            NULL,                 // 保留
            NULL,                 // 可选的提示结构
            CRYPTPROTECT_UI_FORBIDDEN, // 禁止 UI 交互
            &data_out)) {           // 输出的加密数据
        
        // 分配内存并复制加密后的数据
        *protected_data_size = data_out.cbData;
        *protected_data = (unsigned char*)platform_allocate_memory(*protected_data_size);
        if (!*protected_data) {
            LocalFree(data_out.pbData); // 释放 DPAPI 分配的内存
            return false;
        }
        memcpy(*protected_data, data_out.pbData, *protected_data_size);
        LocalFree(data_out.pbData); // 释放 DPAPI 分配的内存
        return true;
    } else {
        // 加密失败
        // 可以记录 GetLastError()
        platform_log_message(3, "CryptProtectData failed");
        return false;
    }
}

// 使用 DPAPI 解密数据 (针对当前用户)
bool platform_unprotect_data(const unsigned char* protected_data, size_t protected_data_size,
                           unsigned char** plain_data, size_t* plain_data_size) {
    if (!protected_data || protected_data_size == 0 || !plain_data || !plain_data_size) {
        return false;
    }

    DATA_BLOB data_in;
    DATA_BLOB data_out;
    // DATA_BLOB entropy_blob; // 如果加密时使用了熵，这里也需要

    data_in.pbData = (BYTE*)protected_data;
    data_in.cbData = (DWORD)protected_data_size;

    *plain_data = NULL;
    *plain_data_size = 0;
    LPWSTR description = NULL;

    // 使用 CryptUnprotectData 进行解密
    if (CryptUnprotectData(
            &data_in,            // 输入的加密数据
            &description,        // 输出描述（如果提供）
            NULL,                // 可选熵
            NULL,                // 保留
            NULL,                // 可选的提示结构
            CRYPTPROTECT_UI_FORBIDDEN, // 禁止 UI 交互
            &data_out)) {          // 输出的解密数据
        
        // 分配内存并复制解密后的数据
        *plain_data_size = data_out.cbData;
        *plain_data = (unsigned char*)platform_allocate_memory(*plain_data_size);
        if (!*plain_data) {
            LocalFree(data_out.pbData); // 释放 DPAPI 分配的内存
            if (description) LocalFree(description);
            return false;
        }
        memcpy(*plain_data, data_out.pbData, *plain_data_size);
        
        // 重要：安全地清除 DPAPI 返回的明文数据内存
        platform_secure_zero_memory(data_out.pbData, data_out.cbData);
        LocalFree(data_out.pbData); // 释放 DPAPI 分配的内存
        if (description) LocalFree(description);
        return true;
    } else {
        // 解密失败
        // 可以记录 GetLastError()
        platform_log_message(3, "CryptUnprotectData failed");
        if (description) LocalFree(description);
        return false;
    }
}

/* 内存管理 */

void* platform_allocate_memory(size_t size) {
    return HeapAlloc(GetProcessHeap(), HEAP_ZERO_MEMORY, size);
}

void platform_free_memory(void* memory) {
    if (memory) {
        HeapFree(GetProcessHeap(), 0, memory);
    }
}

/* 字符串处理 */

char* platform_strdup(const char* source) {
    if (!source) {
        return NULL;
    }
    
    size_t len = strlen(source) + 1;
    char* result = (char*)platform_allocate_memory(len);
    if (result) {
        StringCchCopyA(result, len, source);
    }
    return result;
}

int platform_stricmp(const char* s1, const char* s2) {
    return _stricmp(s1, s2);
}

#endif /* _WIN32 */ 