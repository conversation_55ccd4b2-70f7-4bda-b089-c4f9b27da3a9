/*
 * utils.h
 * 
 * 实用工具函数头文件
 * 定义一些通用的辅助函数
 */

#pragma once

#include <fltKernel.h>

// 文件路径函数
NTSTATUS
UtilGetFileName(
    _In_ PFLT_CALLBACK_DATA Data,
    _Out_ PUNICODE_STRING FileName
    );

BOOLEAN
UtilIsFileTypeMatch(
    _In_ PCUNICODE_STRING FileName,
    _In_ PCUNICODE_STRING Extension
    );

BOOLEAN
UtilIsPathMatch(
    _In_ PCUNICODE_STRING FilePath,
    _In_ PCUNICODE_STRING Pattern
    );

// 进程相关函数
NTSTATUS
UtilGetProcessName(
    _Out_ PUNICODE_STRING ProcessName
    );

NTSTATUS
UtilGetProcessImagePath(
    _Out_ PUNICODE_STRING ProcessPath
    );

// 用户相关函数
NTSTATUS
UtilGetCurrentUserSid(
    _Out_ PUNICODE_STRING UserSid
    );

// 日志函数
VOID
UtilLogEvent(
    _In_ ULONG EventLevel,
    _In_ PCSTR Format,
    ...
    );

// 字符串函数
NTSTATUS
UtilDuplicateUnicodeString(
    _In_ PCUNICODE_STRING Source,
    _Out_ PUNICODE_STRING Destination
    );

VOID
UtilFreeUnicodeString(
    _In_ PUNICODE_STRING String
    );

// 内存函数
PVOID
UtilAllocateMemory(
    _In_ SIZE_T Size,
    _In_ ULONG Tag
    );

VOID
UtilFreeMemory(
    _In_ PVOID Memory
    );

// 调试函数
#if DBG
#define UtilDebugPrint(Level, Format, ...) \
    UtilLogEvent(Level, Format, __VA_ARGS__)
#else
#define UtilDebugPrint(Level, Format, ...)
#endif 