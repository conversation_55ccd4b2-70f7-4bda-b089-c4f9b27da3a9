#ifndef CRYPTO_API_CRYPTO_UTILS_C_API_H
#define CRYPTO_API_CRYPTO_UTILS_C_API_H

#include <stddef.h> // For size_t
#include <stdint.h> // For uint8_t

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 使用SM4-GCM算法加密数据。
 *
 * @param plaintext 指向明文数据的指针。
 * @param plaintext_len 明文数据的长度（字节）。
 * @param key 指向16字节SM4密钥的指针。
 * @param key_len 密钥长度，必须为16。
 * @param iv 指向12字节初始化向量(IV)的指针。IV对于每个密钥和明文对必须是唯一的。
 * @param iv_len IV的长度，必须为12。
 * @param out_combined_data 指向输出缓冲区的指针，用于存储 IV + Ciphertext + Tag。
 *                          调用者必须确保此缓冲区足够大。
 *                          推荐大小: iv_len + plaintext_len + 16 (tag_len)。
 * @param p_out_combined_data_len 输入时，*p_out_combined_data_len 表示 out_combined_data 的容量；
 *                                输出时，*p_out_combined_data_len 表示实际写入 out_combined_data 的字节数。
 * @return 0 表示成功，非0表示错误代码。
 *         具体错误代码可以定义为枚举或宏，例如:
 *         1: 无效参数 (如null指针, 错误的key/iv长度)
 *         2: 内存分配失败 (如果内部需要动态分配)
 *         3: OpenSSL内部错误/加密失败
 */
int sm4_gcm_encrypt_c_api(
    const uint8_t* plaintext,
    size_t plaintext_len,
    const uint8_t* key,
    size_t key_len,
    const uint8_t* iv,
    size_t iv_len,
    uint8_t* out_combined_data,
    size_t* p_out_combined_data_len
);

/**
 * @brief 使用SM4-GCM算法解密数据。
 *
 * @param combined_data 指向待解密数据的指针 (期望结构: IV + Ciphertext + Tag)。
 * @param combined_data_len 待解密数据的总长度。
 * @param key 指向16字节SM4密钥的指针。
 * @param key_len 密钥长度，必须为16。
 * @param out_plaintext 指向输出缓冲区的指针，用于存储解密后的明文。
 *                      调用者必须确保此缓冲区足够大。
 *                      推荐大小: combined_data_len - 12 (iv_len) - 16 (tag_len)。
 * @param p_out_plaintext_len 输入时，*p_out_plaintext_len 表示 out_plaintext 的容量；
 *                            输出时，*p_out_plaintext_len 表示实际写入 out_plaintext 的字节数。
 * @return 0 表示成功，非0表示错误代码。
 *         具体错误代码可以定义为枚举或宏，例如:
 *         1: 无效参数 (如null指针, 错误的key长度, combined_data过短)
 *         2: 内存分配失败
 *         3: OpenSSL内部错误/解密失败
 *         4: 认证失败 (Tag不匹配)
 */
int sm4_gcm_decrypt_c_api(
    const uint8_t* combined_data,
    size_t combined_data_len,
    const uint8_t* key,
    size_t key_len,
    uint8_t* out_plaintext,
    size_t* p_out_plaintext_len
);

#ifdef __cplusplus
}
#endif

#endif // CRYPTO_API_CRYPTO_UTILS_C_API_H 