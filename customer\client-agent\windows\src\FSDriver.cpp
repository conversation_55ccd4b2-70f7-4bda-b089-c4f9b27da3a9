﻿#include "FSDriver.h"
#include <iostream> // For error logging in PoC
#include <stdexcept> // For exceptions

// TODO: Replace with proper logging
#define LOG_DRIVER_ERROR(msg) std::wcerr << L"[DRIVER_ERROR] " << msg << L" (Code: " << GetLastError() << L")" << std::endl;

FSDriver::FSDriver(const std::wstring& directoryPath, std::shared_ptr<CoreEngine> engine)
    : m_directoryPath(directoryPath), m_engine(engine) {
    if (!m_engine) {
        throw std::invalid_argument("CoreEngine cannot be null.");
    }
    // Create event for stopping the monitor thread
    m_hStopEvent = CreateEvent(nullptr, TRUE, FALSE, nullptr);
    if (m_hStopEvent == nullptr) {
        throw std::runtime_error("Failed to create stop event.");
    }
    // Create event for OVERLAPPED structure
    m_overlapped.hEvent = CreateEvent(nullptr, TRUE, FALSE, nullptr);
    if (m_overlapped.hEvent == nullptr) {
        CloseHandle(m_hStopEvent);
        throw std::runtime_error("Failed to create overlapped event.");
    }
}

FSDriver::~FSDriver() {
    stopMonitoring(); // Ensure monitoring is stopped
    if (m_hDirectory != INVALID_HANDLE_VALUE) {
        CloseHandle(m_hDirectory);
    }
    if (m_hStopEvent != nullptr) {
        CloseHandle(m_hStopEvent);
    }
     if (m_overlapped.hEvent != nullptr) {
        CloseHandle(m_overlapped.hEvent);
    }
}

bool FSDriver::startMonitoring() {
    if (m_isRunning) {
        return true; // Already running
    }

    m_hDirectory = CreateFileW(
        m_directoryPath.c_str(),
        FILE_LIST_DIRECTORY,
        FILE_SHARE_READ | FILE_SHARE_WRITE | FILE_SHARE_DELETE,
        nullptr,
        OPEN_EXISTING,
        FILE_FLAG_BACKUP_SEMANTICS | FILE_FLAG_OVERLAPPED,
        nullptr
    );

    if (m_hDirectory == INVALID_HANDLE_VALUE) {
        LOG_DRIVER_ERROR(L"Failed to open directory handle for " << m_directoryPath);
        return false;
    }

    m_isRunning = true;
    ResetEvent(m_hStopEvent); // Ensure stop event is not set initially
    m_monitorThread = std::thread(&FSDriver::monitorLoop, this);

    return true;
}

void FSDriver::stopMonitoring() {
    if (!m_isRunning) {
        return;
    }

    m_isRunning = false;
    if (m_hStopEvent != nullptr) {
        SetEvent(m_hStopEvent); // Signal the monitor loop to stop
    }

    // Cancel pending I/O
    if (m_hDirectory != INVALID_HANDLE_VALUE) {
       CancelIoEx(m_hDirectory, &m_overlapped);
    }

    if (m_monitorThread.joinable()) {
        m_monitorThread.join();
    }

     if (m_hDirectory != INVALID_HANDLE_VALUE) {
        CloseHandle(m_hDirectory);
        m_hDirectory = INVALID_HANDLE_VALUE;
    }
}

void FSDriver::monitorLoop() {
    DWORD dwBytesReturned = 0;

    while (m_isRunning) {
        BOOL success = ReadDirectoryChangesW(
            m_hDirectory,
            m_buffer,
            BUFFER_SIZE,
            TRUE, // Watch subtree
            FILE_NOTIFY_CHANGE_FILE_NAME | FILE_NOTIFY_CHANGE_DIR_NAME |
            FILE_NOTIFY_CHANGE_ATTRIBUTES | FILE_NOTIFY_CHANGE_SIZE |
            FILE_NOTIFY_CHANGE_LAST_WRITE | FILE_NOTIFY_CHANGE_CREATION,
            &dwBytesReturned, // This is ignored for async operations
            &m_overlapped,
            nullptr // No completion routine
        );

        if (!success) {
            // Check if the error is because CancelIoEx was called
            if (GetLastError() == ERROR_OPERATION_ABORTED) {
                break; // Expected when stopping
            }
            LOG_DRIVER_ERROR(L"ReadDirectoryChangesW failed initial call.");
            // Consider more robust error handling or retrying
            std::this_thread::sleep_for(std::chrono::seconds(1)); // Simple retry delay
            continue;
        }

        // Wait for the I/O operation to complete or the stop event
        HANDLE handles[] = { m_overlapped.hEvent, m_hStopEvent };
        DWORD waitResult = WaitForMultipleObjects(2, handles, FALSE, INFINITE);

        if (waitResult == WAIT_OBJECT_0) {
            // I/O completed
            DWORD bytesTransferred = 0;
            if (!GetOverlappedResult(m_hDirectory, &m_overlapped, &bytesTransferred, FALSE /* Don't wait */)) {
                // Check if the error is because CancelIoEx was called
                if (GetLastError() == ERROR_OPERATION_ABORTED) {
                    break; // Expected when stopping
                }
                LOG_DRIVER_ERROR(L"GetOverlappedResult failed.");
                continue;
            }

            if (bytesTransferred == 0) { // Can indicate buffer overflow
                LOG_DRIVER_ERROR(L"Buffer overflow detected or no data transferred");
                FileEvent overflowEvent;
                overflowEvent.type = FileEventType::OVERFLOW;
                overflowEvent.filePath1 = m_directoryPath;
                if (m_engine) {
                    m_engine->onFileEvent(overflowEvent);
                }
                performFullDirectoryScan();
                continue;
            }

            // Process the notification buffer
            FILE_NOTIFY_INFORMATION* pNotify = (FILE_NOTIFY_INFORMATION*)m_buffer;
            while (pNotify != nullptr) {
                if ((BYTE*)pNotify < m_buffer || (BYTE*)pNotify >= m_buffer + BUFFER_SIZE) {
                    LOG_DRIVER_ERROR(L"Invalid notification pointer detected");
                    break;
                }
                
                FileEvent event;
                DWORD maxLength = min(pNotify->FileNameLength / sizeof(WCHAR), MAX_PATH - 1);
                std::wstring fileName(pNotify->FileName, maxLength);
                event.filePath1 = m_directoryPath + L"\\" + fileName;

                switch (pNotify->Action) {
                    case FILE_ACTION_ADDED:
                        event.type = FileEventType::CREATED;
                        break;
                    case FILE_ACTION_RENAMED_NEW_NAME:
                        event.type = FileEventType::RENAMED_NEW;
                        m_lastRenamedFile = event.filePath1;
                        break;
                    case FILE_ACTION_REMOVED:
                        event.type = FileEventType::DELETED;
                        break;
                    case FILE_ACTION_RENAMED_OLD_NAME:
                        event.type = FileEventType::RENAMED_OLD;
                        event.filePath2 = m_lastRenamedFile;
                        m_lastRenamedFile.clear();
                        break;
                    case FILE_ACTION_MODIFIED:
                        event.type = FileEventType::MODIFIED;
                        break;
                    default:
                        LOG_DRIVER_ERROR(L"Unknown file action: " << pNotify->Action);
                        goto next_record;
                }

                if (m_engine) {
                    m_engine->onFileEvent(event);
                }

            next_record:
                if (pNotify->NextEntryOffset == 0) {
                    break;
                }
                pNotify = (FILE_NOTIFY_INFORMATION*)((BYTE*)pNotify + pNotify->NextEntryOffset);
            }
            // Reset the event for the next overlapped operation
            ResetEvent(m_overlapped.hEvent);

        } else if (waitResult == WAIT_OBJECT_0 + 1) {
            // Stop event was signaled
            break;
        } else {
            // WaitForMultipleObjects failed
            LOG_DRIVER_ERROR(L"WaitForMultipleObjects failed.");
            break; // Exit loop on error
        }
    }
    // Loop finished
}

void FSDriver::performFullDirectoryScan() {
    // 当检测到缓冲区溢出时，执行完整扫描以确保捕获所有变化
    LOG_DRIVER_ERROR(L"Performing full directory scan due to potential buffer overflow");
    scanDirectory(m_directoryPath);
}

void FSDriver::scanDirectory(const std::wstring& path) {
    std::wstring searchPath = path + L"\\*";
    WIN32_FIND_DATAW findData;
    HANDLE hFind = FindFirstFileW(searchPath.c_str(), &findData);
    
    if (hFind == INVALID_HANDLE_VALUE) {
        LOG_DRIVER_ERROR(L"Failed to scan directory: " << path);
        return;
    }
    
    do {
        // 跳过 "." 和 ".." 目录
        if (wcscmp(findData.cFileName, L".") == 0 || wcscmp(findData.cFileName, L"..") == 0) {
            continue;
        }
        
        std::wstring fullPath = path + L"\\" + findData.cFileName;
        
        // 根据文件属性生成适当的事件
        FileEvent event;
        event.filePath1 = fullPath;
        // 使用MODIFIED类型表示找到的文件，因为我们不知道它是新文件还是已修改文件
        event.type = FileEventType::SCANNED;
        
        // 通知引擎
        if (m_engine) {
            m_engine->onFileEvent(event);
        }
        
        // 如果是目录，递归扫描
        if (findData.dwFileAttributes & FILE_ATTRIBUTE_DIRECTORY) {
            scanDirectory(fullPath);
        }
    } while (FindNextFileW(hFind, &findData));
    
    FindClose(hFind);
} 