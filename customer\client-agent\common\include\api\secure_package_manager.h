#ifndef CRYPTO_SECURE_PACKAGE_MANAGER_H
#define CRYPTO_SECURE_PACKAGE_MANAGER_H

#include <string>
#include <vector>
#include <functional>
#include <optional>
#include <map>
#include <future>
#include "secure_package_generator.h"

namespace crypto {
namespace secure_package {

/**
 * 外发包访问结果
 */
enum class PackageAccessResult {
    Success,             // 访问成功
    InvalidPassword,     // 密码错误
    Expired,             // 已过期
    MaxAccessExceeded,   // 超过最大访问次数
    DeviceMismatch,      // 设备不匹配
    PackageCorrupted,    // 包已损坏
    PermissionDenied,    // 权限不足
    UnknownError         // 未知错误
};

/**
 * 外发包文件信息
 */
struct PackageFileInfo {
    std::string fileName;
    std::string contentType;
    int64_t fileSize;
    std::string thumbnailPath;
};

/**
 * 外发包信息
 */
struct PackageInfo {
    std::string packageId;
    std::string name;
    std::string description;
    int64_t totalSize;
    std::string creatorInfo;
    std::string creationTime;
    std::optional<std::string> expiryTime;
    std::optional<int> maxAccessCount;
    int currentAccessCount;
    bool allowPrinting;
    bool allowEditing;
    bool requireDeviceBinding;
    std::optional<std::string> watermarkText;
    std::vector<PackageFileInfo> files;
};

/**
 * 安全外发包管理器接口
 * 负责解析和管理安全外发包
 */
class SecurePackageManager {
public:
    virtual ~SecurePackageManager() = default;
    
    /**
     * 创建外发包生成器
     * 
     * @return 外发包生成器
     */
    virtual std::unique_ptr<SecurePackageGenerator> createGenerator() = 0;
    
    /**
     * 打开外发包
     * 
     * @param packagePath 外发包路径
     * @return 外发包基本信息
     */
    virtual std::optional<PackageInfo> openPackage(const std::string& packagePath) = 0;
    
    /**
     * 验证外发包密码
     * 
     * @param packagePath 外发包路径
     * @param password 密码
     * @return 访问结果
     */
    virtual PackageAccessResult validatePassword(const std::string& packagePath, const std::string& password) = 0;
    
    /**
     * 提取外发包中的文件
     * 
     * @param packagePath 外发包路径
     * @param password 密码
     * @param fileName 要提取的文件名
     * @param outputPath 输出路径
     * @param progress 进度回调
     * @return 访问结果
     */
    virtual PackageAccessResult extractFile(
        const std::string& packagePath, 
        const std::string& password,
        const std::string& fileName,
        const std::string& outputPath,
        ProgressCallback progress = nullptr) = 0;
    
    /**
     * 提取外发包中的所有文件
     * 
     * @param packagePath 外发包路径
     * @param password 密码
     * @param outputDir 输出目录
     * @param progress 进度回调
     * @return 访问结果
     */
    virtual PackageAccessResult extractAllFiles(
        const std::string& packagePath, 
        const std::string& password,
        const std::string& outputDir,
        ProgressCallback progress = nullptr) = 0;
    
    /**
     * 异步创建外发包
     * 
     * @param files 文件路径列表
     * @param outputPath 输出路径
     * @param options 配置选项
     * @param progress 进度回调
     * @return 异步任务
     */
    virtual std::future<bool> createPackageAsync(
        const std::vector<std::string>& files,
        const std::string& outputPath,
        const PackageOptions& options,
        ProgressCallback progress = nullptr) = 0;
    
    /**
     * 异步上传外发包到服务器
     * 
     * @param packagePath 外发包路径
     * @param metadata 元数据
     * @param progress 进度回调
     * @return 异步任务，返回服务器分配的包ID
     */
    virtual std::future<std::optional<std::string>> uploadPackageAsync(
        const std::string& packagePath,
        const std::map<std::string, std::string>& metadata,
        ProgressCallback progress = nullptr) = 0;
};

} // namespace secure_package
} // namespace crypto

#endif // CRYPTO_SECURE_PACKAGE_MANAGER_H 