#!/bin/bash
#
# Cryptosystem Linux 客户端编译脚本
#

set -e

# 显示帮助
show_help() {
    echo "用法: $0 [选项]"
    echo "编译 Cryptosystem Linux 客户端"
    echo ""
    echo "选项:"
    echo "  -h, --help        显示帮助信息"
    echo "  -c, --clean       清理编译目录"
    echo "  -d, --debug       编译调试版本"
    echo "  --install-deps    安装依赖包"
    echo ""
}

# 安装依赖
install_dependencies() {
    echo "正在安装依赖..."
    if [ -f /etc/debian_version ]; then
        # Debian/Ubuntu
        sudo apt-get update
        sudo apt-get install -y build-essential cmake pkg-config libssl-dev libfuse-dev libjson-c-dev libcurl4-openssl-dev uuid-dev
    elif [ -f /etc/redhat-release ]; then
        # CentOS/RHEL/Fedora
        sudo yum install -y gcc gcc-c++ make cmake pkgconfig openssl-devel fuse-devel json-c-devel libcurl-devel libuuid-devel
    elif [ -f /etc/arch-release ]; then
        # Arch Linux
        sudo pacman -Sy --needed gcc make cmake pkgconf openssl fuse2 json-c curl util-linux
    elif [ -f /etc/SuSE-release ] || [ -f /etc/SUSE-brand ]; then
        # SUSE
        sudo zypper install -y gcc gcc-c++ make cmake pkg-config libopenssl-devel fuse-devel libjson-c-devel libcurl-devel libuuid-devel
    else
        echo "无法识别的发行版，请手动安装以下依赖："
        echo "- gcc/g++"
        echo "- make"
        echo "- cmake"
        echo "- pkg-config"
        echo "- OpenSSL 开发库"
        echo "- FUSE 开发库"
        echo "- JSON-C 开发库"
        echo "- libcurl 开发库"
        echo "- libuuid 开发库"
        exit 1
    fi
    echo "依赖安装完成"
}

# 解析命令行参数
CLEAN=0
DEBUG=0
INSTALL_DEPS=0

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -c|--clean)
            CLEAN=1
            shift
            ;;
        -d|--debug)
            DEBUG=1
            shift
            ;;
        --install-deps)
            INSTALL_DEPS=1
            shift
            ;;
        *)
            echo "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 安装依赖
if [ $INSTALL_DEPS -eq 1 ]; then
    install_dependencies
fi

# 获取脚本目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
BUILD_DIR="$SCRIPT_DIR/build"

# 清理
if [ $CLEAN -eq 1 ]; then
    echo "清理构建目录..."
    rm -rf "$BUILD_DIR"
    echo "清理完成"
    exit 0
fi

# 创建构建目录
mkdir -p "$BUILD_DIR"
cd "$BUILD_DIR"

# 配置CMake
echo "配置项目..."
CMAKE_OPTIONS=""

if [ $DEBUG -eq 1 ]; then
    CMAKE_OPTIONS="-DCMAKE_BUILD_TYPE=Debug"
else
    CMAKE_OPTIONS="-DCMAKE_BUILD_TYPE=Release"
fi

cmake $CMAKE_OPTIONS "$SCRIPT_DIR"

# 编译
echo "开始编译..."
make -j$(nproc)

echo "编译完成"

# 显示输出文件信息
echo ""
echo "编译成功，生成的文件:"
echo "- $BUILD_DIR/cryptosystem_client - 命令行客户端"
echo "- $BUILD_DIR/libcryptosystem.so - 加密库"
echo ""
echo "可以使用以下命令安装:"
echo "sudo cp $BUILD_DIR/cryptosystem_client /usr/local/bin/"
echo "sudo cp $BUILD_DIR/libcryptosystem.so /usr/local/lib/"
echo "sudo ldconfig"