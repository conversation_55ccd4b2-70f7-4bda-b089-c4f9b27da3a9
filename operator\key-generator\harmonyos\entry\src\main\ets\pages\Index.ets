import hilog from '@ohos.hilog';
import { KeyGenerationViewModel } from '../viewmodel/KeyGenerationViewModel';
import { ClientInfo, CryptoAlgorithm, KeyGenerationRequest, KeyInfo, KeyStatus } from '../model/KeyModels';

@Entry
@Component
struct Index {
  @State private currentTabIndex: number = 0;
  @State private keyGenerationViewModel: KeyGenerationViewModel = new KeyGenerationViewModel();
  @State private isDatabaseConnected: boolean = false;
  @State private isLoading: boolean = false;
  @State private statusMessage: string = '正在初始化...';
  // 表单数据
  @State private clientId: string = '';
  @State private clientName: string = '';
  @State private description: string = '';

  aboutToAppear() {
    this.initializeApp();
  }

  build() {
    Column() {
      // 标题栏
      this.buildHeader()

      // 主要内容区域
      if (this.isLoading) {
        this.buildLoadingView()
      } else {
        this.buildMainContent()
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#f5f5f5')
  }

  @Builder
  buildHeader() {
    Row() {
      Text('企业密钥生成器')
        .fontSize(24)
        .fontWeight(FontWeight.Bold)
        .fontColor('#1a1a1a')
        .margin({ left: 24 })

      Blank()

      // 数据库状态指示器
      Row({ space: 8 }) {
        Circle({ width: 8, height: 8 })
          .fill(this.isDatabaseConnected ? '#00c851' : '#ff4444')

        Text(this.isDatabaseConnected ? '数据库已连接' : '数据库未连接')
          .fontSize(14)
          .fontColor(this.isDatabaseConnected ? '#00c851' : '#ff4444')
      }
      .margin({ right: 24 })
    }
    .width('100%')
    .height(64)
    .backgroundColor('#ffffff')
    .shadow({
      radius: 4,
      color: '#00000010',
      offsetY: 2
    })
  }

  @Builder
  buildLoadingView() {
    Column() {
      Text(this.statusMessage)
        .fontSize(16)
        .fontColor('#666666')
        .margin({ top: 48 })
    }
    .width('100%')
    .layoutWeight(1)
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
  }

  @Builder
  buildMainContent() {
    Tabs({ barPosition: BarPosition.Start }) {
      TabContent() {
        this.buildKeyGenerationTab()
      }
      .tabBar('密钥生成')

      TabContent() {
        this.buildKeyManagementTab()
      }
      .tabBar('密钥管理')

      TabContent() {
        this.buildClientManagementTab()
      }
      .tabBar('客户管理')

      TabContent() {
        this.buildKeyDistributionTab()
      }
      .tabBar('密钥分发')

      TabContent() {
        this.buildAuditLogTab()
      }
      .tabBar('审计日志')

      TabContent() {
        this.buildSettingsTab()
      }
      .tabBar('系统设置')
    }
    .layoutWeight(1)
    .backgroundColor('#ffffff')
    .margin({ top: 1 })
    .onChange((index: number) => {
      this.currentTabIndex = index;
    })
  }

  @Builder
  buildKeyGenerationTab() {
    Scroll() {
      Column({ space: 24 }) {
        // 密钥生成表单
        this.buildKeyGenerationForm()

        // 生成结果显示
        if (this.keyGenerationViewModel.lastGenerationResult) {
          this.buildGenerationResult()
        }
      }
      .padding(24)
    }
  }

  @Builder
  buildKeyGenerationForm() {
    Column({ space: 16 }) {
      Text('密钥生成')
        .fontSize(20)
        .fontWeight(FontWeight.Bold)
        .alignSelf(ItemAlign.Start)

      // 客户信息
      this.buildFormItem('客户名称 *', this.keyGenerationViewModel.clientName, (value: string) => {
        this.keyGenerationViewModel.clientName = value;
      })

      // 密钥名称
      this.buildFormItem('密钥名称 *', this.keyGenerationViewModel.keyName, (value: string) => {
        this.keyGenerationViewModel.keyName = value;
      })

      // 算法选择
      this.buildAlgorithmSelector()

      // 日期选择
      Row({ space: 16 }) {
        this.buildDatePicker('生效日期', this.keyGenerationViewModel.effectiveDate, (value: string) => {
          this.keyGenerationViewModel.effectiveDate = value;
        })

        this.buildDatePicker('过期日期', this.keyGenerationViewModel.expirationDate, (value: string) => {
          this.keyGenerationViewModel.expirationDate = value;
        })
      }

      // 用途描述
      this.buildTextAreaItem('用途描述', this.keyGenerationViewModel.purpose, (value: string) => {
        this.keyGenerationViewModel.purpose = value;
      })

      // 验证错误显示
      if (this.keyGenerationViewModel.validationErrors.length > 0) {
        Column({ space: 4 }) {
          ForEach(this.keyGenerationViewModel.validationErrors, (error: string) => {
            Text(error)
              .fontSize(12)
              .fontColor('#ff4444')
              .alignSelf(ItemAlign.Start)
          })
        }
        .width('100%')
        .padding(12)
        .backgroundColor('#fff0f0')
        .borderRadius(4)
      }

      // 操作按钮
      Row({ space: 12 }) {
        Button('重置')
          .type(ButtonType.Capsule)
          .backgroundColor('#f0f0f0')
          .fontColor('#666666')
          .layoutWeight(1)
          .height(44)
          .onClick(() => {
            this.keyGenerationViewModel.resetForm();
          })

        Button(this.keyGenerationViewModel.isGenerating ? '生成中...' : '生成密钥')
          .type(ButtonType.Capsule)
          .backgroundColor('#0066cc')
          .fontColor('#ffffff')
          .layoutWeight(1)
          .height(44)
          .enabled(!this.keyGenerationViewModel.isGenerating && this.isDatabaseConnected)
          .onClick(() => {
            this.generateKey();
          })
      }
    }
    .width('100%')
    .padding(20)
    .backgroundColor('#ffffff')
    .borderRadius(8)
  }

  @Builder
  buildFormItem(label: string, value: string, onChange: (value: string) => void) {
    Column({ space: 8 }) {
      Text(label)
        .fontSize(14)
        .fontWeight(FontWeight.Medium)
        .alignSelf(ItemAlign.Start)

      TextInput({ text: value, placeholder: `请输入${label.replace(' *', '')}` })
        .fontSize(14)
        .backgroundColor('#f8f9fa')
        .borderRadius(4)
        .onChange(onChange)
    }
    .width('100%')
  }

  @Builder
  buildTextAreaItem(label: string, value: string, onChange: (value: string) => void) {
    Column({ space: 8 }) {
      Text(label)
        .fontSize(14)
        .fontWeight(FontWeight.Medium)
        .alignSelf(ItemAlign.Start)

      TextArea({ text: value, placeholder: `请输入${label}` })
        .fontSize(14)
        .backgroundColor('#f8f9fa')
        .borderRadius(4)
        .height(80)
        .onChange(onChange)
    }
    .width('100%')
  }

  @Builder
  buildAlgorithmSelector() {
    Column({ space: 8 }) {
      Text('加密算法')
        .fontSize(14)
        .fontWeight(FontWeight.Medium)
        .alignSelf(ItemAlign.Start)

      Select([
        { value: 0, text: 'SM4 (国密)' },
        { value: 1, text: 'AES-256' },
        { value: 2, text: 'AES-128' }
      ])
        .selected(this.keyGenerationViewModel.selectedAlgorithm)
        .value(this.keyGenerationViewModel.getAlgorithmName(this.keyGenerationViewModel.selectedAlgorithm))
        .fontSize(14)
        .onSelect((index: number) => {
          this.keyGenerationViewModel.selectedAlgorithm = index;
        })
    }
    .width('100%')
  }

  @Builder
  buildDatePicker(label: string, value: string, onChange: (value: string) => void) {
    Column({ space: 8 }) {
      Text(label)
        .fontSize(14)
        .fontWeight(FontWeight.Medium)
        .alignSelf(ItemAlign.Start)

      TextInput({ text: value, placeholder: 'YYYY-MM-DD' })
        .type(InputType.Date)
        .fontSize(14)
        .backgroundColor('#f8f9fa')
        .borderRadius(4)
        .onChange(onChange)
    }
    .layoutWeight(1)
  }

  @Builder
  buildGenerationResult() {
    Column({ space: 16 }) {
      Text('生成结果')
        .fontSize(18)
        .fontWeight(FontWeight.Bold)
        .alignSelf(ItemAlign.Start)

      if (this.keyGenerationViewModel.lastGenerationResult) {
        Column({ space: 12 }) {
          this.buildResultItem('密钥ID', this.keyGenerationViewModel.lastGenerationResult.keyId)
          this.buildResultItem('密钥名称', this.keyGenerationViewModel.lastGenerationResult.keyName)
          this.buildResultItem('算法',
            this.keyGenerationViewModel.getAlgorithmName(this.keyGenerationViewModel.selectedAlgorithm))
          this.buildResultItem('状态', this.keyGenerationViewModel.lastGenerationResult.status)
          this.buildResultItem('指纹', this.keyGenerationViewModel.lastGenerationResult.fingerprint)
        }
        .width('100%')
        .padding(16)
        .backgroundColor('#f0fff0')
        .borderRadius(8)
      }
    }
    .width('100%')
    .padding(20)
    .backgroundColor('#ffffff')
    .borderRadius(8)
  }

  @Builder
  buildResultItem(label: string, value: string) {
    Row() {
      Text(`${label}:`)
        .fontSize(14)
        .fontColor('#666666')
        .width(80)

      Text(value)
        .fontSize(14)
        .fontColor('#1a1a1a')
        .layoutWeight(1)
    }
    .width('100%')
  }

  // 其他Tab的占位符实现
  @Builder
  buildKeyManagementTab() {
    Center() {
      Text('密钥管理功能')
        .fontSize(16)
        .fontColor('#666666')
    }
  }

  @Builder
  buildClientManagementTab() {
    Center() {
      Text('客户管理功能')
        .fontSize(16)
        .fontColor('#666666')
    }
  }

  @Builder
  buildKeyDistributionTab() {
    Center() {
      Text('密钥分发功能')
        .fontSize(16)
        .fontColor('#666666')
    }
  }

  @Builder
  buildAuditLogTab() {
    Center() {
      Text('审计日志功能')
        .fontSize(16)
        .fontColor('#666666')
    }
  }

  @Builder
  buildSettingsTab() {
    Center() {
      Text('系统设置功能')
        .fontSize(16)
        .fontColor('#666666')
    }
  }

  /**
   * 初始化应用
   */
  private async initializeApp() {
    try {
      this.isLoading = true;
      this.statusMessage = '正在连接数据库...';

      // 检查数据库连接
      await this.keyGenerationViewModel.checkDatabaseConnection();
      this.isDatabaseConnected = this.keyGenerationViewModel.isDatabaseConnected;

      this.statusMessage = '初始化完成';
      hilog.info(0x0000, 'KeyGenerator', 'Application initialized successfully');
    } catch (error) {
      this.statusMessage = '初始化失败';
      hilog.error(0x0000, 'KeyGenerator', 'Failed to initialize application: %{public}s', error.message);
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * 生成密钥
   */
  private async generateKey() {
    const success = await this.keyGenerationViewModel.generateKey();
    if (success) {
      hilog.info(0x0000, 'KeyGenerator', 'Key generated successfully');
    } else {
      hilog.error(0x0000, 'KeyGenerator', 'Key generation failed');
    }
  }
}
