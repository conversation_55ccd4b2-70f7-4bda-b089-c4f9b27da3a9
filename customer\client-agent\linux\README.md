# Cryptosystem Linux 客户端

Cryptosystem Linux 客户端是一个提供透明文件加密和安全管理的系统组件，支持 AES-GCM 和国密 SM4 加密算法。

## 主要功能

- **文件透明加密**：在指定路径自动加密/解密文件，用户无需关心具体的加密细节
- **多种加密算法**：支持 AES-GCM 和国密 SM4-GCM 算法
- **安全密钥管理**：与服务器安全同步，支持密钥版本控制
- **基于策略的控制**：根据服务器下发的策略控制加密行为
- **可扩展性**：支持通过 FUSE 文件系统或内核模块集成
- **命令行界面**：提供简单易用的命令行工具

## 系统要求

- Linux 内核 4.4 或更高版本
- 支持的发行版：
  - Ubuntu 18.04+
  - Debian 10+
  - CentOS/RHEL 7+
  - Fedora 32+
  - 其他支持 FUSE 的 Linux 发行版

## 安装依赖

```bash
# Debian/Ubuntu
sudo apt-get install build-essential cmake pkg-config libssl-dev libfuse-dev libjson-c-dev libcurl4-openssl-dev uuid-dev

# CentOS/RHEL/Fedora
sudo yum install gcc gcc-c++ make cmake pkgconfig openssl-devel fuse-devel json-c-devel libcurl-devel libuuid-devel

# 或使用编译脚本自动安装依赖
./build.sh --install-deps
```

## 编译安装

```bash
# 编译
./build.sh

# 编译调试版本
./build.sh --debug

# 安装
sudo cp build/cryptosystem_client /usr/local/bin/
sudo cp build/libcryptosystem.so /usr/local/lib/
sudo ldconfig
```

## 快速入门

### 1. 设备注册

首先需要向管理服务器注册设备：

```bash
cryptosystem_client --register --server=https://server.example.com --org=org1 --email=<EMAIL> --name=mypc
```

### 2. 配置加密路径

添加需要加密的路径：

```bash
cryptosystem_client --add-path=/home/<USER>/documents
```

### 3. 启用加密

```bash
cryptosystem_client --enable
```

### 4. 查看状态

```bash
cryptosystem_client --status
```

### 5. 同步策略

```bash
cryptosystem_client --sync
```

## 命令行选项

```
命令选项:
  -h, --help              显示帮助信息
  -s, --status            显示当前状态
  -r, --register          注册设备
  -e, --enable            启用加密
  -d, --disable           禁用加密
  -a, --add-path=PATH     添加加密路径
  -m, --rm-path=PATH      删除加密路径
  -l, --list-paths        列出加密路径
  -y, --sync              同步策略

注册参数:
  -S, --server=URL        服务器URL
  -O, --org=ID            组织ID
  -E, --email=EMAIL       用户邮箱
  -N, --name=NAME         设备名称

其他选项:
  -c, --config-dir=DIR    配置目录路径
```

## 架构说明

Cryptosystem Linux 客户端由以下核心组件组成：

1. **加密核心模块 (crypto_manager)**：提供加密/解密 API，支持 AES 和 SM4 算法
2. **文件过滤器 (file_filter)**：提供路径过滤和文件操作检测
3. **操作处理 (filter_operations)**：处理各种文件操作的加解密转换
4. **系统集成 (filter_integration)**：与 Linux 系统集成，提供透明加解密
5. **密钥同步服务 (key_sync_service)**：与服务器安全通信，获取密钥和策略
6. **策略管理器 (policy_manager)**：应用加密策略，控制加密行为
7. **客户端配置 (client_config)**：管理设备配置和注册

## 开发者

- 集成自己的应用：使用库 API 集成加密功能
- 扩展加密算法：通过 `crypto_manager` 扩展
- 自定义策略：通过实现新的策略类型和处理逻辑