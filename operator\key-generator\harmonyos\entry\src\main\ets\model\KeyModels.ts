/**
 * 密钥类型枚举
 */
export enum KeyType {
  MasterKey = 1, // 主密钥
  WorkKey = 2, // 工作密钥
  TransportKey = 3, // 传输密钥
  StorageKey = 4 // 存储密钥
}

/**
 * 加密算法枚举
 */
export enum CryptoAlgorithm {
  AES256 = 1, // AES-256
  AES128 = 2, // AES-128
  SM4 = 3, // SM4（国密）
  RSA2048 = 4, // RSA-2048
  RSA4096 = 5, // RSA-4096
  SM2 = 6, // SM2（国密）
  ECCP256 = 7, // ECC P-256
  ECCP384 = 8 // ECC P-384
}

/**
 * 密钥状态枚举
 */
export enum KeyStatus {
  Pending = '待激活',
  Active = '活跃',
  Suspended = '已暂停',
  Revoked = '已吊销',
  Expired = '过期',
  Destroyed = '已销毁'
}

/**
 * 分发状态枚举
 */
export enum DistributionStatus {
  Pending = 'pending', // 待执行
  InProgress = 'in_progress', // 执行中
  Completed = 'completed', // 已完成
  Failed = 'failed', // 失败
  Cancelled = 'cancelled' // 已取消
}

/**
 * 日志级别枚举
 */
export enum LogLevel {
  Info = 'info', // 信息
  Warning = 'warning', // 警告
  Error = 'error', // 错误
  Critical = 'critical' // 严重
}

/**
 * 密钥信息实体
 */
export class KeyInfo {
  /** 密钥ID */
  keyId: string = '';

  /** 密钥名称 */
  keyName: string = '';

  /** 密钥类型 */
  keyType: KeyType = KeyType.WorkKey;

  /** 加密算法 */
  algorithm: CryptoAlgorithm = CryptoAlgorithm.AES256;

  /** 密钥长度（位） */
  keyLength: number = 256;

  /** 密钥状态 */
  status: KeyStatus = KeyStatus.Pending;

  /** 创建时间 */
  createdTime: Date = new Date();

  /** 生效时间 */
  effectiveTime: Date = new Date();

  /** 过期时间 */
  expiryTime: Date = new Date();

  /** 创建者 */
  createdBy: string = '';

  /** 客户单位 */
  clientUnit: string = '';

  /** 用途描述 */
  purpose: string = '';

  /** 密钥值（加密存储） */
  keyValue: string = '';

  /** 密钥指纹 */
  fingerprint: string = '';

  /** 版本号 */
  version: number = 1;

  /** 父密钥ID（用于密钥层次结构） */
  parentKeyId?: string;

  /** 使用次数 */
  usageCount: number = 0;

  /** 最大使用次数 */
  maxUsageCount?: number;

  /** 最后使用时间 */
  lastUsedTime?: Date;

  /** 备注 */
  remarks: string = '';

  /** 是否可导出 */
  exportable: boolean = false;

  /** 密钥标签 */
  tags: string[] = [];

  /** 扩展属性 */
  metadata: Record<string, any> = {};

  constructor(data?: Partial<KeyInfo>) {
    if (data) {
      Object.assign(this, data);
    }
  }

  /**
   * 检查密钥是否过期
   */
  isExpired(): boolean {
    return new Date() > this.expiryTime;
  }

  /**
   * 检查密钥是否活跃
   */
  isActive(): boolean {
    return this.status === KeyStatus.Active && !this.isExpired();
  }

  /**
   * 获取剩余有效天数
   */
  getRemainingDays(): number {
    const now = new Date();
    const diffTime = this.expiryTime.getTime() - now.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  /**
   * 检查是否即将过期（30天内）
   */
  isExpiringSoon(): boolean {
    return this.getRemainingDays() <= 30 && this.getRemainingDays() > 0;
  }
}

/**
 * 密钥生成请求实体
 */
export class KeyGenerationRequest {
  /** 密钥名称 */
  keyName: string = '';

  /** 密钥类型 */
  keyType: KeyType = KeyType.WorkKey;

  /** 加密算法 */
  algorithm: CryptoAlgorithm = CryptoAlgorithm.AES256;

  /** 密钥长度 */
  keyLength: number = 256;

  /** 有效期（天） */
  validityDays: number = 365;

  /** 客户单位 */
  clientUnit: string = '';

  /** 用途描述 */
  purpose: string = '';

  /** 是否可导出 */
  exportable: boolean = false;

  /** 最大使用次数 */
  maxUsageCount?: number;

  /** 父密钥ID */
  parentKeyId?: string;

  /** 密钥标签 */
  tags: string[] = [];

  /** 备注 */
  remarks: string = '';

  /** 扩展属性 */
  metadata: Record<string, any> = {};

  constructor(data?: Partial<KeyGenerationRequest>) {
    if (data) {
      Object.assign(this, data);
    }
  }

  /**
   * 验证请求参数
   */
  validate(): string[] {
    const errors: string[] = [];

    if (!this.keyName || this.keyName.trim() === '') {
      errors.push('密钥名称不能为空');
    }

    if (!this.clientUnit || this.clientUnit.trim() === '') {
      errors.push('客户单位不能为空');
    }

    if (this.validityDays <= 0) {
      errors.push('有效期必须大于0天');
    }

    if (this.keyLength <= 0) {
      errors.push('密钥长度必须大于0');
    }

    return errors;
  }
}

/**
 * 客户信息实体
 */
export class ClientInfo {
  /** 客户ID */
  clientId: string = '';

  /** 客户名称 */
  clientName: string = '';

  /** 客户代码 */
  clientCode?: string;

  /** 联系人 */
  contactPerson?: string;

  /** 联系电话 */
  contactPhone?: string;

  /** 联系邮箱 */
  contactEmail?: string;

  /** 地址 */
  address?: string;

  /** 描述信息 */
  description?: string;

  /** 创建时间 */
  createdTime: Date = new Date();

  /** 最后修改时间 */
  lastModified: Date = new Date();

  /** 是否激活 */
  isActive: boolean = true;

  /** 备注 */
  remarks?: string;

  constructor(data?: Partial<ClientInfo>) {
    if (data) {
      Object.assign(this, data);
    }
  }
}

/**
 * 密钥分发任务实体
 */
export class KeyDistributionTask {
  /** 任务ID */
  taskId: string = '';

  /** 任务名称 */
  taskName: string = '';

  /** 目标客户列表 */
  targetClients: string[] = [];

  /** 密钥ID列表 */
  keyIds: string[] = [];

  /** 任务描述 */
  description?: string;

  /** 分发状态 */
  status: DistributionStatus = DistributionStatus.Pending;

  /** 创建者 */
  createdBy: string = '';

  /** 创建时间 */
  createdTime: Date = new Date();

  /** 计划执行时间 */
  scheduledTime?: Date;

  /** 实际执行时间 */
  executedTime?: Date;

  /** 完成时间 */
  completedTime?: Date;

  /** 最后修改时间 */
  lastModified: Date = new Date();

  /** 执行结果 */
  result?: string;

  /** 错误信息 */
  errorMessage?: string;

  /** 进度百分比 */
  progress: number = 0;

  constructor(data?: Partial<KeyDistributionTask>) {
    if (data) {
      Object.assign(this, data);
    }
  }
}

/**
 * 审计日志条目实体
 */
export class AuditLogEntry {
  /** 日志ID */
  logId: string = '';

  /** 时间戳 */
  timestamp: Date = new Date();

  /** 日志级别 */
  level: LogLevel = LogLevel.Info;

  /** 操作类型 */
  operation: string = '';

  /** 用户ID */
  userId: string = '';

  /** 用户名 */
  userName?: string;

  /** 客户端信息 */
  clientInfo?: string;

  /** IP地址 */
  ipAddress?: string;

  /** 资源ID */
  resourceId?: string;

  /** 操作描述 */
  description?: string;

  /** 操作结果 */
  result?: string;

  /** 是否成功 */
  success: boolean = true;

  /** 错误代码 */
  errorCode?: string;

  /** 会话ID */
  sessionId?: string;

  /** 附加数据 */
  metadata?: Record<string, any>;

  constructor(data?: Partial<AuditLogEntry>) {
    if (data) {
      Object.assign(this, data);
    }
  }
}

/**
 * 系统设置实体
 */
export class SystemSettings {
  /** 安全设置 */
  security: {
    /** 启用强密码策略 */
    enforceStrongPassword: boolean;
    /** 会话超时时间（分钟） */
    sessionTimeoutMinutes: number;
    /** 启用登录失败锁定 */
    enableLoginLockout: boolean;
    /** 最大登录失败次数 */
    maxLoginAttempts: number;
  } = {
    enforceStrongPassword: true,
    sessionTimeoutMinutes: 30,
    enableLoginLockout: true,
    maxLoginAttempts: 5
  };

  /** 密钥生成设置 */
  keyGeneration: {
    /** 默认密钥长度 */
    defaultKeyLength: number;
    /** 默认有效期（天） */
    defaultValidityDays: number;
    /** 启用自动备份 */
    enableAutoBackup: boolean;
  } = {
    defaultKeyLength: 256,
    defaultValidityDays: 365,
    enableAutoBackup: true
  };

  /** 系统设置 */
  system: {
    /** 系统名称 */
    systemName: string;
    /** 数据库连接超时（秒） */
    databaseTimeoutSeconds: number;
    /** 启用调试模式 */
    enableDebugMode: boolean;
  } = {
    systemName: '企业密钥生成器',
    databaseTimeoutSeconds: 30,
    enableDebugMode: false
  };

  /** 网络设置 */
  network: {
    /** 服务端口 */
    serverPort: number;
    /** 启用HTTPS */
    enableHttps: boolean;
    /** 连接超时（秒） */
    connectionTimeoutSeconds: number;
  } = {
    serverPort: 8443,
    enableHttps: true,
    connectionTimeoutSeconds: 30
  };

  /** 日志设置 */
  logging: {
    /** 日志级别 */
    logLevel: string;
    /** 日志保留天数 */
    retentionDays: number;
    /** 启用审计日志 */
    enableAuditLog: boolean;
  } = {
    logLevel: 'info',
    retentionDays: 90,
    enableAuditLog: true
  };

  /** 最后修改时间 */
  lastModified: Date = new Date();

  constructor(data?: Partial<SystemSettings>) {
    if (data) {
      Object.assign(this, data);
    }
  }
}
