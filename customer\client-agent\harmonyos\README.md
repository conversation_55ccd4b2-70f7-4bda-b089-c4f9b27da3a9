# 鸿蒙客户端代理 (HarmonyOS Client Agent)

企业文档加密系统的鸿蒙平台客户端代理，提供透明文件加解密、策略管理和安全监控功能。

## 功能特性

### 核心功能
- **透明文件加解密**: 基于策略的实时文件加解密
- **文件监控**: 实时监控指定目录的文件变化
- **策略管理**: 动态同步和应用加密策略
- **国密算法支持**: 支持SM4等国密算法

### 安全特性
- **主密钥管理**: 安全的密钥存储和管理
- **文件完整性验证**: SHA-256哈希校验
- **权限控制**: 基于鸿蒙系统权限模型
- **审计日志**: 完整的操作审计记录

## 技术架构

### 开发框架
- **开发语言**: ArkTS/ArkUI
- **系统版本**: HarmonyOS 4.0+
- **加密库**: HarmonyOS Security Framework
- **数据存储**: HarmonyOS Preferences

### 核心组件

#### 服务层 (Services)
- `CryptoService`: 核心加密服务
- `FileMonitorService`: 文件监控服务

#### 管理层 (Managers)
- `PolicyManager`: 策略管理器

#### 数据模型 (Models)
- `CryptoModels`: 加密相关数据模型
- `PolicyModels`: 策略相关数据模型
- `FileModels`: 文件监控数据模型

#### 用户界面 (UI)
- `Index`: 主界面，提供状态监控和基本控制

## 项目结构

```
harmonyos/
├── module.json5                    # 模块配置
├── package.json                   # 项目配置
├── README.md                      # 本文档
└── src/main/
    ├── ets/
    │   ├── entryability/
    │   │   └── ClientAgentAbility.ets    # 主入口能力
    │   ├── services/
    │   │   ├── CryptoService.ets          # 加密服务
    │   │   └── FileMonitorService.ets     # 文件监控服务
    │   ├── managers/
    │   │   └── PolicyManager.ets          # 策略管理器
    │   ├── models/
    │   │   ├── CryptoModels.ets           # 加密数据模型
    │   │   ├── PolicyModels.ets           # 策略数据模型
    │   │   └── FileModels.ets             # 文件数据模型
    │   └── pages/
    │       └── Index.ets                  # 主界面
    └── resources/
        └── base/profile/
            └── main_pages.json            # 页面配置
```

## 开发环境

### 前置要求
- DevEco Studio 4.0+
- HarmonyOS SDK API 10+
- Node.js 16+

### 环境配置
1. 安装DevEco Studio
2. 配置HarmonyOS SDK
3. 创建签名证书
4. 配置设备连接

## 构建与部署

### 开发构建
```bash
# 清理项目
hvigor clean

# 安装依赖
hvigor install

# 构建项目
hvigor assembleHap

# 构建发布版本
hvigor assembleHap --mode release
```

### 生产部署
```bash
# 构建HAP包
hvigor assembleHap --mode release

# 安装到设备
hvigor installHap

# 或使用HDC工具手动安装
hdc install entry-default-signed.hap
```

## 配置说明

### 权限配置
应用需要以下权限：
- `ohos.permission.READ_WRITE_DOWNLOAD_DIRECTORY`: 文件访问权限
- `ohos.permission.FILE_ACCESS_MANAGER`: 文件管理权限
- `ohos.permission.INTERNET`: 网络通信权限

### 策略配置
默认加密策略：
- 文档类型：`.doc, .docx, .pdf, .txt, .xls, .xlsx, .ppt, .pptx`
- 监控路径：`/storage/emulated/0/Documents/`, `/storage/emulated/0/Download/`

## 安全注意事项

### 密钥安全
- 主密钥使用Base64编码存储在Preferences中
- 运行时密钥存储在内存中，应用退出时自动清零
- 文件加密使用PBKDF2密钥派生

### 文件安全
- 加密文件头包含完整性校验信息
- 支持文件内容熵值检测，防止重复加密
- 文件操作异常时提供完整错误信息

### 通信安全
- 策略同步支持TLS加密通信
- API调用包含认证信息
- 网络通信异常时自动重试

## 故障排除

### 常见问题

1. **服务无法启动**
   - 检查权限配置是否正确
   - 确认HarmonyOS版本兼容性
   - 查看系统日志获取详细错误信息

2. **文件加密失败**
   - 检查文件是否在监控路径内
   - 确认策略配置是否正确
   - 验证主密钥是否已设置

3. **策略同步失败**
   - 检查网络连接状态
   - 确认服务器地址配置
   - 验证API密钥是否有效

### 日志调试
```typescript
// 启用详细日志
hilog.info(0x0000, 'ClientAgent', '调试信息');
hilog.error(0x0000, 'ClientAgent', '错误信息: %{public}s', error.message);
```

## 版本历史

### v1.0.0 (2025-01-20)
- 初始版本发布
- 基础加密功能实现
- 文件监控和策略管理
- 国密SM4算法支持
- 鸿蒙原生界面

## 贡献指南

1. Fork项目到个人仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 许可证

本项目采用MIT许可证 - 查看 [LICENSE](../../LICENSE) 文件了解详情。

## 联系方式

- 项目维护者：企业文档加密系统开发团队
- 技术支持：请通过GitHub Issues反馈问题
- 更多文档：请查看项目根目录的docs文件夹 