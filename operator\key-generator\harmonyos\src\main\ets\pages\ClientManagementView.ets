import hilog from '@ohos.hilog';
import { ClientManagementViewModel } from '../viewmodels/ClientManagementViewModel';
import { ClientInfo } from '../models/KeyModels';

@Component
export struct ClientManagementView {
  @State private viewModel: ClientManagementViewModel = new ClientManagementViewModel();
  @State private isLoading: boolean = false;
  @State private showAddDialog: boolean = false;
  @State private showEditDialog: boolean = false;
  @State private editingClient: ClientInfo | null = null;
  @State private searchText: string = '';

  // 新增客户表单
  @State private newClientForm: {
    clientName: string;
    clientCode: string;
    contactPerson: string;
    contactEmail: string;
    contactPhone: string;
    description: string;
  } = {
    clientName: '',
    clientCode: '',
    contactPerson: '',
    contactEmail: '',
    contactPhone: '',
    description: ''
  };

  // 编辑客户表单
  @State private editClientForm: {
    clientName: string;
    clientCode: string;
    contactPerson: string;
    contactEmail: string;
    contactPhone: string;
    description: string;
  } = {
    clientName: '',
    clientCode: '',
    contactPerson: '',
    contactEmail: '',
    contactPhone: '',
    description: ''
  };

  private static readonly TAG = 'ClientManagementView';
  private static readonly DOMAIN = 0x0000;

  aboutToAppear() {
    this.initializeView();
  }

  /**
   * 初始化视图
   */
  private async initializeView() {
    try {
      hilog.info(ClientManagementView.DOMAIN, ClientManagementView.TAG, 'Initializing client management view');
      this.isLoading = true;
      await this.viewModel.initialize();
      await this.viewModel.loadClients();
    } catch (error) {
      hilog.error(ClientManagementView.DOMAIN, ClientManagementView.TAG, 'Failed to initialize view: %{public}s', error.message);
    } finally {
      this.isLoading = false;
    }
  }

  build() {
    Column() {
      // 标题栏
      this.buildHeader()

      // 搜索和操作栏
      this.buildSearchAndActions()

      // 客户列表
      if (this.isLoading) {
        this.buildLoadingView()
      } else {
        this.buildClientList()
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#f5f5f5')
    .bindContentCover(this.showAddDialog, this.buildAddClientDialog(), {
      modalTransition: ModalTransition.DEFAULT
    })
    .bindContentCover(this.showEditDialog, this.buildEditClientDialog(), {
      modalTransition: ModalTransition.DEFAULT
    })
  }

  @Builder
  buildHeader() {
    Row() {
      Text('客户管理')
        .fontSize(24)
        .fontWeight(FontWeight.Bold)
        .fontColor('#1a1a1a')
        .margin({ left: 24 })

      Blank()

      Text(`共 ${this.viewModel.clientList.length} 个客户`)
        .fontSize(14)
        .fontColor('#666666')
        .margin({ right: 24 })
    }
    .width('100%')
    .height(64)
    .backgroundColor('#ffffff')
    .shadow({
      radius: 4,
      color: '#00000010',
      offsetY: 2
    })
  }

  @Builder
  buildSearchAndActions() {
    Row({ space: 12 }) {
      // 搜索框
      TextInput({ placeholder: '搜索客户名称或代码...' })
        .layoutWeight(1)
        .fontSize(14)
        .backgroundColor('#f8f9fa')
        .borderRadius(4)
        .onChange((value: string) => {
          this.searchText = value;
          this.viewModel.searchClients(value);
        })

      // 搜索按钮
      Button('搜索')
        .type(ButtonType.Capsule)
        .backgroundColor('#0066cc')
        .fontColor('#ffffff')
        .fontSize(14)
        .height(40)
        .onClick(() => {
          this.viewModel.searchClients(this.searchText);
        })

      // 添加客户按钮
      Button('添加客户')
        .type(ButtonType.Capsule)
        .backgroundColor('#00c851')
        .fontColor('#ffffff')
        .fontSize(14)
        .height(40)
        .onClick(() => {
          this.showAddClientDialog();
        })

      // 刷新按钮
      Button('刷新')
        .type(ButtonType.Capsule)
        .backgroundColor('#666666')
        .fontColor('#ffffff')
        .fontSize(14)
        .height(40)
        .onClick(() => {
          this.viewModel.loadClients();
        })
    }
    .width('100%')
    .padding({ horizontal: 24, vertical: 16 })
    .backgroundColor('#ffffff')
    .margin({ top: 1 })
  }

  @Builder
  buildLoadingView() {
    Column() {
      Text('正在加载客户信息...')
        .fontSize(16)
        .fontColor('#999999')
        .margin({ top: 48 })
    }
    .width('100%')
    .layoutWeight(1)
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
  }

  @Builder
  buildClientList() {
    if (this.viewModel.filteredClients.length === 0) {
      Column() {
        Text(this.searchText ? '未找到匹配的客户' : '暂无客户数据')
          .fontSize(16)
          .fontColor('#999999')
          .margin({ top: 48 })

        if (!this.searchText) {
          Button('添加第一个客户')
            .type(ButtonType.Capsule)
            .backgroundColor('#0066cc')
            .fontColor('#ffffff')
            .fontSize(14)
            .margin({ top: 16 })
            .onClick(() => {
              this.showAddClientDialog();
            })
        }
      }
      .width('100%')
      .layoutWeight(1)
      .justifyContent(FlexAlign.Center)
      .alignItems(HorizontalAlign.Center)
    } else {
      List({ space: 1 }) {
        ForEach(this.viewModel.filteredClients, (client: ClientInfo, index: number) => {
          ListItem() {
            this.buildClientListItem(client, index)
          }
        })
      }
      .layoutWeight(1)
      .backgroundColor('#f5f5f5')
      .padding({ horizontal: 24, top: 8 })
    }
  }

  @Builder
  buildClientListItem(client: ClientInfo, index: number) {
    Column({ space: 12 }) {
      // 客户基本信息
      Row() {
        Column({ space: 4 }) {
          Text(client.clientName)
            .fontSize(18)
            .fontWeight(FontWeight.Medium)
            .fontColor('#1a1a1a')
            .alignSelf(ItemAlign.Start)

          Text(`代码: ${client.clientCode || '未设置'}`)
            .fontSize(14)
            .fontColor('#666666')
            .alignSelf(ItemAlign.Start)
        }
        .alignItems(HorizontalAlign.Start)
        .layoutWeight(1)

        Blank()

        // 状态标识
        Text(client.isActive ? '活跃' : '停用')
          .fontSize(12)
          .fontWeight(FontWeight.Medium)
          .fontColor(client.isActive ? '#00c851' : '#ff4444')
          .padding({ horizontal: 8, vertical: 2 })
          .backgroundColor(client.isActive ? '#e8f5e8' : '#ffeaea')
          .borderRadius(12)
      }

      // 联系信息
      if (client.contactPerson || client.contactEmail || client.contactPhone) {
        Column({ space: 4 }) {
          if (client.contactPerson) {
            Text(`联系人: ${client.contactPerson}`)
              .fontSize(12)
              .fontColor('#999999')
              .alignSelf(ItemAlign.Start)
          }

          Row({ space: 16 }) {
            if (client.contactEmail) {
              Text(`邮箱: ${client.contactEmail}`)
                .fontSize(12)
                .fontColor('#999999')
            }

            if (client.contactPhone) {
              Text(`电话: ${client.contactPhone}`)
                .fontSize(12)
                .fontColor('#999999')
            }
          }
          .alignSelf(ItemAlign.Start)
        }
      }

      // 操作按钮
      Row({ space: 8 }) {
        Text(`创建时间: ${this.formatDate(client.createdTime)}`)
          .fontSize(12)
          .fontColor('#999999')

        Blank()

        Button('编辑')
          .type(ButtonType.Capsule)
          .backgroundColor('#f0f0f0')
          .fontColor('#666666')
          .fontSize(12)
          .height(28)
          .onClick(() => {
            this.editClient(client);
          })

        Button(client.isActive ? '停用' : '启用')
          .type(ButtonType.Capsule)
          .backgroundColor(client.isActive ? '#fff0f0' : '#f0fff0')
          .fontColor(client.isActive ? '#ff4444' : '#00c851')
          .fontSize(12)
          .height(28)
          .onClick(() => {
            this.toggleClientStatus(client);
          })

        Button('删除')
          .type(ButtonType.Capsule)
          .backgroundColor('#fff0f0')
          .fontColor('#ff4444')
          .fontSize(12)
          .height(28)
          .onClick(() => {
            this.deleteClient(client);
          })
      }
    }
    .width('100%')
    .padding(16)
    .backgroundColor('#ffffff')
    .borderRadius(8)
    .margin({ vertical: 4 })
  }

  /**
   * 显示添加客户对话框
   */
  private showAddClientDialog() {
    // 重置表单
    this.newClientForm = {
      clientName: '',
      clientCode: '',
      contactPerson: '',
      contactEmail: '',
      contactPhone: '',
      description: ''
    };
    this.showAddDialog = true;
  }

  /**
   * 编辑客户
   */
  private editClient(client: ClientInfo) {
    this.editingClient = client;
    this.editClientForm = {
      clientName: client.clientName,
      clientCode: client.clientCode || '',
      contactPerson: client.contactPerson || '',
      contactEmail: client.contactEmail || '',
      contactPhone: client.contactPhone || '',
      description: client.description || ''
    };
    this.showEditDialog = true;
  }

  /**
   * 切换客户状态
   */
  private async toggleClientStatus(client: ClientInfo) {
    try {
      await this.viewModel.toggleClientStatus(client.clientId);
      await this.viewModel.loadClients(); // 刷新列表
    } catch (error) {
      hilog.error(ClientManagementView.DOMAIN, ClientManagementView.TAG, 'Failed to toggle client status: %{public}s', error.message);
    }
  }

  /**
   * 删除客户
   */
  private async deleteClient(client: ClientInfo) {
    try {
      // 这里应该显示确认对话框，简化处理直接删除
      await this.viewModel.deleteClient(client.clientId);
      await this.viewModel.loadClients(); // 刷新列表
    } catch (error) {
      hilog.error(ClientManagementView.DOMAIN, ClientManagementView.TAG, 'Failed to delete client: %{public}s', error.message);
    }
  }

  /**
   * 格式化日期
   */
  private formatDate(date: Date): string {
    if (!date) return '-';
    return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
  }

  /**
   * 添加客户对话框
   */
  @Builder
  buildAddClientDialog() {
    Column() {
      Column({ space: 24 }) {
        // 标题
        Row() {
          Text('添加新客户')
            .fontSize(20)
            .fontWeight(FontWeight.Bold)
            .fontColor('#1a1a1a')
            .layoutWeight(1)

          Button('×')
            .type(ButtonType.Circle)
            .backgroundColor('#f0f0f0')
            .fontColor('#666666')
            .fontSize(18)
            .width(32)
            .height(32)
            .onClick(() => {
              this.showAddDialog = false;
            })
        }
        .width('100%')

        // 表单内容
        Scroll() {
          Column({ space: 16 }) {
            // 客户名称
            this.buildFormItem('客户名称 *', this.newClientForm.clientName, (value: string) => {
              this.newClientForm.clientName = value;
            })

            // 客户代码
            this.buildFormItem('客户代码', this.newClientForm.clientCode, (value: string) => {
              this.newClientForm.clientCode = value;
            })

            // 联系人
            this.buildFormItem('联系人', this.newClientForm.contactPerson, (value: string) => {
              this.newClientForm.contactPerson = value;
            })

            // 联系邮箱
            this.buildFormItem('联系邮箱', this.newClientForm.contactEmail, (value: string) => {
              this.newClientForm.contactEmail = value;
            })

            // 联系电话
            this.buildFormItem('联系电话', this.newClientForm.contactPhone, (value: string) => {
              this.newClientForm.contactPhone = value;
            })

            // 描述
            this.buildTextAreaItem('描述信息', this.newClientForm.description, (value: string) => {
              this.newClientForm.description = value;
            })
          }
        }
        .height(300)

        // 操作按钮
        Row({ space: 12 }) {
          Button('取消')
            .type(ButtonType.Capsule)
            .backgroundColor('#f0f0f0')
            .fontColor('#666666')
            .fontSize(16)
            .layoutWeight(1)
            .height(44)
            .onClick(() => {
              this.showAddDialog = false;
            })

          Button('保存')
            .type(ButtonType.Capsule)
            .backgroundColor('#0066cc')
            .fontColor('#ffffff')
            .fontSize(16)
            .layoutWeight(1)
            .height(44)
            .onClick(() => {
              this.saveNewClient();
            })
        }
        .width('100%')
      }
      .width('100%')
      .maxWidth(400)
      .padding(24)
      .backgroundColor('#ffffff')
      .borderRadius(12)
      .shadow({
        radius: 16,
        color: '#00000020',
        offsetY: 4
      })
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#00000080')
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
    .onClick(() => {
      this.showAddDialog = false;
    })
    .onTouch((event: TouchEvent) => {
      if (event.type === TouchType.Down) {
        event.stopPropagation();
      }
    })
  }

  /**
   * 编辑客户对话框
   */
  @Builder
  buildEditClientDialog() {
    Column() {
      Column({ space: 24 }) {
        // 标题
        Row() {
          Text('编辑客户信息')
            .fontSize(20)
            .fontWeight(FontWeight.Bold)
            .fontColor('#1a1a1a')
            .layoutWeight(1)

          Button('×')
            .type(ButtonType.Circle)
            .backgroundColor('#f0f0f0')
            .fontColor('#666666')
            .fontSize(18)
            .width(32)
            .height(32)
            .onClick(() => {
              this.showEditDialog = false;
            })
        }
        .width('100%')

        // 表单内容
        Scroll() {
          Column({ space: 16 }) {
            // 客户名称
            this.buildFormItem('客户名称 *', this.editClientForm.clientName, (value: string) => {
              this.editClientForm.clientName = value;
            })

            // 客户代码
            this.buildFormItem('客户代码', this.editClientForm.clientCode, (value: string) => {
              this.editClientForm.clientCode = value;
            })

            // 联系人
            this.buildFormItem('联系人', this.editClientForm.contactPerson, (value: string) => {
              this.editClientForm.contactPerson = value;
            })

            // 联系邮箱
            this.buildFormItem('联系邮箱', this.editClientForm.contactEmail, (value: string) => {
              this.editClientForm.contactEmail = value;
            })

            // 联系电话
            this.buildFormItem('联系电话', this.editClientForm.contactPhone, (value: string) => {
              this.editClientForm.contactPhone = value;
            })

            // 描述
            this.buildTextAreaItem('描述信息', this.editClientForm.description, (value: string) => {
              this.editClientForm.description = value;
            })
          }
        }
        .height(300)

        // 操作按钮
        Row({ space: 12 }) {
          Button('取消')
            .type(ButtonType.Capsule)
            .backgroundColor('#f0f0f0')
            .fontColor('#666666')
            .fontSize(16)
            .layoutWeight(1)
            .height(44)
            .onClick(() => {
              this.showEditDialog = false;
            })

          Button('保存')
            .type(ButtonType.Capsule)
            .backgroundColor('#0066cc')
            .fontColor('#ffffff')
            .fontSize(16)
            .layoutWeight(1)
            .height(44)
            .onClick(() => {
              this.saveEditClient();
            })
        }
        .width('100%')
      }
      .width('100%')
      .maxWidth(400)
      .padding(24)
      .backgroundColor('#ffffff')
      .borderRadius(12)
      .shadow({
        radius: 16,
        color: '#00000020',
        offsetY: 4
      })
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#00000080')
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
    .onClick(() => {
      this.showEditDialog = false;
    })
    .onTouch((event: TouchEvent) => {
      if (event.type === TouchType.Down) {
        event.stopPropagation();
      }
    })
  }

  /**
   * 表单项组件
   */
  @Builder
  buildFormItem(label: string, value: string, onChange: (value: string) => void) {
    Column({ space: 8 }) {
      Text(label)
        .fontSize(14)
        .fontWeight(FontWeight.Medium)
        .fontColor('#333333')
        .alignSelf(ItemAlign.Start)

      TextInput({ text: value, placeholder: `请输入${label.replace(' *', '')}` })
        .fontSize(14)
        .backgroundColor('#ffffff')
        .borderRadius(4)
        .borderWidth(1)
        .borderColor('#e0e0e0')
        .onChange(onChange)
    }
    .width('100%')
  }

  /**
   * 文本域组件
   */
  @Builder
  buildTextAreaItem(label: string, value: string, onChange: (value: string) => void) {
    Column({ space: 8 }) {
      Text(label)
        .fontSize(14)
        .fontWeight(FontWeight.Medium)
        .fontColor('#333333')
        .alignSelf(ItemAlign.Start)

      TextArea({ text: value, placeholder: `请输入${label}` })
        .fontSize(14)
        .backgroundColor('#ffffff')
        .borderRadius(4)
        .borderWidth(1)
        .borderColor('#e0e0e0')
        .height(80)
        .onChange(onChange)
    }
    .width('100%')
  }

  /**
   * 保存新客户
   */
  private async saveNewClient() {
    try {
      // 表单验证
      if (!this.newClientForm.clientName.trim()) {
        hilog.warn(ClientManagementView.DOMAIN, ClientManagementView.TAG, 'Client name is required');
        return;
      }

      // 创建客户信息
      const clientInfo: ClientInfo = {
        clientId: '', // 由服务端生成
        clientName: this.newClientForm.clientName.trim(),
        clientCode: this.newClientForm.clientCode.trim() || undefined,
        contactPerson: this.newClientForm.contactPerson.trim() || undefined,
        contactEmail: this.newClientForm.contactEmail.trim() || undefined,
        contactPhone: this.newClientForm.contactPhone.trim() || undefined,
        description: this.newClientForm.description.trim() || undefined,
        isActive: true,
        createdTime: new Date(),
        lastModified: new Date()
      };

      await this.viewModel.addClient(clientInfo);
      await this.viewModel.loadClients(); // 刷新列表
      this.showAddDialog = false;

      hilog.info(ClientManagementView.DOMAIN, ClientManagementView.TAG, 'Client added successfully');
    } catch (error) {
      hilog.error(ClientManagementView.DOMAIN, ClientManagementView.TAG, 'Failed to add client: %{public}s', error.message);
    }
  }

  /**
   * 保存编辑的客户
   */
  private async saveEditClient() {
    if (!this.editingClient) return;

    try {
      // 表单验证
      if (!this.editClientForm.clientName.trim()) {
        hilog.warn(ClientManagementView.DOMAIN, ClientManagementView.TAG, 'Client name is required');
        return;
      }

      // 更新客户信息
      const updates: Partial<ClientInfo> = {
        clientName: this.editClientForm.clientName.trim(),
        clientCode: this.editClientForm.clientCode.trim() || undefined,
        contactPerson: this.editClientForm.contactPerson.trim() || undefined,
        contactEmail: this.editClientForm.contactEmail.trim() || undefined,
        contactPhone: this.editClientForm.contactPhone.trim() || undefined,
        description: this.editClientForm.description.trim() || undefined,
        lastModified: new Date()
      };

      await this.viewModel.updateClient(this.editingClient.clientId, updates);
      await this.viewModel.loadClients(); // 刷新列表
      this.showEditDialog = false;
      this.editingClient = null;

      hilog.info(ClientManagementView.DOMAIN, ClientManagementView.TAG, 'Client updated successfully');
    } catch (error) {
      hilog.error(ClientManagementView.DOMAIN, ClientManagementView.TAG, 'Failed to update client: %{public}s', error.message);
    }
  }
}