using CryptoSystem.DeclassificationClient.Models;

namespace CryptoSystem.DeclassificationClient.Services
{
    /// <summary>
    /// 脱密服务接口
    /// </summary>
    public interface IDeclassificationService
    {
        /// <summary>
        /// 创建脱密任务
        /// </summary>
        /// <param name="task">脱密任务信息</param>
        /// <returns>创建的任务ID</returns>
        Task<string> CreateTaskAsync(DeclassificationTask task);

        /// <summary>
        /// 获取任务列表
        /// </summary>
        /// <param name="status">任务状态过滤</param>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <returns>任务列表</returns>
        Task<(List<DeclassificationTask> Tasks, int TotalCount)> GetTasksAsync(
            DeclassificationStatus? status = null, 
            int pageIndex = 0, 
            int pageSize = 20);

        /// <summary>
        /// 获取任务详情
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>任务详情</returns>
        Task<DeclassificationTask?> GetTaskAsync(string taskId);

        /// <summary>
        /// 更新任务状态
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="status">新状态</param>
        /// <param name="errorMessage">错误信息（可选）</param>
        /// <returns>是否更新成功</returns>
        Task<bool> UpdateTaskStatusAsync(string taskId, DeclassificationStatus status, string? errorMessage = null);

        /// <summary>
        /// 更新任务进度
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="progressPercentage">进度百分比</param>
        /// <returns>是否更新成功</returns>
        Task<bool> UpdateTaskProgressAsync(string taskId, int progressPercentage);

        /// <summary>
        /// 开始处理脱密任务
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="progress">进度回调</param>
        /// <returns>处理结果</returns>
        Task<bool> ProcessTaskAsync(string taskId, IProgress<int>? progress = null);

        /// <summary>
        /// 取消脱密任务
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>是否取消成功</returns>
        Task<bool> CancelTaskAsync(string taskId);

        /// <summary>
        /// 删除脱密任务
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>是否删除成功</returns>
        Task<bool> DeleteTaskAsync(string taskId);

        /// <summary>
        /// 添加文件到任务
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="filePaths">文件路径列表</param>
        /// <returns>添加的文件数量</returns>
        Task<int> AddFilesToTaskAsync(string taskId, List<string> filePaths);

        /// <summary>
        /// 从任务中移除文件
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="fileId">文件ID</param>
        /// <returns>是否移除成功</returns>
        Task<bool> RemoveFileFromTaskAsync(string taskId, string fileId);

        /// <summary>
        /// 生成安全外发包
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="packagePassword">包密码（可选）</param>
        /// <returns>安全包信息</returns>
        Task<SecurePackage?> GenerateSecurePackageAsync(string taskId, string? packagePassword = null);

        /// <summary>
        /// 获取安全包列表
        /// </summary>
        /// <param name="taskId">任务ID（可选）</param>
        /// <returns>安全包列表</returns>
        Task<List<SecurePackage>> GetSecurePackagesAsync(string? taskId = null);

        /// <summary>
        /// 下载安全包
        /// </summary>
        /// <param name="packageId">包ID</param>
        /// <param name="targetPath">目标路径</param>
        /// <returns>下载是否成功</returns>
        Task<bool> DownloadSecurePackageAsync(string packageId, string targetPath);

        /// <summary>
        /// 验证安全包
        /// </summary>
        /// <param name="packagePath">包文件路径</param>
        /// <param name="password">包密码</param>
        /// <returns>验证结果</returns>
        Task<bool> ValidateSecurePackageAsync(string packagePath, string? password = null);

        /// <summary>
        /// 获取任务统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        Task<Dictionary<string, object>> GetTaskStatisticsAsync();

        /// <summary>
        /// 批量处理任务
        /// </summary>
        /// <param name="taskIds">任务ID列表</param>
        /// <param name="progress">进度回调</param>
        /// <returns>处理结果</returns>
        Task<Dictionary<string, bool>> BatchProcessTasksAsync(List<string> taskIds, IProgress<int>? progress = null);
    }
} 