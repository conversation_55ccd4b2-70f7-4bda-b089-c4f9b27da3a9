import UIAbility from '@ohos.app.ability.UIAbility';
import hilog from '@ohos.hilog';
import window from '@ohos.window';

export default class EntryAbility extends UIAbility {
  onCreate(want, launchParam) {
    hilog.info(0x0000, 'SystemManager', '%{public}s', 'Ability onCreate');
  }

  onDestroy() {
    hilog.info(0x0000, 'SystemManager', '%{public}s', 'Ability onDestroy');
  }

  onWindowStageCreate(windowStage: window.WindowStage) {
    hilog.info(0x0000, 'SystemManager', '%{public}s', 'Ability onWindowStageCreate');

    windowStage.getMainWindow((err, data) => {
      if (err.code) {
        hilog.error(0x0000, 'SystemManager', 'Failed to obtain the main window. Error: %{public}s', 
          JSON.stringify(err) ?? '');
        return;
      }
      
      data.setWindowLayoutFullScreen(false);
      data.setWindowSize(1100, 800);
      data.setWindowTitle('系统管理器 v1.4.0');
    });

    windowStage.loadContent('pages/Index', (err, data) => {
      if (err.code) {
        hilog.error(0x0000, 'SystemManager', 'Failed to load the content. Error: %{public}s', 
          JSON.stringify(err) ?? '');
        return;
      }
      hilog.info(0x0000, 'SystemManager', 'Succeeded in loading the content. Data: %{public}s',
        JSON.stringify(data) ?? '');
    });
  }

  onWindowStageDestroy() {
    hilog.info(0x0000, 'SystemManager', '%{public}s', 'Ability onWindowStageDestroy');
  }

  onForeground() {
    hilog.info(0x0000, 'SystemManager', '%{public}s', 'Ability onForeground');
  }

  onBackground() {
    hilog.info(0x0000, 'SystemManager', '%{public}s', 'Ability onBackground');
  }
}
