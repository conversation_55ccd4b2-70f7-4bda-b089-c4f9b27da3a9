#ifndef CRYPTO_SECURE_PACKAGE_GENERATOR_H
#define CRYPTO_SECURE_PACKAGE_GENERATOR_H

#include <string>
#include <vector>
#include <functional>
#include <chrono>
#include <optional>
#include <map>

namespace crypto {
namespace secure_package {

/**
 * 外发包配置选项
 */
struct PackageOptions {
    // 密码保护
    std::optional<std::string> password;
    std::string passwordHint;
    
    // 权限控制
    bool allowPrinting = false;
    bool allowEditing = false;
    
    // 有效期设置 (从当前时间开始计算的秒数)
    std::optional<int64_t> expirySeconds;
    
    // 访问计数限制
    std::optional<int> maxAccessCount;
    
    // 设备绑定
    bool requireDeviceBinding = false;
    std::optional<std::string> deviceId;
    
    // 水印设置
    std::optional<std::string> watermarkText;
    
    // 其他元数据
    std::map<std::string, std::string> metadata;
};

/**
 * 进度回调函数类型
 * @param progress 进度百分比 (0-100)
 * @param message 当前操作描述
 */
using ProgressCallback = std::function<void(int progress, const std::string& message)>;

/**
 * 安全外发包生成器接口
 * 用于生成自解压的加密文件包
 */
class SecurePackageGenerator {
public:
    virtual ~SecurePackageGenerator() = default;
    
    /**
     * 添加文件到外发包
     * 
     * @param filePath 文件路径
     * @return 是否成功
     */
    virtual bool addFile(const std::string& filePath) = 0;
    
    /**
     * 添加内存数据到外发包作为文件
     * 
     * @param fileName 文件名
     * @param data 文件数据
     * @return 是否成功
     */
    virtual bool addData(const std::string& fileName, const std::vector<uint8_t>& data) = 0;
    
    /**
     * 设置外发包选项
     * 
     * @param options 选项配置
     */
    virtual void setOptions(const PackageOptions& options) = 0;
    
    /**
     * 生成自解压外发包
     * 
     * @param outputPath 输出路径
     * @param callback 进度回调
     * @return 是否成功
     */
    virtual bool generatePackage(const std::string& outputPath, ProgressCallback callback = nullptr) = 0;
    
    /**
     * 验证外发包的完整性
     * 
     * @param packagePath 外发包路径
     * @return 是否有效
     */
    virtual bool validatePackage(const std::string& packagePath) = 0;
};

} // namespace secure_package
} // namespace crypto

#endif // CRYPTO_SECURE_PACKAGE_GENERATOR_H 