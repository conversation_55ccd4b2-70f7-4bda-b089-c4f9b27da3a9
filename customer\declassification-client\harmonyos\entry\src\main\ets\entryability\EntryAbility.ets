import UIAbility from '@ohos.app.ability.UIAbility';
import hilog from '@ohos.hilog';
import window from '@ohos.window';

export default class EntryAbility extends UIAbility {
  onCreate(want, launchParam) {
    hilog.info(0x0000, 'DeclassificationClient', '%{public}s', 'Ability onCreate');
  }

  onDestroy() {
    hilog.info(0x0000, 'DeclassificationClient', '%{public}s', 'Ability onDestroy');
  }

  onWindowStageCreate(windowStage: window.WindowStage) {
    hilog.info(0x0000, 'DeclassificationClient', '%{public}s', 'Ability onWindowStageCreate');

    windowStage.getMainWindow((err, data) => {
      if (err.code) {
        hilog.error(0x0000, 'DeclassificationClient', 'Failed to obtain the main window. Error: %{public}s', 
          JSON.stringify(err) ?? '');
        return;
      }
      
      data.setWindowLayoutFullScreen(false);
      data.setWindowSize(1000, 700);
      data.setWindowTitle('脱密客户端 v1.4.0');
    });

    windowStage.loadContent('pages/Index', (err, data) => {
      if (err.code) {
        hilog.error(0x0000, 'DeclassificationClient', 'Failed to load the content. Error: %{public}s', 
          JSON.stringify(err) ?? '');
        return;
      }
      hilog.info(0x0000, 'DeclassificationClient', 'Succeeded in loading the content. Data: %{public}s',
        JSON.stringify(data) ?? '');
    });
  }

  onWindowStageDestroy() {
    hilog.info(0x0000, 'DeclassificationClient', '%{public}s', 'Ability onWindowStageDestroy');
  }

  onForeground() {
    hilog.info(0x0000, 'DeclassificationClient', '%{public}s', 'Ability onForeground');
  }

  onBackground() {
    hilog.info(0x0000, 'DeclassificationClient', '%{public}s', 'Ability onBackground');
  }
}
