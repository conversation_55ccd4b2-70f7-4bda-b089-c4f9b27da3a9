# 构建产物
build/
out/
bin/
obj/

# Visual Studio
.vs/
*.user
*.suo
*.sdf
*.opensdf
*.vcxproj.user
*.vcproj.user

# CMake
CMakeCache.txt
CMakeFiles/
cmake_install.cmake
Makefile
*.make

# Node.js (HarmonyOS)
node_modules/
package-lock.json
yarn.lock
.hvigor/

# 日志文件
*.log
*.tmp
*.temp

# 编译产物
*.exe
*.dll
*.so
*.dylib
*.a
*.lib
*.o
*.obj

# 平台特定
.DS_Store
Thumbs.db
*.swp
*.swo
*~

# IDE特定
.vscode/
.idea/
*.iml

# 测试产物
Testing/
test-results/
TestResults/

# 临时文件
*.bak
*.backup
*.orig 