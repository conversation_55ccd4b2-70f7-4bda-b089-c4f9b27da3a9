/*
 * policy.h
 * 
 * 策略管理头文件
 * 定义加密策略相关的数据结构和函数
 */

#pragma once

#include <fltKernel.h>

// 策略类型枚举
typedef enum _ENC_POLICY_TYPE {
    PolicyTypeEncrypt = 0,   // 加密策略
    PolicyTypeExempt,        // 豁免策略
    PolicyTypeMaximum        // 边界值
} ENC_POLICY_TYPE;

// 策略匹配类型枚举
typedef enum _ENC_POLICY_MATCH_TYPE {
    MatchTypeFileExtension = 0,    // 文件扩展名匹配
    MatchTypePath,                 // 路径匹配
    MatchTypeProcess,              // 进程匹配
    MatchTypeUser,                 // 用户匹配
    MatchTypeMaximum               // 边界值
} ENC_POLICY_MATCH_TYPE;

// 策略规则结构
typedef struct _ENC_POLICY_RULE {
    // 规则标识
    ULONG RuleId;
    
    // 策略类型
    ENC_POLICY_TYPE PolicyType;
    
    // 匹配类型
    ENC_POLICY_MATCH_TYPE MatchType;
    
    // 匹配内容
    UNICODE_STRING MatchData;
    
    // 规则优先级 (数值越小优先级越高)
    ULONG Priority;
    
    // 链表项
    LIST_ENTRY RuleListEntry;
    
} ENC_POLICY_RULE, *PENC_POLICY_RULE;

// 策略管理器结构
typedef struct _ENC_POLICY_MANAGER {
    // 规则计数
    ULONG RuleCount;
    
    // 规则链表头
    LIST_ENTRY RuleListHead;
    
    // 策略资源锁
    ERESOURCE PolicyLock;
    
    // 策略版本
    ULONG PolicyVersion;
    
    // 策略更新时间
    LARGE_INTEGER LastUpdateTime;
    
} ENC_POLICY_MANAGER, *PENC_POLICY_MANAGER;

// 管理函数
NTSTATUS
PolicyInitialize(
    VOID
    );

VOID
PolicyCleanup(
    VOID
    );

// 策略评估函数
BOOLEAN
PolicyShouldEncryptFile(
    _In_ PCUNICODE_STRING FileName,
    _In_opt_ PCUNICODE_STRING ProcessName,
    _In_opt_ PCUNICODE_STRING UserSid
    );

BOOLEAN
PolicyIsFileExempted(
    _In_ PCUNICODE_STRING FileName,
    _In_opt_ PCUNICODE_STRING ProcessName,
    _In_opt_ PCUNICODE_STRING UserSid
    );

// 规则管理函数
NTSTATUS
PolicyAddRule(
    _In_ PENC_POLICY_RULE Rule
    );

NTSTATUS
PolicyRemoveRule(
    _In_ ULONG RuleId
    );

NTSTATUS
PolicyClearAllRules(
    _In_ BOOLEAN ForceUnlock
    );

NTSTATUS
PolicyLoadFromRegistry(
    _In_ PUNICODE_STRING RegistryPath
    );

// 通信接口
NTSTATUS
PolicyHandleDeviceControl(
    _In_ PFLT_CALLBACK_DATA Data,
    _In_ PCFLT_RELATED_OBJECTS FltObjects
    ); 