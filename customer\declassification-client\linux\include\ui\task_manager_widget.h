#pragma once

#include <QWidget>
#include <vector>

#include "models/task_models.h"

// Forward declarations
class QTreeWidget;
class QTreeWidgetItem;
class QMenu;

namespace DeclassificationClient::UI {

class TaskManagerWidget : public QWidget {
    Q_OBJECT

public:
    explicit TaskManagerWidget(QWidget* parent = nullptr);
    ~TaskManagerWidget() override;

    void updateTasks(const std::vector<Models::DeclassificationTask>& tasks);
    bool isTaskSelected() const;

signals:
    void taskSelected(const QString& taskId);
    void taskDeletionRequested(const QString& taskId);

private slots:
    void showContextMenu(const QPoint& point);
    void processSelectedTask();
    void cancelSelectedTask();
    void deleteSelectedTask();
    void viewTaskDetails();
    void onTaskSelectionChanged(QTreeWidgetItem* current, QTreeWidgetItem* previous);

private:
    void setupUI();
    void setupConnections();
    void updateTaskItem(QTreeWidgetItem* item, const Models::DeclassificationTask& task);

    QTreeWidget* taskTreeWidget_;
    QMenu* contextMenu_;
};

} // namespace DeclassificationClient::UI 