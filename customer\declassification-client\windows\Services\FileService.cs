using System.Security.Cryptography;
using CryptoSystem.DeclassificationClient.Models;
using Microsoft.Extensions.Logging;

namespace CryptoSystem.DeclassificationClient.Services
{
    public class FileService : IFileService
    {
        private readonly ILogger<FileService> _logger;
        private readonly ICryptoService _cryptoService;
        private readonly Dictionary<string, FileType> _fileTypeMap;
        private readonly Dictionary<string, SecurityLevel> _extensionSecurityMap;

        public FileService(ILogger<FileService> logger, ICryptoService cryptoService)
        {
            _logger = logger;
            _cryptoService = cryptoService;
            
            // 初始化文件类型映射
            _fileTypeMap = new Dictionary<string, FileType>(StringComparer.OrdinalIgnoreCase)
            {
                // 文档类型
                { ".doc", FileType.Document }, { ".docx", FileType.Document },
                { ".pdf", FileType.Document }, { ".txt", FileType.Document },
                { ".rtf", FileType.Document }, { ".odt", FileType.Document },
                
                // 表格类型
                { ".xls", FileType.Spreadsheet }, { ".xlsx", FileType.Spreadsheet },
                { ".csv", FileType.Spreadsheet }, { ".ods", FileType.Spreadsheet },
                
                // 演示文稿
                { ".ppt", FileType.Presentation }, { ".pptx", FileType.Presentation },
                { ".odp", FileType.Presentation },
                
                // 图片类型
                { ".jpg", FileType.Image }, { ".jpeg", FileType.Image },
                { ".png", FileType.Image }, { ".gif", FileType.Image },
                { ".bmp", FileType.Image }, { ".tiff", FileType.Image },
                { ".svg", FileType.Image },
                
                // 音频类型
                { ".mp3", FileType.Audio }, { ".wav", FileType.Audio },
                { ".flac", FileType.Audio }, { ".aac", FileType.Audio },
                
                // 视频类型
                { ".mp4", FileType.Video }, { ".avi", FileType.Video },
                { ".mkv", FileType.Video }, { ".mov", FileType.Video },
                { ".wmv", FileType.Video },
                
                // 压缩文件
                { ".zip", FileType.Archive }, { ".rar", FileType.Archive },
                { ".7z", FileType.Archive }, { ".tar", FileType.Archive },
                { ".gz", FileType.Archive },
                
                // 可执行文件
                { ".exe", FileType.Executable }, { ".msi", FileType.Executable },
                { ".bat", FileType.Executable }, { ".cmd", FileType.Executable },
                { ".ps1", FileType.Executable }
            };

            // 初始化扩展名安全级别映射
            _extensionSecurityMap = new Dictionary<string, SecurityLevel>(StringComparer.OrdinalIgnoreCase)
            {
                // 高风险文件类型
                { ".exe", SecurityLevel.Confidential }, { ".msi", SecurityLevel.Confidential },
                { ".bat", SecurityLevel.Confidential }, { ".cmd", SecurityLevel.Confidential },
                { ".ps1", SecurityLevel.Confidential }, { ".scr", SecurityLevel.Confidential },
                
                // 中风险文件类型
                { ".doc", SecurityLevel.Internal }, { ".docx", SecurityLevel.Internal },
                { ".xls", SecurityLevel.Internal }, { ".xlsx", SecurityLevel.Internal },
                { ".ppt", SecurityLevel.Internal }, { ".pptx", SecurityLevel.Internal },
                { ".pdf", SecurityLevel.Internal },
                
                // 低风险文件类型
                { ".txt", SecurityLevel.Public }, { ".csv", SecurityLevel.Public },
                { ".jpg", SecurityLevel.Public }, { ".png", SecurityLevel.Public },
                { ".gif", SecurityLevel.Public }
            };
        }

        public async Task<DeclassificationFile?> AnalyzeFileAsync(string filePath)
        {
            try
            {
                _logger.LogInformation("分析文件: {FilePath}", filePath);
                
                if (!File.Exists(filePath))
                {
                    _logger.LogWarning("文件不存在: {FilePath}", filePath);
                    return null;
                }

                var fileInfo = new FileInfo(filePath);
                var extension = fileInfo.Extension.ToLowerInvariant();
                
                var declassificationFile = new DeclassificationFile
                {
                    FileId = Guid.NewGuid().ToString(),
                    FileName = fileInfo.Name,
                    FilePath = filePath,
                    FileSize = fileInfo.Length,
                    FileType = DetectFileType(filePath),
                    Extension = extension,
                    CreatedTime = fileInfo.CreationTime,
                    ModifiedTime = fileInfo.LastWriteTime,
                    MD5Hash = await CalculateFileMD5Async(filePath),
                    IsEncrypted = await IsFileEncryptedAsync(filePath),
                    SecurityLevel = await GetFileSecurityLevelAsync(filePath)
                };

                // 分析文件内容（如果是文本文件）
                if (IsTextFile(extension))
                {
                    declassificationFile.ContentPreview = await GetFileContentPreviewAsync(filePath, 500);
                }

                // 检查文件完整性
                declassificationFile.IntegrityStatus = await ValidateFileIntegrityAsync(filePath, declassificationFile.MD5Hash) 
                    ? "完整" : "损坏";

                _logger.LogInformation("文件分析完成: {FileName}, 类型: {FileType}, 大小: {FileSize}", 
                    declassificationFile.FileName, declassificationFile.FileType, FormatFileSize(declassificationFile.FileSize));

                return declassificationFile;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "分析文件失败: {FilePath}", filePath);
                return null;
            }
        }

        public async Task<List<DeclassificationFile>> AnalyzeFilesAsync(List<string> filePaths, IProgress<int>? progress = null)
        {
            var results = new List<DeclassificationFile>();
            var totalFiles = filePaths.Count;
            var processedFiles = 0;

            _logger.LogInformation("开始批量分析 {Count} 个文件", totalFiles);

            foreach (var filePath in filePaths)
            {
                try
                {
                    var file = await AnalyzeFileAsync(filePath);
                    if (file != null)
                    {
                        results.Add(file);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "分析文件失败: {FilePath}", filePath);
                }

                processedFiles++;
                var progressPercentage = (int)((double)processedFiles / totalFiles * 100);
                progress?.Report(progressPercentage);
            }

            _logger.LogInformation("批量文件分析完成，成功分析 {SuccessCount}/{TotalCount} 个文件", results.Count, totalFiles);
            return results;
        }

        public async Task<string> CalculateFileMD5Async(string filePath)
        {
            try
            {
                return await _cryptoService.CalculateFileHashAsync(filePath, "MD5");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算文件MD5失败: {FilePath}", filePath);
                return string.Empty;
            }
        }

        public FileType DetectFileType(string filePath)
        {
            var extension = Path.GetExtension(filePath).ToLowerInvariant();
            
            if (_fileTypeMap.TryGetValue(extension, out var fileType))
            {
                return fileType;
            }

            // 尝试通过文件内容检测
            try
            {
                using var stream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
                var buffer = new byte[16];
                stream.Read(buffer, 0, buffer.Length);

                // 检查常见文件头
                if (IsPdfFile(buffer)) return FileType.Document;
                if (IsOfficeFile(buffer)) return FileType.Document;
                if (IsImageFile(buffer)) return FileType.Image;
                if (IsArchiveFile(buffer)) return FileType.Archive;
                if (IsExecutableFile(buffer)) return FileType.Executable;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "通过文件头检测文件类型失败: {FilePath}", filePath);
            }

            return FileType.Other;
        }

        public async Task<bool> IsFileEncryptedAsync(string filePath)
        {
            return await _cryptoService.IsFileEncryptedAsync(filePath);
        }

        public async Task<SecurityLevel> GetFileSecurityLevelAsync(string filePath)
        {
            try
            {
                var extension = Path.GetExtension(filePath).ToLowerInvariant();
                
                // 首先检查扩展名映射
                if (_extensionSecurityMap.TryGetValue(extension, out var securityLevel))
                {
                    return securityLevel;
                }

                // 检查是否为加密文件
                if (await IsFileEncryptedAsync(filePath))
                {
                    return SecurityLevel.Confidential;
                }

                // 检查文件大小（大文件可能包含敏感信息）
                var fileSize = GetFileSize(filePath);
                if (fileSize > 100 * 1024 * 1024) // 100MB
                {
                    return SecurityLevel.Internal;
                }

                // 文本文件内容分析
                if (IsTextFile(extension))
                {
                    var content = await GetFileContentPreviewAsync(filePath, 1000);
                    if (ContainsSensitiveKeywords(content))
                    {
                        return SecurityLevel.Confidential;
                    }
                }

                return SecurityLevel.Public;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "评估文件安全级别失败: {FilePath}", filePath);
                return SecurityLevel.Internal; // 默认为内部级别
            }
        }

        public async Task<bool> CopyFileAsync(string sourcePath, string targetPath, bool overwrite = false)
        {
            try
            {
                _logger.LogInformation("复制文件: {Source} -> {Target}", sourcePath, targetPath);
                
                if (!File.Exists(sourcePath))
                {
                    _logger.LogError("源文件不存在: {SourcePath}", sourcePath);
                    return false;
                }

                if (File.Exists(targetPath) && !overwrite)
                {
                    _logger.LogError("目标文件已存在且不允许覆盖: {TargetPath}", targetPath);
                    return false;
                }

                // 确保目标目录存在
                var targetDir = Path.GetDirectoryName(targetPath);
                if (!string.IsNullOrEmpty(targetDir))
                {
                    Directory.CreateDirectory(targetDir);
                }

                // 异步复制文件
                using var sourceStream = new FileStream(sourcePath, FileMode.Open, FileAccess.Read);
                using var targetStream = new FileStream(targetPath, FileMode.Create, FileAccess.Write);
                await sourceStream.CopyToAsync(targetStream);

                _logger.LogInformation("文件复制成功: {TargetPath}", targetPath);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "复制文件失败: {Source} -> {Target}", sourcePath, targetPath);
                return false;
            }
        }

        public async Task<bool> MoveFileAsync(string sourcePath, string targetPath, bool overwrite = false)
        {
            try
            {
                if (await CopyFileAsync(sourcePath, targetPath, overwrite))
                {
                    await DeleteFileAsync(sourcePath);
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "移动文件失败: {Source} -> {Target}", sourcePath, targetPath);
                return false;
            }
        }

        public async Task<bool> DeleteFileAsync(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    return true; // 文件不存在，视为删除成功
                }

                // 使用安全删除
                return await _cryptoService.SecureDeleteFileAsync(filePath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除文件失败: {FilePath}", filePath);
                return false;
            }
        }

        public async Task<bool> CreateDirectoryAsync(string directoryPath)
        {
            try
            {
                if (Directory.Exists(directoryPath))
                {
                    return true; // 目录已存在
                }

                Directory.CreateDirectory(directoryPath);
                _logger.LogInformation("创建目录成功: {DirectoryPath}", directoryPath);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建目录失败: {DirectoryPath}", directoryPath);
                return false;
            }
        }

        public string GetTempFilePath(string extension = ".tmp")
        {
            var tempDir = Path.GetTempPath();
            var fileName = $"crypto_temp_{Guid.NewGuid()}{extension}";
            return Path.Combine(tempDir, fileName);
        }

        public string GetTempDirectoryPath()
        {
            var tempDir = Path.GetTempPath();
            var dirName = $"crypto_temp_{Guid.NewGuid()}";
            var fullPath = Path.Combine(tempDir, dirName);
            Directory.CreateDirectory(fullPath);
            return fullPath;
        }

        public async Task<int> CleanupTempFilesAsync(int olderThanHours = 24)
        {
            try
            {
                var tempDir = Path.GetTempPath();
                var cutoffTime = DateTime.Now.AddHours(-olderThanHours);
                var deletedCount = 0;

                // 清理临时文件
                var tempFiles = Directory.GetFiles(tempDir, "crypto_temp_*", SearchOption.TopDirectoryOnly);
                foreach (var file in tempFiles)
                {
                    try
                    {
                        var fileInfo = new FileInfo(file);
                        if (fileInfo.CreationTime < cutoffTime)
                        {
                            await DeleteFileAsync(file);
                            deletedCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "清理临时文件失败: {FilePath}", file);
                    }
                }

                // 清理临时目录
                var tempDirs = Directory.GetDirectories(tempDir, "crypto_temp_*", SearchOption.TopDirectoryOnly);
                foreach (var dir in tempDirs)
                {
                    try
                    {
                        var dirInfo = new DirectoryInfo(dir);
                        if (dirInfo.CreationTime < cutoffTime)
                        {
                            Directory.Delete(dir, true);
                            deletedCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "清理临时目录失败: {DirectoryPath}", dir);
                    }
                }

                _logger.LogInformation("清理临时文件完成，删除 {Count} 个项目", deletedCount);
                return deletedCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理临时文件失败");
                return 0;
            }
        }

        public async Task<bool> ValidateFileIntegrityAsync(string filePath, string expectedMD5)
        {
            try
            {
                var actualMD5 = await CalculateFileMD5Async(filePath);
                return string.Equals(actualMD5, expectedMD5, StringComparison.OrdinalIgnoreCase);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证文件完整性失败: {FilePath}", filePath);
                return false;
            }
        }

        public long GetFileSize(string filePath)
        {
            try
            {
                return new FileInfo(filePath).Length;
            }
            catch
            {
                return 0;
            }
        }

        public string FormatFileSize(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
            int counter = 0;
            decimal number = bytes;
            
            while (Math.Round(number / 1024) >= 1)
            {
                number /= 1024;
                counter++;
            }
            
            return $"{number:n1} {suffixes[counter]}";
        }

        public bool FileExists(string filePath) => File.Exists(filePath);

        public bool DirectoryExists(string directoryPath) => Directory.Exists(directoryPath);

        public string GetFileExtension(string filePath) => Path.GetExtension(filePath);

        public string GetFileNameWithoutExtension(string filePath) => Path.GetFileNameWithoutExtension(filePath);

        public string GetFileName(string filePath) => Path.GetFileName(filePath);

        public string GetDirectoryPath(string filePath) => Path.GetDirectoryName(filePath) ?? string.Empty;

        // 私有辅助方法
        private bool IsTextFile(string extension)
        {
            var textExtensions = new[] { ".txt", ".csv", ".xml", ".json", ".log", ".ini", ".cfg", ".conf" };
            return Array.Exists(textExtensions, ext => ext.Equals(extension, StringComparison.OrdinalIgnoreCase));
        }

        private async Task<string> GetFileContentPreviewAsync(string filePath, int maxLength)
        {
            try
            {
                using var reader = new StreamReader(filePath, Encoding.UTF8);
                var buffer = new char[maxLength];
                var charsRead = await reader.ReadAsync(buffer, 0, maxLength);
                return new string(buffer, 0, charsRead);
            }
            catch
            {
                return string.Empty;
            }
        }

        private bool ContainsSensitiveKeywords(string content)
        {
            if (string.IsNullOrEmpty(content)) return false;

            var sensitiveKeywords = new[]
            {
                "密码", "password", "secret", "confidential", "机密",
                "内部", "private", "token", "key", "证书", "certificate"
            };

            return sensitiveKeywords.Any(keyword => 
                content.Contains(keyword, StringComparison.OrdinalIgnoreCase));
        }

        private bool IsPdfFile(byte[] header)
        {
            return header.Length >= 4 && 
                   header[0] == 0x25 && header[1] == 0x50 && 
                   header[2] == 0x44 && header[3] == 0x46; // %PDF
        }

        private bool IsOfficeFile(byte[] header)
        {
            return header.Length >= 8 && 
                   header[0] == 0x50 && header[1] == 0x4B && 
                   header[2] == 0x03 && header[3] == 0x04; // ZIP header (Office 2007+)
        }

        private bool IsImageFile(byte[] header)
        {
            if (header.Length < 4) return false;
            
            // JPEG
            if (header[0] == 0xFF && header[1] == 0xD8) return true;
            // PNG
            if (header[0] == 0x89 && header[1] == 0x50 && header[2] == 0x4E && header[3] == 0x47) return true;
            // GIF
            if (header[0] == 0x47 && header[1] == 0x49 && header[2] == 0x46) return true;
            // BMP
            if (header[0] == 0x42 && header[1] == 0x4D) return true;
            
            return false;
        }

        private bool IsArchiveFile(byte[] header)
        {
            if (header.Length < 4) return false;
            
            // ZIP
            if (header[0] == 0x50 && header[1] == 0x4B) return true;
            // RAR
            if (header[0] == 0x52 && header[1] == 0x61 && header[2] == 0x72) return true;
            // 7Z
            if (header[0] == 0x37 && header[1] == 0x7A) return true;
            
            return false;
        }

        private bool IsExecutableFile(byte[] header)
        {
            return header.Length >= 2 && header[0] == 0x4D && header[1] == 0x5A; // MZ header
        }
    }
}