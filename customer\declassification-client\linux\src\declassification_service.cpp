#include "declassification_service.h"
#include <algorithm>
#include <stdexcept>
#include <chrono>

namespace DeclassificationClient {

DeclassificationService::DeclassificationService() {
    loadInitialData();
}

DeclassificationService::~DeclassificationService() = default;

bool DeclassificationService::initialize() {
    // In a real application, this would connect to a backend, etc.
    return true;
}

void DeclassificationService::shutdown() {
    // Clean up resources
}

std::vector<Models::DeclassificationTask> DeclassificationService::getTasks() const {
    return tasks_;
}

std::optional<Models::DeclassificationTask> DeclassificationService::getTask(const std::string& taskId) const {
    auto it = std::find_if(tasks_.begin(), tasks_.end(), [&](const auto& task) {
        return task.taskId == taskId;
    });

    if (it != tasks_.end()) {
        return *it;
    }
    return std::nullopt;
}

bool DeclassificationService::createTask(const Models::DeclassificationTask& task) {
    tasks_.push_back(task);
    return true;
}

bool DeclassificationService::updateTask(const Models::DeclassificationTask& task) {
    auto it = std::find_if(tasks_.begin(), tasks_.end(), [&](const auto& t) {
        return t.taskId == task.taskId;
    });

    if (it != tasks_.end()) {
        *it = task;
        return true;
    }
    return false;
}

bool DeclassificationService::deleteTask(const std::string& taskId) {
    auto it = std::remove_if(tasks_.begin(), tasks_.end(), [&](const auto& task) {
        return task.taskId == taskId;
    });

    if (it != tasks_.end()) {
        tasks_.erase(it, tasks_.end());
    return true;
    }
        return false;
    }
    
void DeclassificationService::loadInitialData() {
    tasks_.clear();
    long long now = std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now().time_since_epoch()).count();
    tasks_.push_back({ "task-001", "Annual Report Q4", Models::TaskStatus::Pending, 0, now, {} });
    tasks_.push_back({ "task-002", "Project Phoenix Specs", Models::TaskStatus::Processing, 50, now - 3600, {} });
    tasks_.push_back({ "task-003", "Marketing Budget", Models::TaskStatus::Completed, 100, now - 86400, {} });
}

} // namespace DeclassificationClient