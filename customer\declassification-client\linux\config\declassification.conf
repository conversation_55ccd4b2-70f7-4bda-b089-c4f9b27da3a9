# CryptoSystem Declassification Client Configuration
# 脱密客户端配置文件

[general]
# 应用程序基本配置
app_name = CryptoSystem Declassification Client
app_version = 1.0.0
log_level = INFO
log_max_size = 10MB
log_retention_days = 30

[server]
# 服务器连接配置
server_host = localhost
server_port = 8443
use_tls = true
verify_certificate = true
connection_timeout = 30
heartbeat_interval = 60

[security]
# 安全配置
encryption_algorithm = SM4-GCM
key_size = 256
max_file_size = 100MB
password_min_length = 8
session_timeout = 3600

[processing]
# 处理配置
max_concurrent_tasks = 5
max_concurrent_files = 10
temp_directory = /tmp/cryptosystem
output_directory = ~/Documents/DeclassificationOutput
enable_watermark = true
watermark_text = {user_name} - {timestamp}

[ui]
# 界面配置
theme = dark
language = zh_CN
window_width = 1024
window_height = 768
auto_refresh_interval = 30
show_system_tray = true
minimize_to_tray = true

[audit]
# 审计配置
enable_audit = true
audit_level = DETAILED
audit_file = /var/log/cryptosystem/declassification.log
audit_max_size = 50MB
audit_retention_days = 90

[package]
# 安全包配置
default_compression = true
default_encryption = true
default_password_protection = false
max_access_count = 10
default_expiration_days = 30
package_format = zip

[network]
# 网络配置
proxy_enabled = false
proxy_host = 
proxy_port = 
proxy_username = 
proxy_password = 
connect_timeout = 30
read_timeout = 60

[performance]
# 性能配置
memory_limit = 1024MB
cpu_limit = 80%
io_priority = normal
background_processing = true
progress_update_interval = 1000

[notifications]
# 通知配置
enable_notifications = true
show_completion_notification = true
show_error_notification = true
notification_timeout = 5000
sound_enabled = false 