﻿#include "PolicyManager.h"
#include "KeyManager.h"

#include <iostream>
#include <fstream>
#include <algorithm>
#include <regex>
#include <sstream>
#include <chrono>
#include <iomanip>
#include <windows.h>
#include <filesystem>
#include <setupapi.h>
#include <devguid.h>
#include <cfgmgr32.h>

#pragma comment(lib, "setupapi.lib")

namespace fs = std::filesystem;

// 临时日志宏，后期需要替换为正式日志系统
#define LOG_POLICY_INFO(msg) std::cout << "[POLICY_MANAGER] [INFO] " << msg << std::endl
#define LOG_POLICY_ERROR(msg) std::cerr << "[POLICY_MANAGER] [ERROR] " << msg << std::endl
#define LOG_POLICY_DEBUG(msg) std::cout << "[POLICY_MANAGER] [DEBUG] " << msg << std::endl

// 临时配置常量，后期需要从配置文件读取
const std::string POLICY_STORAGE_PATH = ".\\policies.json";
const std::string DEFAULT_ALGORITHM = "SM4-GCM";
const int POLICY_SYNC_INTERVAL_MS = 300000; // 5分钟

// 获取当前时间戳（ISO 8601格式）
std::string getCurrentTimestamp() {
    auto now = std::chrono::system_clock::now();
    auto time_t_now = std::chrono::system_clock::to_time_t(now);
    
    std::tm tm_now = {};
    localtime_s(&tm_now, &time_t_now); // 使用安全版本
    
    std::stringstream ss;
    ss << std::put_time(&tm_now, "%Y-%m-%dT%H:%M:%S");
    return ss.str();
}

// 获取运行中的进程路径
std::string getRunningProcessPath(DWORD processId = 0) {
    if (processId == 0) {
        processId = GetCurrentProcessId();
    }
    
    HANDLE processHandle = OpenProcess(PROCESS_QUERY_INFORMATION | PROCESS_VM_READ, FALSE, processId);
    if (processHandle == nullptr) {
        return "";
    }
    
    char pathBuffer[MAX_PATH];
    DWORD bufferSize = MAX_PATH;
    
    BOOL result = QueryFullProcessImageNameA(processHandle, 0, pathBuffer, &bufferSize);
    CloseHandle(processHandle);
    
    if (!result) {
        return "";
    }
    
    return std::string(pathBuffer);
}

// 从wstring转换为string
std::string wstringToString(const std::wstring& wstr) {
    if (wstr.empty()) {
        return "";
    }
    
    int size_needed = WideCharToMultiByte(CP_UTF8, 0, wstr.c_str(), -1, nullptr, 0, nullptr, nullptr);
    std::string strTo(size_needed, 0);
    WideCharToMultiByte(CP_UTF8, 0, wstr.c_str(), -1, &strTo[0], size_needed, nullptr, nullptr);
    strTo.resize(size_needed - 1);  // 移除结尾的null终止符
    
    return strTo;
}

// 单例实现
PolicyManager& PolicyManager::getInstance() {
    static PolicyManager instance;
    return instance;
}

PolicyManager::PolicyManager() : m_initialized(false), m_auditCallback(nullptr) {
    LOG_POLICY_INFO("PolicyManager 构造");
}

PolicyManager::~PolicyManager() {
    if (m_initialized) {
        shutdown();
    }
    LOG_POLICY_INFO("PolicyManager 析构");
}

bool PolicyManager::initialize() {
    if (m_initialized) {
        LOG_POLICY_INFO("PolicyManager 已经初始化");
        return true;
    }
    
    // 加载策略
    if (!loadPoliciesFromStorage()) {
        LOG_POLICY_ERROR("加载策略失败，使用空缓存启动");
        // 继续执行，不返回失败，因为可能是首次运行
    }
    
    // TODO: 设置定期同步策略的定时器
    
    m_initialized = true;
    LOG_POLICY_INFO("PolicyManager 初始化完成");
    return true;
}

void PolicyManager::shutdown() {
    if (!m_initialized) {
        return;
    }
    
    // 保存策略
    if (!savePolicies()) {
        LOG_POLICY_ERROR("保存策略失败");
    }
    
    m_initialized = false;
    LOG_POLICY_INFO("PolicyManager 已关闭");
}

bool PolicyManager::loadPolicies() {
    return loadPoliciesFromStorage();
}

bool PolicyManager::savePolicies() {
    bool success = true;
    std::lock_guard<std::mutex> lock(m_policiesMutex);
    
    for (const auto& pair : m_policies) {
        if (!savePolicyToStorage(*pair.second)) {
            LOG_POLICY_ERROR("保存策略失败: " << pair.first);
            success = false;
        }
    }
    
    return success;
}

bool PolicyManager::syncPoliciesFromServer() {
    // TODO: 实现与服务器的实际同步
    // 临时：返回成功
    LOG_POLICY_INFO("同步策略从服务器 (模拟)");
    return true;
}

bool PolicyManager::addPolicy(const Policy& policy) {
    if (!m_initialized) {
        LOG_POLICY_ERROR("PolicyManager未初始化");
        return false;
    }
    
    std::lock_guard<std::mutex> lock(m_policiesMutex);
    
    // 检查是否已存在
    if (m_policies.find(policy.id) != m_policies.end()) {
        LOG_POLICY_ERROR("策略已存在: " << policy.id);
        return false;
    }
    
    // 添加策略
    auto policyPtr = std::make_shared<Policy>(policy);
    m_policies[policy.id] = policyPtr;
    
    // 保存策略
    if (!savePolicyToStorage(policy)) {
        LOG_POLICY_ERROR("保存策略失败: " << policy.id);
        m_policies.erase(policy.id);
        return false;
    }
    
    // 记录审计
    logPolicyEvent(policy.id, policy.createdBy, "CREATE", "", "success");
    
    LOG_POLICY_INFO("添加策略: " << policy.id << " - " << policy.name);
    return true;
}

bool PolicyManager::updatePolicy(const Policy& policy) {
    if (!m_initialized) {
        LOG_POLICY_ERROR("PolicyManager未初始化");
        return false;
    }
    
    std::lock_guard<std::mutex> lock(m_policiesMutex);
    
    // 检查是否存在
    auto it = m_policies.find(policy.id);
    if (it == m_policies.end()) {
        LOG_POLICY_ERROR("策略不存在: " << policy.id);
        return false;
    }
    
    // 更新策略
    *(it->second) = policy;
    
    // 保存策略
    if (!savePolicyToStorage(policy)) {
        LOG_POLICY_ERROR("保存策略失败: " << policy.id);
        return false;
    }
    
    // 记录审计
    logPolicyEvent(policy.id, "", "UPDATE", "", "success");
    
    LOG_POLICY_INFO("更新策略: " << policy.id << " - " << policy.name);
    return true;
}

bool PolicyManager::removePolicy(const std::string& policyId) {
    if (!m_initialized) {
        LOG_POLICY_ERROR("PolicyManager未初始化");
        return false;
    }
    
    std::lock_guard<std::mutex> lock(m_policiesMutex);
    
    // 检查是否存在
    auto it = m_policies.find(policyId);
    if (it == m_policies.end()) {
        LOG_POLICY_ERROR("策略不存在: " << policyId);
        return false;
    }
    
    // 记录审计
    logPolicyEvent(policyId, "", "DELETE", "", "success");
    
    // 删除策略
    m_policies.erase(it);
    
    // TODO: 从存储中删除策略
    
    LOG_POLICY_INFO("删除策略: " << policyId);
    return true;
}

bool PolicyManager::enablePolicy(const std::string& policyId, bool enable) {
    if (!m_initialized) {
        LOG_POLICY_ERROR("PolicyManager未初始化");
        return false;
    }
    
    std::lock_guard<std::mutex> lock(m_policiesMutex);
    
    // 检查是否存在
    auto it = m_policies.find(policyId);
    if (it == m_policies.end()) {
        LOG_POLICY_ERROR("策略不存在: " << policyId);
        return false;
    }
    
    // 更新状态
    it->second->enabled = enable;
    
    // 保存策略
    if (!savePolicyToStorage(*(it->second))) {
        LOG_POLICY_ERROR("保存策略失败: " << policyId);
        return false;
    }
    
    // 记录审计
    logPolicyEvent(policyId, "", enable ? "ENABLE" : "DISABLE", "", "success");
    
    LOG_POLICY_INFO((enable ? "启用" : "禁用") << "策略: " << policyId);
    return true;
}

PolicyEvaluationResult PolicyManager::evaluateFilePolicy(const std::wstring& filePath, 
                                                        const std::string& userId,
                                                        const std::string& appPath) {
    PolicyEvaluationResult result;
    
    if (!m_initialized) {
        LOG_POLICY_ERROR("PolicyManager未初始化");
        return result;
    }
    
    // 获取适用的规则
    std::vector<PolicyRule> applicableRules = getApplicableRules(filePath, userId, appPath);
    
    if (applicableRules.empty()) {
        LOG_POLICY_INFO("没有适用于文件的策略规则: " << wstringToString(filePath));
        return result;
    }
    
    // 选择优先级最高的规则
    PolicyRule selectedRule = selectHighestPriorityRule(applicableRules);
    
    // 设置结果
    result.appliedRuleId = selectedRule.id;
    std::string appliedPolicyId;
    
    {
        std::lock_guard<std::mutex> lock(m_policiesMutex);
        for (const auto& pair : m_policies) {
            for (const auto& rule : pair.second->rules) {
                if (rule.id == selectedRule.id) {
                    appliedPolicyId = pair.second->id;
                    result.appliedPolicyId = appliedPolicyId;
                    break;
                }
            }
            if (!appliedPolicyId.empty()) {
                break;
            }
        }
    }
    
    // 根据规则类型设置结果
    switch (selectedRule.action) {
        case PolicyAction::ENCRYPT:
            result.shouldEncrypt = true;
            result.encryptionAlgorithm = selectedRule.algorithm.empty() ? DEFAULT_ALGORITHM : selectedRule.algorithm;
            break;
        case PolicyAction::DECRYPT:
            result.shouldDecrypt = true;
            break;
        case PolicyAction::DENY:
            result.shouldDeny = true;
            break;
        case PolicyAction::ALLOW:
            // 仅允许，不执行特定操作
            break;
        case PolicyAction::AUDIT:
            result.shouldAudit = true;
            break;
        case PolicyAction::NOTIFY:
            result.shouldNotify = true;
            break;
    }
    
    // 复制额外参数
    result.parameters = selectedRule.parameters;
    
    LOG_POLICY_INFO("策略评估结果: " << wstringToString(filePath) << 
                   " -> 规则: " << selectedRule.id << 
                   " (策略: " << result.appliedPolicyId << ")");
    
    return result;
}

void PolicyManager::logPolicyEvent(const std::string& policyId, 
                                   const std::string& userId, 
                                   const std::string& action,
                                   const std::string& resourceId,
                                   const std::string& result,
                                   const std::string& details) {
    // 创建审计事件
    AuditEvent event;
    event.type = AuditEventType::POLICY_APPLIED;
    event.userId = userId;
    event.resourceId = resourceId.empty() ? policyId : resourceId;
    event.resourceType = resourceId.empty() ? "policy" : "file";
    event.timestamp = getCurrentTimestamp();
    event.result = result;
    
    // 构造详情JSON
    std::ostringstream detailsJson;
    detailsJson << "{";
    detailsJson << "\"policyId\":\"" << policyId << "\",";
    detailsJson << "\"action\":\"" << action << "\"";
    
    if (!details.empty()) {
        // 检查details是否已经是JSON格式
        if (details[0] == '{' && details[details.size()-1] == '}') {
            // 移除大括号
            std::string innerDetails = details.substr(1, details.size() - 2);
            detailsJson << "," << innerDetails;
        } else {
            detailsJson << ",\"details\":\"" << details << "\"";
        }
    }
    
    detailsJson << "}";
    event.details = detailsJson.str();
    
    // 使用回调函数发送审计事件
    if (m_auditCallback) {
        m_auditCallback(event);
    }
    
    LOG_POLICY_INFO("记录策略事件: " << policyId << " - " << action);
}

std::shared_ptr<Policy> PolicyManager::getPolicy(const std::string& policyId) {
    if (!m_initialized) {
        LOG_POLICY_ERROR("PolicyManager未初始化");
        return nullptr;
    }
    
    std::lock_guard<std::mutex> lock(m_policiesMutex);
    
    auto it = m_policies.find(policyId);
    if (it == m_policies.end()) {
        return nullptr;
    }
    
    return it->second;
}

std::vector<std::shared_ptr<Policy>> PolicyManager::getAllPolicies() {
    std::vector<std::shared_ptr<Policy>> results;
    
    if (!m_initialized) {
        LOG_POLICY_ERROR("PolicyManager未初始化");
        return results;
    }
    
    std::lock_guard<std::mutex> lock(m_policiesMutex);
    
    for (const auto& pair : m_policies) {
        if (pair.second->enabled) {
            results.push_back(pair.second);
        }
    }
    
    return results;
}

std::vector<std::shared_ptr<Policy>> PolicyManager::getPoliciesByType(PolicyType type) {
    std::vector<std::shared_ptr<Policy>> results;
    
    if (!m_initialized) {
        LOG_POLICY_ERROR("PolicyManager未初始化");
        return results;
    }
    
    std::lock_guard<std::mutex> lock(m_policiesMutex);
    
    for (const auto& pair : m_policies) {
        if (!pair.second->enabled) {
            continue;
        }
        
        // 检查策略是否包含指定类型的规则
        bool hasMatchingRule = false;
        for (const auto& rule : pair.second->rules) {
            if (rule.type == type) {
                hasMatchingRule = true;
                break;
            }
        }
        
        if (hasMatchingRule) {
            results.push_back(pair.second);
        }
    }
    
    return results;
}

void PolicyManager::setAuditEventCallback(AuditEventCallback callback) {
    m_auditCallback = callback;
}

bool PolicyManager::loadPoliciesFromStorage() {
    // 临时实现：检查文件是否存在
    if (!fs::exists(POLICY_STORAGE_PATH)) {
        // 创建示例策略以便测试
        createSamplePolicies();
        return true;
    }
    
    // TODO: 实现从存储加载策略
    // 临时实现：加载示例策略
    createSamplePolicies();
    
    LOG_POLICY_INFO("加载策略 (模拟)");
    return true;
}

bool PolicyManager::savePolicyToStorage(const Policy& policy) {
    // TODO: 实现将策略保存到存储
    // 临时实现：返回成功
    LOG_POLICY_INFO("保存策略: " << policy.id << " (模拟)");
    return true;
}

std::vector<PolicyRule> PolicyManager::getApplicableRules(const std::wstring& filePath, 
                                                         const std::string& userId,
                                                         const std::string& appPath) {
    std::vector<PolicyRule> applicableRules;
    
    std::string actualAppPath = appPath.empty() ? getRunningProcessPath() : appPath;
    std::string filePathStr = wstringToString(filePath);
    
    std::lock_guard<std::mutex> lock(m_policiesMutex);
    
    for (const auto& policyPair : m_policies) {
        const auto& policy = policyPair.second;
        
        // 跳过禁用的策略
        if (!policy->enabled) {
            continue;
        }
        
        for (const auto& rule : policy->rules) {
            bool matches = false;
            
            switch (rule.type) {
                case PolicyType::FILE_PATH:
                    // 对文件路径应用通配符匹配
                    matches = matchesPattern(filePath, rule.pattern);
                    break;
                    
                case PolicyType::FILE_TYPE:
                    // 检查文件扩展名
                    {
                        std::wstring extension = fs::path(filePath).extension().wstring();
                        // 转换为小写进行比较
                        std::transform(extension.begin(), extension.end(), extension.begin(), ::towlower);
                        std::string extensionStr = wstringToString(extension);
                        
                        std::string patternLower = rule.pattern;
                        std::transform(patternLower.begin(), patternLower.end(), patternLower.begin(), ::tolower);
                        
                        matches = (extensionStr == patternLower);
                    }
                    break;
                    
                case PolicyType::APPLICATION:
                    // 检查应用路径
                    matches = (actualAppPath.find(rule.pattern) != std::string::npos);
                    break;
                    
                case PolicyType::USER:
                    // 检查用户ID
                    matches = (userId == rule.pattern);
                    break;
                    
                // 其他类型暂未实现
                default:
                    break;
            }
            
            if (matches) {
                applicableRules.push_back(rule);
            }
        }
    }
    
    return applicableRules;
}

PolicyRule PolicyManager::selectHighestPriorityRule(const std::vector<PolicyRule>& rules) {
    if (rules.empty()) {
        // 返回一个空规则
        return PolicyRule();
    }
    
    // 找出优先级最高的规则
    return *std::max_element(rules.begin(), rules.end(), 
        [](const PolicyRule& a, const PolicyRule& b) {
            return a.priority < b.priority;
        });
}

bool PolicyManager::matchesPattern(const std::wstring& filePath, const std::string& pattern) {
    // 将Windows路径转换为字符串
    std::string filePathStr = wstringToString(filePath);
    
    // 简单的通配符匹配
    // 将通配符模式转换为正则表达式
    std::string regexPattern = pattern;
    
    // 替换通配符为正则表达式
    size_t pos = 0;
    while ((pos = regexPattern.find("*", pos)) != std::string::npos) {
        regexPattern.replace(pos, 1, ".*");
        pos += 2;
    }
    
    pos = 0;
    while ((pos = regexPattern.find("?", pos)) != std::string::npos) {
        regexPattern.replace(pos, 1, ".");
        pos += 1;
    }
    
    // 在Windows中，路径分隔符可能是反斜杠，需要转义
    pos = 0;
    while ((pos = regexPattern.find("\\", pos)) != std::string::npos) {
        regexPattern.replace(pos, 1, "\\\\");
        pos += 2;
    }
    
    try {
        std::regex regex(regexPattern, std::regex::icase); // 不区分大小写
        return std::regex_match(filePathStr, regex);
    } catch (const std::regex_error&) {
        LOG_POLICY_ERROR("无效的正则表达式模式: " << pattern);
        return false;
    }
}

// 创建示例策略用于测试
void PolicyManager::createSamplePolicies() {
    // 示例1：对文档文件夹应用加密策略
    Policy policy1;
    policy1.id = "P001";
    policy1.name = "文档文件夹加密策略";
    policy1.description = "自动加密用户文档文件夹中的所有文件";
    policy1.enabled = true;
    policy1.createdAt = getCurrentTimestamp();
    policy1.modifiedAt = getCurrentTimestamp();
    
    PolicyRule rule1;
    rule1.id = "R001";
    rule1.name = "文档文件夹规则";
    rule1.type = PolicyType::FILE_PATH;
    rule1.pattern = "C:\\Users\\<USER>\\Documents\\*";
    rule1.action = PolicyAction::ENCRYPT;
    rule1.priority = 10;
    rule1.algorithm = "SM4-GCM";
    
    policy1.rules.push_back(rule1);
    
    // 示例2：禁止访问特定扩展名文件
    Policy policy2;
    policy2.id = "P002";
    policy2.name = "敏感文件保护策略";
    policy2.description = "禁止访问高度敏感文件类型";
    policy2.enabled = true;
    policy2.createdAt = getCurrentTimestamp();
    policy2.modifiedAt = getCurrentTimestamp();
    
    PolicyRule rule2a;
    rule2a.id = "R002a";
    rule2a.name = "源代码保护";
    rule2a.type = PolicyType::FILE_TYPE;
    rule2a.pattern = ".cpp";
    rule2a.action = PolicyAction::DENY;
    rule2a.priority = 20;
    
    PolicyRule rule2b;
    rule2b.id = "R002b";
    rule2b.name = "数据库保护";
    rule2b.type = PolicyType::FILE_TYPE;
    rule2b.pattern = ".mdb";
    rule2b.action = PolicyAction::DENY;
    rule2b.priority = 20;
    
    policy2.rules.push_back(rule2a);
    policy2.rules.push_back(rule2b);
    
    // 示例3：USB存储设备控制策略（新增）
    Policy policy3;
    policy3.id = "P003";
    policy3.name = "USB存储设备控制策略";
    policy3.description = "控制USB存储设备的访问权限";
    policy3.enabled = true;
    policy3.createdAt = getCurrentTimestamp();
    policy3.modifiedAt = getCurrentTimestamp();
    
    PolicyRule rule3a;
    rule3a.id = "R003a";
    rule3a.name = "USB存储设备只读";
    rule3a.type = PolicyType::USB_DEVICE;
    rule3a.pattern = "USB_STORAGE";
    rule3a.action = PolicyAction::READ_ONLY;
    rule3a.priority = 30;
    
    PolicyRule rule3b;
    rule3b.id = "R003b";
    rule3b.name = "USB设备强制加密";
    rule3b.type = PolicyType::USB_DEVICE;
    rule3b.pattern = "USBSTOR*";
    rule3b.action = PolicyAction::FORCE_ENCRYPT;
    rule3b.priority = 25;
    rule3b.algorithm = "SM4-GCM";
    
    policy3.rules.push_back(rule3a);
    policy3.rules.push_back(rule3b);
    
    // 示例4：外设通用控制策略（新增）
    Policy policy4;
    policy4.id = "P004";
    policy4.name = "外设通用控制策略";
    policy4.description = "控制各类外设的访问权限";
    policy4.enabled = true;
    policy4.createdAt = getCurrentTimestamp();
    policy4.modifiedAt = getCurrentTimestamp();
    
    PolicyRule rule4a;
    rule4a.id = "R004a";
    rule4a.name = "打印机允许";
    rule4a.type = PolicyType::PERIPHERAL;
    rule4a.pattern = "USB_PRINTER";
    rule4a.action = PolicyAction::ALLOW;
    rule4a.priority = 30;
    
    PolicyRule rule4b;
    rule4b.id = "R004b";
    rule4b.name = "蓝牙设备禁止";
    rule4b.type = PolicyType::PERIPHERAL;
    rule4b.pattern = "BLUETOOTH";
    rule4b.action = PolicyAction::DENY;
    rule4b.priority = 40;
    
    policy4.rules.push_back(rule4a);
    policy4.rules.push_back(rule4b);
    
    // 添加示例策略
    {
        std::lock_guard<std::mutex> lock(m_policiesMutex);
        m_policies[policy1.id] = std::make_shared<Policy>(policy1);
        m_policies[policy2.id] = std::make_shared<Policy>(policy2);
        m_policies[policy3.id] = std::make_shared<Policy>(policy3);
        m_policies[policy4.id] = std::make_shared<Policy>(policy4);
    }
}

// 实现外设管理相关方法

// 获取所有连接的外设
std::vector<PeripheralInfo> PolicyManager::getConnectedPeripherals() {
    std::vector<PeripheralInfo> peripherals;
    
    if (!m_initialized) {
        LOG_POLICY_ERROR("PolicyManager未初始化");
        return peripherals;
    }
    
    // 锁定外设缓存
    std::lock_guard<std::mutex> lock(m_peripheralMutex);
    
    // 首先检查缓存
    if (!m_peripheralCache.empty()) {
        for (const auto& pair : m_peripheralCache) {
            peripherals.push_back(pair.second);
        }
        return peripherals;
    }
    
    // 缓存为空，枚举系统中的外设
    // 以USB设备为例，使用SetupDi API枚举设备
    HDEVINFO deviceInfoSet = SetupDiGetClassDevs(
        &GUID_DEVCLASS_USB,
        NULL,
        NULL,
        DIGCF_PRESENT | DIGCF_DEVICEINTERFACE
    );
    
    if (deviceInfoSet == INVALID_HANDLE_VALUE) {
        LOG_POLICY_ERROR("获取USB设备列表失败，错误码: " << GetLastError());
        return peripherals;
    }
    
    SP_DEVINFO_DATA deviceInfoData;
    deviceInfoData.cbSize = sizeof(SP_DEVINFO_DATA);
    
    for (DWORD i = 0; SetupDiEnumDeviceInfo(deviceInfoSet, i, &deviceInfoData); i++) {
        PeripheralInfo peripheral;
        
        // 获取设备实例ID
        char deviceID[MAX_DEVICE_ID_LEN] = {0};
        if (SetupDiGetDeviceInstanceIdA(deviceInfoSet, &deviceInfoData, deviceID, MAX_DEVICE_ID_LEN, NULL)) {
            peripheral.id = deviceID;
            
            // 确定外设类型
            peripheral.type = determinePeripheralType(deviceID);
            
            // 获取设备描述
            char deviceDesc[MAX_PATH] = {0};
            DWORD propertyRegDataType = 0;
            
            if (SetupDiGetDeviceRegistryPropertyA(
                deviceInfoSet, 
                &deviceInfoData, 
                SPDRP_DEVICEDESC, 
                &propertyRegDataType, 
                (PBYTE)deviceDesc, 
                MAX_PATH, 
                NULL)) {
                peripheral.name = deviceDesc;
            }
            
            // 获取硬件ID
            char hardwareIds[MAX_PATH] = {0};
            if (SetupDiGetDeviceRegistryPropertyA(
                deviceInfoSet, 
                &deviceInfoData, 
                SPDRP_HARDWAREID, 
                &propertyRegDataType, 
                (PBYTE)hardwareIds, 
                MAX_PATH, 
                NULL)) {
                peripheral.hardwareId = hardwareIds;
                
                // 解析VID和PID
                std::string hwid = hardwareIds;
                std::transform(hwid.begin(), hwid.end(), hwid.begin(), ::toupper);
                
                size_t vidPos = hwid.find("VID_");
                if (vidPos != std::string::npos) {
                    peripheral.vendorId = hwid.substr(vidPos + 4, 4);
                }
                
                size_t pidPos = hwid.find("PID_");
                if (pidPos != std::string::npos) {
                    peripheral.productId = hwid.substr(pidPos + 4, 4);
                }
            }
            
            // 将外设添加到结果和缓存
            peripherals.push_back(peripheral);
            m_peripheralCache[peripheral.id] = peripheral;
        }
    }
    
    // 清理
    SetupDiDestroyDeviceInfoList(deviceInfoSet);
    
    // TODO: 还可以枚举其他类型的外设，如蓝牙设备、网络适配器等
    
    return peripherals;
}

// 获取特定外设的信息
PeripheralInfo PolicyManager::getPeripheralInfo(const std::string& deviceId) {
    PeripheralInfo peripheral;
    
    if (!m_initialized) {
        LOG_POLICY_ERROR("PolicyManager未初始化");
        return peripheral;
    }
    
    // 锁定外设缓存
    std::lock_guard<std::mutex> lock(m_peripheralMutex);
    
    // 检查缓存
    auto it = m_peripheralCache.find(deviceId);
    if (it != m_peripheralCache.end()) {
        return it->second;
    }
    
    // 缓存中没有此设备，尝试获取所有设备再查找
    getConnectedPeripherals(); // 更新缓存
    
    it = m_peripheralCache.find(deviceId);
    if (it != m_peripheralCache.end()) {
        return it->second;
    }
    
    // 设备未找到
    return peripheral;
}

// 评估外设策略
PolicyEvaluationResult PolicyManager::evaluatePeripheralPolicy(const std::string& deviceId, const std::string& userId) {
    PolicyEvaluationResult result;
    result.shouldEncrypt = false;
    result.shouldDecrypt = false;
    result.shouldDeny = false;
    result.shouldAudit = true; // 默认审计
    result.shouldNotify = false;
    result.readOnly = false;
    result.forceEncrypt = false;
    
    if (!m_initialized) {
        LOG_POLICY_ERROR("PolicyManager未初始化");
        result.shouldDeny = true; // 未初始化时默认拒绝
        return result;
    }
    
    // 获取适用的规则
    std::vector<PolicyRule> rules = getApplicablePeripheralRules(deviceId, userId);
    
    if (rules.empty()) {
        // 没有适用的规则，使用默认动作（允许访问）
        return result;
    }
    
    // 获取优先级最高的规则
    PolicyRule highestRule = selectHighestPriorityRule(rules);
    
    // 根据规则设置结果
    switch (highestRule.action) {
        case PolicyAction::ENCRYPT:
            result.shouldEncrypt = true;
            result.encryptionAlgorithm = highestRule.algorithm.empty() ? DEFAULT_ALGORITHM : highestRule.algorithm;
            break;
            
        case PolicyAction::DECRYPT:
            result.shouldDecrypt = true;
            break;
            
        case PolicyAction::DENY:
            result.shouldDeny = true;
            break;
            
        case PolicyAction::ALLOW:
            // 默认允许，无需额外设置
            break;
            
        case PolicyAction::AUDIT:
            result.shouldAudit = true;
            break;
            
        case PolicyAction::NOTIFY:
            result.shouldNotify = true;
            break;
            
        case PolicyAction::READ_ONLY:
            result.readOnly = true;
            break;
            
        case PolicyAction::FORCE_ENCRYPT:
            result.forceEncrypt = true;
            result.encryptionAlgorithm = highestRule.algorithm.empty() ? DEFAULT_ALGORITHM : highestRule.algorithm;
            break;
    }
    
    // 设置应用的策略和规则ID
    result.appliedPolicyId = highestRule.id.substr(0, highestRule.id.find_first_of(':'));
    result.appliedRuleId = highestRule.id;
    
    // 复制额外参数
    result.parameters = highestRule.parameters;
    
    // 记录审计事件
    if (result.shouldAudit) {
        std::string action = "PERIPHERAL_ACCESS";
        std::string resultStr = result.shouldDeny ? "denied" : "allowed";
        
        std::stringstream details;
        details << "Device: " << deviceId;
        if (!result.shouldDeny) {
            if (result.readOnly) details << " (Read-Only)";
            if (result.forceEncrypt) details << " (Force-Encrypt)";
        }
        
        logPolicyEvent(result.appliedPolicyId, userId, action, deviceId, resultStr, details.str());
    }
    
    return result;
}

// 判断外设是否允许访问
bool PolicyManager::isPeripheralAllowed(const std::string& deviceId, const std::string& userId) {
    PolicyEvaluationResult result = evaluatePeripheralPolicy(deviceId, userId);
    return !result.shouldDeny;
}

// 阻止外设
bool PolicyManager::blockPeripheral(const std::string& deviceId) {
    if (!m_initialized) {
        LOG_POLICY_ERROR("PolicyManager未初始化");
        return false;
    }
    
    return addPeripheralPolicy(deviceId, PolicyAction::DENY, 100); // 高优先级的拒绝策略
}

// 允许外设
bool PolicyManager::allowPeripheral(const std::string& deviceId, PolicyAction action) {
    if (!m_initialized) {
        LOG_POLICY_ERROR("PolicyManager未初始化");
        return false;
    }
    
    // 验证action是有效的允许操作
    if (action != PolicyAction::ALLOW && 
        action != PolicyAction::READ_ONLY && 
        action != PolicyAction::FORCE_ENCRYPT) {
        LOG_POLICY_ERROR("无效的允许操作");
        return false;
    }
    
    return addPeripheralPolicy(deviceId, action, 90); // 较高优先级的允许策略
}

// 添加外设策略
bool PolicyManager::addPeripheralPolicy(const std::string& deviceId, PolicyAction action,
                                     int priority, bool createPolicy) {
    if (!m_initialized) {
        LOG_POLICY_ERROR("PolicyManager未初始化");
        return false;
    }
    
    // 获取外设信息
    PeripheralInfo peripheral = getPeripheralInfo(deviceId);
    if (peripheral.id.empty()) {
        LOG_POLICY_ERROR("未找到外设: " << deviceId);
        return false;
    }
    
    // 创建外设策略ID和规则
    std::string policyId = "P_USB_" + peripheral.vendorId + "_" + peripheral.productId;
    std::string ruleId = "R_USB_" + peripheral.id;
    std::string policyName = "外设策略: " + peripheral.name;
    
    PolicyRule rule;
    rule.id = ruleId;
    rule.name = "外设规则: " + peripheral.name;
    rule.type = PolicyType::USB_DEVICE;
    rule.pattern = peripheral.id; // 使用设备ID作为匹配模式
    rule.action = action;
    rule.priority = priority;
    
    // 如果是加密相关的操作，设置加密算法
    if (action == PolicyAction::ENCRYPT || action == PolicyAction::FORCE_ENCRYPT) {
        rule.algorithm = DEFAULT_ALGORITHM;
    }
    
    // 添加规则到现有策略或创建新策略
    std::lock_guard<std::mutex> lock(m_policiesMutex);
    
    if (!createPolicy && m_policies.find(policyId) == m_policies.end()) {
        LOG_POLICY_ERROR("策略不存在: " << policyId);
        return false;
    }
    
    if (createPolicy && m_policies.find(policyId) == m_policies.end()) {
        // 创建新策略
        Policy newPolicy;
        newPolicy.id = policyId;
        newPolicy.name = policyName;
        newPolicy.description = "自动创建的外设策略";
        newPolicy.enabled = true;
        newPolicy.createdAt = getCurrentTimestamp();
        newPolicy.modifiedAt = getCurrentTimestamp();
        newPolicy.rules.push_back(rule);
        
        m_policies[policyId] = std::make_shared<Policy>(newPolicy);
        
        // 保存策略
        if (!savePolicyToStorage(newPolicy)) {
            LOG_POLICY_ERROR("保存策略失败: " << policyId);
            m_policies.erase(policyId);
            return false;
        }
        
        LOG_POLICY_INFO("创建外设策略: " << policyId);
    } else {
        // 向现有策略添加规则
        auto& policy = m_policies[policyId];
        
        // 检查规则是否已存在
        auto it = std::find_if(policy->rules.begin(), policy->rules.end(),
            [&ruleId](const PolicyRule& r) { return r.id == ruleId; });
        
        if (it != policy->rules.end()) {
            // 更新规则
            *it = rule;
        } else {
            // 添加规则
            policy->rules.push_back(rule);
        }
        
        policy->modifiedAt = getCurrentTimestamp();
        
        // 保存策略
        if (!savePolicyToStorage(*policy)) {
            LOG_POLICY_ERROR("保存策略失败: " << policyId);
            return false;
        }
        
        LOG_POLICY_INFO("更新外设策略: " << policyId);
    }
    
    return true;
}

// 获取适用于外设的规则
std::vector<PolicyRule> PolicyManager::getApplicablePeripheralRules(const std::string& deviceId,
                                                                const std::string& userId) {
    std::vector<PolicyRule> applicableRules;
    
    // 获取外设信息
    PeripheralInfo peripheral = getPeripheralInfo(deviceId);
    if (peripheral.id.empty()) {
        LOG_POLICY_ERROR("未找到外设: " << deviceId);
        return applicableRules;
    }
    
    std::lock_guard<std::mutex> lock(m_policiesMutex);
    
    for (const auto& policyPair : m_policies) {
        const auto& policy = policyPair.second;
        
        // 跳过禁用的策略
        if (!policy->enabled) {
            continue;
        }
        
        for (const auto& rule : policy->rules) {
            bool matches = false;
            
            switch (rule.type) {
                case PolicyType::USB_DEVICE:
                    // 检查设备ID匹配
                    matches = matchesPeripheralPattern(peripheral, rule.pattern);
                    break;
                    
                case PolicyType::PERIPHERAL:
                    // 通用外设匹配
                    matches = matchesPeripheralPattern(peripheral, rule.pattern);
                    break;
                    
                case PolicyType::USER:
                    // 检查用户ID
                    matches = (userId == rule.pattern);
                    break;
                    
                // 其他类型暂不适用于外设
                default:
                    break;
            }
            
            if (matches) {
                applicableRules.push_back(rule);
            }
        }
    }
    
    return applicableRules;
}

// 检查外设是否匹配模式
bool PolicyManager::matchesPeripheralPattern(const PeripheralInfo& peripheral, const std::string& pattern) {
    // 多种匹配方式：完整设备ID、vendorId:productId、硬件ID部分匹配等
    
    // 完整设备ID匹配
    if (peripheral.id == pattern) {
        return true;
    }
    
    // VID:PID格式匹配
    std::string vidPid = peripheral.vendorId + ":" + peripheral.productId;
    if (vidPid == pattern) {
        return true;
    }
    
    // 硬件ID部分匹配
    if (!peripheral.hardwareId.empty() && 
        peripheral.hardwareId.find(pattern) != std::string::npos) {
        return true;
    }
    
    // 设备名称匹配
    if (!peripheral.name.empty() && pattern[0] == '*') {
        std::string namePattern = pattern.substr(1); // 去掉开头的*
        if (peripheral.name.find(namePattern) != std::string::npos) {
            return true;
        }
    }
    
    // 设备类型匹配
    if (pattern == "USB_STORAGE" && peripheral.type == PeripheralType::USB_STORAGE) {
        return true;
    }
    
    return false;
}

// 确定外设类型
PeripheralType PolicyManager::determinePeripheralType(const std::string& deviceId) {
    // 根据设备ID确定外设类型的简单实现
    // 在实际应用中，应该使用更完善的设备识别机制
    
    std::string upperDeviceId = deviceId;
    std::transform(upperDeviceId.begin(), upperDeviceId.end(), upperDeviceId.begin(), ::toupper);
    
    if (upperDeviceId.find("USBSTOR") != std::string::npos) {
        return PeripheralType::USB_STORAGE;
    }
    
    if (upperDeviceId.find("PRINTER") != std::string::npos ||
        upperDeviceId.find("PRINT") != std::string::npos) {
        return PeripheralType::USB_PRINTER;
    }
    
    if (upperDeviceId.find("HID") != std::string::npos) {
        return PeripheralType::USB_HID;
    }
    
    if (upperDeviceId.find("BLUETOOTH") != std::string::npos) {
        return PeripheralType::BLUETOOTH;
    }
    
    if (upperDeviceId.find("NET") != std::string::npos ||
        upperDeviceId.find("ETHERNET") != std::string::npos) {
        return PeripheralType::NETWORK_ADAPTER;
    }
    
    if (upperDeviceId.find("COM") != std::string::npos ||
        upperDeviceId.find("SERIAL") != std::string::npos) {
        return PeripheralType::COM_PORT;
    }
    
    if (upperDeviceId.find("USB") != std::string::npos) {
        return PeripheralType::USB_OTHER;
    }
    
    return PeripheralType::OTHER;
} 