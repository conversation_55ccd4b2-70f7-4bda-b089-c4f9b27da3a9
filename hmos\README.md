# 文档加密系统 - HarmonyOS 版本

![HarmonyOS](https://img.shields.io/badge/HarmonyOS-5.1.1-blue)
![开发状态](https://img.shields.io/badge/开发状态-开发中-yellow)
![构建工具](https://img.shields.io/badge/构建工具-hvigor-green)

## 📱 项目概述

这是文档加密系统的 HarmonyOS 版本，基于 ArkTS + ArkUI 开发，为鸿蒙生态提供企业级文档加密与安全管理功能。

## 🏗️ 项目结构

```text
hmos/
├── AppScope/                   # 应用全局配置
│   ├── app.json5              # 应用基本信息配置
│   └── resources/             # 全局资源文件
├── entry/                     # 主模块
│   ├── src/                   # 源代码目录
│   ├── build-profile.json5    # 模块构建配置
│   ├── hvigorfile.ts         # 模块构建脚本
│   └── oh-package.json5       # 模块依赖配置
├── hvigor/                    # 构建工具配置
│   └── hvigor-config.json5    # hvigor 全局配置
├── build-profile.json5        # 项目构建配置
├── hvigorfile.ts             # 项目构建脚本
├── oh-package.json5          # 项目依赖配置
└── local.properties          # 本地环境配置
```

## 🛠️ 开发环境要求

### 必需软件

- **DevEco Studio**: 4.0 或更高版本
- **HarmonyOS SDK**: 5.1.1 (API Level 19) 或更高
- **Node.js**: 16.x 或更高版本

### 系统要求

- **操作系统**: Windows 10/11, macOS 10.15+, Ubuntu 18.04+
- **内存**: 最低 8GB RAM，推荐 16GB+
- **存储**: 最低 10GB 可用空间

## 🚀 快速开始

### 1. 环境准备

确保已安装 DevEco Studio 并配置好 HarmonyOS SDK。

### 2. 项目导入

```bash
# 克隆项目（如果还没有）
git clone <repository-url>
cd cryptosystem/hmos

# 或者直接在 DevEco Studio 中打开 hmos 目录
```

### 3. 依赖安装

```bash
# 安装项目依赖
hvigor clean
hvigor install
```

### 4. 构建项目

#### 使用 DevEco Studio

1. 在 DevEco Studio 中打开 `hmos` 目录
2. 等待项目同步完成
3. 点击 "Build" → "Build Hap(s)/App(s)"

#### 使用命令行

```bash
# 清理项目
hvigor clean

# 构建调试版本
hvigor assembleHap

# 构建发布版本
hvigor assembleHap --mode release

# 运行测试
hvigor test
```

### 5. 运行项目

```bash
# 安装到设备/模拟器
hvigor installHap

# 或在 DevEco Studio 中点击运行按钮
```

## 📋 可用命令

| 命令 | 描述 |
|------|------|
| `hvigor clean` | 清理构建产物 |
| `hvigor assembleHap` | 构建 HAP 包 |
| `hvigor assembleHap --mode release` | 构建发布版本 |
| `hvigor installHap` | 安装到设备 |
| `hvigor uninstallHap` | 从设备卸载 |
| `hvigor test` | 运行单元测试 |

## 🔧 配置说明

### 应用配置 (AppScope/app.json5)

- **bundleName**: `com.example.myapplication` (需要修改为实际包名)
- **versionCode**: 应用版本号
- **versionName**: 应用版本名称

### 构建配置 (build-profile.json5)

- **targetSdkVersion**: 目标 SDK 版本 (5.1.1)
- **compatibleSdkVersion**: 兼容的最低 SDK 版本
- **runtimeOS**: 运行时操作系统 (HarmonyOS)

## 📦 依赖管理

### 开发依赖

- `@ohos/hypium`: 单元测试框架
- `@ohos/hamock`: 模拟框架

### 添加新依赖

```bash
# 添加运行时依赖
hvigor add <package-name>

# 添加开发依赖
hvigor add <package-name> --dev
```

## 🐛 常见问题

### 1. npm 命令错误

**问题**: 运行 `npm install` 等命令报错找不到 package.json

**解决**: HarmonyOS 项目使用 `hvigor` 而不是 `npm`，请使用：

```bash
hvigor clean
hvigor install
hvigor assembleHap
```

### 2. 构建失败

**问题**: 构建时出现依赖或配置错误

**解决**:

```bash
# 清理并重新构建
hvigor clean
hvigor install
hvigor assembleHap
```

### 3. 设备连接问题

**问题**: 无法连接到设备或模拟器

**解决**:

- 确保设备已开启开发者模式和 USB 调试
- 检查 DevEco Studio 中的设备管理器
- 重启 ADB 服务

## 📚 相关文档

- [HarmonyOS 开发指南](https://developer.harmonyos.com/cn/docs)
- [ArkTS 语言参考](https://developer.harmonyos.com/cn/docs/documentation/doc-guides-V3/arkts-get-started-0000001504769321-V3)
- [ArkUI 框架](https://developer.harmonyos.com/cn/docs/documentation/doc-guides-V3/arkui-overview-0000001524569345-V3)
- [hvigor 构建工具](https://developer.harmonyos.com/cn/docs/documentation/doc-guides-V3/hvigor-0000001505433201-V3)

## 🔗 项目链接

- [主项目 README](../README.md)
- [用户需求文档](../user_requirements.md)
- [客户端组件说明](../customer/README.md)

---

**注意**: 这是一个 HarmonyOS 项目，请使用 `hvigor` 命令而不是 `npm` 命令进行构建和管理。
