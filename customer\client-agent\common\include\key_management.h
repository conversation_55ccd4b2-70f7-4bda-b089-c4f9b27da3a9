#pragma once

#include <string>
#include <vector>
#include <memory>
#include <map>
#include <cstdint>

namespace crypto {
namespace key {

/**
 * 密钥类型枚举
 */
enum class KeyType {
    ROOT,           // 根密钥 (RK)
    ORGANIZATION,   // 组织密钥 (OK)
    USER,           // 用户密钥 (UK)
    DEVICE,         // 设备密钥 (DK)
    FILE            // 文件密钥 (FK)
};

/**
 * 密钥状态枚举
 */
enum class KeyStatus {
    ACTIVE,         // 活动
    INACTIVE,       // 非活动
    REVOKED         // 已吊销
};

/**
 * 密钥元数据
 */
using KeyMetadata = std::map<std::string, std::string>;

/**
 * 密钥基础类
 */
class Key {
public:
    virtual ~Key() = default;

    // 获取密钥ID
    const std::string& GetKeyId() const { return keyId_; }

    // 获取密钥类型
    KeyType GetKeyType() const { return keyType_; }

    // 获取密钥状态
    KeyStatus GetStatus() const { return status_; }

    // 获取父密钥ID
    const std::string& GetParentKeyId() const { return parentKeyId_; }

    // 获取创建时间
    const std::string& GetCreationTimestamp() const { return creationTimestamp_; }

    // 获取最后更新时间
    const std::string& GetLastUpdateTimestamp() const { return lastUpdateTimestamp_; }

    // 获取元数据
    const KeyMetadata& GetMetadata() const { return metadata_; }

protected:
    std::string keyId_;
    KeyType keyType_;
    KeyStatus status_;
    std::string parentKeyId_;
    std::string creationTimestamp_;
    std::string lastUpdateTimestamp_;
    KeyMetadata metadata_;
};

/**
 * 文件密钥类
 */
class FileKey : public Key {
public:
    // 获取文件ID
    const std::string& GetFileId() const { return fileId_; }

    // 获取加密算法
    const std::string& GetEncryptionAlgorithm() const { return encryptionAlgorithm_; }

    // 获取文件路径
    const std::string& GetFilePath() const { return filePath_; }

    // 获取加密的密钥材料
    const std::vector<uint8_t>& GetEncryptedKeyMaterial() const { return encryptedKeyMaterial_; }

private:
    std::string fileId_;
    std::string encryptionAlgorithm_;
    std::string filePath_;
    std::vector<uint8_t> encryptedKeyMaterial_;
};

/**
 * 密钥管理接口
 */
class KeyManager {
public:
    virtual ~KeyManager() = default;

    // 创建文件密钥
    virtual std::shared_ptr<FileKey> CreateFileKey(
        const std::string& parentKeyId,
        const std::string& fileId,
        const std::string& filePath,
        const std::string& algorithm) = 0;

    // 获取文件密钥 (通过密钥ID)
    virtual std::shared_ptr<FileKey> GetFileKey(const std::string& keyId) = 0;

    // 获取文件密钥 (通过文件ID)
    virtual std::shared_ptr<FileKey> GetFileKeyByFileId(const std::string& fileId) = 0;

    // 获取文件密钥 (通过文件路径)
    virtual std::shared_ptr<FileKey> GetFileKeyByFilePath(const std::string& filePath) = 0;

    // 吊销密钥
    virtual bool RevokeKey(const std::string& keyId, bool recursive = false) = 0;
};

/**
 * 密钥材料管理接口
 */
class KeyMaterialManager {
public:
    virtual ~KeyMaterialManager() = default;

    // 生成文件密钥
    virtual std::vector<uint8_t> GenerateFileKey(int keySize = 256) = 0;

    // 使用父密钥加密文件密钥
    virtual std::vector<uint8_t> EncryptFileKey(
        const std::string& parentKeyId,
        const std::vector<uint8_t>& keyMaterial) = 0;

    // 解密文件密钥
    virtual std::vector<uint8_t> DecryptFileKey(
        const std::string& parentKeyId,
        const std::vector<uint8_t>& encryptedKeyMaterial) = 0;
};

} // namespace key
} // namespace crypto 