using CryptoSystem.DeclassificationClient.Models;

namespace CryptoSystem.DeclassificationClient.Services
{
    /// <summary>
    /// 审计服务接口
    /// </summary>
    public interface IAuditService
    {
        /// <summary>
        /// 记录审计日志
        /// </summary>
        /// <param name="operationType">操作类型</param>
        /// <param name="operationDescription">操作描述</param>
        /// <param name="userId">用户ID</param>
        /// <param name="userName">用户名</param>
        /// <param name="relatedTaskId">相关任务ID</param>
        /// <param name="relatedFileId">相关文件ID</param>
        /// <param name="result">操作结果</param>
        /// <param name="additionalInfo">附加信息</param>
        /// <returns>日志ID</returns>
        Task<string> LogAsync(
            string operationType,
            string operationDescription,
            string userId,
            string userName = "",
            string relatedTaskId = "",
            string relatedFileId = "",
            string result = "Success",
            string additionalInfo = "");

        /// <summary>
        /// 记录任务创建日志
        /// </summary>
        /// <param name="task">脱密任务</param>
        /// <param name="userId">用户ID</param>
        /// <param name="userName">用户名</param>
        /// <returns>日志ID</returns>
        Task<string> LogTaskCreatedAsync(DeclassificationTask task, string userId, string userName);

        /// <summary>
        /// 记录任务状态变更日志
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="oldStatus">旧状态</param>
        /// <param name="newStatus">新状态</param>
        /// <param name="userId">用户ID</param>
        /// <param name="userName">用户名</param>
        /// <param name="reason">变更原因</param>
        /// <returns>日志ID</returns>
        Task<string> LogTaskStatusChangedAsync(
            string taskId,
            DeclassificationStatus oldStatus,
            DeclassificationStatus newStatus,
            string userId,
            string userName,
            string reason = "");

        /// <summary>
        /// 记录文件操作日志
        /// </summary>
        /// <param name="operationType">操作类型（添加、删除、处理等）</param>
        /// <param name="fileId">文件ID</param>
        /// <param name="fileName">文件名</param>
        /// <param name="taskId">关联任务ID</param>
        /// <param name="userId">用户ID</param>
        /// <param name="userName">用户名</param>
        /// <param name="result">操作结果</param>
        /// <returns>日志ID</returns>
        Task<string> LogFileOperationAsync(
            string operationType,
            string fileId,
            string fileName,
            string taskId,
            string userId,
            string userName,
            string result = "Success");

        /// <summary>
        /// 记录安全包生成日志
        /// </summary>
        /// <param name="package">安全包</param>
        /// <param name="userId">用户ID</param>
        /// <param name="userName">用户名</param>
        /// <returns>日志ID</returns>
        Task<string> LogPackageGeneratedAsync(SecurePackage package, string userId, string userName);

        /// <summary>
        /// 记录安全包下载日志
        /// </summary>
        /// <param name="packageId">包ID</param>
        /// <param name="packageName">包名称</param>
        /// <param name="downloadPath">下载路径</param>
        /// <param name="userId">用户ID</param>
        /// <param name="userName">用户名</param>
        /// <param name="clientIP">客户端IP</param>
        /// <returns>日志ID</returns>
        Task<string> LogPackageDownloadedAsync(
            string packageId,
            string packageName,
            string downloadPath,
            string userId,
            string userName,
            string clientIP = "");

        /// <summary>
        /// 记录登录日志
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="userName">用户名</param>
        /// <param name="clientIP">客户端IP</param>
        /// <param name="result">登录结果</param>
        /// <param name="failureReason">失败原因</param>
        /// <returns>日志ID</returns>
        Task<string> LogLoginAsync(
            string userId,
            string userName,
            string clientIP = "",
            string result = "Success",
            string failureReason = "");

        /// <summary>
        /// 记录配置变更日志
        /// </summary>
        /// <param name="configKey">配置键</param>
        /// <param name="oldValue">旧值</param>
        /// <param name="newValue">新值</param>
        /// <param name="userId">用户ID</param>
        /// <param name="userName">用户名</param>
        /// <returns>日志ID</returns>
        Task<string> LogConfigurationChangedAsync(
            string configKey,
            string oldValue,
            string newValue,
            string userId,
            string userName);

        /// <summary>
        /// 获取审计日志列表
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="operationType">操作类型过滤</param>
        /// <param name="userId">用户ID过滤</param>
        /// <param name="result">结果过滤</param>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <returns>审计日志列表</returns>
        Task<(List<AuditLog> Logs, int TotalCount)> GetLogsAsync(
            DateTime? startTime = null,
            DateTime? endTime = null,
            string operationType = "",
            string userId = "",
            string result = "",
            int pageIndex = 0,
            int pageSize = 50);

        /// <summary>
        /// 获取任务相关的审计日志
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <returns>审计日志列表</returns>
        Task<(List<AuditLog> Logs, int TotalCount)> GetTaskLogsAsync(
            string taskId,
            int pageIndex = 0,
            int pageSize = 50);

        /// <summary>
        /// 获取文件相关的审计日志
        /// </summary>
        /// <param name="fileId">文件ID</param>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <returns>审计日志列表</returns>
        Task<(List<AuditLog> Logs, int TotalCount)> GetFileLogsAsync(
            string fileId,
            int pageIndex = 0,
            int pageSize = 50);

        /// <summary>
        /// 获取用户操作日志
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <returns>审计日志列表</returns>
        Task<(List<AuditLog> Logs, int TotalCount)> GetUserLogsAsync(
            string userId,
            DateTime? startTime = null,
            DateTime? endTime = null,
            int pageIndex = 0,
            int pageSize = 50);

        /// <summary>
        /// 获取审计统计信息
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns>统计信息</returns>
        Task<Dictionary<string, object>> GetAuditStatisticsAsync(
            DateTime? startTime = null,
            DateTime? endTime = null);

        /// <summary>
        /// 导出审计日志
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="operationType">操作类型过滤</param>
        /// <param name="userId">用户ID过滤</param>
        /// <param name="exportPath">导出路径</param>
        /// <param name="format">导出格式（CSV, Excel, JSON）</param>
        /// <returns>导出是否成功</returns>
        Task<bool> ExportLogsAsync(
            DateTime? startTime = null,
            DateTime? endTime = null,
            string operationType = "",
            string userId = "",
            string exportPath = "",
            string format = "CSV");

        /// <summary>
        /// 清理过期日志
        /// </summary>
        /// <param name="retentionDays">保留天数</param>
        /// <returns>清理的日志数量</returns>
        Task<int> CleanupExpiredLogsAsync(int retentionDays = 365);

        /// <summary>
        /// 获取当前客户端IP地址
        /// </summary>
        /// <returns>IP地址</returns>
        string GetClientIPAddress();
    }
} 