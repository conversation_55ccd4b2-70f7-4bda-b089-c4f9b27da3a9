﻿#pragma once

#include <string>
#include <memory>
#include <vector>
#include "../common/CoreTypes.h" // 包含所有共享类型定义
#include "../keymanager/KeyManager.h" // 添加KeyManager头文件

// 前向声明
class PolicyManager;
struct PeripheralInfo;

// 使用CryptoSystem命名空间
using namespace CryptoSystem;

class CoreEngine {
public:
    CoreEngine();
    virtual ~CoreEngine();

    // 初始化引擎
    bool initialize();
    // 关闭引擎
    void shutdown();
    // 暂停/恢复处理
    void setPaused(bool paused);
    // 获取当前状态
    EngineState getState() const;
    
    // 文件事件处理（由文件系统驱动调用）
    virtual void onFileEvent(const FileEvent& event);
    
    // 手动处理特定文件
    bool processFile(const std::wstring& filePath, bool forceEncrypt = false);
    // 获取文件状态（是否已加密等）
    bool getFileStatus(const std::wstring& filePath, bool& isEncrypted);
    
    // 外设管理相关方法
    std::vector<PeripheralInfo> getConnectedPeripherals();
    bool isPeripheralAllowed(const std::string& deviceId, const std::string& userId = "");
    bool blockPeripheral(const std::string& deviceId);
    bool allowPeripheral(const std::string& deviceId, bool readOnly = false, bool forceEncrypt = false);
    void onPeripheralEvent(const std::string& deviceId, const std::string& eventType);
    
    // Prevent copying and assignment
    CoreEngine(const CoreEngine&) = delete;
    CoreEngine& operator=(const CoreEngine&) = delete;
    
private:
    // 内部文件处理方法
    bool shouldProcessFile(const std::wstring& filePath);
    bool applyFilePolicy(const std::wstring& filePath);
    void logFileAction(const std::wstring& filePath, const std::string& action, bool success);
    
    // 内部外设处理方法
    bool evaluateAndApplyPeripheralPolicy(const std::string& deviceId, const std::string& userId = "");
    void logPeripheralAction(const std::string& deviceId, const std::string& action, bool success);
    
    // 时间戳工具函数
    std::string getCurrentTimestamp();
    
    // 组件引用
    std::shared_ptr<KeyManager> m_keyManager;
    std::shared_ptr<PolicyManager> m_policyManager;
    
    // 状态管理
    EngineState m_state;
    bool m_isPaused;
    
    // 辅助方法
    std::string getFileExtension(const std::wstring& filePath);
    bool isFileEncrypted(const std::wstring& filePath);
};