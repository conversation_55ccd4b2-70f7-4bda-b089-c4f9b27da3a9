#!/bin/bash

# =====================================================
# 企业文档加密系统 - 数据库维护脚本
# 版本: 1.4.0
# 作者: 数据库管理员
# 创建时间: 2025-01-20
# 更新时间: 2025-01-20 - 专注PostgreSQL，增强功能
# 说明: 专注于PostgreSQL数据库的自动化维护
# =====================================================

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# 加载环境变量
if [ -f "$PROJECT_DIR/.env" ]; then
    source "$PROJECT_DIR/.env"
fi

# 默认配置
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"
DB_NAME="${DB_NAME:-cryptosystem}"
DB_USER="${DB_USER:-crypto}"
DB_PASSWORD="${DB_PASSWORD:-19891025}"
BACKUP_DIR="${PROJECT_DIR}/backups"
LOG_DIR="${PROJECT_DIR}/logs"
RETENTION_DAYS="${BACKUP_RETENTION_DAYS:-30}"

# 日志文件
MAINTENANCE_LOG="${LOG_DIR}/maintenance.log"

# 确保日志目录存在
mkdir -p "$LOG_DIR"

# 增强的日志函数
log_info() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${BLUE}[INFO]${NC} $message"
    echo "[$timestamp] [INFO] $message" >> "$MAINTENANCE_LOG"
}

log_warn() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${YELLOW}[WARN]${NC} $message"
    echo "[$timestamp] [WARN] $message" >> "$MAINTENANCE_LOG"
}

log_error() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${RED}[ERROR]${NC} $message" >&2
    echo "[$timestamp] [ERROR] $message" >> "$MAINTENANCE_LOG"
}

log_success() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${GREEN}[SUCCESS]${NC} $message"
    echo "[$timestamp] [SUCCESS] $message" >> "$MAINTENANCE_LOG"
}

log_debug() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${PURPLE}[DEBUG]${NC} $message"
    echo "[$timestamp] [DEBUG] $message" >> "$MAINTENANCE_LOG"
}

# 进度条函数
show_progress() {
    local duration=$1
    local message=$2
    
    echo -ne "${CYAN}[PROGRESS]${NC} $message "
    for ((i=0; i<duration; i++)); do
        echo -ne "▓"
        sleep 0.1
    done
    echo " 完成"
}

# 检查数据库连接
check_db_connection() {
    log_info "🔍 检查数据库连接..."
    
    if command -v psql >/dev/null 2>&1; then
        if PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" >/dev/null 2>&1; then
            log_success "数据库连接正常"
            return 0
        fi
    fi
    
    log_error "数据库连接失败"
    return 1
}

# 自动备份数据库
backup_database() {
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_file="$BACKUP_DIR/postgres_backup_${timestamp}.sql"
    local backup_compressed="$BACKUP_DIR/postgres_backup_${timestamp}.sql.gz"
    
    log_info "💾 开始备份数据库..."
    
    # 确保备份目录存在
    mkdir -p "$BACKUP_DIR"
    
    # 显示备份信息
    echo "📋 备份配置："
    echo "   🗄️  数据库: $DB_NAME"
    echo "   📁 备份目录: $BACKUP_DIR"
    echo "   ⏰ 开始时间: $(date)"
    echo
    
    # 执行备份
    show_progress 20 "正在备份数据库"
    
    if PGPASSWORD="$DB_PASSWORD" pg_dump -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" --verbose --format=plain --no-owner --no-privileges > "$backup_file" 2>/dev/null; then
        log_success "数据库备份完成: $backup_file"
        
        # 压缩备份文件
        if gzip "$backup_file"; then
            log_info "📦 备份文件已压缩: $backup_compressed"
            backup_file="$backup_compressed"
        fi
        
        # 记录备份信息
        local backup_size=$(du -h "$backup_file" | cut -f1)
        log_info "📏 备份文件大小: $backup_size"
        
        # 验证备份文件
        if [ -s "$backup_file" ]; then
            log_success "✅ 备份验证通过"
            
            # 记录备份到日志
            echo "$(date '+%Y-%m-%d %H:%M:%S') | BACKUP_SUCCESS | $backup_file | $backup_size" >> "$LOG_DIR/backup_history.log"
        else
            log_error "❌ 备份文件为空或不存在"
            return 1
        fi
    else
        log_error "数据库备份失败"
        return 1
    fi
    
    return 0
}

# 清理旧备份
cleanup_old_backups() {
    local retention_days=${1:-$RETENTION_DAYS}
    
    log_info "🧹 清理${retention_days}天前的备份文件..."
    
    if [ ! -d "$BACKUP_DIR" ]; then
        log_warn "备份目录不存在: $BACKUP_DIR"
        return 0
    fi
    
    local deleted_count=0
    local total_saved_space=0
    
    # 查找并删除旧备份文件
    while IFS= read -r -d '' file; do
        if [ -f "$file" ]; then
            local file_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null)
            total_saved_space=$((total_saved_space + file_size))
            rm -f "$file"
            deleted_count=$((deleted_count + 1))
            log_debug "删除旧备份: $(basename "$file")"
        fi
    done < <(find "$BACKUP_DIR" -name "postgres_backup_*.sql*" -type f -mtime +$retention_days -print0 2>/dev/null)
    
    if [ $deleted_count -gt 0 ]; then
        local saved_space_mb=$((total_saved_space / 1024 / 1024))
        log_success "清理完成：删除了 $deleted_count 个旧备份文件，节省了 ${saved_space_mb}MB 空间"
    else
        log_info "没有找到需要清理的旧备份文件"
    fi
    
    # 显示当前备份状态
    local current_count=$(find "$BACKUP_DIR" -name "postgres_backup_*.sql*" -type f | wc -l)
    local current_size=$(du -sh "$BACKUP_DIR" 2>/dev/null | cut -f1)
    log_info "📊 当前备份状态: $current_count 个文件，总大小 $current_size"
}

# 获取数据库性能统计
get_db_stats() {
    log_info "📊 获取数据库性能统计..."
    
    if ! check_db_connection; then
        return 1
    fi
    
    echo "📈 数据库性能概览："
    echo "================================="
    
    # 基本统计信息
    local stats_query="
    SELECT 
        '数据库大小' as 指标,
        pg_size_pretty(pg_database_size('$DB_NAME')) as 值,
        '数据' as 类型
    UNION ALL
    SELECT 
        '活跃连接数' as 指标,
        count(*)::text as 值,
        '连接' as 类型
    FROM pg_stat_activity 
    WHERE state = 'active'
    UNION ALL
    SELECT 
        '总连接数' as 指标,
        count(*)::text as 值,
        '连接' as 类型
    FROM pg_stat_activity
    UNION ALL
    SELECT 
        '缓存命中率' as 指标,
        CASE 
            WHEN sum(blks_hit + blks_read) > 0 THEN 
                round(sum(blks_hit)*100.0/sum(blks_hit+blks_read), 2)::text || '%'
            ELSE 'N/A'
        END as 值,
        '性能' as 类型
    FROM pg_stat_database
    WHERE datname = '$DB_NAME'
    UNION ALL
    SELECT 
        '事务提交数' as 指标,
        xact_commit::text as 值,
        '事务' as 类型
    FROM pg_stat_database
    WHERE datname = '$DB_NAME'
    UNION ALL
    SELECT 
        '事务回滚数' as 指标,
        xact_rollback::text as 值,
        '事务' as 类型
    FROM pg_stat_database
    WHERE datname = '$DB_NAME';
    "
    
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "$stats_query" 2>/dev/null
}

# 检查表空间使用情况
check_table_sizes() {
    log_info "💾 检查表空间使用情况..."
    
    echo "📊 表空间使用情况 (TOP 10)："
    echo "================================="
    
    local size_query="
    SELECT 
        schemaname as 模式,
        tablename as 表名,
        pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as 总大小,
        pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) as 表大小,
        pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename) - pg_relation_size(schemaname||'.'||tablename)) as 索引大小,
        pg_stat_get_tuples_inserted(c.oid) as 插入数,
        pg_stat_get_tuples_updated(c.oid) as 更新数,
        pg_stat_get_tuples_deleted(c.oid) as 删除数
    FROM pg_tables t
    JOIN pg_class c ON c.relname = t.tablename
    WHERE t.schemaname = 'public'
    ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
    LIMIT 10;
    "
    
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "$size_query" 2>/dev/null
}

# 数据库优化
optimize_database() {
    log_info "🔧 开始数据库优化..."
    
    # 更新统计信息
    log_info "📊 更新表统计信息..."
    show_progress 15 "分析数据库统计信息"
    
    if PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "ANALYZE;" >/dev/null 2>&1; then
        log_success "统计信息更新完成"
    else
        log_error "统计信息更新失败"
    fi
    
    # 清理死元组
    log_info "🧹 清理死元组..."
    show_progress 20 "执行数据库清理"
    
    if PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "VACUUM;" >/dev/null 2>&1; then
        log_success "死元组清理完成"
    else
        log_error "死元组清理失败"
    fi
    
    # 重建索引（如果需要）
    log_info "🔍 检查索引状态..."
    local index_query="
    SELECT 
        schemaname,
        tablename,
        attname,
        n_distinct,
        correlation
    FROM pg_stats 
    WHERE schemaname = 'public' 
    AND n_distinct < 100 
    AND correlation < 0.1
    LIMIT 5;
    "
    
    local index_result=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "$index_query" 2>/dev/null)
    
    if [ -n "$index_result" ]; then
        log_warn "发现一些索引可能需要重建，请检查性能"
    else
        log_success "索引状态正常"
    fi
    
    log_success "数据库优化完成"
}

# 检查慢查询
check_slow_queries() {
    log_info "🐌 检查慢查询..."
    
    echo "⚡ 慢查询统计："
    echo "================================="
    
    # 检查是否启用了pg_stat_statements
    local ext_check=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT 1 FROM pg_extension WHERE extname = 'pg_stat_statements';" 2>/dev/null)
    
    if [ -z "$ext_check" ]; then
        log_warn "pg_stat_statements扩展未启用，无法查看慢查询统计"
        echo "建议在postgresql.conf中启用：shared_preload_libraries = 'pg_stat_statements'"
        return 1
    fi
    
    local slow_query="
    SELECT 
        left(query, 60) as 查询,
        calls as 调用次数,
        round(total_exec_time::numeric, 2) as 总耗时_ms,
        round(mean_exec_time::numeric, 2) as 平均耗时_ms,
        rows as 影响行数
    FROM pg_stat_statements 
    WHERE mean_exec_time > 1000
    ORDER BY mean_exec_time DESC 
    LIMIT 10;
    "
    
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "$slow_query" 2>/dev/null
}

# 增强的健康检查
health_check() {
    log_info "🏥 执行数据库健康检查..."
    
    local health_score=100
    local issues=()
    
    # 检查数据库连接
    if ! check_db_connection; then
        log_error "❌ 数据库连接异常"
        health_score=$((health_score - 50))
        issues+=("数据库连接失败")
        return 1
    fi
    
    # 检查磁盘空间
    local disk_usage=$(df "$BACKUP_DIR" | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ "$disk_usage" -gt 90 ]; then
        log_error "❌ 磁盘空间严重不足：${disk_usage}%"
        health_score=$((health_score - 30))
        issues+=("磁盘空间不足")
    elif [ "$disk_usage" -gt 80 ]; then
        log_warn "⚠️  磁盘空间紧张：${disk_usage}%"
        health_score=$((health_score - 10))
        issues+=("磁盘空间紧张")
    else
        log_success "✅ 磁盘使用率正常：${disk_usage}%"
    fi
    
    # 检查连接数
    local connection_query="SELECT count(*) FROM pg_stat_activity;"
    local connections=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "$connection_query" 2>/dev/null | xargs)
    
    if [ "$connections" -gt 180 ]; then
        log_error "❌ 连接数过高：$connections"
        health_score=$((health_score - 20))
        issues+=("连接数过高")
    elif [ "$connections" -gt 150 ]; then
        log_warn "⚠️  连接数较高：$connections"
        health_score=$((health_score - 5))
        issues+=("连接数偏高")
    else
        log_success "✅ 连接数正常：$connections"
    fi
    
    # 检查长时间运行的查询
    local long_queries=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT count(*) FROM pg_stat_activity WHERE state = 'active' AND now() - query_start > interval '5 minutes';" 2>/dev/null | xargs)
    
    if [ "$long_queries" -gt 0 ]; then
        log_warn "⚠️  发现 $long_queries 个长时间运行的查询"
        health_score=$((health_score - 5))
        issues+=("存在长时间运行的查询")
    else
        log_success "✅ 没有长时间运行的查询"
    fi
    
    # 检查锁冲突
    local lock_conflicts=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT count(*) FROM pg_locks WHERE NOT granted;" 2>/dev/null | xargs)
    
    if [ "$lock_conflicts" -gt 0 ]; then
        log_warn "⚠️  发现 $lock_conflicts 个锁冲突"
        health_score=$((health_score - 10))
        issues+=("存在锁冲突")
    else
        log_success "✅ 没有锁冲突"
    fi
    
    # 检查表膨胀
    local bloat_query="
    SELECT count(*) FROM (
        SELECT 
            schemaname,
            tablename,
            pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
        FROM pg_tables 
        WHERE schemaname = 'public'
        AND pg_total_relation_size(schemaname||'.'||tablename) > 100*1024*1024
    ) AS large_tables;
    "
    
    local large_tables=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "$bloat_query" 2>/dev/null | xargs)
    
    if [ "$large_tables" -gt 10 ]; then
        log_warn "⚠️  发现 $large_tables 个大表，可能存在膨胀"
        health_score=$((health_score - 5))
        issues+=("可能存在表膨胀")
    fi
    
    # 输出健康评分
    echo
    echo "🏥 健康检查结果："
    echo "================================="
    echo "健康评分: $health_score/100"
    
    if [ $health_score -ge 90 ]; then
        log_success "✅ 数据库健康状况：优秀"
    elif [ $health_score -ge 70 ]; then
        log_warn "⚠️  数据库健康状况：良好"
    elif [ $health_score -ge 50 ]; then
        log_warn "⚠️  数据库健康状况：一般"
    else
        log_error "❌ 数据库健康状况：差"
    fi
    
    if [ ${#issues[@]} -gt 0 ]; then
        echo "发现的问题："
        for issue in "${issues[@]}"; do
            echo "  - $issue"
        done
    fi
    
    echo
    return 0
}

# 生成增强的维护报告
generate_report() {
    local report_file="$LOG_DIR/maintenance_report_$(date +%Y%m%d_%H%M%S).txt"
    
    log_info "📋 生成维护报告..."
    
    mkdir -p "$LOG_DIR"
    
    {
        echo "========================================"
        echo "企业文档加密系统 - 数据库维护报告"
        echo "========================================"
        echo "生成时间: $(date)"
        echo "数据库: $DB_NAME"
        echo "主机: $DB_HOST:$DB_PORT"
        echo "用户: $DB_USER"
        echo "========================================"
        echo
        
        echo "1. 数据库健康检查"
        echo "----------------------------------------"
        health_check
        echo
        
        echo "2. 数据库性能统计"
        echo "----------------------------------------"
        get_db_stats
        echo
        
        echo "3. 表空间使用情况"
        echo "----------------------------------------"
        check_table_sizes
        echo
        
        echo "4. 慢查询统计"
        echo "----------------------------------------"
        check_slow_queries
        echo
        
        echo "5. 备份状态"
        echo "----------------------------------------"
        if [ -d "$BACKUP_DIR" ]; then
            local backup_count=$(find "$BACKUP_DIR" -name "postgres_backup_*.sql*" -type f | wc -l)
            local backup_size=$(du -sh "$BACKUP_DIR" 2>/dev/null | cut -f1)
            echo "备份文件数量: $backup_count"
            echo "备份目录大小: $backup_size"
            echo "最新备份: $(ls -t "$BACKUP_DIR"/postgres_backup_*.sql* 2>/dev/null | head -1 | xargs basename 2>/dev/null || echo '无')"
        else
            echo "备份目录不存在"
        fi
        echo
        
        echo "6. 系统资源状态"
        echo "----------------------------------------"
        echo "CPU使用率: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)"
        echo "内存使用率: $(free | grep Mem | awk '{printf("%.2f%%", $3/$2 * 100.0)}')"
        echo "磁盘使用率: $(df -h "$BACKUP_DIR" | awk 'NR==2 {print $5}')"
        echo
        
        echo "========================================"
        echo "报告生成完成"
        echo "========================================"
    } > "$report_file"
    
    log_success "维护报告已生成: $report_file"
    
    # 如果配置了邮件通知，发送报告
    if [ "${ALERT_EMAIL_ENABLED:-false}" = "true" ] && [ -n "${ALERT_EMAIL_RECIPIENTS:-}" ]; then
        send_email_report "$report_file"
    fi
}

# 发送邮件报告
send_email_report() {
    local report_file="$1"
    
    if command -v mail >/dev/null 2>&1; then
        log_info "📧 发送维护报告邮件..."
        
        local subject="数据库维护报告 - $(date +%Y-%m-%d)"
        
        if mail -s "$subject" "$ALERT_EMAIL_RECIPIENTS" < "$report_file"; then
            log_success "邮件发送成功"
        else
            log_error "邮件发送失败"
        fi
    else
        log_warn "mail命令不可用，无法发送邮件报告"
    fi
}

# 数据库性能分析
performance_analysis() {
    log_info "🔍 执行数据库性能分析..."
    
    echo "🚀 性能分析报告："
    echo "================================="
    
    # 查询性能统计
    echo "📊 查询性能统计："
    local query_stats="
    SELECT 
        'SELECT' as 查询类型,
        sum(calls) as 调用次数,
        round(sum(total_exec_time)::numeric, 2) as 总耗时_ms,
        round(avg(mean_exec_time)::numeric, 2) as 平均耗时_ms
    FROM pg_stat_statements 
    WHERE query LIKE 'SELECT%'
    UNION ALL
    SELECT 
        'INSERT' as 查询类型,
        sum(calls) as 调用次数,
        round(sum(total_exec_time)::numeric, 2) as 总耗时_ms,
        round(avg(mean_exec_time)::numeric, 2) as 平均耗时_ms
    FROM pg_stat_statements 
    WHERE query LIKE 'INSERT%'
    UNION ALL
    SELECT 
        'UPDATE' as 查询类型,
        sum(calls) as 调用次数,
        round(sum(total_exec_time)::numeric, 2) as 总耗时_ms,
        round(avg(mean_exec_time)::numeric, 2) as 平均耗时_ms
    FROM pg_stat_statements 
    WHERE query LIKE 'UPDATE%'
    UNION ALL
    SELECT 
        'DELETE' as 查询类型,
        sum(calls) as 调用次数,
        round(sum(total_exec_time)::numeric, 2) as 总耗时_ms,
        round(avg(mean_exec_time)::numeric, 2) as 平均耗时_ms
    FROM pg_stat_statements 
    WHERE query LIKE 'DELETE%';
    "
    
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "$query_stats" 2>/dev/null || log_warn "pg_stat_statements扩展未启用"
    
    echo
    echo "📈 索引使用统计："
    local index_stats="
    SELECT 
        schemaname as 模式,
        tablename as 表名,
        indexname as 索引名,
        idx_scan as 扫描次数,
        idx_tup_read as 读取行数,
        idx_tup_fetch as 获取行数
    FROM pg_stat_user_indexes 
    WHERE idx_scan > 0
    ORDER BY idx_scan DESC
    LIMIT 10;
    "
    
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "$index_stats" 2>/dev/null
    
    echo
    echo "🔄 I/O统计："
    local io_stats="
    SELECT 
        'heap_blks_read' as 指标,
        sum(heap_blks_read) as 值,
        '磁盘读取块数' as 描述
    FROM pg_stat_user_tables
    UNION ALL
    SELECT 
        'heap_blks_hit' as 指标,
        sum(heap_blks_hit) as 值,
        '缓存命中块数' as 描述
    FROM pg_stat_user_tables
    UNION ALL
    SELECT 
        'idx_blks_read' as 指标,
        sum(idx_blks_read) as 值,
        '索引磁盘读取块数' as 描述
    FROM pg_stat_user_tables
    UNION ALL
    SELECT 
        'idx_blks_hit' as 指标,
        sum(idx_blks_hit) as 值,
        '索引缓存命中块数' as 描述
    FROM pg_stat_user_tables;
    "
    
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "$io_stats" 2>/dev/null
}

# 显示增强的帮助信息
show_help() {
    echo "企业文档加密系统 - PostgreSQL数据库维护脚本"
    echo
    echo "用法: $0 [命令] [参数]"
    echo
    echo "🔧 核心维护命令:"
    echo "  backup              执行数据库备份"
    echo "  cleanup [days]      清理旧备份（默认${RETENTION_DAYS}天）"
    echo "  optimize            数据库优化（ANALYZE + VACUUM）"
    echo "  full                执行完整维护流程"
    echo
    echo "📊 分析监控命令:"
    echo "  stats               显示数据库统计信息"
    echo "  health              执行健康检查"
    echo "  performance         性能分析"
    echo "  slowquery           慢查询检查"
    echo "  report              生成维护报告"
    echo
    echo "🛠️  系统命令:"
    echo "  help                显示帮助信息"
    echo "  version             显示版本信息"
    echo
    echo "📋 使用示例:"
    echo "  $0 backup           # 备份数据库"
    echo "  $0 cleanup 7        # 清理7天前的备份"
    echo "  $0 full             # 执行完整维护"
    echo "  $0 health           # 健康检查"
    echo "  $0 performance      # 性能分析"
    echo "  $0 report           # 生成维护报告"
    echo
    echo "🎯 专注支持: PostgreSQL 数据库系统"
    echo "📧 配置邮件通知: 在 .env 文件中设置 ALERT_EMAIL_ENABLED=true"
}

# 显示版本信息
show_version() {
    echo "企业文档加密系统 - 数据库维护脚本"
    echo "版本: 1.4.0"
    echo "作者: 数据库管理员"
    echo "更新时间: 2025-01-20"
    echo "支持数据库: PostgreSQL"
    echo "功能: 自动化数据库维护、监控、备份、性能分析"
}

# 执行完整维护
full_maintenance() {
    log_info "🚀 开始执行完整维护..."
    
    local start_time=$(date +%s)
    
    echo "📋 完整维护包含以下步骤："
    echo "  1. 健康检查"
    echo "  2. 数据库备份"
    echo "  3. 清理旧备份"
    echo "  4. 数据库优化"
    echo "  5. 性能分析"
    echo "  6. 生成维护报告"
    echo
    
    # 步骤1: 健康检查
    log_info "📋 步骤 1/6: 执行健康检查..."
    if ! health_check; then
        log_error "健康检查发现严重问题，建议先解决问题再进行维护"
        read -p "是否继续执行维护? (y/N): " confirm
        if [[ ! $confirm =~ ^[Yy]$ ]]; then
            log_info "维护已取消"
            return 1
        fi
    fi
    
    # 步骤2: 数据库备份
    log_info "📋 步骤 2/6: 执行数据库备份..."
    if ! backup_database; then
        log_error "备份失败，终止维护"
        return 1
    fi
    
    # 步骤3: 清理旧备份
    log_info "📋 步骤 3/6: 清理旧备份..."
    cleanup_old_backups
    
    # 步骤4: 数据库优化
    log_info "📋 步骤 4/6: 执行数据库优化..."
    optimize_database
    
    # 步骤5: 性能分析
    log_info "📋 步骤 5/6: 执行性能分析..."
    performance_analysis
    
    # 步骤6: 生成维护报告
    log_info "📋 步骤 6/6: 生成维护报告..."
    generate_report
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    log_success "🎉 完整维护执行完成！"
    log_info "⏱️  总耗时: ${duration}秒"
    
    echo
    echo "📊 维护摘要："
    echo "================================="
    echo "开始时间: $(date -d @$start_time)"
    echo "结束时间: $(date -d @$end_time)"
    echo "总耗时: ${duration}秒"
    echo "维护日志: $MAINTENANCE_LOG"
    echo "================================="
}

# 主函数
main() {
    # 记录脚本开始执行
    log_info "启动数据库维护脚本，命令: ${1:-help}"
    
    case "${1:-help}" in
        backup)
            backup_database
            ;;
        cleanup)
            cleanup_old_backups "$2"
            ;;
        optimize)
            optimize_database
            ;;
        stats)
            get_db_stats
            ;;
        health)
            health_check
            ;;
        performance)
            performance_analysis
            ;;
        slowquery)
            check_slow_queries
            ;;
        report)
            generate_report
            ;;
        full)
            full_maintenance
            ;;
        version)
            show_version
            ;;
        help)
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            echo
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@" 