#pragma once

#include <string>
#include <fstream>
#include <iostream>
#include <mutex>
#include <chrono>
#include <iomanip>
#include <sstream>

/**
 * 简单的日志类，支持不同级别的日志记录
 */
class Logger {
public:
    enum LogLevel {
        DEBUG,
        INFO,
        WARNING,
        ERROR,
        CRITICAL
    };

    /**
     * 设置日志级别
     * @param level 日志级别
     */
    static void setLogLevel(LogLevel level);

    /**
     * 设置日志文件
     * @param filename 日志文件名
     * @return 是否成功
     */
    static bool setLogFile(const std::string& filename);

    /**
     * 记录调试信息
     * @param message 日志消息
     */
    static void debug(const std::string& message);

    /**
     * 记录普通信息
     * @param message 日志消息
     */
    static void info(const std::string& message);

    /**
     * 记录警告信息
     * @param message 日志消息
     */
    static void warning(const std::string& message);

    /**
     * 记录错误信息
     * @param message 日志消息
     */
    static void error(const std::string& message);

    /**
     * 记录严重错误信息
     * @param message 日志消息
     */
    static void critical(const std::string& message);

    /**
     * 关闭日志系统
     */
    static void shutdown();

private:
    static LogLevel s_level;
    static std::ofstream s_logFile;
    static std::mutex s_mutex;
    static bool s_toConsole;
    static bool s_toFile;

    /**
     * 内部日志记录方法
     * @param level 日志级别
     * @param message 日志消息
     */
    static void log(LogLevel level, const std::string& message);

    /**
     * 获取当前时间字符串
     * @return 格式化的时间字符串
     */
    static std::string getCurrentTimeString();

    /**
     * 获取日志级别字符串
     * @param level 日志级别
     * @return 日志级别字符串
     */
    static std::string getLevelString(LogLevel level);
}; 