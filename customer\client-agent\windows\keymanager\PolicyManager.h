﻿#pragma once

#include <string>
#include <vector>
#include <map>
#include <mutex>
#include <memory>
#include <functional>

// 前向声明
struct AuditEvent;

// 策略类型枚举
enum class PolicyType {
    FILE_PATH,      // 基于文件路径
    FILE_TYPE,      // 基于文件类型
    APPLICATION,    // 基于应用程序
    USER,           // 基于用户
    ORGANIZATION,   // 基于组织/部门
    LOCATION,       // 基于位置
    NETWORK,        // 基于网络状态
    USB_DEVICE,     // 基于USB设备
    PERIPHERAL,     // 基于其他外设
    CUSTOM          // 自定义策略
};

// 策略操作枚举
enum class PolicyAction {
    ENCRYPT,        // 加密
    DECRYPT,        // 解密
    DENY,           // 拒绝访问
    ALLOW,          // 允许访问
    AUDIT,          // 仅审计
    NOTIFY,         // 通知用户
    READ_ONLY,      // 只读访问
    FORCE_ENCRYPT   // 强制加密
};

// 外设类型枚举
enum class PeripheralType {
    USB_STORAGE,    // USB存储设备
    USB_PRINTER,    // USB打印机
    USB_HID,        // USB人机接口设备
    USB_OTHER,      // 其他USB设备
    BLUETOOTH,      // 蓝牙设备
    NETWORK_ADAPTER,// 网络适配器
    COM_PORT,       // COM端口
    OTHER           // 其他外设
};

// 外设信息结构
struct PeripheralInfo {
    std::string id;                // 设备唯一标识
    std::string name;              // 设备名称
    PeripheralType type;           // 设备类型
    std::string vendorId;          // 供应商ID
    std::string productId;         // 产品ID
    std::string serialNumber;      // 序列号
    std::string hardwareId;        // 硬件ID
    std::map<std::string, std::string> attributes; // 其他设备属性
};

// 策略规则结构
struct PolicyRule {
    std::string id;
    std::string name;
    PolicyType type;
    std::string pattern;         // 匹配模式（如文件路径通配符）
    PolicyAction action;
    int priority;                // 优先级，数字越大优先级越高
    std::string algorithm;       // 加密算法（当action为ENCRYPT时）
    std::map<std::string, std::string> parameters; // 附加参数
};

// 策略结构
struct Policy {
    std::string id;
    std::string name;
    std::string description;
    bool enabled;
    std::string organizationId;
    std::string createdBy;
    std::string createdAt;       // ISO 8601 格式
    std::string modifiedAt;      // ISO 8601 格式
    std::vector<PolicyRule> rules;
};

// 策略评估结果
struct PolicyEvaluationResult {
    bool shouldEncrypt;
    bool shouldDecrypt;
    bool shouldDeny;
    bool shouldAudit;
    bool shouldNotify;
    bool readOnly;               // 只读访问标志
    bool forceEncrypt;           // 强制加密标志
    std::string appliedPolicyId;
    std::string appliedRuleId;
    std::string encryptionAlgorithm;
    std::map<std::string, std::string> parameters;
};

// 策略管理器类
class PolicyManager {
public:
    // 单例访问
    static PolicyManager& getInstance();
    
    // 初始化与关闭
    bool initialize();
    void shutdown();
    
    // 策略加载与更新
    bool loadPolicies();
    bool savePolicies();
    bool syncPoliciesFromServer();
    
    // 策略管理
    bool addPolicy(const Policy& policy);
    bool updatePolicy(const Policy& policy);
    bool removePolicy(const std::string& policyId);
    bool enablePolicy(const std::string& policyId, bool enable);
    
    // 策略评估
    PolicyEvaluationResult evaluateFilePolicy(const std::wstring& filePath, 
                                              const std::string& userId = "",
                                              const std::string& appPath = "");
    
    // 外设管理相关方法
    std::vector<PeripheralInfo> getConnectedPeripherals();
    PeripheralInfo getPeripheralInfo(const std::string& deviceId);
    PolicyEvaluationResult evaluatePeripheralPolicy(const std::string& deviceId, 
                                                  const std::string& userId = "");
    bool isPeripheralAllowed(const std::string& deviceId, 
                             const std::string& userId = "");
    bool blockPeripheral(const std::string& deviceId);
    bool allowPeripheral(const std::string& deviceId, PolicyAction action = PolicyAction::ALLOW);
    bool addPeripheralPolicy(const std::string& deviceId, PolicyAction action,
                             int priority = 10, bool createPolicy = true);
    
    // 审计功能（与KeyManager协作）
    void logPolicyEvent(const std::string& policyId, 
                        const std::string& userId, 
                        const std::string& action,
                        const std::string& resourceId,
                        const std::string& result,
                        const std::string& details = "");
    
    // 策略查询
    std::shared_ptr<Policy> getPolicy(const std::string& policyId);
    std::vector<std::shared_ptr<Policy>> getAllPolicies();
    std::vector<std::shared_ptr<Policy>> getPoliciesByType(PolicyType type);
    
    // 回调注册
    using AuditEventCallback = std::function<void(const AuditEvent&)>;
    void setAuditEventCallback(AuditEventCallback callback);
    
private:
    PolicyManager(); // 私有构造函数(单例模式)
    ~PolicyManager();
    
    // 禁止复制
    PolicyManager(const PolicyManager&) = delete;
    PolicyManager& operator=(const PolicyManager&) = delete;
    
    // 内部方法
    bool loadPoliciesFromStorage();
    bool savePolicyToStorage(const Policy& policy);
    std::vector<PolicyRule> getApplicableRules(const std::wstring& filePath, 
                                              const std::string& userId,
                                              const std::string& appPath);
    std::vector<PolicyRule> getApplicablePeripheralRules(const std::string& deviceId,
                                                       const std::string& userId);
    PolicyRule selectHighestPriorityRule(const std::vector<PolicyRule>& rules);
    bool matchesPattern(const std::wstring& filePath, const std::string& pattern);
    bool matchesPeripheralPattern(const PeripheralInfo& peripheral, const std::string& pattern);
    void createSamplePolicies();
    PeripheralType determinePeripheralType(const std::string& deviceId);
    
    // 数据成员
    std::map<std::string, std::shared_ptr<Policy>> m_policies;
    std::map<std::string, PeripheralInfo> m_peripheralCache;
    std::mutex m_policiesMutex;
    std::mutex m_peripheralMutex;
    AuditEventCallback m_auditCallback;
    bool m_initialized;
}; 