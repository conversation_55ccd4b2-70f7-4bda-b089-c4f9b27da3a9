import Foundation
import CommonCrypto
import Security

/// 加密算法类型
enum EncryptionAlgorithm {
    case aesGCM  // AES-GCM 模式
    case sm4GCM  // SM4-GCM 模式 (通过自定义实现)
}

/// 加密状态码
enum CryptoResult {
    case success
    case invalidParameters
    case cryptoFailed
    case outOfMemory
    case unauthorized
    case keyNotFound
    case algorithmNotSupported
}

/// 加密上下文
class CryptoContext {
    let algorithm: EncryptionAlgorithm
    let keyVersion: UInt32
    let key: Data
    
    init(algorithm: EncryptionAlgorithm, keyVersion: UInt32, key: Data) {
        self.algorithm = algorithm
        self.keyVersion = keyVersion
        self.key = key
    }
}

/// 文件头结构
struct FileHeader {
    let signature: Data       // 签名 "ENCF"
    let version: UInt32       // 版本号
    let keyVersion: UInt32    // 密钥版本
    let algorithm: UInt8      // 算法类型
    let mode: UInt8           // 加密模式
    let originalFileSize: UInt64  // 原始文件大小
    let checksum: UInt32      // 校验和
    let reserved: Data        // 保留字段
    
    init() {
        signature = "ENCF".data(using: .ascii)!
        version = 1
        keyVersion = 0
        algorithm = 0
        mode = 0
        originalFileSize = 0
        checksum = 0
        reserved = Data(count: 16)
    }
    
    init?(data: Data) {
        guard data.count >= 36 else { return nil }
        
        let reader = DataReader(data: data)
        
        guard let sig = reader.readData(length: 4),
              let ver = reader.readUInt32(),
              let keyVer = reader.readUInt32(),
              let alg = reader.readUInt8(),
              let md = reader.readUInt8(),
              let size = reader.readUInt64(),
              let check = reader.readUInt32(),
              let res = reader.readData(length: 16) else {
            return nil
        }
        
        // 验证签名
        guard let sigStr = String(data: sig, encoding: .ascii), sigStr == "ENCF" else {
            return nil
        }
        
        signature = sig
        version = ver
        keyVersion = keyVer
        algorithm = alg
        mode = md
        originalFileSize = size
        checksum = check
        reserved = res
    }
    
    func toData() -> Data {
        var result = Data()
        result.append(signature)
        result.append(withUnsafeBytes(of: version.littleEndian) { Data($0) })
        result.append(withUnsafeBytes(of: keyVersion.littleEndian) { Data($0) })
        result.append(withUnsafeBytes(of: algorithm) { Data($0) })
        result.append(withUnsafeBytes(of: mode) { Data($0) })
        result.append(withUnsafeBytes(of: originalFileSize.littleEndian) { Data($0) })
        result.append(withUnsafeBytes(of: checksum.littleEndian) { Data($0) })
        result.append(reserved)
        
        return result
    }
}

/// 数据读取辅助类
private class DataReader {
    let data: Data
    var offset: Int = 0
    
    init(data: Data) {
        self.data = data
    }
    
    func readData(length: Int) -> Data? {
        guard offset + length <= data.count else { return nil }
        let range = offset..<(offset + length)
        offset += length
        return data.subdata(in: range)
    }
    
    func readUInt8() -> UInt8? {
        guard offset + 1 <= data.count else { return nil }
        let byte = data[offset]
        offset += 1
        return byte
    }
    
    func readUInt32() -> UInt32? {
        guard let bytes = readData(length: 4) else { return nil }
        return bytes.withUnsafeBytes { $0.load(as: UInt32.self).littleEndian }
    }
    
    func readUInt64() -> UInt64? {
        guard let bytes = readData(length: 8) else { return nil }
        return bytes.withUnsafeBytes { $0.load(as: UInt64.self).littleEndian }
    }
}

/// 加密管理器
class CryptoManager {
    // 单例
    static let shared = CryptoManager()
    private let gcmTagLength = 16
    private let gcmIVLength = 12
    
    private init() {
        // 私有初始化
    }
    
    /// 计算CRC32校验和
    func computeChecksum(data: Data) -> UInt32 {
        var crc: UInt32 = 0xFFFFFFFF
        
        // CRC32表
        let table: [UInt32] = [
            0x00000000, 0x77073096, 0xEE0E612C, 0x990951BA, 0x076DC419, 0x706AF48F, 0xE963A535, 0x9E6495A3,
            0x0EDB8832, 0x79DCB8A4, 0xE0D5E91E, 0x97D2D988, 0x09B64C2B, 0x7EB17CBD, 0xE7B82D07, 0x90BF1D91,
            0x1DB71064, 0x6AB020F2, 0xF3B97148, 0x84BE41DE, 0x1ADAD47D, 0x6DDDE4EB, 0xF4D4B551, 0x83D385C7,
            0x136C9856, 0x646BA8C0, 0xFD62F97A, 0x8A65C9EC, 0x14015C4F, 0x63066CD9, 0xFA0F3D63, 0x8D080DF5,
            0x3B6E20C8, 0x4C69105E, 0xD56041E4, 0xA2677172, 0x3C03E4D1, 0x4B04D447, 0xD20D85FD, 0xA50AB56B,
            0x35B5A8FA, 0x42B2986C, 0xDBBBC9D6, 0xACBCF940, 0x32D86CE3, 0x45DF5C75, 0xDCD60DCF, 0xABD13D59,
            0x26D930AC, 0x51DE003A, 0xC8D75180, 0xBFD06116, 0x21B4F4B5, 0x56B3C423, 0xCFBA9599, 0xB8BDA50F,
            0x2802B89E, 0x5F058808, 0xC60CD9B2, 0xB10BE924, 0x2F6F7C87, 0x58684C11, 0xC1611DAB, 0xB6662D3D,
            0x76DC4190, 0x01DB7106, 0x98D220BC, 0xEFD5102A, 0x71B18589, 0x06B6B51F, 0x9FBFE4A5, 0xE8B8D433,
            0x7807C9A2, 0x0F00F934, 0x9609A88E, 0xE10E9818, 0x7F6A0DBB, 0x086D3D2D, 0x91646C97, 0xE6635C01,
            0x6B6B51F4, 0x1C6C6162, 0x856530D8, 0xF262004E, 0x6C0695ED, 0x1B01A57B, 0x8208F4C1, 0xF50FC457,
            0x65B0D9C6, 0x12B7E950, 0x8BBEB8EA, 0xFCB9887C, 0x62DD1DDF, 0x15DA2D49, 0x8CD37CF3, 0xFBD44C65,
            0x4DB26158, 0x3AB551CE, 0xA3BC0074, 0xD4BB30E2, 0x4ADFA541, 0x3DD895D7, 0xA4D1C46D, 0xD3D6F4FB,
            0x4369E96A, 0x346ED9FC, 0xAD678846, 0xDA60B8D0, 0x44042D73, 0x33031DE5, 0xAA0A4C5F, 0xDD0D7CC9,
            0x5005713C, 0x270241AA, 0xBE0B1010, 0xC90C2086, 0x5768B525, 0x206F85B3, 0xB966D409, 0xCE61E49F,
            0x5EDEF90E, 0x29D9C998, 0xB0D09822, 0xC7D7A8B4, 0x59B33D17, 0x2EB40D81, 0xB7BD5C3B, 0xC0BA6CAD,
            0xEDB88320, 0x9ABFB3B6, 0x03B6E20C, 0x74B1D29A, 0xEAD54739, 0x9DD277AF, 0x04DB2615, 0x73DC1683,
            0xE3630B12, 0x94643B84, 0x0D6D6A3E, 0x7A6A5AA8, 0xE40ECF0B, 0x9309FF9D, 0x0A00AE27, 0x7D079EB1,
            0xF00F9344, 0x8708A3D2, 0x1E01F268, 0x6906C2FE, 0xF762575D, 0x806567CB, 0x196C3671, 0x6E6B06E7,
            0xFED41B76, 0x89D32BE0, 0x10DA7A5A, 0x67DD4ACC, 0xF9B9DF6F, 0x8EBEEFF9, 0x17B7BE43, 0x60B08ED5,
            0xD6D6A3E8, 0xA1D1937E, 0x38D8C2C4, 0x4FDFF252, 0xD1BB67F1, 0xA6BC5767, 0x3FB506DD, 0x48B2364B,
            0xD80D2BDA, 0xAF0A1B4C, 0x36034AF6, 0x41047A60, 0xDF60EFC3, 0xA867DF55, 0x316E8EEF, 0x4669BE79,
            0xCB61B38C, 0xBC66831A, 0x256FD2A0, 0x5268E236, 0xCC0C7795, 0xBB0B4703, 0x220216B9, 0x5505262F,
            0xC5BA3BBE, 0xB2BD0B28, 0x2BB45A92, 0x5CB36A04, 0xC2D7FFA7, 0xB5D0CF31, 0x2CD99E8B, 0x5BDEAE1D,
            0x9B64C2B0, 0xEC63F226, 0x756AA39C, 0x026D930A, 0x9C0906A9, 0xEB0E363F, 0x72076785, 0x05005713,
            0x95BF4A82, 0xE2B87A14, 0x7BB12BAE, 0x0CB61B38, 0x92D28E9B, 0xE5D5BE0D, 0x7CDCEFB7, 0x0BDBDF21,
            0x86D3D2D4, 0xF1D4E242, 0x68DDB3F8, 0x1FDA836E, 0x81BE16CD, 0xF6B9265B, 0x6FB077E1, 0x18B74777,
            0x88085AE6, 0xFF0F6A70, 0x66063BCA, 0x11010B5C, 0x8F659EFF, 0xF862AE69, 0x616BFFD3, 0x166CCF45,
            0xA00AE278, 0xD70DD2EE, 0x4E048354, 0x3903B3C2, 0xA7672661, 0xD06016F7, 0x4969474D, 0x3E6E77DB,
            0xAED16A4A, 0xD9D65ADC, 0x40DF0B66, 0x37D83BF0, 0xA9BCAE53, 0xDEBB9EC5, 0x47B2CF7F, 0x30B5FFE9,
            0xBDBDF21C, 0xCABAC28A, 0x53B39330, 0x24B4A3A6, 0xBAD03605, 0xCDD70693, 0x54DE5729, 0x23D967BF,
            0xB3667A2E, 0xC4614AB8, 0x5D681B02, 0x2A6F2B94, 0xB40BBE37, 0xC30C8EA1, 0x5A05DF1B, 0x2D02EF8D
        ]
        
        for byte in data {
            crc = table[Int((crc ^ UInt32(byte)) & 0xFF)] ^ (crc >> 8)
        }
        
        return crc ^ 0xFFFFFFFF
    }
    
    /// 创建加密上下文
    func createContext(algorithm: EncryptionAlgorithm, keyVersion: UInt32) -> (context: CryptoContext?, result: CryptoResult) {
        // 从密钥管理器获取密钥
        guard let keyData = KeyManager.shared.getKey(version: keyVersion) else {
            return (nil, .keyNotFound)
        }
        
        let context = CryptoContext(algorithm: algorithm, keyVersion: keyVersion, key: keyData)
        return (context, .success)
    }
    
    /// 释放加密上下文
    func destroyContext(context: CryptoContext?) {
        // Swift 的 ARC 会自动处理内存
    }
    
    /// AES-GCM 加密
    private func aesGCMEncrypt(context: CryptoContext, plainText: Data) -> (cipherText: Data?, result: CryptoResult) {
        // 生成随机IV
        var iv = Data(count: gcmIVLength)
        let result = iv.withUnsafeMutableBytes { ivBytes in
            SecRandomCopyBytes(kSecRandomDefault, gcmIVLength, ivBytes.baseAddress!)
        }
        
        if result != errSecSuccess {
            return (nil, .cryptoFailed)
        }
        
        // 准备加密参数
        let keyData = context.key
        var cryptoResult: CryptoResult = .success
        
        // 使用 CryptoKit 进行AES-GCM加密
        guard let cipher = encryptAESGCM(key: keyData, iv: iv, plaintext: plainText) else {
            return (nil, .cryptoFailed)
        }
        
        // 组装结果: IV + 密文 + 认证标签
        var result = Data()
        result.append(iv)           // IV
        result.append(cipher)       // 包含密文和标签
        
        return (result, cryptoResult)
    }
    
    /// AES-GCM 解密
    private func aesGCMDecrypt(context: CryptoContext, cipherText: Data) -> (plainText: Data?, result: CryptoResult) {
        // 检查大小
        if cipherText.count < gcmIVLength + gcmTagLength {
            return (nil, .invalidParameters)
        }
        
        // 提取IV
        let iv = cipherText.prefix(gcmIVLength)
        
        // 提取密文+标签
        let encryptedData = cipherText.suffix(from: gcmIVLength)
        
        // 使用 CryptoKit 进行AES-GCM解密
        guard let plaintext = decryptAESGCM(key: context.key, iv: iv, ciphertext: encryptedData) else {
            return (nil, .cryptoFailed)
        }
        
        return (plaintext, .success)
    }
    
    /// 封装AES-GCM加密操作（可使用CryptoKit或CommonCrypto）
    private func encryptAESGCM(key: Data, iv: Data, plaintext: Data) -> Data? {
        #if canImport(CryptoKit)
        if #available(macOS 10.15, *) {
            // 使用CryptoKit（iOS 13.0+, macOS 10.15+）
            import CryptoKit
            
            do {
                let symmetricKey = SymmetricKey(data: key)
                let nonce = try AES.GCM.Nonce(data: iv)
                let sealedBox = try AES.GCM.seal(plaintext, using: symmetricKey, nonce: nonce)
                
                // 组合密文和认证标签
                return sealedBox.ciphertext + sealedBox.tag
            } catch {
                print("AES-GCM加密错误: \(error)")
                return nil
            }
        } else {
            // 降级到CommonCrypto实现
            // 注意：这里需要自行实现AES-GCM，因为老版本macOS的CommonCrypto没有直接的GCM支持
            return nil
        }
        #else
        // 降级到自定义实现
        return nil
        #endif
    }
    
    /// 封装AES-GCM解密操作
    private func decryptAESGCM(key: Data, iv: Data, ciphertext: Data) -> Data? {
        #if canImport(CryptoKit)
        if #available(macOS 10.15, *) {
            // 使用CryptoKit
            import CryptoKit
            
            do {
                let symmetricKey = SymmetricKey(data: key)
                let nonce = try AES.GCM.Nonce(data: iv)
                
                // 分离密文和认证标签
                let actualCiphertext = ciphertext.prefix(ciphertext.count - gcmTagLength)
                let tag = ciphertext.suffix(gcmTagLength)
                
                // 创建密封盒子
                let sealedBox = try AES.GCM.SealedBox(nonce: nonce, 
                                                     ciphertext: actualCiphertext, 
                                                     tag: tag)
                
                // 解密
                return try AES.GCM.open(sealedBox, using: symmetricKey)
            } catch {
                print("AES-GCM解密错误: \(error)")
                return nil
            }
        } else {
            // 降级到CommonCrypto实现
            return nil
        }
        #else
        // 降级到自定义实现
        return nil
        #endif
    }
    
    /// SM4-GCM 加密 (通过C API调用 client/common/crypto_utils)
    private func sm4GCMEncrypt(context: CryptoContext, plainText: Data) -> (cipherText: Data?, result: CryptoResult) {
        // 1. 获取密钥 (来自context)
        let keyData = context.key
        guard keyData.count == 16 else {
            print("[CryptoManager] [SM4_ENC_ERR] Invalid SM4 key size: \(keyData.count)")
            return (nil, .invalidParameters) // SM4 key must be 16 bytes
        }

        // 2. 生成随机IV (12 bytes for GCM)
        var ivData = Data(count: gcmIVLength) // gcmIVLength is 12
        let ivGenResult = ivData.withUnsafeMutableBytes { ivBytes in
            SecRandomCopyBytes(kSecRandomDefault, gcmIVLength, ivBytes.baseAddress!)
        }
        guard ivGenResult == errSecSuccess else {
            print("[CryptoManager] [SM4_ENC_ERR] Failed to generate IV for SM4-GCM")
            return (nil, .cryptoFailed)
        }

        // 3. 准备C API调用参数
        var outCombinedDataLen = ivData.count + plainText.count + gcmTagLength // 预估输出长度 IV + Ciphertext + Tag (16)
        var outCombinedData = Data(count: outCombinedDataLen)

        let cApiResult: Int32 = keyData.withUnsafeBytes { keyPtr in
            ivData.withUnsafeBytes { ivPtr in
                plainText.withUnsafeBytes { plainTextPtr in
                    outCombinedData.withUnsafeMutableBytes { outCombinedDataPtr in
                        sm4_gcm_encrypt_c_api(
                            plainTextPtr.baseAddress?.assumingMemoryBound(to: UInt8.self),
                            plainText.count,
                            keyPtr.baseAddress?.assumingMemoryBound(to: UInt8.self),
                            keyData.count,
                            ivPtr.baseAddress?.assumingMemoryBound(to: UInt8.self),
                            ivData.count,
                            outCombinedDataPtr.baseAddress?.assumingMemoryBound(to: UInt8.self),
                            &outCombinedDataLen
                        )
                    }
                }
            }
        }
        
        // 4. 处理C API结果
        switch cApiResult {
            case C_API_SUCCESS:
                if outCombinedDataLen > outCombinedData.count {
                     // This case should ideally be handled if C_API_ERROR_BUFFER_TOO_SMALL was returned
                     // and a second attempt with correct buffer size was made.
                     // For now, if p_out_combined_data_len > initial capacity, it implies an issue.
                    print("[CryptoManager] [SM4_ENC_ERR] Encrypted data length \(outCombinedDataLen) exceeds buffer capacity \(outCombinedData.count).")
                    return (nil, .cryptoFailed) // Or a more specific error
                }
                outCombinedData.count = outCombinedDataLen // 调整Data对象的实际大小
                return (outCombinedData, .success)
            case C_API_ERROR_INVALID_PARAM:
                print("[CryptoManager] [SM4_ENC_ERR] C API returned invalid parameter for encryption.")
                return (nil, .invalidParameters)
            case C_API_ERROR_BUFFER_TOO_SMALL: // C API告知缓冲区太小
                 print("[CryptoManager] [SM4_ENC_ERR] C API returned buffer too small. Required: \(outCombinedDataLen).")
                // 理想情况下，这里应该用 outCombinedDataLen 重新分配 outCombinedData 并重试
                // 为简化，暂时返回错误
                return (nil, .outOfMemory) // or a more specific error for buffer size
            case C_API_ERROR_ENCRYPTION_FAILED:
                print("[CryptoManager] [SM4_ENC_ERR] C API encryption failed.")
                return (nil, .cryptoFailed)
            default:
                print("[CryptoManager] [SM4_ENC_ERR] C API unknown error \(cApiResult) for encryption.")
                return (nil, .cryptoFailed)
        }
    }
    
    /// SM4-GCM 解密 (通过C API调用 client/common/crypto_utils)
    private func sm4GCMDecrypt(context: CryptoContext, cipherText: Data) -> (plainText: Data?, result: CryptoResult) {
        // 1. 获取密钥
        let keyData = context.key
        guard keyData.count == 16 else {
            print("[CryptoManager] [SM4_DEC_ERR] Invalid SM4 key size: \(keyData.count)")
            return (nil, .invalidParameters)
        }
        
        // 2. 检查输入数据长度 (至少需要 IV + Tag)
        let minLength = gcmIVLength + gcmTagLength
        guard cipherText.count >= minLength else {
            print("[CryptoManager] [SM4_DEC_ERR] Ciphertext too short for IV and Tag: \(cipherText.count)")
            return (nil, .invalidParameters)
        }
        
        // 3. 准备C API调用参数
        // 预估明文最大长度
        var outPlaintextLen = cipherText.count - gcmIVLength - gcmTagLength 
        // 即使明文为空，也分配1字节，避免0大小缓冲区的潜在问题，后续根据outPlaintextLen调整
        var outPlaintextData = Data(count: max(1, outPlaintextLen)) 
                                           
        let cApiResult: Int32 = cipherText.withUnsafeBytes { combinedDataPtr in
            keyData.withUnsafeBytes { keyPtr in
                outPlaintextData.withUnsafeMutableBytes { outPlaintextPtr in
                    sm4_gcm_decrypt_c_api(
                        combinedDataPtr.baseAddress?.assumingMemoryBound(to: UInt8.self),
                        cipherText.count,
                        keyPtr.baseAddress?.assumingMemoryBound(to: UInt8.self),
                        keyData.count,
                        outPlaintextPtr.baseAddress?.assumingMemoryBound(to: UInt8.self),
                        &outPlaintextLen
                    )
                }
            }
        }
        
        // 4. 处理C API结果
        switch cApiResult {
            case C_API_SUCCESS:
                if outPlaintextLen > outPlaintextData.count {
                     // Buffer too small case should ideally be handled by C_API_ERROR_BUFFER_TOO_SMALL
                    print("[CryptoManager] [SM4_DEC_ERR] Decrypted data length \(outPlaintextLen) exceeds buffer capacity \(outPlaintextData.count).")
                    return (nil, .cryptoFailed)
                }
                outPlaintextData.count = outPlaintextLen // 调整为实际大小
                return (outPlaintextData, .success)
            case C_API_ERROR_INVALID_PARAM:
                print("[CryptoManager] [SM4_DEC_ERR] C API returned invalid parameter for decryption.")
                return (nil, .invalidParameters)
            case C_API_ERROR_BUFFER_TOO_SMALL:
                 print("[CryptoManager] [SM4_DEC_ERR] C API returned buffer too small for plaintext. Required: \(outPlaintextLen).")
                // 理想情况下，这里应该用 outPlaintextLen 重新分配并重试
                return (nil, .outOfMemory)
            case C_API_ERROR_AUTH_FAILED:
                print("[CryptoManager] [SM4_DEC_ERR] C API authentication failed (tag mismatch).")
                return (nil, .cryptoFailed) // 认证失败是最常见的解密失败
            case C_API_ERROR_DECRYPTION_FAILED:
                print("[CryptoManager] [SM4_DEC_ERR] C API decryption failed (other error).")
                return (nil, .cryptoFailed)
            default:
                print("[CryptoManager] [SM4_DEC_ERR] C API unknown error \(cApiResult) for decryption.")
                return (nil, .cryptoFailed)
        }
    }
    
    /// 加密操作
    func encrypt(context: CryptoContext, plainText: Data) -> (cipherText: Data?, result: CryptoResult) {
        switch context.algorithm {
        case .aesGCM:
            return aesGCMEncrypt(context: context, plainText: plainText)
        case .sm4GCM:
            return sm4GCMEncrypt(context: context, plainText: plainText)
        }
    }
    
    /// 解密操作
    func decrypt(context: CryptoContext, cipherText: Data) -> (plainText: Data?, result: CryptoResult) {
        switch context.algorithm {
        case .aesGCM:
            return aesGCMDecrypt(context: context, cipherText: cipherText)
        case .sm4GCM:
            return sm4GCMDecrypt(context: context, cipherText: cipherText)
        }
    }
    
    /// 创建文件头
    func createFileHeader(context: CryptoContext, originalFileSize: UInt64) -> (header: FileHeader, result: CryptoResult) {
        var header = FileHeader()
        header.keyVersion = context.keyVersion
        
        // 设置算法和模式
        switch context.algorithm {
        case .aesGCM:
            header.algorithm = 0 // AES
            header.mode = 1      // GCM
        case .sm4GCM:
            header.algorithm = 1 // SM4
            header.mode = 1      // GCM
        }
        
        header.originalFileSize = originalFileSize
        
        // 计算校验和
        let headerData = header.toData()
        header.checksum = computeChecksum(data: headerData)
        
        return (header, .success)
    }
    
    /// 验证文件头
    func verifyFileHeader(headerData: Data) -> (header: FileHeader?, context: CryptoContext?, result: CryptoResult) {
        // 解析文件头
        guard let header = FileHeader(data: headerData) else {
            return (nil, nil, .invalidParameters)
        }
        
        // 获取算法类型
        let algorithm: EncryptionAlgorithm
        if header.algorithm == 0 && header.mode == 1 {
            algorithm = .aesGCM
        } else if header.algorithm == 1 && header.mode == 1 {
            algorithm = .sm4GCM
        } else {
            return (header, nil, .algorithmNotSupported)
        }
        
        // 创建加密上下文
        let contextResult = createContext(algorithm: algorithm, keyVersion: header.keyVersion)
        if contextResult.result != .success {
            return (header, nil, contextResult.result)
        }
        
        return (header, contextResult.context, .success)
    }
}

/// 密钥管理器（使用 Keychain）
class KeyManager {
    static let shared = KeyManager()
    private var keyCache: [UInt32: Data] = [:]
    private let keychainService: String

    private init() {
        // 使用 Bundle Identifier 作为 Keychain 服务名，确保唯一性
        if let bundleId = Bundle.main.bundleIdentifier {
            keychainService = bundleId
        } else {
            // 后备方案，但在实际应用中应确保 Bundle ID 存在
            keychainService = "com.example.cryptosystem.client"
            print("[KeyManager] [WARN] Bundle identifier not found, using default keychain service.")
        }
        // 初始化时尝试加载所有已知密钥
        // 注意：这里简化处理，实际可能需要管理一个密钥版本列表
        // 或者在 getKey 时按需加载
        loadAllKeysFromKeychain()
        print("[KeyManager] [INFO] KeyManager initialized. Loaded \(keyCache.count) keys from Keychain.")
    }

    // 将版本号转换为 Keychain 账户名
    private func accountName(for version: UInt32) -> String {
        return "key_version_\(version)"
    }

    // 保存密钥到 Keychain
    private func saveKeyToKeychain(version: UInt32, key: Data) -> Bool {
        let account = accountName(for: version)
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: keychainService,
            kSecAttrAccount as String: account,
            kSecValueData as String: key,
            kSecAttrAccessible as String: kSecAttrAccessibleWhenUnlockedThisDeviceOnly // 保护级别
        ]

        // 先尝试删除旧条目（如果存在）
        SecItemDelete(query as CFDictionary)

        // 添加新条目
        let status = SecItemAdd(query as CFDictionary, nil)
        if status == errSecSuccess {
            print("[KeyManager] [INFO] Key version \(version) saved to Keychain.")
            return true
        } else {
            print("[KeyManager] [ERROR] Failed to save key version \(version) to Keychain. Status: \(status)")
            return false
        }
    }

    // 从 Keychain 加载密钥
    private func loadKeyFromKeychain(version: UInt32) -> Data? {
        let account = accountName(for: version)
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: keychainService,
            kSecAttrAccount as String: account,
            kSecReturnData as String: kCFBooleanTrue!,
            kSecMatchLimit as String: kSecMatchLimitOne
        ]

        var item: CFTypeRef?
        let status = SecItemCopyMatching(query as CFDictionary, &item)

        if status == errSecSuccess {
            if let existingItem = item as? Data {
                print("[KeyManager] [INFO] Key version \(version) loaded from Keychain.")
                return existingItem
            }
        } else if status != errSecItemNotFound {
            print("[KeyManager] [ERROR] Failed to load key version \(version) from Keychain. Status: \(status)")
        }
        
        return nil
    }
    
    // 从 Keychain 删除密钥
    private func deleteKeyFromKeychain(version: UInt32) -> Bool {
        let account = accountName(for: version)
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: keychainService,
            kSecAttrAccount as String: account
        ]
        
        let status = SecItemDelete(query as CFDictionary)
        if status == errSecSuccess || status == errSecItemNotFound {
            print("[KeyManager] [INFO] Key version \(version) deleted from Keychain (or not found).")
            return true
        } else {
            print("[KeyManager] [ERROR] Failed to delete key version \(version) from Keychain. Status: \(status)")
            return false
        }
    }

    // 尝试加载所有已知的或可能的密钥（简化实现）
    private func loadAllKeysFromKeychain() {
        // 实际应用中需要更可靠的方式来知道哪些密钥版本存在
        // 这里仅作示例，尝试加载版本 0 到 10
        for version: UInt32 in 0...10 {
            if let key = loadKeyFromKeychain(version: version) {
                keyCache[version] = key
            }
        }
    }

    // 获取密钥 (优先缓存，然后 Keychain)
    func getKey(version: UInt32) -> Data? {
        if let cachedKey = keyCache[version] {
            return cachedKey
        }
        
        if let loadedKey = loadKeyFromKeychain(version: version) {
            keyCache[version] = loadedKey // 加入缓存
            return loadedKey
        }
        
        print("[KeyManager] [WARN] Key version \(version) not found in cache or Keychain.")
        return nil
    }

    // 保存密钥 (到 Keychain 和缓存)
    // 这个方法需要被外部调用，例如在密钥生成或从服务器获取后
    @discardableResult // 允许调用者忽略返回值
    func saveKey(version: UInt32, key: Data) -> Bool {
        if saveKeyToKeychain(version: version, key: key) {
            keyCache[version] = key // 更新缓存
            return true
        } else {
            return false
        }
    }

    // 删除密钥 (从 Keychain 和缓存)
    // 需要外部调用，例如在密钥吊销后
    @discardableResult
    func deleteKey(version: UInt32) -> Bool {
        if deleteKeyFromKeychain(version: version) {
            keyCache.removeValue(forKey: version) // 从缓存移除
            return true
        } else {
            return false
        }
    }
}