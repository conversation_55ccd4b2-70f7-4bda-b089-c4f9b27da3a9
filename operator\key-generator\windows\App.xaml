<Application x:Class="KeyGenerator.App"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="clr-namespace:KeyGenerator.Converters"
    xmlns:views="clr-namespace:KeyGenerator.Views"
    xmlns:viewmodels="clr-namespace:KeyGenerator.ViewModels">

    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="Resources/Colors.xaml"/>
                <ResourceDictionary Source="Resources/Styles.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- 转换器 -->
            <local:StatusToBrushConverter x:Key="StatusToBrushConverter"/>
            <local:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

            <!-- ViewModels 到 Views 的 DataTemplate 映射 -->
            <DataTemplate x:Key="KeyGenerationViewTemplate" DataType="{x:Type viewmodels:KeyGenerationViewModel}">
                <views:KeyGenerationView/>
            </DataTemplate>

            <DataTemplate x:Key="KeyManagementViewTemplate" DataType="{x:Type viewmodels:KeyManagementViewModel}">
                <views:KeyManagementView/>
            </DataTemplate>

            <!-- 全局样式 -->
            <Style TargetType="Window">
                <Setter Property="FontFamily" Value="Microsoft YaHei UI, Segoe UI"/>
                <Setter Property="FontSize" Value="12"/>
            </Style>

            <Style TargetType="Button">
                <Setter Property="Padding" Value="10,5"/>
                <Setter Property="Margin" Value="5,2"/>
                <Setter Property="MinWidth" Value="80"/>
                <Setter Property="FontSize" Value="12"/>
            </Style>

            <Style TargetType="TextBox">
                <Setter Property="Padding" Value="5,3"/>
                <Setter Property="Margin" Value="2"/>
                <Setter Property="FontSize" Value="12"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="BorderBrush" Value="#CCCCCC"/>
            </Style>

            <Style TargetType="ComboBox">
                <Setter Property="Padding" Value="5,3"/>
                <Setter Property="Margin" Value="2"/>
                <Setter Property="FontSize" Value="12"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="BorderBrush" Value="#CCCCCC"/>
            </Style>

            <Style TargetType="DatePicker">
                <Setter Property="Padding" Value="5,3"/>
                <Setter Property="Margin" Value="2"/>
                <Setter Property="FontSize" Value="12"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="BorderBrush" Value="#CCCCCC"/>
            </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application> 