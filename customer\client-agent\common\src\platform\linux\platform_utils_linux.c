#include "platform/platform_utils.h"

#ifdef __linux__

#include <string.h>
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <ctype.h>
#include <fnmatch.h>
#include <pwd.h>
#include <sys/types.h>
#include <time.h>

/* 文件与路径操作 */

bool platform_get_file_extension(const char* filePath, char** extension) {
    if (!filePath || !extension) {
        return false;
    }
    
    const char* ext = strrchr(filePath, '.');
    if (!ext) {
        *extension = platform_strdup("");
        return true;
    }
    
    *extension = platform_strdup(ext);
    return (*extension != NULL);
}

bool platform_is_file_type_match(const char* filePath, const char* extension) {
    if (!filePath || !extension) {
        return false;
    }
    
    const char* ext = strrchr(filePath, '.');
    if (!ext) {
        return false;
    }
    
    return (platform_stricmp(ext, extension) == 0);
}

bool platform_is_path_match(const char* filePath, const char* pattern) {
    if (!filePath || !pattern) {
        return false;
    }
    
    // 使用fnmatch进行通配符匹配，FNM_PATHNAME确保/不被*匹配
    return (fnmatch(pattern, filePath, 0) == 0);
}

/* 进程相关操作 */

bool platform_get_process_name(char** processName) {
    if (!processName) {
        return false;
    }
    
    char path[1024];
    ssize_t count = readlink("/proc/self/exe", path, sizeof(path) - 1);
    if (count <= 0) {
        return false;
    }
    
    path[count] = '\0';
    
    // 找到最后一个斜杠
    char* lastSlash = strrchr(path, '/');
    if (!lastSlash) {
        return false;
    }
    
    *processName = platform_strdup(lastSlash + 1);
    return (*processName != NULL);
}

bool platform_get_process_path(char** processPath) {
    if (!processPath) {
        return false;
    }
    
    char path[1024];
    ssize_t count = readlink("/proc/self/exe", path, sizeof(path) - 1);
    if (count <= 0) {
        return false;
    }
    
    path[count] = '\0';
    
    *processPath = platform_strdup(path);
    return (*processPath != NULL);
}

/* 用户相关操作 */

bool platform_get_current_user_id(char** userId) {
    if (!userId) {
        return false;
    }
    
    uid_t uid = geteuid();
    struct passwd* pwd = getpwuid(uid);
    if (!pwd) {
        // 如果无法获取用户名，使用UID作为字符串
        char uidString[20];
        snprintf(uidString, sizeof(uidString), "%u", uid);
        *userId = platform_strdup(uidString);
        return (*userId != NULL);
    }
    
    *userId = platform_strdup(pwd->pw_name);
    return (*userId != NULL);
}

/* 日志与调试 */

void platform_log_message(int level, const char* message) {
    if (!message) {
        return;
    }
    
    // 将级别转换为字符串
    const char* levelStr;
    switch (level) {
    case 0: levelStr = "DEBUG"; break;
    case 1: levelStr = "INFO "; break;
    case 2: levelStr = "WARN "; break;
    case 3: levelStr = "ERROR"; break;
    default: levelStr = "?????"; break;
    }
    
    // 获取时间
    time_t now;
    struct tm tm_now;
    time(&now);
    localtime_r(&now, &tm_now);
    
    // 输出到标准错误
    FILE* output = level >= 2 ? stderr : stdout;
    fprintf(output, "[%02d:%02d:%02d][%s] %s\n",
            tm_now.tm_hour, tm_now.tm_min, tm_now.tm_sec,
            levelStr, message);
    fflush(output);
}

/* 内存管理 */

void* platform_allocate_memory(size_t size) {
    return calloc(1, size);
}

void platform_free_memory(void* memory) {
    free(memory);
}

/* 字符串处理 */

char* platform_strdup(const char* source) {
    if (!source) {
        return NULL;
    }
    
    return strdup(source);
}

// 不区分大小写比较字符串的实现
int platform_stricmp(const char* s1, const char* s2) {
    if (!s1 && !s2) {
        return 0;
    }
    if (!s1) {
        return -1;
    }
    if (!s2) {
        return 1;
    }
    
    for (; *s1 && *s2; s1++, s2++) {
        if (tolower((unsigned char)*s1) != tolower((unsigned char)*s2)) {
            break;
        }
    }
    
    return tolower((unsigned char)*s1) - tolower((unsigned char)*s2);
}

#endif /* __linux__ */ 