/*
 * crypto_manager.h
 *
 * Cryptosystem Linux 加密模块头文件
 * 定义加密函数和数据结构
 */

#ifndef CRYPTO_MANAGER_H
#define CRYPTO_MANAGER_H

#include <stddef.h>
#include <stdint.h>

// 加密算法类型
typedef enum {
    ALGORITHM_NONE = 0, // 无加密
    ALGORITHM_AES = 1,  // AES 加密
    ALGORITHM_SM4 = 2   // 国密 SM4 加密
} crypto_algorithm;

// 加密模式
typedef enum {
    MODE_NONE = 0,    // 无模式
    MODE_CBC = 1,     // CBC 模式
    MODE_GCM = 2,     // GCM 模式 (推荐)
    MODE_CTR = 3      // CTR 模式
} crypto_mode;

// 算法常量
#define CRYPTO_KEY_SIZE_AES_128 16
#define CRYPTO_KEY_SIZE_AES_256 32
#define CRYPTO_KEY_SIZE_SM4     16
#define CRYPTO_IV_SIZE          16
#define CRYPTO_TAG_SIZE         16
#define CRYPTO_CHECKSUM_SIZE    4

// 错误代码
#define CRYPTO_SUCCESS           0  // 成功
#define CRYPTO_ERROR_INVALID    -1  // 无效参数
#define CRYPTO_ERROR_MEMORY     -2  // 内存分配失败
#define CRYPTO_ERROR_ALGORITHM  -3  // 算法不支持
#define CRYPTO_ERROR_KEYVER     -4  // 密钥版本无效
#define CRYPTO_ERROR_CRYPTO     -5  // 加密/解密操作失败
#define CRYPTO_ERROR_AUTH       -6  // 认证标签验证失败

/**
 * 初始化加密模块
 * 
 * @return 成功返回CRYPTO_SUCCESS，失败返回错误代码
 */
int crypto_init(void);

/**
 * 清理加密模块
 */
void crypto_cleanup(void);

/**
 * 加密数据
 * 
 * @param algorithm     加密算法
 * @param mode          加密模式
 * @param key_version   密钥版本
 * @param iv            初始化向量(16字节)
 * @param plaintext     明文数据
 * @param plaintext_len 明文长度
 * @param ciphertext    输出的密文缓冲区
 * @param ciphertext_len 输入时为缓冲区大小，输出时为实际密文长度
 * 
 * @return 成功返回CRYPTO_SUCCESS，失败返回错误代码
 */
int crypto_encrypt(
    crypto_algorithm algorithm,
    crypto_mode mode,
    uint32_t key_version,
    const uint8_t *iv,
    const uint8_t *plaintext,
    size_t plaintext_len,
    uint8_t *ciphertext,
    size_t *ciphertext_len
);

/**
 * 解密数据
 * 
 * @param algorithm     加密算法
 * @param mode          加密模式
 * @param key_version   密钥版本
 * @param iv            初始化向量(16字节)
 * @param ciphertext    密文数据
 * @param ciphertext_len 密文长度
 * @param plaintext     输出的明文缓冲区
 * @param plaintext_len 输入时为缓冲区大小，输出时为实际明文长度
 * 
 * @return 成功返回CRYPTO_SUCCESS，失败返回错误代码
 */
int crypto_decrypt(
    crypto_algorithm algorithm,
    crypto_mode mode,
    uint32_t key_version,
    const uint8_t *iv,
    const uint8_t *ciphertext,
    size_t ciphertext_len,
    uint8_t *plaintext,
    size_t *plaintext_len
);

/**
 * 计算数据校验和
 * 
 * @param data      数据
 * @param data_len  数据长度
 * 
 * @return 返回32位校验和
 */
uint32_t crypto_compute_checksum(const uint8_t *data, size_t data_len);

/**
 * 生成随机数据
 * 
 * @param buffer    输出缓冲区
 * @param length    需要的随机字节数
 * 
 * @return 成功返回CRYPTO_SUCCESS，失败返回错误代码
 */
int crypto_generate_random(uint8_t *buffer, size_t length);

/**
 * 获取本地存储的密钥
 * 
 * @param algorithm   加密算法
 * @param key_version 密钥版本
 * @param key_buffer  密钥输出缓冲区
 * @param key_length  密钥长度
 * 
 * @return 成功返回CRYPTO_SUCCESS，失败返回错误代码
 */
int crypto_get_key(
    crypto_algorithm algorithm,
    uint32_t key_version,
    uint8_t *key_buffer,
    size_t *key_length
);

/**
 * 设置本地存储的密钥
 * 
 * @param algorithm   加密算法
 * @param key_version 密钥版本
 * @param key_buffer  密钥数据
 * @param key_length  密钥长度
 * 
 * @return 成功返回CRYPTO_SUCCESS，失败返回错误代码
 */
int crypto_set_key(
    crypto_algorithm algorithm,
    uint32_t key_version,
    const uint8_t *key_buffer,
    size_t key_length
);

#endif /* CRYPTO_MANAGER_H */ 