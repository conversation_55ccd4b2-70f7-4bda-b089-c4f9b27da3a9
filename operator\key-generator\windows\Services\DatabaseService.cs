using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Npgsql;
using MySql.Data.MySqlClient;
using System.Data.SqlClient;
using KeyGenerator.Models;

namespace KeyGenerator.Services
{
    /// <summary>
    /// 数据库服务接口
    /// </summary>
    public interface IDatabaseService
    {
        Task<bool> TestConnectionAsync();
        Task<bool> InitializeDatabaseAsync();
        Task<string> SaveMasterKeyAsync(MasterKeyEntity masterKey);
        Task<MasterKeyEntity> GetMasterKeyAsync(string keyId);
        Task<List<MasterKeyEntity>> GetMasterKeysAsync(string? clientId = null);
        Task<List<MasterKeyEntity>> GetAllMasterKeysAsync();
        Task<bool> UpdateKeyStatusAsync(string keyId, KeyStatus status);
        Task<bool> UpdateMasterKeyAsync(MasterKeyEntity masterKey);
        Task<bool> DeleteKeyAsync(string keyId);
        Task<bool> DeleteMasterKeyAsync(string keyId);
        Task<string> SaveClientAsync(ClientEntity client);
        Task<ClientEntity> GetClientAsync(string clientId);
        Task<List<ClientEntity>> GetClientsAsync();
        Task<List<ClientEntity>> GetAllClientsAsync();
        Task<bool> DeleteClientAsync(string clientId);
        Task<bool> SaveAuditLogAsync(AuditLogEntity auditLog);
        Task<List<AuditLogEntity>> GetAuditLogsAsync(DateTime from, DateTime to, string? userFilter);
    }

    /// <summary>
    /// 数据库服务实现
    /// </summary>
    public class DatabaseService : IDatabaseService
    {
        private readonly ILogger<DatabaseService> _logger;
        private readonly IConfiguration _configuration;
        private readonly DatabaseType _databaseType;
        private readonly string _connectionString;
        private readonly bool _useInMemoryMode;

        // 内存模式存储（用于演示）
        private readonly List<MasterKeyEntity> _masterKeys;
        private readonly List<ClientEntity> _clients;
        private readonly List<AuditLogEntity> _auditLogs;

        public DatabaseService(ILogger<DatabaseService> logger, IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
            
            // 读取数据库配置
            var dbConfig = _configuration.GetSection("DatabaseSettings");
            var dbType = dbConfig.GetValue<string>("DatabaseType", "PostgreSQL");
            _useInMemoryMode = dbConfig.GetValue<bool>("UseInMemoryMode", true);
            
            // 从ConnectionStrings中读取连接字符串
            switch (dbType)
            {
                case "PostgreSQL":
                    _connectionString = _configuration.GetConnectionString("DefaultConnection") ?? "Host=localhost;Database=cryptosystem;Username=crypto;Password=********";
                    _databaseType = DatabaseType.PostgreSQL;
                    break;
                case "MySQL":
                    _connectionString = _configuration.GetConnectionString("MySqlConnection") ?? "Server=localhost;Database=cryptosystem;Uid=crypto;Pwd=********";
                    _databaseType = DatabaseType.MySQL;
                    break;
                case "SqlServer":
                    _connectionString = _configuration.GetConnectionString("SqlServerConnection") ?? "Server=localhost;Database=cryptosystem;Trusted_Connection=true";
                    _databaseType = DatabaseType.SqlServer;
                    break;
                default:
                    _connectionString = _configuration.GetConnectionString("DefaultConnection") ?? "Host=localhost;Database=cryptosystem;Username=crypto;Password=********";
                    _databaseType = DatabaseType.PostgreSQL;
                    break;
            }
            
            // 初始化内存数据存储（无论是否使用内存模式都要初始化，避免null引用）
            _masterKeys = [];
            _clients = [];
            _auditLogs = [];
            
            if (_useInMemoryMode)
            {
                InitializeDemoData();
                _logger.LogInformation("数据库服务初始化完成（内存模式）");
            }
            else
            {
                _logger.LogInformation("数据库服务初始化完成（{DatabaseType}模式）", _databaseType);
            }
        }

        /// <summary>
        /// 测试数据库连接
        /// </summary>
        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                if (_useInMemoryMode)
                {
                    await Task.Delay(100); // 模拟连接测试
                    _logger.LogInformation("数据库连接测试成功（内存模式）");
                    return true;
                }

                using var connection = CreateConnection();
                await OpenConnectionAsync(connection);
                _logger.LogInformation("数据库连接测试成功（{DatabaseType}）", _databaseType);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "数据库连接测试失败");
                return false;
            }
        }

        /// <summary>
        /// 初始化数据库
        /// </summary>
        public async Task<bool> InitializeDatabaseAsync()
        {
            try
            {
                await Task.Delay(100); // 模拟初始化
                _logger.LogInformation("数据库初始化完成（内存模式）");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "数据库初始化失败");
                return false;
            }
        }

        /// <summary>
        /// 保存主密钥
        /// </summary>
        public async Task<string> SaveMasterKeyAsync(MasterKeyEntity masterKey)
        {
            try
            {
                await Task.Delay(50); // 模拟数据库操作
                
                // 检查是否已存在
                var existingIndex = _masterKeys.FindIndex(k => k.KeyId == masterKey.KeyId);
                if (existingIndex >= 0)
                {
                    _masterKeys[existingIndex] = masterKey;
                }
                else
                {
                    _masterKeys.Add(masterKey);
                }
                
                _logger.LogInformation("主密钥保存成功：{KeyId}", masterKey.KeyId);
                return masterKey.KeyId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存主密钥失败：{KeyId}", masterKey.KeyId);
                throw;
            }
        }

        /// <summary>
        /// 获取主密钥
        /// </summary>
        public async Task<MasterKeyEntity> GetMasterKeyAsync(string keyId)
        {
            try
            {
                await Task.Delay(50); // 模拟数据库查询
                return _masterKeys.Find(k => k.KeyId == keyId) ?? throw new KeyNotFoundException($"未找到密钥: {keyId}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取主密钥失败：{KeyId}", keyId);
                throw;
            }
        }

        /// <summary>
        /// 获取主密钥列表
        /// </summary>
        public async Task<List<MasterKeyEntity>> GetMasterKeysAsync(string? clientId = null)
        {
            try
            {
                await Task.Delay(50); // 模拟数据库查询
                
                if (string.IsNullOrEmpty(clientId))
                {
                    return [.._masterKeys];
                }
                
                return _masterKeys.FindAll(k => k.ClientId == clientId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取主密钥列表失败");
                throw;
            }
        }

        /// <summary>
        /// 更新密钥状态
        /// </summary>
        public async Task<bool> UpdateKeyStatusAsync(string keyId, KeyStatus status)
        {
            try
            {
                await Task.Delay(50); // 模拟数据库操作
                
                var key = _masterKeys.Find(k => k.KeyId == keyId);
                if (key != null)
                {
                    key.KeyStatus = status;
                    return true;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新密钥状态失败：{KeyId}", keyId);
                throw;
            }
        }

        /// <summary>
        /// 删除密钥
        /// </summary>
        public async Task<bool> DeleteKeyAsync(string keyId)
        {
            try
            {
                await Task.Delay(50); // 模拟数据库操作
                
                var index = _masterKeys.FindIndex(k => k.KeyId == keyId);
                if (index >= 0)
                {
                    _masterKeys.RemoveAt(index);
                    return true;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除密钥失败：{KeyId}", keyId);
                throw;
            }
        }

        /// <summary>
        /// 保存客户信息
        /// </summary>
        public async Task<string> SaveClientAsync(ClientEntity client)
        {
            try
            {
                await Task.Delay(50); // 模拟数据库操作
                
                var existingIndex = _clients.FindIndex(c => c.ClientId == client.ClientId);
                if (existingIndex >= 0)
                {
                    _clients[existingIndex] = client;
                }
                else
                {
                    _clients.Add(client);
                }
                
                return client.ClientId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存客户信息失败：{ClientId}", client.ClientId);
                throw;
            }
        }

        /// <summary>
        /// 获取客户信息
        /// </summary>
        public async Task<ClientEntity> GetClientAsync(string clientId)
        {
            try
            {
                await Task.Delay(50); // 模拟数据库查询
                return _clients.Find(c => c.ClientId == clientId) ?? throw new KeyNotFoundException($"未找到客户: {clientId}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取客户信息失败：{ClientId}", clientId);
                throw;
            }
        }

        /// <summary>
        /// 获取客户列表
        /// </summary>
        public async Task<List<ClientEntity>> GetClientsAsync()
        {
            try
            {
                await Task.Delay(50); // 模拟数据库查询
                return [.._clients];
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取客户列表失败");
                throw;
            }
        }

        /// <summary>
        /// 获取所有主密钥
        /// </summary>
        public async Task<List<MasterKeyEntity>> GetAllMasterKeysAsync()
        {
            try
            {
                await Task.Delay(50); // 模拟数据库查询
                return [.._masterKeys];
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有主密钥失败");
                throw;
            }
        }

        /// <summary>
        /// 更新主密钥
        /// </summary>
        public async Task<bool> UpdateMasterKeyAsync(MasterKeyEntity masterKey)
        {
            try
            {
                await Task.Delay(50); // 模拟数据库操作
                
                var index = _masterKeys.FindIndex(k => k.KeyId == masterKey.KeyId);
                if (index >= 0)
                {
                    _masterKeys[index] = masterKey;
                    return true;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新主密钥失败：{KeyId}", masterKey.KeyId);
                throw;
            }
        }

        /// <summary>
        /// 删除主密钥
        /// </summary>
        public async Task<bool> DeleteMasterKeyAsync(string keyId)
        {
            try
            {
                await Task.Delay(50); // 模拟数据库操作
                
                var index = _masterKeys.FindIndex(k => k.KeyId == keyId);
                if (index >= 0)
                {
                    _masterKeys.RemoveAt(index);
                    return true;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除主密钥失败：{KeyId}", keyId);
                throw;
            }
        }

        /// <summary>
        /// 获取所有客户
        /// </summary>
        public async Task<List<ClientEntity>> GetAllClientsAsync()
        {
            try
            {
                await Task.Delay(50); // 模拟数据库查询
                return [.._clients];
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有客户失败");
                throw;
            }
        }

        /// <summary>
        /// 删除客户
        /// </summary>
        public async Task<bool> DeleteClientAsync(string clientId)
        {
            try
            {
                await Task.Delay(50); // 模拟数据库操作
                var clientToRemove = _clients.Find(c => c.ClientId == clientId);
                if (clientToRemove != null)
                {
                    _clients.Remove(clientToRemove);
                    _logger.LogInformation("客户删除成功：{ClientId}", clientId);
                    return true;
                }
                else
                {
                    _logger.LogWarning("未找到要删除的客户：{ClientId}", clientId);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除客户失败：{ClientId}", clientId);
                return false;
            }
        }

        /// <summary>
        /// 保存审计日志
        /// </summary>
        public async Task<bool> SaveAuditLogAsync(AuditLogEntity auditLog)
        {
            try
            {
                await Task.Delay(50); // 模拟数据库操作
                _auditLogs.Add(auditLog);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存审计日志失败");
                return false;
            }
        }

        /// <summary>
        /// 获取审计日志
        /// </summary>
        public async Task<List<AuditLogEntity>> GetAuditLogsAsync(DateTime from, DateTime to, string? userFilter)
        {
            try
            {
                await Task.Delay(50); // 模拟数据库查询
                return _auditLogs.FindAll(log => log.CreatedTime >= from && log.CreatedTime <= to && (string.IsNullOrEmpty(userFilter) || log.CreatedBy.Contains(userFilter)));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取审计日志失败");
                throw;
            }
        }

        #region 私有方法

        /// <summary>
        /// 创建数据库连接
        /// </summary>
        private IDbConnection CreateConnection()
        {
            if (string.IsNullOrEmpty(_connectionString))
                throw new InvalidOperationException("数据库连接字符串未配置");

            return _databaseType switch
            {
                DatabaseType.PostgreSQL => new NpgsqlConnection(_connectionString),
                DatabaseType.MySQL => new MySqlConnection(_connectionString),
                DatabaseType.SqlServer => new SqlConnection(_connectionString),
                _ => throw new NotSupportedException($"不支持的数据库类型: {_databaseType}")
            };
        }

        /// <summary>
        /// 打开数据库连接
        /// </summary>
        private static async Task OpenConnectionAsync(IDbConnection connection)
        {
            if (connection.State != ConnectionState.Open)
            {
                if (connection is DbConnection dbConnection)
                {
                    await dbConnection.OpenAsync();
                }
                else
                {
                    connection.Open();
                }
            }
        }

        /// <summary>
        /// 创建数据库命令
        /// </summary>
        private static IDbCommand CreateCommand(IDbConnection connection, string sql)
        {
            var command = connection.CreateCommand();
            command.CommandText = sql;
            command.CommandType = CommandType.Text;
            return command;
        }

        /// <summary>
        /// 执行数据库读取命令
        /// </summary>
        private static async Task<IDataReader> ExecuteReaderAsync(IDbCommand command)
        {
            if (command is DbCommand dbCommand)
            {
                return await dbCommand.ExecuteReaderAsync();
            }
            else
            {
                return command.ExecuteReader();
            }
        }

        /// <summary>
        /// 读取数据库结果
        /// </summary>
        private static async Task<bool> ReadAsync(IDataReader reader)
        {
            if (reader is DbDataReader dbReader)
            {
                return await dbReader.ReadAsync();
            }
            else
            {
                return reader.Read();
            }
        }

        /// <summary>
        /// 确定数据库类型
        /// </summary>
        private static DatabaseType DetermineDatabaseType(string connectionString)
        {
            if (string.IsNullOrEmpty(connectionString))
                return DatabaseType.PostgreSQL;
            
            connectionString = connectionString.ToLower();
            
            if (connectionString.Contains("server=") && connectionString.Contains("uid="))
                return DatabaseType.MySQL;
            
            if (connectionString.Contains("data source=") || connectionString.Contains("server=") && connectionString.Contains("user id="))
                return DatabaseType.SqlServer;
            
            return DatabaseType.PostgreSQL;
        }

        /// <summary>
        /// 添加参数到命令
        /// </summary>
        private static void AddParameter(IDbCommand command, string name, object value)
        {
            var parameter = command.CreateParameter();
            parameter.ParameterName = name;
            parameter.Value = value ?? DBNull.Value;
            command.Parameters.Add(parameter);
        }

        /// <summary>
        /// 初始化演示数据
        /// </summary>
        private void InitializeDemoData()
        {
            // 添加演示客户
            _clients.Add(new ClientEntity
            {
                ClientId = "CLIENT_001",
                ClientName = "演示客户",
                ClientCode = "DEMO_001",
                ContactPerson = "张三",
                ContactPhone = "13800138000",
                ContactEmail = "<EMAIL>",
                Address = "北京市朝阳区演示大厦",
                Industry = "金融",
                ClientLevel = "STANDARD",
                LicenseType = "企业版",
                MaxUsers = 100,
                MaxDevices = 50,
                ContractStartDate = DateTime.Today.AddMonths(-6),
                ContractEndDate = DateTime.Today.AddYears(1),
                IsActive = true,
                Status = "ACTIVE",
                CreatedTime = DateTime.UtcNow.AddMonths(-6),
                CreatedBy = "系统管理员",
                Remarks = "演示客户，用于测试"
            });

            _clients.Add(new ClientEntity
            {
                ClientId = "CLIENT_002",
                ClientName = "测试客户",
                ClientCode = "TEST_001",
                ContactPerson = "李四",
                ContactPhone = "13900139000",
                ContactEmail = "<EMAIL>",
                Address = "上海市浦东新区测试大厦",
                Industry = "制造业",
                ClientLevel = "VIP",
                LicenseType = "旗舰版",
                MaxUsers = 500,
                MaxDevices = 200,
                ContractStartDate = DateTime.Today.AddMonths(-3),
                ContractEndDate = DateTime.Today.AddYears(2),
                IsActive = true,
                Status = "ACTIVE",
                CreatedTime = DateTime.UtcNow.AddMonths(-3),
                CreatedBy = "系统管理员",
                Remarks = "测试客户，用于功能验证"
            });
        }

        #endregion
    }

    /// <summary>
    /// 数据库类型枚举
    /// </summary>
    public enum DatabaseType
    {
        PostgreSQL,
        MySQL,
        SqlServer
    }
} 