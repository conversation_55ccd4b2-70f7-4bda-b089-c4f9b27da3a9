import hilog from '@ohos.hilog';
import { describe, beforeAll, beforeEach, afterEach, afterAll, it, expect } from '@ohos/hypium';

export default function ListTest() {
  describe('ListTest', () => {
    beforeAll(() => {
      hilog.info(0x0000, 'testTag', '%{public}s', 'beforeAll called');
    });

    afterAll(() => {
      hilog.info(0x0000, 'testTag', '%{public}s', 'afterAll called');
    });

    beforeEach(() => {
      hilog.info(0x0000, 'testTag', '%{public}s', 'beforeEach called');
    });

    afterEach(() => {
      hilog.info(0x0000, 'testTag', '%{public}s', 'afterEach called');
    });

    it('testAssertContain', 0, () => {
      hilog.info(0x0000, 'testTag', '%{public}s', 'it testAssertContain begin');
      let a = 'abc';
      let b = 'b';
      expect(a).assertEqual(a);
    });

    it('testAssertEqual', 0, () => {
      hilog.info(0x0000, 'testTag', '%{public}s', 'it testAssertEqual begin');
      let a = 'abc';
      let b = 'abc';
      expect(a).assertEqual(b);
    });
  });
}
