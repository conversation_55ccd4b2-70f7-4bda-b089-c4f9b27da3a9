#include "Logger.h"

// 静态成员初始化
Logger::LogLevel Logger::s_level = Logger::LogLevel::INFO;
std::ofstream Logger::s_logFile;
std::mutex Logger::s_mutex;
bool Logger::s_toConsole = true;
bool Logger::s_toFile = false;

void Logger::setLogLevel(LogLevel level) {
    std::lock_guard<std::mutex> lock(s_mutex);
    s_level = level;
}

bool Logger::setLogFile(const std::string& filename) {
    std::lock_guard<std::mutex> lock(s_mutex);
    
    if (s_logFile.is_open()) {
        s_logFile.close();
    }
    
    s_logFile.open(filename, std::ios::out | std::ios::app);
    s_toFile = s_logFile.is_open();
    return s_toFile;
}

void Logger::debug(const std::string& message) {
    log(LogLevel::DEBUG, message);
}

void Logger::info(const std::string& message) {
    log(LogLevel::INFO, message);
}

void Logger::warning(const std::string& message) {
    log(LogLevel::WARNING, message);
}

void Logger::error(const std::string& message) {
    log(LogLevel::ERROR, message);
}

void Logger::critical(const std::string& message) {
    log(LogLevel::CRITICAL, message);
}

void Logger::shutdown() {
    std::lock_guard<std::mutex> lock(s_mutex);
    if (s_logFile.is_open()) {
        s_logFile.close();
    }
}

void Logger::log(LogLevel level, const std::string& message) {
    if (level < s_level) {
        return;
    }
    
    std::string formattedMessage = getCurrentTimeString() + " [" + getLevelString(level) + "] " + message;
    
    std::lock_guard<std::mutex> lock(s_mutex);
    
    // 输出到控制台
    if (s_toConsole) {
        if (level >= LogLevel::ERROR) {
            std::cerr << formattedMessage << std::endl;
        } else {
            std::cout << formattedMessage << std::endl;
        }
    }
    
    // 输出到文件
    if (s_toFile && s_logFile.is_open()) {
        s_logFile << formattedMessage << std::endl;
        s_logFile.flush();
    }
}

std::string Logger::getCurrentTimeString() {
    auto now = std::chrono::system_clock::now();
    auto time = std::chrono::system_clock::to_time_t(now);
    
    std::stringstream ss;
    struct tm timeinfo;
    localtime_s(&timeinfo, &time);
    ss << std::put_time(&timeinfo, "%Y-%m-%d %H:%M:%S");
    
    return ss.str();
}

std::string Logger::getLevelString(LogLevel level) {
    switch (level) {
        case LogLevel::DEBUG:
            return "DEBUG";
        case LogLevel::INFO:
            return "INFO";
        case LogLevel::WARNING:
            return "WARN";
        case LogLevel::ERROR:
            return "ERROR";
        case LogLevel::CRITICAL:
            return "CRITICAL";
        default:
            return "UNKNOWN";
    }
} 