#pragma once

#include <QWidget>
#include <vector>

#include "models/file_models.h"

// Forward declarations
class QTableWidget;
class QTableWidgetItem;
class QMenu;
class QPushButton;

namespace DeclassificationClient::UI {

class FileListWidget : public QWidget {
    Q_OBJECT

public:
    explicit FileListWidget(QWidget* parent = nullptr);
    ~FileListWidget() override;

    void updateFiles(const std::vector<Models::File>& files);

    int getRowCount() const;
    void updateFileStatus(int row, const QString& status, const QIcon& icon);

signals:
    void logMessage(const QString& message);

private slots:
    void addFile();
    void removeSelectedFile();
    void showContextMenu(const QPoint& point);

private:
    void setupUI();
    void setupConnections();

    QTableWidget* fileTableWidget_;
    QPushButton* addFileButton_;
    QPushButton* removeFileButton_;
    QMenu* contextMenu_;
};

} // namespace DeclassificationClient::UI 