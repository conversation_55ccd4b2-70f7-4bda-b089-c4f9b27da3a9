﻿#include "KeyManager.h"
#include "PolicyManager.h"

#include <iostream>
#include <fstream>
#include <chrono>
#include <thread>
#include <sstream>
#include <iomanip>
#include <random>
#include <algorithm>
#include <windows.h>
#include <bcrypt.h>
#include <filesystem>

// 临时日志宏，后期需要替换为正式日志系统
#define LOG_KEY_INFO(msg) std::cout << "[KEY_MANAGER] [INFO] " << msg << std::endl
#define LOG_KEY_ERROR(msg) std::cerr << "[KEY_MANAGER] [ERROR] " << msg << std::endl
#define LOG_KEY_DEBUG(msg) std::cout << "[KEY_MANAGER] [DEBUG] " << msg << std::endl

namespace fs = std::filesystem;

// 临时配置常量，后期需要从配置文件读取
const std::string DEFAULT_ALGORITHM = "SM4-GCM";
const std::string KEY_STORAGE_PATH = ".\\keys.dat";
const std::string AUDIT_CACHE_PATH = ".\\audit_events.dat";
const int AUDIT_CACHE_MAX_SIZE = 100;
const int AUDIT_SYNC_INTERVAL_MS = 60000; // 60秒

// 获取当前时间戳（ISO 8601格式）
std::string getCurrentTimestamp() {
    auto now = std::chrono::system_clock::now();
    auto itt = std::chrono::system_clock::to_time_t(now);
    std::tm result_tm = {}; // Initialize to zero
#ifdef _WIN32
    errno_t err = localtime_s(&result_tm, &itt); // Use secure version on Windows
    if (err) {
        LOG_KEY_ERROR("localtime_s failed with error code: " << err);
        // Return a default timestamp or throw an exception
        return "1970-01-01T00:00:00"; 
    }
#else
    // 在其他平台使用线程安全版本
    struct tm* result_ptr = nullptr;
#ifdef __GNUC__
    // 使用线程安全的localtime_r
    result_ptr = localtime_r(&itt, &result_tm);
    if (result_ptr == nullptr) {
        LOG_KEY_ERROR("localtime_r failed");
        return "1970-01-01T00:00:00";
    }
#else
    // 最后的后备方案
    result_ptr = localtime(&itt);
    if (result_ptr == nullptr) {
        LOG_KEY_ERROR("localtime failed");
        return "1970-01-01T00:00:00";
    }
    result_tm = *result_ptr; // Copy the result
#endif
#endif
    std::ostringstream ss;
    // Use std::put_time, ensuring result_tm is valid
    ss << std::put_time(&result_tm, "%Y-%m-%dT%H:%M:%S"); // ISO 8601-like format
    // Add milliseconds if needed
    // auto millis = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()) % 1000;
    // ss << '.' << std::setfill('0') << std::setw(3) << millis.count() << 'Z'; // C++20 chrono might offer better ways
    return ss.str();
}

// 生成随机字符串
std::string generateRandomString(size_t length) {
    const std::string chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    std::random_device rd;
    std::mt19937 generator(rd());
    std::uniform_int_distribution<> distribution(0, static_cast<int>(chars.size() - 1));
    
    std::string result;
    result.reserve(length);
    for (size_t i = 0; i < length; ++i) {
        result += chars[distribution(generator)];
    }
    return result;
}

// 单例实现
KeyManager& KeyManager::getInstance() {
    static KeyManager instance;
    return instance;
}

KeyManager::KeyManager() : m_initialized(false), m_policyManager(nullptr) {
    LOG_KEY_INFO("KeyManager 构造");
    // Try to load keys from storage on initialization
    if (!loadKeysFromStorage()) {
        LOG_KEY_ERROR("初始化时加载密钥失败");
    }
    // Initialize audit cache sync mechanism (e.g., start a background thread)
    // For simplicity, we'll sync when cache is full or explicitly flushed
}

KeyManager::~KeyManager() {
    if (m_initialized) {
        shutdown();
    }
    LOG_KEY_INFO("KeyManager 析构");
}

bool KeyManager::initialize() {
    if (m_initialized) {
        LOG_KEY_INFO("KeyManager 已经初始化");
        return true;
    }
    
    // 加载密钥
    if (!loadKeysFromStorage()) {
        LOG_KEY_ERROR("加载密钥失败，使用空缓存启动");
        // 继续执行，不返回失败，因为可能是首次运行
    }
    
    // 设置定期同步审计事件的定时器
    m_stopAuditSyncThread = false;
    m_auditSyncThread = std::thread([this]() {
        while (!m_stopAuditSyncThread) {
            std::this_thread::sleep_for(std::chrono::milliseconds(AUDIT_SYNC_INTERVAL_MS));
            
            // 检查是否需要同步
            bool needSync = false;
            {
                std::lock_guard<std::mutex> lock(m_auditCacheMutex);
                needSync = !m_auditCache.empty();
            }
            
            if (needSync) {
                syncAuditEvents();
            }
        }
    });
    
    m_initialized = true;
    LOG_KEY_INFO("KeyManager 初始化完成");
    return true;
}

void KeyManager::shutdown() {
    if (!m_initialized) {
        return;
    }
    
    // 停止审计同步线程
    m_stopAuditSyncThread = true;
    if (m_auditSyncThread.joinable()) {
        m_auditSyncThread.join();
    }
    
    // 保存密钥
    if (!saveKeysToStorage()) {
        LOG_KEY_ERROR("保存密钥失败");
    }
    
    // 同步剩余审计事件
    if (!syncAuditEvents()) {
        LOG_KEY_ERROR("同步审计事件失败");
    }
    
    m_initialized = false;
    LOG_KEY_INFO("KeyManager 已关闭");
}

void KeyManager::setPolicyManager(std::shared_ptr<PolicyManager> policyManager) {
    m_policyManager = policyManager;
    LOG_KEY_INFO("设置PolicyManager");
}

std::string KeyManager::createKey(KeyType type, const std::string& associatedId) {
    if (!m_initialized) {
        LOG_KEY_ERROR("KeyManager未初始化");
        return "";
    }
    
    // 生成新的密钥ID
    std::string keyId = generateKeyId();
    
    // 创建密钥信息
    auto keyInfo = std::make_shared<KeyInfo>();
    keyInfo->keyId = keyId;
    keyInfo->type = type;
    keyInfo->status = KeyStatus::ACTIVE;
    keyInfo->algorithm = DEFAULT_ALGORITHM;
    keyInfo->createdAt = getCurrentTimestamp();
    keyInfo->associatedId = associatedId;
    keyInfo->hasParentKey = false;
    
    // 生成实际的加密密钥并安全存储
    NTSTATUS status = STATUS_SUCCESS;
    BCRYPT_ALG_HANDLE hAlgorithm = NULL;
    UCHAR keyMaterial[32]; // 256位密钥
    ULONG keySize = 0;
    
    try {
        // 打开算法提供程序
        if (!BCRYPT_SUCCESS(status = BCryptOpenAlgorithmProvider(
            &hAlgorithm,
            BCRYPT_AES_ALGORITHM,
            NULL,
            0)))
        {
            throw std::runtime_error("BCryptOpenAlgorithmProvider失败: " + std::to_string(status));
        }
        
        // 生成随机密钥
        if (!BCRYPT_SUCCESS(status = BCryptGenRandom(
            NULL,
            keyMaterial,
            sizeof(keyMaterial),
            BCRYPT_USE_SYSTEM_PREFERRED_RNG)))
        {
            throw std::runtime_error("BCryptGenRandom失败: " + std::to_string(status));
        }
        
        // 使用系统保护的数据API加密密钥材料
        DATA_BLOB dataIn, dataOut;
        dataIn.cbData = sizeof(keyMaterial);
        dataIn.pbData = keyMaterial;
        
        if (!CryptProtectData(
            &dataIn,
            L"KeyManager_Key", // 描述
            NULL,              // 可选熵 (TODO: 考虑添加应用特定的熵)
            NULL,              // 保留
            NULL,              // 提示结构
            CRYPTPROTECT_UI_FORBIDDEN | CRYPTPROTECT_CURRENT_USER, // 标志: 绑定到当前用户，禁止UI
            &dataOut))
        {
            throw std::runtime_error("CryptProtectData失败: " + std::to_string(GetLastError()));
        }
        
        // 保存加密的密钥材料
        keyInfo->encryptedKeyMaterial.assign(reinterpret_cast<char*>(dataOut.pbData), dataOut.cbData);
        
        // 释放加密后的数据
        LocalFree(dataOut.pbData);
    }
    catch (std::exception& e) {
        LOG_KEY_ERROR("密钥生成失败: " << e.what());
        if (hAlgorithm) {
            BCryptCloseAlgorithmProvider(hAlgorithm, 0);
        }
        return "";
    }
    
    if (hAlgorithm) {
        BCryptCloseAlgorithmProvider(hAlgorithm, 0);
    }
    
    // 清空内存中的原始密钥材料
    SecureZeroMemory(keyMaterial, sizeof(keyMaterial));
    
    // 存储到缓存
    {
        std::lock_guard<std::mutex> lock(m_keyCacheMutex);
        m_keyCache[keyId] = keyInfo;
    }
    
    // 触发审计事件
    AuditEvent event;
    event.type = AuditEventType::KEY_CREATED;
    event.resourceId = keyId;
    event.resourceType = "key";
    event.timestamp = getCurrentTimestamp();
    event.result = "success";
    event.details = "{\\\"type\\\":\\\"" + std::to_string(static_cast<int>(type)) + "\\\", \\\"associatedId\\\":\\\"" + associatedId + "\\\"}";
    logAuditEvent(event);
    
    LOG_KEY_INFO("创建密钥: " << keyId);
    return keyId;
}

bool KeyManager::rotateKey(const std::string& keyId) {
    if (!m_initialized) {
        LOG_KEY_ERROR("KeyManager未初始化");
        return false;
    }
    
    std::shared_ptr<KeyInfo> keyInfo;
    {
        std::lock_guard<std::mutex> lock(m_keyCacheMutex);
        auto it = m_keyCache.find(keyId);
        if (it == m_keyCache.end()) {
            LOG_KEY_ERROR("找不到密钥: " << keyId);
            return false;
        }
        keyInfo = it->second;
    }
    
    // 创建新密钥
    std::string newKeyId = createKey(keyInfo->type, keyInfo->associatedId);
    if (newKeyId.empty()) {
        LOG_KEY_ERROR("创建新密钥失败");
        return false;
    }
    
    // 更新父子关系
    {
        std::lock_guard<std::mutex> lock(m_keyCacheMutex);
        keyInfo->status = KeyStatus::ROTATING;
        
        auto newKeyIt = m_keyCache.find(newKeyId);
        if (newKeyIt != m_keyCache.end()) {
            auto newKeyInfo = newKeyIt->second;
            newKeyInfo->hasParentKey = true;
            newKeyInfo->parentKeyId = keyId;
        }
    }
    
    // 触发审计事件
    AuditEvent event;
    event.type = AuditEventType::KEY_ROTATED;
    event.resourceId = keyId;
    event.resourceType = "key";
    event.timestamp = getCurrentTimestamp();
    event.details = "{\"newKeyId\":\"" + newKeyId + "\"}";
    event.result = "success";
    logAuditEvent(event);
    
    LOG_KEY_INFO("轮换密钥: " << keyId << " -> " << newKeyId);
    return true;
}

bool KeyManager::revokeKey(const std::string& keyId) {
    if (!m_initialized) {
        LOG_KEY_ERROR("KeyManager未初始化");
        return false;
    }
    
    {
        std::lock_guard<std::mutex> lock(m_keyCacheMutex);
        auto it = m_keyCache.find(keyId);
        if (it == m_keyCache.end()) {
            LOG_KEY_ERROR("找不到密钥: " << keyId);
            return false;
        }
        
        it->second->status = KeyStatus::REVOKED;
    }
    
    // 触发审计事件
    AuditEvent event;
    event.type = AuditEventType::KEY_REVOKED;
    event.resourceId = keyId;
    event.resourceType = "key";
    event.timestamp = getCurrentTimestamp();
    event.result = "success";
    logAuditEvent(event);
    
    LOG_KEY_INFO("撤销密钥: " << keyId);
    return true;
}

std::shared_ptr<KeyInfo> KeyManager::getKeyInfo(const std::string& keyId) {
    if (!m_initialized) {
        LOG_KEY_ERROR("KeyManager未初始化");
        return nullptr;
    }
    
    std::lock_guard<std::mutex> lock(m_keyCacheMutex);
    auto it = m_keyCache.find(keyId);
    if (it == m_keyCache.end()) {
        LOG_KEY_DEBUG("密钥未在缓存中找到: " << keyId);
        return nullptr;
    }
    
    // 触发审计事件
    AuditEvent event;
    event.type = AuditEventType::KEY_ACCESSED;
    event.resourceId = keyId;
    event.resourceType = "key";
    event.timestamp = getCurrentTimestamp();
    event.result = "success";
    logAuditEvent(event);
    
    return it->second;
}

std::vector<std::shared_ptr<KeyInfo>> KeyManager::getKeysByType(KeyType type) {
    std::vector<std::shared_ptr<KeyInfo>> result;
    
    if (!m_initialized) {
        LOG_KEY_ERROR("KeyManager未初始化");
        return result;
    }
    
    std::lock_guard<std::mutex> lock(m_keyCacheMutex);
    for (const auto& pair : m_keyCache) {
        if (pair.second->type == type) {
            result.push_back(pair.second);
        }
    }
    
    return result;
}

std::string KeyManager::getDocumentKey(const std::string& documentId, bool createIfNotExist) {
    if (!m_initialized) {
        LOG_KEY_ERROR("KeyManager未初始化");
        return "";
    }
    
    // 查找现有的文档密钥
    std::string keyId;
    {
        std::lock_guard<std::mutex> lock(m_keyCacheMutex);
        for (const auto& pair : m_keyCache) {
            if (pair.second->type == KeyType::DOCUMENT && 
                pair.second->associatedId == documentId &&
                pair.second->status == KeyStatus::ACTIVE) {
                keyId = pair.second->keyId;
                break;
            }
        }
    }
    
    // 如果未找到密钥且需要创建
    if (keyId.empty() && createIfNotExist) {
        keyId = createKey(KeyType::DOCUMENT, documentId);
    }
    
    if (keyId.empty() && createIfNotExist) {
        LOG_KEY_DEBUG("未找到文档密钥，且不允许创建: " << documentId);
        return "";
    }
    
    return keyId;
}

std::string KeyManager::getUserKey(const std::string& userId, bool createIfNotExist) {
    if (!m_initialized) {
        LOG_KEY_ERROR("KeyManager未初始化");
        return "";
    }
    
    // 查找现有的用户密钥
    std::string keyId;
    {
        std::lock_guard<std::mutex> lock(m_keyCacheMutex);
        for (const auto& pair : m_keyCache) {
            if (pair.second->type == KeyType::USER && 
                pair.second->associatedId == userId &&
                pair.second->status == KeyStatus::ACTIVE) {
                keyId = pair.second->keyId;
                break;
            }
        }
    }
    
    // 如果未找到密钥且需要创建
    if (keyId.empty() && createIfNotExist) {
        keyId = createKey(KeyType::USER, userId);
    }
    
    if (keyId.empty() && createIfNotExist) {
        LOG_KEY_DEBUG("未找到用户密钥，且不允许创建: " << userId);
        return "";
    }
    
    return keyId;
}

std::vector<uint8_t> KeyManager::encrypt(const std::vector<uint8_t>& data, const std::string& keyId) {
    if (!m_initialized) {
        LOG_KEY_ERROR("KeyManager未初始化");
        return {};
    }
    
    // 验证密钥
    if (!validateKey(keyId)) {
        LOG_KEY_ERROR("无效的密钥ID: " << keyId);
        return {};
    }
    
    // TODO: 实现实际的加密逻辑
    // 临时实现：简单异或加密（仅用于PoC）
    std::vector<uint8_t> result = data;
    for (auto& byte : result) {
        byte ^= 0x42; // 简单异或
    }
    
    // 触发审计事件
    AuditEvent event;
    event.type = AuditEventType::DOCUMENT_ENCRYPTED;
    event.resourceId = keyId;
    event.resourceType = "data";
    event.timestamp = getCurrentTimestamp();
    event.result = "success";
    logAuditEvent(event);
    
    return result;
}

std::vector<uint8_t> KeyManager::decrypt(const std::vector<uint8_t>& data, const std::string& keyId) {
    if (!m_initialized) {
        LOG_KEY_ERROR("KeyManager未初始化");
        return {};
    }
    
    // 验证密钥
    if (!validateKey(keyId)) {
        LOG_KEY_ERROR("无效的密钥ID: " << keyId);
        return {};
    }
    
    // TODO: 实现实际的解密逻辑
    // 临时实现：简单异或解密（仅用于PoC）
    std::vector<uint8_t> result = data;
    for (auto& byte : result) {
        byte ^= 0x42; // 简单异或（与加密相同）
    }
    
    // 触发审计事件
    AuditEvent event;
    event.type = AuditEventType::DOCUMENT_DECRYPTED;
    event.resourceId = keyId;
    event.resourceType = "data";
    event.timestamp = getCurrentTimestamp();
    event.result = "success";
    logAuditEvent(event);
    
    return result;
}

bool KeyManager::encryptFile(const std::wstring& filePath, const std::string& keyIdHint) {
    if (!m_initialized) {
        LOG_KEY_ERROR("加密文件失败: KeyManager未初始化");
        return false;
    }

    std::string actualKeyId = keyIdHint;
    std::string documentIdStr = wstringToString(filePath);

    if (actualKeyId.empty()) {
        actualKeyId = getDocumentKey(documentIdStr, true); // 获取或创建文档密钥
        if (actualKeyId.empty()) {
            LOG_KEY_ERROR("加密文件失败: 无法获取或创建文档密钥 for " << documentIdStr);
            return false;
        }
        LOG_KEY_INFO("encryptFile: Using document key " << actualKeyId << " for file: " << documentIdStr);
    }

    std::vector<uint8_t> fileContent;
    try {
        fileContent = readFileContent(filePath);
    } catch (const std::exception& e) {
         LOG_KEY_ERROR("加密文件失败: 读取文件时发生错误: " << documentIdStr << " - " << e.what());
         return false;
    }

    std::vector<uint8_t> encryptedContent = encrypt(fileContent, actualKeyId);

    if (encryptedContent.empty() && !fileContent.empty()) {
        LOG_KEY_ERROR("加密文件失败: 内存加密步骤返回空 (Key ID: " << actualKeyId << ", File: " << documentIdStr << ")");
        return false;
    }

    try {
        if (!writeFileContent(filePath, encryptedContent)) {
             LOG_KEY_ERROR("加密文件失败: 写入加密内容时发生错误: " << documentIdStr);
             return false;
        }
    } catch (const std::exception& e) {
         LOG_KEY_ERROR("加密文件失败: 写入文件时发生错误: " << documentIdStr << " - " << e.what());
         return false;
    }

    AuditEvent event;
    event.type = AuditEventType::DOCUMENT_ENCRYPTED;
    event.resourceId = documentIdStr;
    event.resourceType = "file";
    event.timestamp = getCurrentTimestamp();
    event.result = "success";
    std::shared_ptr<KeyInfo> kInfo = getKeyInfo(actualKeyId);
    event.details = "{\"keyId\":\"" + actualKeyId + "\", \"algorithm\":\"" + (kInfo ? kInfo->algorithm : "unknown") + "\"}";
    logAuditEvent(event);

    LOG_KEY_INFO("成功加密文件: " << documentIdStr << " using key: " << actualKeyId);
    return true;
}

bool KeyManager::decryptFile(const std::wstring& filePath) {
     if (!m_initialized) {
        LOG_KEY_ERROR("解密文件失败: KeyManager未初始化");
        return false;
    }
    std::string documentIdStr = wstringToString(filePath);

    std::vector<uint8_t> encryptedContent;
    try {
        encryptedContent = readFileContent(filePath);
    } catch (const std::exception& e) {
         LOG_KEY_ERROR("解密文件失败: 读取加密文件时发生错误: " << documentIdStr << " - " << e.what());
         return false;
    }
    
    if (encryptedContent.empty()) {
        LOG_KEY_WARN("解密文件: 文件为空，无需操作: " << documentIdStr);
        // 如果需要，可以在这里写入一个空的解密文件，或者直接返回true
        // bool writeEmpty = writeFileContent(filePath, {}); 
        return true; 
    }

    // 关键问题: 如何确定用于解密的 keyId?
    // 当前加密格式似乎不存储keyId。
    // 临时解决方案: 尝试使用与文件路径关联的文档密钥ID来解密。
    std::string keyId = getDocumentKey(documentIdStr, false); // 只获取，不创建
    if (keyId.empty()) {
        LOG_KEY_ERROR("解密文件失败: 无法找到与文件关联的文档密钥 ID for " << documentIdStr);
        // 在实际系统中，可能需要更复杂的密钥解析机制，例如从文件元数据或外部存储中获取。
        return false;
    }
    LOG_KEY_INFO("decryptFile: Attempting to use document key " << keyId << " for file: " << documentIdStr);

    std::vector<uint8_t> decryptedContent = decrypt(encryptedContent, keyId);

    if (decryptedContent.empty() && !encryptedContent.empty()) { 
        LOG_KEY_ERROR("解密文件失败: 内存解密步骤失败或返回空 (Key ID: " << keyId << ", File: " << documentIdStr << ")");
        return false;
    }

     try {
        if (!writeFileContent(filePath, decryptedContent)) {
             LOG_KEY_ERROR("解密文件失败: 写入解密内容时发生错误: " << documentIdStr);
             return false;
        }
    } catch (const std::exception& e) {
         LOG_KEY_ERROR("解密文件失败: 写入文件时发生错误: " << documentIdStr << " - " << e.what());
         return false;
    }

    AuditEvent event;
    event.type = AuditEventType::DOCUMENT_DECRYPTED;
    event.resourceId = documentIdStr;
    event.resourceType = "file";
    event.timestamp = getCurrentTimestamp();
    event.result = "success";
    std::shared_ptr<KeyInfo> kInfo = getKeyInfo(keyId);
    event.details = "{\"keyId\":\"" + keyId + "\", \"algorithm\":\"" + (kInfo ? kInfo->algorithm : "unknown") + "\"}";
    logAuditEvent(event);

    LOG_KEY_INFO("成功解密文件: " << documentIdStr << " using key: " << keyId);
    return true;
}

void KeyManager::logAuditEvent(const AuditEvent& event) {
    // 缓存审计事件
    cacheAuditEvent(event);
    
    // 如果缓存达到一定大小，尝试同步
    if (m_auditEventCache.size() >= AUDIT_CACHE_MAX_SIZE) {
        syncAuditEvents();
    }
}

bool KeyManager::syncAuditEvents() {
    if (m_auditEventCache.empty()) {
        return true;
    }
    
    std::vector<AuditEvent> eventsToSync;
    {
        std::lock_guard<std::mutex> lock(m_auditCacheMutex);
        eventsToSync = m_auditEventCache;
        m_auditEventCache.clear();
    }
    
    // TODO: 实现与服务器的实际同步
    // 临时：将事件写入本地文件
    try {
        std::ofstream outFile(AUDIT_CACHE_PATH, std::ios::app);
        if (!outFile.is_open()) {
            LOG_KEY_ERROR("无法打开审计文件: " << AUDIT_CACHE_PATH);
            
            // 恢复缓存
            std::lock_guard<std::mutex> lock(m_auditCacheMutex);
            m_auditEventCache.insert(m_auditEventCache.begin(), eventsToSync.begin(), eventsToSync.end());
            return false;
        }
        
        for (const auto& event : eventsToSync) {
            outFile << event.timestamp << "|"
                    << static_cast<int>(event.type) << "|"
                    << event.userId << "|"
                    << event.resourceId << "|"
                    << event.resourceType << "|"
                    << event.result << "|"
                    << event.details << std::endl;
        }
        
        outFile.close();
        LOG_KEY_INFO("同步 " << eventsToSync.size() << " 个审计事件");
        return true;
    } catch (const std::exception& e) {
        LOG_KEY_ERROR("同步审计事件异常: " << e.what());
        
        // 恢复缓存
        std::lock_guard<std::mutex> lock(m_auditCacheMutex);
        m_auditEventCache.insert(m_auditEventCache.begin(), eventsToSync.begin(), eventsToSync.end());
        return false;
    }
}

bool KeyManager::loadKeysFromStorage() {
    try {
        // Check existence before opening to provide clearer log messages
        if (!fs::exists(KEY_STORAGE_PATH)) {
            LOG_KEY_INFO("密钥存储文件不存在，将创建新文件: " << KEY_STORAGE_PATH);
            return true; // Not an error if it doesn't exist yet
        }

        std::ifstream inFile(KEY_STORAGE_PATH, std::ios::binary);
        if (!inFile.is_open()) {
            LOG_KEY_ERROR("无法打开密钥存储文件: " << KEY_STORAGE_PATH);
            return false; // It exists but couldn't be opened
        }
        
        // TODO: 实现实际的密钥加载逻辑
        // 临时：简单的格式读取
        std::string line;
        while (std::getline(inFile, line)) {
            std::istringstream iss(line);
            std::string keyId, typeStr, statusStr, algorithm, createdAt, expiresAt, associatedId, hasParentKeyStr, parentKeyId;
            
            if (std::getline(iss, keyId, '|') &&
                std::getline(iss, typeStr, '|') &&
                std::getline(iss, statusStr, '|') &&
                std::getline(iss, algorithm, '|') &&
                std::getline(iss, createdAt, '|') &&
                std::getline(iss, expiresAt, '|') &&
                std::getline(iss, associatedId, '|') &&
                std::getline(iss, hasParentKeyStr, '|') &&
                std::getline(iss, parentKeyId, '|')) {
                
                auto keyInfo = std::make_shared<KeyInfo>();
                keyInfo->keyId = keyId;
                keyInfo->type = static_cast<KeyType>(std::stoi(typeStr));
                keyInfo->status = static_cast<KeyStatus>(std::stoi(statusStr));
                keyInfo->algorithm = algorithm;
                keyInfo->createdAt = createdAt;
                keyInfo->expiresAt = expiresAt;
                keyInfo->associatedId = associatedId;
                keyInfo->hasParentKey = (hasParentKeyStr == "1");
                keyInfo->parentKeyId = parentKeyId;
                
                std::lock_guard<std::mutex> lock(m_keyCacheMutex);
                m_keyCache[keyId] = keyInfo;
            }
        }
        
        inFile.close();
        LOG_KEY_INFO("加载了 " << m_keyCache.size() << " 个密钥");
        return true;
    } catch (const std::exception& e) {
        LOG_KEY_ERROR("加载密钥异常: " << e.what());
        return false;
    }
}

bool KeyManager::saveKeysToStorage() {
    try {
        // Ensure directory exists? Might be needed if path includes directories
        fs::path storagePath(KEY_STORAGE_PATH);
        if (storagePath.has_parent_path()) {
            try { // Creating directories might fail
               fs::create_directories(storagePath.parent_path());
            } catch (const fs::filesystem_error& e) {
                 LOG_KEY_ERROR("无法创建密钥存储目录: " << e.what());
                 return false;
            }
        }
        std::ofstream outFile(KEY_STORAGE_PATH, std::ios::binary);
        if (!outFile.is_open()) {
            LOG_KEY_ERROR("无法打开密钥存储文件进行保存: " << KEY_STORAGE_PATH);
            return false;
        }
        
        std::lock_guard<std::mutex> lock(m_keyCacheMutex);
        for (const auto& pair : m_keyCache) {
            const auto& keyInfo = pair.second;
            outFile << keyInfo->keyId << "|"
                    << static_cast<int>(keyInfo->type) << "|"
                    << static_cast<int>(keyInfo->status) << "|"
                    << keyInfo->algorithm << "|"
                    << keyInfo->createdAt << "|"
                    << keyInfo->expiresAt << "|"
                    << keyInfo->associatedId << "|"
                    << (keyInfo->hasParentKey ? "1" : "0") << "|"
                    << keyInfo->parentKeyId << std::endl;
        }
        
        outFile.close();
        LOG_KEY_INFO("保存了 " << m_keyCache.size() << " 个密钥");
        return true;
    } catch (const std::exception& e) {
        LOG_KEY_ERROR("保存密钥异常: " << e.what());
        return false;
    }
}

std::string KeyManager::generateKeyId() {
    // 简单格式：前缀+时间戳+随机字符串
    std::string prefix = "KEY-";
    auto now = std::chrono::system_clock::now();
    auto duration = now.time_since_epoch();
    auto millis = std::chrono::duration_cast<std::chrono::milliseconds>(duration).count();
    std::string timestamp = std::to_string(millis);
    std::string random = generateRandomString(8);
    
    return prefix + timestamp + "-" + random;
}

bool KeyManager::validateKey(const std::string& keyId) {
    std::lock_guard<std::mutex> lock(m_keyCacheMutex);
    auto it = m_keyCache.find(keyId);
    if (it == m_keyCache.end()) {
        return false;
    }
    
    // 检查密钥状态
    KeyStatus status = it->second->status;
    return (status == KeyStatus::ACTIVE || status == KeyStatus::ROTATING);
}

void KeyManager::cacheAuditEvent(const AuditEvent& event) {
    std::lock_guard<std::mutex> lock(m_auditCacheMutex);
    m_auditEventCache.push_back(event);
}

bool KeyManager::flushAuditEventCache() {
    return syncAuditEvents();
}

std::vector<char> KeyManager::getEncryptedKeyMaterial(const std::string& keyId) {
    std::lock_guard<std::mutex> lock(m_keyCacheMutex);
    auto it = m_keyCache.find(keyId);
    if (it != m_keyCache.end()) {
        return it->second->encryptedKeyMaterial;
    }
    LOG_KEY_ERROR("getEncryptedKeyMaterial: 密钥未找到 " << keyId);
    return {};
}

std::vector<unsigned char> KeyManager::decryptKeyMaterial(const std::string& keyId) {
    std::vector<char> encryptedMaterial = getEncryptedKeyMaterial(keyId);
    if (encryptedMaterial.empty()) {
        LOG_KEY_ERROR("decryptKeyMaterial: 获取加密密钥失败 " << keyId);
        return {};
    }

    unsigned char* decrypted_data = nullptr;
    size_t decrypted_data_size = 0;

    // 调用平台特定的解密函数 (需要 platform_utils.h)
    // 注意：platform_utils_win.c 现在包含这个函数
    #include "../common/src/platform/platform_utils.h" // 临时的 include，最好在头文件或 PCH 中管理

    if (platform_unprotect_data(
            reinterpret_cast<const unsigned char*>(encryptedMaterial.data()), 
            encryptedMaterial.size(),
            &decrypted_data,
            &decrypted_data_size)) 
    {
        if (decrypted_data && decrypted_data_size > 0) {
            // 成功解密，复制到返回的 vector 中
            std::vector<unsigned char> result(decrypted_data, decrypted_data + decrypted_data_size);
            
            // 重要：立即释放平台函数分配的内存，并安全擦除！
            platform_secure_zero_memory(decrypted_data, decrypted_data_size);
            platform_free_memory(decrypted_data); 
            
            LOG_KEY_DEBUG("密钥解密成功: " << keyId);
            return result;
        } else {
            // 解密成功但数据为空或无效？
            LOG_KEY_ERROR("decryptKeyMaterial: platform_unprotect_data 返回成功但数据无效 " << keyId);
            if (decrypted_data) { // 如果分配了内存，也要释放
                 platform_secure_zero_memory(decrypted_data, decrypted_data_size);
                 platform_free_memory(decrypted_data);
            }
            return {};
        }
    } else {
        LOG_KEY_ERROR("decryptKeyMaterial: platform_unprotect_data 失败 " << keyId);
        return {};
    }
}

// --- 以下是文件读写和字符串转换的辅助函数 ---

// 辅助函数：wstring to string (简单实现, 生产环境可能需要更健壮的转换)
std::string wstringToString(const std::wstring& wstr) {
    if (wstr.empty()) return std::string();
    int size_needed = WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), NULL, 0, NULL, NULL);
    std::string strTo(size_needed, 0);
    WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), &strTo[0], size_needed, NULL, NULL);
    return strTo;
}

// 辅助函数：读取文件内容到vector (已存在并假设正确)
std::vector<uint8_t> readFileContent(const std::wstring& filePath) {
    std::ifstream file(filePath, std::ios::binary | std::ios::ate);
    if (!file) {
        throw std::runtime_error("无法打开文件: " + wstringToString(filePath));
    }
    std::streamsize size = file.tellg();
    file.seekg(0, std::ios::beg);
    
    std::vector<uint8_t> buffer(static_cast<size_t>(size));
    if (size > 0) { // 只有当文件非空时才读取
        if (!file.read(reinterpret_cast<char*>(buffer.data()), size)) {
            throw std::runtime_error("读取文件时发生错误: " + wstringToString(filePath));
        }
    }
    return buffer;
}

// 辅助函数：将vector内容写入文件 (覆盖) (已存在并假设正确)
bool writeFileContent(const std::wstring& filePath, const std::vector<uint8_t>& data) {
    std::ofstream file(filePath, std::ios::binary | std::ios::trunc);
    if (!file) {
        return false;
    }
    if (!data.empty()) {
        if (!file.write(reinterpret_cast<const char*>(data.data()), data.size())) {
            return false;
        }
    }
    return true;
}

// --- 文件加密/解密实现 (流式) ---
#include <fstream>
#include <memory> // for unique_ptr

// 假设的 CryptoUtils 流式接口
namespace crypto { namespace api { namespace stream {
    // 简化接口，实际需要更复杂的上下文管理
    class CryptoStreamProcessor {
    public:
        virtual ~CryptoStreamProcessor() = default;
        virtual bool initialize(const std::vector<unsigned char>& key, bool encrypt) = 0;
        virtual bool update(const std::vector<uint8_t>& chunk_in, std::vector<uint8_t>& chunk_out) = 0;
        virtual bool finalize(std::vector<uint8_t>& final_chunk_out) = 0;
    };
    // 工厂函数或其他方式创建实例
    // 声明一个假设的工厂函数，实际实现需要在 CryptoUtils 中
    std::unique_ptr<CryptoStreamProcessor> createStreamProcessor(CryptoUtils::SymmetricAlgorithm algorithm);
}}}

// 流式文件处理核心逻辑
bool KeyManager::processFileStream(
    const std::wstring& inputFilePath,
    const std::wstring& outputFilePath,
    const std::vector<unsigned char>& key,
    bool encrypt)
{
    const size_t BUFFER_SIZE = 4096; // 4KB buffer
    std::vector<uint8_t> inBuffer(BUFFER_SIZE);
    std::vector<uint8_t> outBuffer; // CryptoUtils 会调整大小

    std::ifstream inFile(inputFilePath, std::ios::binary);
    if (!inFile) {
        LOG_KEY_ERROR("无法打开输入文件进行流式处理: " << std::string(inputFilePath.begin(), inputFilePath.end()));
        return false;
    }

    std::ofstream outFile(outputFilePath, std::ios::binary | std::ios::trunc);
    if (!outFile) {
        LOG_KEY_ERROR("无法打开输出文件进行流式处理: " << std::string(outputFilePath.begin(), outputFilePath.end()));
        return false;
    }

    // 假设 crypto::api::stream::createStreamProcessor 存在并返回处理器实例
    // 实际需要根据 CryptoUtils 的实现调整
    // 使用 :: 明确指定全局命名空间或 crypto::api::stream::
    std::unique_ptr<crypto::api::stream::CryptoStreamProcessor> processor = 
        crypto::api::stream::createStreamProcessor(crypto::api::CryptoUtils::SymmetricAlgorithm::AES_256_GCM);

    if (!processor) {
         LOG_KEY_ERROR("无法创建流式加密/解密处理器");
         inFile.close(); // Ensure files are closed on early exit
         outFile.close();
         return false;
    }

    // 初始化加密/解密器
    if (!processor->initialize(key, encrypt)) {
         LOG_KEY_ERROR("初始化流式处理器失败");
         inFile.close();
         outFile.close();
         return false;
    }


    bool success = true;
    try {
        while (inFile) {
            inFile.read(reinterpret_cast<char*>(inBuffer.data()), BUFFER_SIZE);
            std::streamsize bytesRead = inFile.gcount();
            if (bytesRead > 0) {
                std::vector<uint8_t> currentChunk(inBuffer.begin(), inBuffer.begin() + bytesRead);
                if (!processor->update(currentChunk, outBuffer)) {
                    LOG_KEY_ERROR("流式处理更新失败");
                    success = false;
                    break;
                }
                if (!outBuffer.empty()) {
                    outFile.write(reinterpret_cast<const char*>(outBuffer.data()), outBuffer.size());
                     if (!outFile) { // 检查写入错误
                        LOG_KEY_ERROR("写入输出文件流失败");
                        success = false;
                        break;
                    }
                }
            }
             // If less than buffer size was read, it's EOF or error
            if (bytesRead < BUFFER_SIZE) {
                 if (inFile.eof()) {
                     // Reached end of file normally
                     break; 
                 } else if (inFile.fail()) {
                     // Handle potential read error (other than EOF)
                     LOG_KEY_ERROR("读取输入文件流时发生错误");
                     success = false;
                     break;
                 }
             }
        }

        // Only finalize if update loop completed successfully
        if (success) {
            // 完成处理
            if (!processor->finalize(outBuffer)) {
                 LOG_KEY_ERROR("流式处理完成失败");
                 success = false;
            }
             if (success && !outBuffer.empty()) {
                outFile.write(reinterpret_cast<const char*>(outBuffer.data()), outBuffer.size());
                 if (!outFile) { // 检查写入错误
                    LOG_KEY_ERROR("写入最终块到输出文件流失败");
                    success = false;
                 }
            }
        }

    } catch (const std::exception& e) {
        LOG_KEY_ERROR("流式文件处理时发生异常: " << e.what());
        success = false;
    } // catch block automatically handles stack unwinding for processor

    // Ensure files are closed regardless of success/failure within try-catch
    inFile.close();
    outFile.close();

    // 如果处理失败，尝试删除不完整的输出文件
    if (!success) {
        LOG_KEY_INFO("由于处理失败，尝试删除输出文件: " << std::string(outputFilePath.begin(), outputFilePath.end()));
        std::error_code ec;
        if (!fs::remove(outputFilePath, ec)) { // Use filesystem library correctly
             LOG_KEY_WARNING("删除不完整的输出文件失败: " << ec.message());
        } else {
             LOG_KEY_INFO("成功删除不完整的输出文件");
        }
    }


    return success;
}


bool KeyManager::encryptFileStream(const std::wstring& inputFilePath, const std::wstring& outputFilePath, const std::string& keyIdHint) {
    LOG_KEY_DEBUG("开始流式加密文件: " << wstringToString(inputFilePath) << " -> " << wstringToString(outputFilePath));
    if (!m_initialized) {
        LOG_KEY_ERROR("流式加密文件失败: KeyManager未初始化");
        return false;
    }

    std::string actualKeyId = keyIdHint;
    std::string inputPathStr = wstringToString(inputFilePath);
    std::string outputPathStr = wstringToString(outputFilePath);

    if (actualKeyId.empty()) {
        actualKeyId = getDocumentKey(inputPathStr, true); // 获取或创建文档密钥
        if (actualKeyId.empty()) {
            LOG_KEY_ERROR("流式加密文件失败: 无法获取或创建文档密钥 for " << inputPathStr);
            return false;
        }
        LOG_KEY_INFO("encryptFileStream: Using document key " << actualKeyId << " for input file: " << inputPathStr);
    }

    std::shared_ptr<KeyInfo> keyInfo = getKeyInfo(actualKeyId);
    if (!keyInfo || !isKeyActive(actualKeyId) || keyInfo->rawKeyMaterial.empty()) {
        LOG_KEY_ERROR("流式加密文件失败: 密钥无效、非活动状态或原始密钥为空 (Key ID: " << actualKeyId << ")");
        return false;
    }

    // 假设 processFileStream 接收原始密钥和算法 (其内部逻辑待重构)
    // 注意：这里直接使用了 keyInfo->rawKeyMaterial，需要确保其生命周期
    // 在 processFileStream 调用期间有效，并且注意线程安全（如果process是异步的）
    bool result = processFileStream(
        inputFilePath,
        outputFilePath,
        keyInfo->rawKeyMaterial, // 传递原始密钥
        true                   // true for encrypt
        // , keyInfo->algorithm // 如果 processFileStream 需要算法字符串
    );

    if (result) {
        // 触发审计事件
        AuditEvent event;
        event.type = AuditEventType::DOCUMENT_ENCRYPTED; // Consider a STREAM_ENCRYPTED type?
        event.resourceId = inputPathStr;
        event.resourceType = "file_stream";
        event.timestamp = getCurrentTimestamp();
        event.result = "success";
        event.details = "{\"keyId\":\"" + actualKeyId + "\", \"algorithm\":\"" + keyInfo->algorithm + "\", \"output\":\"" + outputPathStr + "\"}";
        logAuditEvent(event);
        LOG_KEY_INFO("成功流式加密文件: " << inputPathStr << " to " << outputPathStr << " using key: " << actualKeyId);
    } else {
         LOG_KEY_ERROR("流式加密文件失败 (processFileStream returned false)");
    }

    return result;
}

bool KeyManager::decryptFileStream(const std::wstring& inputFilePath, const std::wstring& outputFilePath) {
    LOG_KEY_DEBUG("开始流式解密文件: " << wstringToString(inputFilePath) << " -> " << wstringToString(outputFilePath));
    if (!m_initialized) {
        LOG_KEY_ERROR("流式解密文件失败: KeyManager未初始化");
        return false;
    }
    std::string inputPathStr = wstringToString(inputFilePath);
    std::string outputPathStr = wstringToString(outputFilePath);

    // 关键问题: 如何确定用于解密的 keyId?
    // 临时解决方案: 尝试使用与输入文件路径关联的文档密钥ID来解密。
    std::string keyId = getDocumentKey(inputPathStr, false); // 只获取，不创建
    if (keyId.empty()) {
        LOG_KEY_ERROR("流式解密文件失败: 无法找到与文件关联的文档密钥 ID for " << inputPathStr);
        return false;
    }
    LOG_KEY_INFO("decryptFileStream: Attempting to use document key " << keyId << " for input file: " << inputPathStr);

    std::shared_ptr<KeyInfo> keyInfo = getKeyInfo(keyId);
    if (!keyInfo || keyInfo->rawKeyMaterial.empty()) {
        LOG_KEY_ERROR("流式解密文件失败: 密钥不存在或原始密钥为空 (Key ID: " << keyId << ")");
        return false;
    }
    
    // 假设 processFileStream 接收原始密钥和算法 (其内部逻辑待重构)
    bool result = processFileStream(
        inputFilePath,
        outputFilePath,
        keyInfo->rawKeyMaterial, // 传递原始密钥
        false                  // false for decrypt
        // , keyInfo->algorithm // 如果 processFileStream 需要算法字符串
    );

    if (result) {
         // 触发审计事件
        AuditEvent event;
        event.type = AuditEventType::DOCUMENT_DECRYPTED; // STREAM_DECRYPTED?
        event.resourceId = inputPathStr;
        event.resourceType = "file_stream";
        event.timestamp = getCurrentTimestamp();
        event.result = "success";
        event.details = "{\"keyId\":\"" + keyId + "\", \"algorithm\":\"" + keyInfo->algorithm + "\", \"output\":\"" + outputPathStr + "\"}";
        logAuditEvent(event);
        LOG_KEY_INFO("成功流式解密文件: " << inputPathStr << " to " << outputPathStr << " using key: " << keyId);
    } else {
        LOG_KEY_ERROR("流式解密文件失败 (processFileStream returned false)");
    }

    return result;
} 