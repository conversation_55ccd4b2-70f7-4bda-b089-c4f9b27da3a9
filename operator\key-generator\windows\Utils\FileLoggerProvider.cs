using Microsoft.Extensions.Logging;
using System;
using System.IO;
using System.Threading;

namespace KeyGenerator.Utils
{
    /// <summary>
    /// 简单的文件日志提供者
    /// </summary>
    public class FileLoggerProvider(string filePath) : ILoggerProvider
    {
        private readonly string _filePath = filePath;

        public ILogger CreateLogger(string categoryName)
        {
            return new FileLogger(_filePath, categoryName);
        }

        public void Dispose()
        {
            GC.SuppressFinalize(this);
        }
    }

    /// <summary>
    /// 简单的文件日志记录器
    /// </summary>
    public class FileLogger(string filePath, string categoryName) : ILogger
    {
        private readonly string _filePath = filePath;
        private readonly string _categoryName = categoryName;
        private readonly Lock _lock = new();

        public IDisposable? BeginScope<TState>(TState state) where TState : notnull => null;

        public bool IsEnabled(LogLevel logLevel) => true;

        public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception? exception, Func<TState, Exception?, string> formatter)
        {
            if (!IsEnabled(logLevel))
                return;

            try
            {
                lock (_lock)
                {
                    var message = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{logLevel}] [{_categoryName}] {formatter(state, exception)}";
                    if (exception != null)
                    {
                        message += $"\n异常: {exception}";
                    }
                    message += "\n";

                    File.AppendAllText(_filePath, message);
                }
            }
            catch
            {
                // 忽略日志写入失败
            }
        }
    }
} 