#pragma once

#include <string>
#include <memory>
#include "key_service_client.h"
#include "key_lifecycle_client.h"

namespace crypto {
namespace api {

/**
 * 密钥API客户端配置
 */
struct ApiClientConfig {
    std::string serviceUrl;     // 服务URL
    std::string apiKey;         // API密钥
    int timeoutMs = 5000;       // 超时(毫秒)
    bool useTls = true;         // 是否使用TLS
};

/**
 * 统一API客户端
 * 集成密钥服务和生命周期管理功能
 */
class ApiClient {
public:
    /**
     * 构造函数
     * @param config 客户端配置
     */
    explicit ApiClient(const ApiClientConfig& config);

    /**
     * 析构函数
     */
    ~ApiClient();

    // 禁止复制
    ApiClient(const ApiClient&) = delete;
    ApiClient& operator=(const ApiClient&) = delete;

    /**
     * 获取密钥服务客户端
     * @return 密钥服务客户端
     */
    KeyServiceClient& GetKeyServiceClient();

    /**
     * 获取密钥生命周期管理客户端
     * @return 密钥生命周期管理客户端
     */
    KeyLifecycleClient& GetKeyLifecycleClient();

private:
    class Impl;
    std::unique_ptr<Impl> pImpl_;
};

} // namespace api
} // namespace crypto 