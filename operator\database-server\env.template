# =====================================================
# 企业文档加密系统 - 数据库配置环境变量
# 版本: 1.4.0
# 说明: 复制为 .env 文件并修改密码
# 专注支持: PostgreSQL 数据库系统
# =====================================================

# PostgreSQL 数据库配置
POSTGRES_PASSWORD=CryptoSystem2025!

# 数据库连接配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=cryptosystem
DB_USER=crypto
DB_PASSWORD=19891025

# 备份配置
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_LOCATION=./backups
BACKUP_COMPRESSION=gzip

# 监控配置
MONITORING_ENABLED=true
METRICS_PORT=9187
HEALTH_CHECK_INTERVAL=60

# 安全配置
DB_SSL_MODE=prefer
DB_MAX_CONNECTIONS=200
DB_CONNECTION_TIMEOUT=30
DB_IDLE_TIMEOUT=600

# 性能配置
DB_SHARED_BUFFERS=256MB
DB_EFFECTIVE_CACHE_SIZE=1GB
DB_WORK_MEM=4MB
DB_MAINTENANCE_WORK_MEM=64MB

# 日志配置
LOG_LEVEL=INFO
LOG_RETENTION_DAYS=7
LOG_MAX_SIZE=100MB
LOG_LOCATION=./logs

# 告警配置
ALERT_EMAIL_ENABLED=false
ALERT_EMAIL_RECIPIENTS=<EMAIL>
ALERT_DISK_THRESHOLD=85
ALERT_CONNECTION_THRESHOLD=180 