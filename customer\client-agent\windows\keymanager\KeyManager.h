﻿#pragma once

#include <string>
#include <memory>
#include <vector>
#include <map>
#include <functional>
#include <mutex>
#include "../common/AuditTypes.h" // 包含共享的审计类型定义

// 前向声明
class PolicyManager;

// 密钥类型枚举
enum class KeyType {
    USER,       // 用户密钥
    DOCUMENT,   // 文档密钥
    SYSTEM,     // 系统密钥
    GROUP       // 组织/部门密钥
};

// 密钥状态枚举
enum class KeyStatus {
    ACTIVE,     // 活跃状态
    ROTATING,   // 轮换中
    EXPIRED,    // 已过期
    REVOKED     // 已撤销
};

// 密钥信息结构
struct KeyInfo {
    std::string keyId;
    KeyType type;
    KeyStatus status;
    std::string algorithm;      // 如 "SM4-GCM", "AES-256-GCM"
    std::string createdAt;      // ISO 8601 格式
    std::string expiresAt;      // ISO 8601 格式，可为空表示永不过期
    std::string associatedId;   // 关联的用户/文档/组ID
    bool hasParentKey;          // 是否有父密钥
    std::string parentKeyId;    // 父密钥ID
    
    // 由 DPAPI 加密的原始密钥材料，用于持久化
    std::vector<char> encryptedKeyMaterial; 
    
    // 解密后的原始密钥材料，用于运行时加解密操作
    // 由 loadKeysFromStorage 解密填充，在使用后可能需要安全擦除
    std::vector<unsigned char> rawKeyMaterial; 
};

// 审计事件类型和结构定义已移至 ../common/AuditTypes.h (CryptoAudit命名空间)
// 使用 using 声明来简化访问
using AuditEventType = CryptoAudit::AuditEventType;
using AuditEvent = CryptoAudit::AuditEvent;

// 密钥管理器类
class KeyManager {
public:
    // 单例访问
    static KeyManager& getInstance();
    
    // 初始化
    bool initialize();
    void shutdown();
    
    // 与策略管理器集成
    void setPolicyManager(std::shared_ptr<PolicyManager> policyManager);
    
    // 密钥操作
    std::string createKey(KeyType type, const std::string& associatedId);
    bool rotateKey(const std::string& keyId);
    bool revokeKey(const std::string& keyId);
    
    // 密钥检索
    std::shared_ptr<KeyInfo> getKeyInfo(const std::string& keyId);
    std::vector<std::shared_ptr<KeyInfo>> getKeysByType(KeyType type);
    std::string getDocumentKey(const std::string& documentId, bool createIfNotExist = true);
    std::string getUserKey(const std::string& userId, bool createIfNotExist = true);
    
    // 加密/解密操作
    std::vector<uint8_t> encrypt(const std::vector<uint8_t>& data, const std::string& keyId);
    std::vector<uint8_t> decrypt(const std::vector<uint8_t>& data, const std::string& keyId);
    
    // 文件加密/解密
    bool encryptFile(const std::wstring& filePath, const std::string& keyId = "");
    bool decryptFile(const std::wstring& filePath);
    
    // 文件加密/解密 (流式处理，内存优化)
    bool encryptFileStream(const std::wstring& inputFilePath, const std::wstring& outputFilePath, const std::string& keyId = "");
    bool decryptFileStream(const std::wstring& inputFilePath, const std::wstring& outputFilePath);
    
    // 审计功能
    void logAuditEvent(const CryptoAudit::AuditEvent& event);
    bool syncAuditEvents(); // 将本地审计事件同步到服务器
    
    // 检查密钥是否存在且活动
    bool isKeyActive(const std::string& keyId);

    // 获取加密后的密钥材料 (只应在内部或受信任的模块中使用)
    // **注意**: 返回的是加密后的数据，不应直接用于加密操作！
    std::vector<char> getEncryptedKeyMaterial(const std::string& keyId);

    // 解密密钥材料 (返回的 vector 需要调用者负责安全擦除！)
    // **已废弃/内部化**: 解密应在加载时完成并存储在 KeyInfo::rawKeyMaterial。
    // 此函数可能不再需要对外暴露，或者仅用于调试。
    // std::vector<unsigned char> decryptKeyMaterial(const std::string& keyId);
    
    // 新增：获取解密后的原始密钥材料 (内部使用为主)
    // 返回一个常量引用，避免不必要的拷贝
    // 使用前需检查 isKeyActive() 和 rawKeyMaterial 是否为空
    const std::vector<unsigned char>& getRawKeyMaterial(const std::string& keyId);

private:
    KeyManager(); // 私有构造函数(单例模式)
    ~KeyManager();
    
    // 禁止复制
    KeyManager(const KeyManager&) = delete;
    KeyManager& operator=(const KeyManager&) = delete;
    
    // 内部方法
    bool loadKeysFromStorage();
    bool saveKeysToStorage();
    std::string generateKeyId();
    // bool validateKey(const std::string& keyId); // 可能不再需要，或合并到 getKeyInfo/isKeyActive
    
    // 新增：内部解密函数，被 loadKeysFromStorage 调用
    bool decryptAndStoreRawKey(const std::shared_ptr<KeyInfo>& keyInfo);
    
    // 审计事件缓存管理
    void cacheAuditEvent(const CryptoAudit::AuditEvent& event);
    bool flushAuditEventCache();
    
    // 流式文件处理辅助函数
    bool processFileStream(
        const std::wstring& inputFilePath, 
        const std::wstring& outputFilePath, 
        const std::vector<unsigned char>& key, 
        bool encrypt); // true for encrypt, false for decrypt
    
    // 数据成员
    std::shared_ptr<PolicyManager> m_policyManager;
    std::map<std::string, std::shared_ptr<KeyInfo>> m_keyCache;
    std::vector<CryptoAudit::AuditEvent> m_auditEventCache;
    std::mutex m_keyCacheMutex;
    std::mutex m_auditCacheMutex;
    bool m_initialized;

    // 用于存储原始密钥的 map (可选，如果不在 KeyInfo 中存储)
    // std::map<std::string, std::vector<unsigned char>> m_rawKeyMap; 
    // 决定将 rawKeyMaterial 存储在 KeyInfo 中，更内聚。
}; 