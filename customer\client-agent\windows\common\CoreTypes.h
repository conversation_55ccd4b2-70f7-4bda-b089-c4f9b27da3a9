﻿#pragma once

#include <string>

namespace CryptoSystem {

//==== 瀵嗛挜鐩稿叧绫诲瀷 ====

// 瀵嗛挜绫诲瀷鏋氫妇
enum class KeyType {
    USER,       // 鐢ㄦ埛瀵嗛挜
    DOCUMENT,   // 鏂囨。瀵嗛挜
    SYSTEM,     // 绯荤粺瀵嗛挜
    GROUP       // 缁勭粐/閮ㄩ棬瀵嗛挜
};

// 瀵嗛挜鐘舵€佹灇涓?
enum class KeyStatus {
    ACTIVE,     // 娲昏穬鐘舵€?
    ROTATING,   // 杞崲涓?
    EXPIRED,    // 宸茶繃鏈?
    REVOKED     // 宸叉挙閿€
};

// 瀵嗛挜淇℃伅缁撴瀯
struct KeyInfo {
    std::string keyId;
    KeyType type;
    KeyStatus status;
    std::string algorithm;      // 濡?"SM4", "AES-256"
    std::string createdAt;      // ISO 8601 鏍煎紡
    std::string expiresAt;      // ISO 8601 鏍煎紡锛屽彲涓虹┖琛ㄧず姘镐笉杩囨湡
    std::string associatedId;   // 鍏宠仈鐨勭敤鎴?鏂囨。/缁処D
    bool hasParentKey;          // 鏄惁鏈夌埗瀵嗛挜
    std::string parentKeyId;    // 鐖跺瘑閽D
};

//==== 瀹¤鐩稿叧绫诲瀷 ====

// 瀹¤浜嬩欢绫诲瀷鏋氫妇
enum class AuditEventType {
    // 瀵嗛挜鐩稿叧浜嬩欢
    KEY_CREATED,
    KEY_ACCESSED,
    KEY_ROTATED,
    KEY_EXPIRED,
    KEY_REVOKED,
    
    // 绛栫暐鐩稿叧浜嬩欢
    POLICY_APPLIED,
    POLICY_VIOLATED,
    
    // 鏂囨。鐩稿叧浜嬩欢
    DOCUMENT_ENCRYPTED,
    DOCUMENT_DECRYPTED,
    
    // 璁よ瘉鐩稿叧浜嬩欢
    AUTH_SUCCESS,
    AUTH_FAILURE,
    
    // 绯荤粺浜嬩欢
    SYSTEM_ERROR
};

// 瀹¤浜嬩欢缁撴瀯
struct AuditEvent {
    AuditEventType type;
    std::string userId;
    std::string resourceId;     // 鍙兘鏄枃浠禝D銆佸瘑閽D绛?
    std::string resourceType;   // 濡?"document", "key", "policy"
    std::string timestamp;      // ISO 8601 鏍煎紡
    std::string details;        // 棰濆淇℃伅JSON
    std::string result;         // "success", "failure", "denied"绛?
};

//==== 寮曟搸鐩稿叧绫诲瀷 ====

// 寮曟搸鐘舵€佹灇涓?
enum class EngineState {
    INIT,      // 鍒濆鍖栦腑
    RUNNING,   // 姝ｅ父杩愯?
    PAUSED,    // 鏆傚仠鐘舵€?
    ERROR      // 閿欒鐘舵€?
};

// 鏂囦欢浜嬩欢绫诲瀷鏋氫妇
enum class FileEventType {
    CREATED,
    MODIFIED,
    DELETED,
    RENAMED_OLD,  // 更新为更精确的重命名事件类型（旧名称）
    RENAMED_NEW,  // 更新为更精确的重命名事件类型（新名称）
    OVERFLOW,     // 添加用于处理缓冲区溢出情况的事件类型
    SCANNED       // 添加用于完整目录扫描时的事件类型
};

// 鏂囦欢浜嬩欢鏁版嵁缁撴瀯
struct FileEvent {
    FileEventType type;
    std::wstring filePath1;
    std::wstring filePath2; // Used for rename events
};

} // namespace CryptoSystem 
