/*
 * utils.c
 * 
 * 实用工具函数实现文件
 * 实现通用辅助函数
 */

#include <fltKernel.h>
#include <ntstrsafe.h>
#include "utils.h"

#define UTIL_ALLOCATION_TAG 'litU'

//
// 获取文件名
//
NTSTATUS
UtilGetFileName(
    _In_ PFLT_CALLBACK_DATA Data,
    _Out_ PUNICODE_STRING FileName
    )
{
    NTSTATUS status;
    PFLT_FILE_NAME_INFORMATION nameInfo = NULL;
    
    // 参数验证
    if (Data == NULL || FileName == NULL) {
        return STATUS_INVALID_PARAMETER;
    }
    
    // 获取文件名信息
    status = FltGetFileNameInformation(
        Data,
        FLT_FILE_NAME_NORMALIZED | FLT_FILE_NAME_QUERY_DEFAULT,
        &nameInfo
    );
    
    if (!NT_SUCCESS(status)) {
        return status;
    }
    
    // 解析文件名
    status = FltParseFileNameInformation(nameInfo);
    if (!NT_SUCCESS(status)) {
        FltReleaseFileNameInformation(nameInfo);
        return status;
    }
    
    // 复制文件名
    status = UtilDuplicateUnicodeString(&nameInfo->Name, FileName);
    
    // 释放文件名信息
    FltReleaseFileNameInformation(nameInfo);
    
    return status;
}

//
// 判断文件类型是否匹配
//
BOOLEAN
UtilIsFileTypeMatch(
    _In_ PCUNICODE_STRING FileName,
    _In_ PCUNICODE_STRING Extension
    )
{
    USHORT fileNameLength;
    USHORT extensionLength;
    PWCHAR fileNameBuffer;
    PWCHAR extensionBuffer;
    
    // 参数验证
    if (FileName == NULL || Extension == NULL ||
        FileName->Length == 0 || Extension->Length == 0) {
        return FALSE;
    }
    
    fileNameLength = FileName->Length / sizeof(WCHAR);
    extensionLength = Extension->Length / sizeof(WCHAR);
    
    // 如果扩展名比文件名长，肯定不匹配
    if (extensionLength > fileNameLength) {
        return FALSE;
    }
    
    fileNameBuffer = FileName->Buffer;
    extensionBuffer = Extension->Buffer;
    
    // 比较文件扩展名（不区分大小写）
    // 扩展名应该在文件名末尾，如".txt"
    return (_wcsnicmp(
        &fileNameBuffer[fileNameLength - extensionLength],
        extensionBuffer,
        extensionLength
    ) == 0);
}

//
// 判断路径是否匹配
//
BOOLEAN
UtilIsPathMatch(
    _In_ PCUNICODE_STRING FilePath,
    _In_ PCUNICODE_STRING Pattern
    )
{
    PWCHAR filePathBuffer;
    PWCHAR patternBuffer;
    USHORT filePathLength;
    USHORT patternLength;
    
    // 参数验证
    if (FilePath == NULL || Pattern == NULL ||
        FilePath->Length == 0 || Pattern->Length == 0) {
        return FALSE;
    }
    
    filePathBuffer = FilePath->Buffer;
    patternBuffer = Pattern->Buffer;
    filePathLength = FilePath->Length / sizeof(WCHAR);
    patternLength = Pattern->Length / sizeof(WCHAR);
    
    // 简单通配符匹配
    // 目前支持以下模式：
    // 1. 完全匹配
    // 2. 前缀匹配：以 * 结尾
    // 3. 后缀匹配：以 * 开头
    
    // 完全匹配
    if (patternLength > 0 && patternBuffer[patternLength - 1] != L'*' && patternBuffer[0] != L'*') {
        return (FilePath->Length == Pattern->Length &&
                _wcsnicmp(filePathBuffer, patternBuffer, filePathLength) == 0);
    }
    
    // 前缀匹配
    if (patternLength > 1 && patternBuffer[patternLength - 1] == L'*') {
        return (_wcsnicmp(
            filePathBuffer,
            patternBuffer,
            patternLength - 1
        ) == 0);
    }
    
    // 后缀匹配
    if (patternLength > 1 && patternBuffer[0] == L'*') {
        return (filePathLength >= patternLength - 1 &&
                _wcsnicmp(
                    &filePathBuffer[filePathLength - (patternLength - 1)],
                    &patternBuffer[1],
                    patternLength - 1
                ) == 0);
    }
    
    return FALSE;
}

//
// 获取进程名
//
NTSTATUS
UtilGetProcessName(
    _Out_ PUNICODE_STRING ProcessName
    )
{
    NTSTATUS status;
    PEPROCESS currentProcess;
    HANDLE processId;
    UCHAR nameBuffer[64] = {0};  // 进程名缓冲区
    
    // 参数验证
    if (ProcessName == NULL) {
        return STATUS_INVALID_PARAMETER;
    }
    
    // 获取当前进程
    currentProcess = PsGetCurrentProcess();
    if (currentProcess == NULL) {
        return STATUS_UNSUCCESSFUL;
    }
    
    // 获取进程ID
    processId = PsGetProcessId(currentProcess);
    
    // 获取进程名
    status = SeLocateProcessImageName(currentProcess, &ProcessName);
    if (!NT_SUCCESS(status)) {
        // 如果标准方法失败，尝试使用PsGetProcessImageFileName
        // 这个函数不需要导出成功就可以使用
        PUCHAR rawName = PsGetProcessImageFileName(currentProcess);
        if (rawName != NULL) {
            RtlStringCbCopyA((PCHAR)nameBuffer, sizeof(nameBuffer), (PCHAR)rawName);
            status = RtlCreateUnicodeStringFromAsciiz(ProcessName, (PCSZ)nameBuffer);
        }
    }
    
    return status;
}

//
// 获取进程映像路径
//
NTSTATUS
UtilGetProcessImagePath(
    _Out_ PUNICODE_STRING ProcessPath
    )
{
    // 简单实现，直接调用UtilGetProcessName
    return UtilGetProcessName(ProcessPath);
}

//
// 获取当前用户SID
//
NTSTATUS
UtilGetCurrentUserSid(
    _Out_ PUNICODE_STRING UserSid
    )
{
    // 此函数实现较为复杂，需要获取当前进程的令牌，
    // 然后从令牌中提取用户SID
    // 在此简化实现，返回一个空字符串
    
    UserSid->Length = 0;
    UserSid->MaximumLength = 0;
    UserSid->Buffer = NULL;
    
    return STATUS_SUCCESS;
}

//
// 日志函数
//
VOID
UtilLogEvent(
    _In_ ULONG EventLevel,
    _In_ PCSTR Format,
    ...
    )
{
    va_list argList;
    char buffer[256];
    
    // 初始化可变参数列表
    va_start(argList, Format);
    
    // 格式化字符串
    RtlStringCbVPrintfA(buffer, sizeof(buffer), Format, argList);
    
    // 结束可变参数
    va_end(argList);
    
    // 根据事件级别，使用适当的日志函数
    switch (EventLevel) {
    case 1: // 错误
        DbgPrintEx(DPFLTR_IHVDRIVER_ID, DPFLTR_ERROR_LEVEL, "ENCFILTER ERROR: %s\n", buffer);
        break;
    case 2: // 警告
        DbgPrintEx(DPFLTR_IHVDRIVER_ID, DPFLTR_WARNING_LEVEL, "ENCFILTER WARNING: %s\n", buffer);
        break;
    case 3: // 信息
        DbgPrintEx(DPFLTR_IHVDRIVER_ID, DPFLTR_INFO_LEVEL, "ENCFILTER INFO: %s\n", buffer);
        break;
    case 4: // 跟踪
        DbgPrintEx(DPFLTR_IHVDRIVER_ID, DPFLTR_TRACE_LEVEL, "ENCFILTER TRACE: %s\n", buffer);
        break;
    default:
        DbgPrintEx(DPFLTR_IHVDRIVER_ID, DPFLTR_INFO_LEVEL, "ENCFILTER: %s\n", buffer);
        break;
    }
}

//
// 复制Unicode字符串
//
NTSTATUS
UtilDuplicateUnicodeString(
    _In_ PCUNICODE_STRING Source,
    _Out_ PUNICODE_STRING Destination
    )
{
    // 参数验证
    if (Source == NULL || Destination == NULL) {
        return STATUS_INVALID_PARAMETER;
    }
    
    // 如果源字符串为空，设置目标为空
    if (Source->Length == 0 || Source->Buffer == NULL) {
        Destination->Length = 0;
        Destination->MaximumLength = 0;
        Destination->Buffer = NULL;
        return STATUS_SUCCESS;
    }
    
    // 分配缓冲区
    Destination->Buffer = (PWCH)ExAllocatePoolWithTag(
        NonPagedPool,
        Source->Length,
        UTIL_ALLOCATION_TAG
    );
    
    if (Destination->Buffer == NULL) {
        return STATUS_INSUFFICIENT_RESOURCES;
    }
    
    // 复制内容
    RtlCopyMemory(Destination->Buffer, Source->Buffer, Source->Length);
    Destination->Length = Source->Length;
    Destination->MaximumLength = Source->Length;
    
    return STATUS_SUCCESS;
}

//
// 释放Unicode字符串
//
VOID
UtilFreeUnicodeString(
    _In_ PUNICODE_STRING String
    )
{
    if (String == NULL) {
        return;
    }
    
    if (String->Buffer != NULL) {
        ExFreePoolWithTag(String->Buffer, UTIL_ALLOCATION_TAG);
        String->Buffer = NULL;
    }
    
    String->Length = 0;
    String->MaximumLength = 0;
}

//
// 分配内存
//
PVOID
UtilAllocateMemory(
    _In_ SIZE_T Size,
    _In_ ULONG Tag
    )
{
    return ExAllocatePoolWithTag(NonPagedPool, Size, Tag);
}

//
// 释放内存
//
VOID
UtilFreeMemory(
    _In_ PVOID Memory
    )
{
    if (Memory != NULL) {
        ExFreePool(Memory);
    }
} 