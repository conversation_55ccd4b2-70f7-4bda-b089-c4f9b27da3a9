using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using CryptoSystem.SystemManager.Models;
using CryptoSystem.SystemManager.Data;

namespace CryptoSystem.SystemManager.Services
{
    /// <summary>
    /// 策略服务实现
    /// </summary>
    public class PolicyService : IPolicyService
    {
        private readonly SystemManagerDbContext _context;
        private readonly ILogger<PolicyService> _logger;

        public PolicyService(SystemManagerDbContext context, ILogger<PolicyService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<IEnumerable<Policy>> GetAllPoliciesAsync()
        {
            return await _context.Policies
                .Include(p => p.UserPolicies).ThenInclude(up => up.User)
                .Include(p => p.DevicePolicies).ThenInclude(dp => dp.Device)
                .OrderBy(p => p.PolicyName)
                .ToListAsync();
        }

        public async Task<Policy?> GetPolicyByIdAsync(string policyId)
        {
            return await _context.Policies
                .Include(p => p.UserPolicies).ThenInclude(up => up.User)
                .Include(p => p.DevicePolicies).ThenInclude(dp => dp.Device)
                .Include(p => p.PolicyDeployments)
                .FirstOrDefaultAsync(p => p.PolicyId == policyId);
        }

        public async Task<(bool Success, string Message)> CreatePolicyAsync(Policy policy, string createdBy)
        {
            try
            {
                // 验证策略名称唯一性
                if (await _context.Policies.AnyAsync(p => p.PolicyName == policy.PolicyName))
                {
                    return (false, "策略名称已存在");
                }

                // 验证策略内容
                var validationResult = ValidatePolicyContent(policy);
                if (!validationResult.IsValid)
                {
                    return (false, validationResult.ErrorMessage);
                }

                // 设置策略属性
                policy.PolicyId = Guid.NewGuid().ToString("N");
                policy.Status = PolicyStatus.Draft;
                policy.CreatedTime = DateTime.UtcNow;
                policy.CreatedBy = createdBy;
                policy.Version = "1.0";

                _context.Policies.Add(policy);
                await _context.SaveChangesAsync();

                _logger.LogInformation("策略 {PolicyName} 创建成功，创建者：{CreatedBy}", policy.PolicyName, createdBy);
                return (true, "策略创建成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建策略失败：{PolicyName}", policy.PolicyName);
                return (false, "创建策略时发生异常");
            }
        }

        public async Task<(bool Success, string Message)> UpdatePolicyAsync(Policy policy, string modifiedBy)
        {
            try
            {
                var existingPolicy = await _context.Policies.FindAsync(policy.PolicyId);
                if (existingPolicy == null)
                {
                    return (false, "策略不存在");
                }

                // 检查是否可以修改
                if (existingPolicy.Status == PolicyStatus.Active)
                {
                    return (false, "活动状态的策略无法修改，请先停用");
                }

                // 验证策略名称唯一性（排除当前策略）
                if (await _context.Policies.AnyAsync(p => p.PolicyName == policy.PolicyName && p.PolicyId != policy.PolicyId))
                {
                    return (false, "策略名称已存在");
                }

                // 验证策略内容
                var validationResult = ValidatePolicyContent(policy);
                if (!validationResult.IsValid)
                {
                    return (false, validationResult.ErrorMessage);
                }

                // 更新字段
                existingPolicy.PolicyName = policy.PolicyName;
                existingPolicy.PolicyType = policy.PolicyType;
                existingPolicy.Priority = policy.Priority;
                existingPolicy.PolicyContent = policy.PolicyContent;
                existingPolicy.Description = policy.Description;
                existingPolicy.ModifiedTime = DateTime.UtcNow;
                existingPolicy.ModifiedBy = modifiedBy;
                
                // 更新版本号
                var currentVersion = float.Parse(existingPolicy.Version);
                existingPolicy.Version = (currentVersion + 0.1f).ToString("F1");

                await _context.SaveChangesAsync();

                _logger.LogInformation("策略 {PolicyName} 更新成功，修改者：{ModifiedBy}", policy.PolicyName, modifiedBy);
                return (true, "策略更新成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新策略失败：{PolicyName}", policy.PolicyName);
                return (false, "更新策略时发生异常");
            }
        }

        public async Task<(bool Success, string Message)> DeletePolicyAsync(string policyId, string deletedBy)
        {
            try
            {
                var policy = await _context.Policies.FindAsync(policyId);
                if (policy == null)
                {
                    return (false, "策略不存在");
                }

                // 检查是否可以删除
                if (policy.Status == PolicyStatus.Active)
                {
                    return (false, "活动状态的策略无法删除，请先停用");
                }

                // 检查关联关系
                var hasUserAssignments = await _context.UserPolicies.AnyAsync(up => up.PolicyId == policyId);
                var hasDeviceAssignments = await _context.DevicePolicies.AnyAsync(dp => dp.PolicyId == policyId);
                
                if (hasUserAssignments || hasDeviceAssignments)
                {
                    return (false, "策略已关联用户或设备，无法删除");
                }

                _context.Policies.Remove(policy);
                await _context.SaveChangesAsync();

                _logger.LogInformation("策略 {PolicyName} 删除成功，删除者：{DeletedBy}", policy.PolicyName, deletedBy);
                return (true, "策略删除成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除策略失败：{PolicyId}", policyId);
                return (false, "删除策略时发生异常");
            }
        }

        public async Task<(bool Success, string Message)> DeployPolicyAsync(string policyId, IEnumerable<string> targetDeviceIds)
        {
            try
            {
                var policy = await _context.Policies.FindAsync(policyId);
                if (policy == null)
                {
                    return (false, "策略不存在");
                }

                if (policy.Status != PolicyStatus.Active)
                {
                    return (false, "只能部署活动状态的策略");
                }

                var deploymentId = Guid.NewGuid().ToString("N");
                var deploymentTime = DateTime.UtcNow;
                var successCount = 0;
                var failedCount = 0;
                var errorMessages = new List<string>();

                foreach (var deviceId in targetDeviceIds)
                {
                    try
                    {
                        var device = await _context.Devices.FindAsync(deviceId);
                        if (device == null)
                        {
                            failedCount++;
                            errorMessages.Add($"设备 {deviceId} 不存在");
                            continue;
                        }

                        if (device.Status != DeviceStatus.Online)
                        {
                            failedCount++;
                            errorMessages.Add($"设备 {device.DeviceName} 不在线");
                            continue;
                        }

                        // 创建部署记录
                        var deployment = new PolicyDeployment
                        {
                            DeploymentId = Guid.NewGuid().ToString("N"),
                            PolicyId = policyId,
                            DeviceId = deviceId,
                            DeploymentTime = deploymentTime,
                            Status = PolicyDeploymentStatus.InProgress,
                            DeployedBy = "System" // 可以从上下文获取
                        };

                        _context.PolicyDeployments.Add(deployment);

                        // 模拟发送策略到设备
                        var deploySuccess = await SendPolicyToDeviceAsync(deviceId, policy);
                        
                        if (deploySuccess)
                        {
                            deployment.Status = PolicyDeploymentStatus.Success;
                            deployment.CompletedTime = DateTime.UtcNow;
                            successCount++;
                        }
                        else
                        {
                            deployment.Status = PolicyDeploymentStatus.Failed;
                            deployment.ErrorMessage = "发送策略到设备失败";
                            failedCount++;
                            errorMessages.Add($"设备 {device.DeviceName} 部署失败");
                        }
                    }
                    catch (Exception ex)
                    {
                        failedCount++;
                        errorMessages.Add($"设备 {deviceId} 部署异常：{ex.Message}");
                    }
                }

                await _context.SaveChangesAsync();

                var message = $"策略部署完成，成功：{successCount}，失败：{failedCount}";
                if (errorMessages.Any())
                {
                    message += $"，错误详情：{string.Join("; ", errorMessages)}";
                }

                _logger.LogInformation("策略 {PolicyName} 部署完成，成功：{SuccessCount}，失败：{FailedCount}", 
                    policy.PolicyName, successCount, failedCount);

                return (successCount > 0, message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "部署策略失败：{PolicyId}", policyId);
                return (false, "部署策略时发生异常");
            }
        }

        public async Task<PolicyStatistics> GetPolicyStatisticsAsync()
        {
            var totalPolicies = await _context.Policies.CountAsync();
            var activePolicies = await _context.Policies.CountAsync(p => p.Status == PolicyStatus.Active);
            var draftPolicies = await _context.Policies.CountAsync(p => p.Status == PolicyStatus.Draft);
            var inactivePolicies = await _context.Policies.CountAsync(p => p.Status == PolicyStatus.Inactive);

            var policiesByType = await _context.Policies
                .GroupBy(p => p.PolicyType)
                .ToDictionaryAsync(g => g.Key, g => g.Count());

            var policiesByStatus = await _context.Policies
                .GroupBy(p => p.Status)
                .ToDictionaryAsync(g => g.Key, g => g.Count());

            var recentDeployments = await _context.PolicyDeployments
                .Where(pd => pd.DeploymentTime >= DateTime.UtcNow.AddDays(-7))
                .CountAsync();

            var successfulDeployments = await _context.PolicyDeployments
                .Where(pd => pd.Status == PolicyDeploymentStatus.Success)
                .CountAsync();

            var failedDeployments = await _context.PolicyDeployments
                .Where(pd => pd.Status == PolicyDeploymentStatus.Failed)
                .CountAsync();

            return new PolicyStatistics
            {
                TotalPolicies = totalPolicies,
                ActivePolicies = activePolicies,
                DraftPolicies = draftPolicies,
                InactivePolicies = inactivePolicies,
                PoliciesByType = policiesByType,
                PoliciesByStatus = policiesByStatus,
                RecentDeployments = recentDeployments,
                SuccessfulDeployments = successfulDeployments,
                FailedDeployments = failedDeployments,
                LastUpdated = DateTime.UtcNow
            };
        }

        #region 私有辅助方法

        /// <summary>
        /// 验证策略内容
        /// </summary>
        private (bool IsValid, string ErrorMessage) ValidatePolicyContent(Policy policy)
        {
            if (string.IsNullOrWhiteSpace(policy.PolicyName))
            {
                return (false, "策略名称不能为空");
            }

            if (string.IsNullOrWhiteSpace(policy.PolicyContent))
            {
                return (false, "策略内容不能为空");
            }

            // 这里可以添加更多的策略内容验证逻辑
            // 例如JSON格式验证、规则语法验证等

            return (true, string.Empty);
        }

        /// <summary>
        /// 发送策略到设备
        /// </summary>
        private async Task<bool> SendPolicyToDeviceAsync(string deviceId, Policy policy)
        {
            try
            {
                // 这里应该实现实际的设备通信逻辑
                // 暂时模拟发送成功
                await Task.Delay(100); // 模拟网络延迟
                
                _logger.LogInformation("策略 {PolicyName} 已发送到设备 {DeviceId}", policy.PolicyName, deviceId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发送策略到设备失败：{DeviceId}, {PolicyName}", deviceId, policy.PolicyName);
                return false;
            }
        }

        #endregion
    }

    /// <summary>
    /// 策略统计信息
    /// </summary>
    public class PolicyStatistics
    {
        public int TotalPolicies { get; set; }
        public int ActivePolicies { get; set; }
        public int DraftPolicies { get; set; }
        public int InactivePolicies { get; set; }
        public Dictionary<PolicyType, int> PoliciesByType { get; set; } = new();
        public Dictionary<PolicyStatus, int> PoliciesByStatus { get; set; } = new();
        public int RecentDeployments { get; set; }
        public int SuccessfulDeployments { get; set; }
        public int FailedDeployments { get; set; }
        public DateTime LastUpdated { get; set; }
    }
} 